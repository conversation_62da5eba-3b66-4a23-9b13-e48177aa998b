import {registerDatasource} from 'apptile-core';
import Findify from './Findify';
import RechargePayments from './RechargePayments';
import RestApi from './RestApi';
import Stamped from './Stamped';
import ApptileMFAuth from './ApptileMFAuth';
import ApptileQuiz from './ApptileQuiz';
import ApptileMFAuthentication from './ApptileMFAuthentication';
import StampedReviews from './StampedReviews';
import StampedRewards from './StampedRewards';
import ApptileSkinCare from './ApptileSkinCare';
import StorifyMe from './StorifyMe';
import ShopFlo from './ShopFlo';
import JudgeMe from './JudgeMe';
import YagiOrderCancellable from './YagiOrderCancellable';
import JoleneAuth from './JoleneOtpLogin';
import InstagramIntegration from './InstagramIntegration';
import SnackShackPartner from './SnackShackPartner';
import GoogleMaps from './GoogleMaps';
////////////////////
import Meragi from './Meragi';
import SimplyOtpLogin from './SimplyOtpLogin';
import Discourse from './Discourse';
import WizzySearch from './WizzySearch';
import RforRabbitVaccinationTracker from './RforRabbitVaccinationTracker';
import HRTech from './HRTech';
import Lively from './Lively';
import CloudSearch from './CloudSearch';
import OkendoReviews from './OkendoReviews';
import YotpoReviews from './YotpoReviews';
import ReviewsIoReviews from './ReviewsIoReviews';
import Zapiet from './Zapiet';
import Nosto from './Nosto';
import EasyAppointment from './EasyAppointment';
import LoyaltyLion from './LoyaltyLion';
import JoyLoyalty from './JoyLoyalty';
import Pazo from './Pazo';
import AppstleSubscriptions from './AppstleSubscriptions';
import RiseAI from './RiseAI/LoyaltyAndRewards';
import SwatchKing from './SwatchKing';
import ApptileCartUpsell from './ApptileCartUpsell';
import PreOrderWod from './PreOrderWod';
import ApptileCartDiscounts from './ApptileCartDiscounts';
import ApptilePinCodeChecker from './ApptilePinCodeChecker';
import Smile from './Smile';
import OmegaEstimate from './OmegaEstimate';
import Fera from './Fera';
import Rivo from './Rivo';
import Recurpay from './Recurpay';
import TryWithMirra from './TryWithMirra';
import LivelyShoppable from './LivelyShoppable';
import TokiRewards from './TokiRewards';
import Supbase from './Supbase';
import AppMarketplaceIap from './AppMarketplaceIap';

const datasourceToLoad = [
  AppMarketplaceIap,
  Findify,
  RestApi,
  Stamped,
  RechargePayments,
  ApptileMFAuth,
  ApptileQuiz,
  ApptileMFAuthentication,
  StampedReviews,
  StampedRewards,
  ApptileSkinCare,
  StorifyMe,
  ShopFlo,
  JudgeMe,
  YagiOrderCancellable,
  OmegaEstimate,
  JoleneAuth,
  InstagramIntegration,
  SnackShackPartner,
  GoogleMaps,
  Meragi,
  SimplyOtpLogin,
  Discourse,
  WizzySearch,
  RforRabbitVaccinationTracker,
  LoyaltyLion,
  JoyLoyalty,
  Lively,
  LivelyShoppable,
  HRTech,
  OkendoReviews,
  CloudSearch,
  YotpoReviews,
  ReviewsIoReviews,
  Zapiet,
  Nosto,
  EasyAppointment,
  Pazo,
  AppstleSubscriptions,
  RiseAI,
  SwatchKing,
  ApptileCartUpsell,
  PreOrderWod,
  ApptileCartDiscounts,
  ApptilePinCodeChecker,
  Smile,
  Fera,
  Rivo,
  Recurpay,
  TryWithMirra,
  TokiRewards,
  Supbase
];

// Please also add the datasource
// to IntegrationCodesMappingWithDataSource in constants folder to ensure future self serve

export function loadDatasourcePlugins() {
   datasourceToLoad.forEach(v => {
     registerDatasource(v.name, v);
   });
   return Promise.resolve(datasourceToLoad);
}

export default loadDatasourcePlugins;
export {processShopifyGraphqlQueryResponse, fetchInputFieldsForUIEditor} from './utils';
export {IntegrationCodesMappingWithDataSource} from './constants';
export {ApptileMFAuthDsName} from './ApptileMFAuth';
export {ApptileMFAuthenticationDsName} from './ApptileMFAuthentication';
export {IntegrationPlatformType} from './datasourceTypes';
