import Immutable from 'immutable';
import {call} from 'redux-saga/effects';
import {PluginConfigType} from 'apptile-core';
import {AppPageTriggerOptions, PluginListingSettings, PluginModelType, PluginPropertySettings} from 'apptile-core';
import wrapDatasourceModel from '../wrapDatasourceModel';
import {DatasourceQueryDetail, DatasourceQueryReturnValue} from 'apptile-core';
import {AjaxQueryRunner} from 'apptile-core';
import {DatasourcePluginConfig, ISupabaseCredentialResponse, ISupabaseCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {baseDatasourceConfig} from 'apptile-core';
import {Platform} from 'react-native';
import {createClient} from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type SupabaseConfigType = DatasourcePluginConfig &
  ISupabaseCredentials & {
    queryRunner: any;
    supabaseClient: any;
  };

type SupabaseQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  endpoint: string;
  contextInputParams?: {[key: string]: any};
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  transformer?: (data: any, paginationMeta: any) => any;
  paginationResolver?: (inputVariables: any, paginationMeta: any) => any;
};

const SupabaseApiRecords: Record<string, SupabaseQueryDetails> = {
  login: {
    queryType: "post",
    endpoint: "/auth/v1/token",
    editableInputParams: {
      email: "",
      password: "",
    },
    inputResolver: (inputVariables: any) => {
      const { email, password } = inputVariables;
      return {
        email,
        password,
      };
    },
  },
  logout: {
    queryType: "post",
    endpoint: "/auth/v1/logout",
  },
  signup: {
    queryType: "post",
    endpoint: "/auth/v1/signup",
    editableInputParams: {
      email: "",
      password: "",
    },
    inputResolver: (inputVariables: any) => {
      const { email, password } = inputVariables;
      return {
        email,
        password,
      };
    },
  },
};

const propertySettings: PluginPropertySettings = {};
const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'supabase',
  type: 'datasource',
  name: 'supabase',
  description: 'Supabase integration.',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export const SupabaseEditors = {
  basic: [
    {
      type: 'codeInput',
      name: 'supabaseAnonKey',
      props: {
        label: 'supabaseAnonKey',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'supabaseProjectRef',
      props: {
        label: 'supabaseProjectRef',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'supabaseAccessToken',
      props: {
        label: 'supabaseAccessToken',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'supabaseRefreshToken',
      props: {
        label: 'supabaseRefreshToken',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'accessTokenExpiresIn',
      props: {
        label: 'accessTokenExpiresIn',
        placeholder: '',
      },
    },
    {
      type: 'codeInput',
      name: 'apiBaseUrl',
      props: {
        label: 'apiBaseUrl',
        placeholder: 'https://api.supabase.com',
      },
    },
  ],
};

const makeInputParamsResolver = (contextInputParams: {[key: string]: string}) => {
  return (dsConfig: PluginConfigType<SupabaseConfigType>, dsModelValues: PluginModelType) => {
    const dsPluginConfig = dsConfig.get('config') as any as Immutable.Map<string, SupabaseConfigType>;
    if (!dsPluginConfig) return;

    return Object.entries(contextInputParams).reduce((acc, [key, value]) => {
      if (dsPluginConfig && dsPluginConfig.get(value)) {
        return {...acc, [key]: dsPluginConfig.get(value)};
      }
      if (dsModelValues && dsModelValues.get(value)) {
        return {...acc, [key]: dsModelValues.get(value)};
      }
      return acc;
    }, {});
  };
};

const makeInputVariablesTypeCompatible = (
  inputVariables: {[key: string]: any},
  editableInputParams: {[key: string]: any},
) => {
  return Object.entries(inputVariables).reduce((acc, [key, value]) => {
    if (editableInputParams && editableInputParams[key] !== undefined) {
      if (typeof editableInputParams[key] === 'number') {
        return {
          ...acc,
          [key]: isNaN(value) && typeof value === typeof editableInputParams[key] ? value : parseInt(value),
        };
      } else {
        return value
          ? {
              ...acc,
              [key]: value,
            }
          : acc;
      }
    }
    return acc;
  }, {});
};

export default wrapDatasourceModel({
  name: 'Supabase',
  config: {
    ...baseDatasourceConfig,
    supabaseAnonKey: '',
    supabaseProjectRef: '',
    supabaseAccessToken: '',
    supabaseRefreshToken: '',
    supabaseClient: null,
    accessTokenExpiresIn: 0,
    apiBaseUrl: 'https://api.supabase.com',
  } as SupabaseConfigType,

  initDatasource: function* (dsModel: any, dsConfig: PluginConfigType<SupabaseConfigType>, dsModelValues: any) {
    const supabaseAnonKey = dsConfig.config.get('supabaseAnonKey');
    if (!supabaseAnonKey) return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'supabaseClient'],
          newValue: null,
        },
      ],
    };
    const supabaseProjectRef = dsConfig.config.get('supabaseProjectRef');
    const supabaseClient = createClient(
      `https://${supabaseProjectRef}.supabase.co`,
      supabaseAnonKey,
      {
        auth: {
          storage: AsyncStorage,
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: false,
        },
      }
    );

    return {
      modelUpdates: [
        {
          selector: [dsConfig.get('id'), 'supabaseClient'],
          newValue: supabaseClient,
        },
      ],
    };
  },

  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'supabase';
  },

  resolveCredentialConfigs: function (credentials: ISupabaseCredentialResponse): Partial<SupabaseConfigType> | boolean {
    const {SUPABASE_ANON_KEY, SUPABASE_PROJECT_REF, SUPABASE_ACCESS_TOKEN, SUPABASE_REFRESH_TOKEN,
      ACCESS_TOKEN_EXPIRES_IN
    } = credentials;

    return {
      supabaseAnonKey: SUPABASE_ANON_KEY,
      supabaseProjectRef: SUPABASE_PROJECT_REF,
      supabaseAccessToken: SUPABASE_ACCESS_TOKEN,
      supabaseRefreshToken: SUPABASE_REFRESH_TOKEN,
      accessTokenExpiresIn: ACCESS_TOKEN_EXPIRES_IN,
    };
  },

  resolveClearCredentialConfigs: function (): string[] {
    return [
      'supabaseAnonKey',
      'supabaseProjectRef',
      'supabaseAccessToken',
      'supabaseRefreshToken',
      'accessTokenExpiresIn',
    ];
  },

  options: {
    propertySettings,
    pluginListing,
  },
  editors: SupabaseEditors,
});
