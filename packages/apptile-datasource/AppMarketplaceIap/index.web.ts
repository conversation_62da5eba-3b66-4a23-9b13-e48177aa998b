import {
  AppPageTriggerOptions,
  baseDatasourceConfig,
  DatasourceQueryDetail,
  modelUpdateAction,
  PluginConfigType,
  PluginListingSettings,
  PluginPropertySettings,
  store,
} from 'apptile-core';
import {DatasourcePluginConfig, IBaseCredentials, IntegrationPlatformType} from '../datasourceTypes';
import {wrapDatasourceModel} from 'apptile-core';
import iapClient, {
  IAppMarketplaceIapActionsPluginConfigType,
  appMarketplaceIapActionsPluginConfig,
  appMarketplaceIapActionsPropertySettings,
} from './actions';
import { getConfigValue } from 'apptile-core';
import {Product, Subscription, SubscriptionPlatform, initConnection, Purchase} from 'react-native-iap';
import { Platform } from 'react-native';
import {WEB_API_SERVER_ENDPOINT} from '../../apptile-app/.env.json';
import axios from 'axios';

export type TransformerFunction = (data: any) => {data: any; hasNextPage?: boolean; paginationMeta?: any};

export type AppMarketplaceIapPluginConfigType = DatasourcePluginConfig &
  // IAppMarketplaceIapActionsPluginConfigType & {
  {
    appId: string;
    subscriptionIds: string;
    productIds: string;
    consumableProductIds: string;
  };

type IEditableParams = Record<string, any>;


type AppMarketplaceIapQueryDetails = DatasourceQueryDetail & {
  queryType: 'get' | 'post' | 'put' | 'patch' | 'delete';
  editableInputParams: IEditableParams;
  contextInputParams?: {[key: string]: any};
  transformer?: TransformerFunction;
  endpoint: string;
  endpointResolver?: (endpoint: string, inputVariables: any, paginationMeta: any) => string;
  inputResolver?: (inputVariables: any) => any;
  isPaginated?: Boolean;
  paginationMeta?: Record<string, any>;
  paginationResolver?: (inputVariables: Record<string, any>, paginationMeta: any) => Record<string, any>;
};

export const productsListData: Product[] = [
  {
    // One-time purchase item
    type: 'inapp',
    productId: 'com.myapp.extra_coins_1000',
    productIds: ['com.myapp.extra_coins_1000', 'extra_coins_pack_1000'],
    title: '1000 Coins Pack',
    description: 'Sample Consumable Product: Get 1000 extra coins to use in the app',
    price: '4.99',
    currency: 'USD',
    localizedPrice: '$4.99',
    countryCode: 'US',
    // Android specific
    name: '1000 Coins Package',
    oneTimePurchaseOfferDetails: {
      priceCurrencyCode: 'USD',
      formattedPrice: '$4.99',
      priceAmountMicros: '4990000'
    }
  },
  {
    // Another one-time purchase item
    type: 'iap',
    productId: 'com.myapp.remove_ads',
    productIds: ['com.myapp.remove_ads'],
    title: 'Remove Ads',
    description: 'Sample Non-Consumable Product: Remove all advertisements from the app permanently',
    price: '2.99',
    currency: 'USD',
    localizedPrice: '$2.99',
    countryCode: 'US',
    // Android specific
    name: 'Ad-Free Experience',
    oneTimePurchaseOfferDetails: {
      priceCurrencyCode: 'USD',
      formattedPrice: '$2.99',
      priceAmountMicros: '2990000'
    }
  }
];

export const subscriptionsListData: Subscription[] = [
  {
    platform: SubscriptionPlatform.ios,
    type: 'subs',
    productId: 'com.myapp.premium.monthly',
    productIds: ['com.myapp.premium.monthly', 'premium_monthly_sub'],
    title: 'Premium Monthly',
    description: 'Enjoy premium features with our monthly subscription',
    price: '9.99',
    currency: 'USD',
    localizedPrice: '$9.99',
    countryCode: 'US',
    // iOS specific fields
    subscriptionPeriodNumberIOS: '1',
    subscriptionPeriodUnitIOS: 'MONTH',
    introductoryPrice: '4.99',
    introductoryPriceAsAmountIOS: '4.99',
    introductoryPricePaymentModeIOS: 'PAYUPFRONT',
    introductoryPriceNumberOfPeriodsIOS: '1',
    introductoryPriceSubscriptionPeriodIOS: 'MONTH',
    // Optional discounts
    discounts: [
      {
        identifier: 'student_discount',
        type: 'student',
        numberOfPeriods: '12',
        price: '7.99',
        localizedPrice: '$7.99',
        paymentMode: 'PAYASYOUGO',
        subscriptionPeriod: 'MONTH'
      }
    ]
  },
  {
    platform: SubscriptionPlatform.ios,
    type: 'subs',
    productId: 'com.myapp.premium.yearly',
    productIds: ['com.myapp.premium.yearly', 'premium_yearly_sub'],
    title: 'Premium Yearly',
    description: 'Save 40% with our annual premium subscription',
    price: '59.99',
    currency: 'USD',
    localizedPrice: '$59.99',
    originalPrice: '119.88',
    countryCode: 'US',
    // iOS specific fields
    subscriptionPeriodNumberIOS: '1',
    subscriptionPeriodUnitIOS: 'YEAR',
    introductoryPrice: '29.99',
    introductoryPriceAsAmountIOS: '29.99',
    introductoryPricePaymentModeIOS: 'PAYUPFRONT',
    introductoryPriceNumberOfPeriodsIOS: '1',
    introductoryPriceSubscriptionPeriodIOS: 'YEAR',
    // Free trial discount
    discounts: [
      {
        identifier: 'free_trial',
        type: 'trial',
        numberOfPeriods: '1',
        price: '0',
        localizedPrice: 'Free',
        paymentMode: 'FREETRIAL',
        subscriptionPeriod: 'MONTH'
      }
    ]
  }
];

export const availablePurchases:Purchase[] = [
  {
    productId: 'com.myapp.remove_ads',
    transactionId: '1000000700000001',
    type: 'non-consumable',
    transactionReceipt: 'base64encodedreceipt1',
    purchaseDate: new Date().toISOString(),
  },
  {
    productId: 'com.myapp.premium.monthly',
    transactionId: '1000000700000002',
    type: 'subscription',
    transactionReceipt: 'base64encodedreceipt2',
    purchaseDate: new Date().toISOString(),
  },
  {
    productId: 'com.myapp.premium.yearly',
    transactionId: '1000000700000003',
    type: 'subscription',
    transactionReceipt: 'base64encodedreceipt3',
    purchaseDate: new Date().toISOString(),
  }
]

const AppMarketplaceIapApiRecords: Record<string, AppMarketplaceIapQueryDetails> = {};

const propertySettings: PluginPropertySettings = {
  ...appMarketplaceIapActionsPropertySettings,
};

const pluginListing: Partial<PluginListingSettings> = {
  labelPrefix: 'AppMarketplaceIap',
  type: 'datasource',
  name: 'AppMarketplaceIap',
  description: 'App Marketplace - In App Purchase',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Integrations',
  icon: 'datasource',
};

export default wrapDatasourceModel({
  name: 'AppMarketplaceIap',
  config: {
    ...baseDatasourceConfig,
    appId: '',
    subscriptionIds: '',
    productIds: '',
    consumableProductIds: '',
    isPurchaseInProgress: '',
    isProductsLoading: '',
    isSubscriptionsLoading: '',
    isPurchaseHistoryLoading: '',
    isCurrentActivePurchasesLoading: '',
    isSubscriptionInProgress: '',
    queryRunner: 'queryRunner',
    iapClient: 'iapClient',
    ...appMarketplaceIapActionsPluginConfig,
  } as AppMarketplaceIapPluginConfigType,

  initDatasource: async (
    dsModel: any,
    dsConfig: PluginConfigType<AppMarketplaceIapPluginConfigType>,
    dsModelValues: any,
  ) => {
    let appId = null;
    try {
      if (Platform.OS != 'web') {
        appId = await getConfigValue('APP_ID')
      } else {
        appId = store?.getState()?.apptile?.appId
      }
      await initConnection();
    } catch (err) {
      console.error("Inapp purchase datasource initialization failure: ", err);
    }


    axios.get(`${WEB_API_SERVER_ENDPOINT}/api/internal/app/get-inapppurchases-credentials/appId/${appId}`)
        ?.then((response) => {
          const integrationData = response.data;

          store.dispatch(
            modelUpdateAction([
              {
                selector: [dsConfig.get('id'), 'productIds'],
                newValue: integrationData?.credentials?.productIds || ''
              },
              {
                selector: [dsConfig.get('id'), 'subscriptionIds'],
                newValue: integrationData?.credentials?.subscriptionIds || ''
              },
              {
                selector: [dsConfig.get('id'), 'consumableProductIds'],
                newValue: integrationData?.credentials?.consumableProductIds || ''
              }
            ])
          );
        }).catch((err) => {
          console.error("Inapp purchase datasource initialization failure: ", err);
        });


  return {
    modelUpdates: [
      {
        selector: [dsConfig.get('id'), 'iapClient'],
        newValue: iapClient
        }
      ],
    };
  },
  getQueries: function (): Record<string, DatasourceQueryDetail> {
    return AppMarketplaceIapApiRecords;
  },
  getQueryInputParams: function (queryName: string) {
    const queryDetails =
      AppMarketplaceIapApiRecords && AppMarketplaceIapApiRecords[queryName]
        ? AppMarketplaceIapApiRecords[queryName]
        : null;
    return queryDetails && queryDetails.editableInputParams ? queryDetails.editableInputParams : {};
  },

  resolveCredentialConfigs: function (
    credentials: IBaseCredentials,
  ): Partial<AppMarketplaceIapPluginConfigType> | boolean {
    const {appId} = credentials;
    if (!appId) return false;
    return {
      appId: appId,
    };
  },
  resolveClearCredentialConfigs: function (): string[] {
    return ['appId'];
  },
  getPlatformIdentifier: function (): IntegrationPlatformType {
    return 'AppMarketplaceIap';
  },

  runQuery: function* (
    dsModel,
    dsConfig,
    dsModelValues,
    queryName: string,
    inputVariables: any,
    options?: AppPageTriggerOptions,
  ) {},
  options: {
    propertySettings,
    pluginListing,
  }
});