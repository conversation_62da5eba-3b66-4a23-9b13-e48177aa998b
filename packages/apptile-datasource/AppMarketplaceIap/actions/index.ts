import {
  logger,
  modelUpdateAction,
  PluginPropertySettings,
  store
} from 'apptile-core';
import _ from 'lodash';
import { Platform } from 'react-native';
import {
  ErrorCode,
  finishTransaction,
  getAvailablePurchases,
  getProducts,
  getPurchaseHistory,
  getSubscriptions,
  Product,
  Purchase,
  PurchaseError,
  purchaseUpdatedListener,
  requestPurchase,
  requestSubscription,
  Subscription
} from 'react-native-iap';

purchaseUpdatedListener(async (purchase: Purchase) => {
  const consumableProductIds: string[] = store.getState().appModel.jsModel.AppMarketplaceIap.consumableProductIds?.split(',') || [];
    try {
      let success = false;
      switch (Platform.OS) {
        case 'android':
          if (purchase && purchase.purchaseToken) {
            const transactionCompletionResponse = await finishTransaction({
              purchase,
              isConsumable: consumableProductIds.includes(purchase.productId),
            });
            success = !!transactionCompletionResponse;
          }
          break;
        case 'ios':
          if (purchase && purchase.transactionReceipt) {
            const transactionCompletionResponse = await finishTransaction({
              purchase,
              isConsumable: consumableProductIds.includes(purchase.productId),
            });
            success = !!transactionCompletionResponse;
          }
          break;
        default:
          break;
      }
      if (success) {
        store.dispatch(
          modelUpdateAction([
            {
              selector: ['AppMarketplaceIap', 'isCurrentActivePurchasesLoading'],
              newValue: true
            },
          ]),
        );
        const availablePurchases: Purchase[] = await getAvailablePurchases();
        store.dispatch(
          modelUpdateAction([
            {
              selector: ['AppMarketplaceIap', 'availablePurchases'],
              newValue: availablePurchases
            },
            {
              selector: ['AppMarketplaceIap', 'isCurrentActivePurchasesLoading'],
              newValue: false
            },
          ]),
        );
      }
    } catch (error) {
      logger.error('[iap-purchaseUpdatedListener] Error handling purchase', error);
  }
});

export interface IAppMarketplaceIapActionsPluginConfigType {}

export const appMarketplaceIapActionsPluginConfig: IAppMarketplaceIapActionsPluginConfigType = {};

export const appMarketplaceIapActionsPropertySettings: PluginPropertySettings = {};

const getIAPErrorMesssage = (error: PurchaseError) => {
  switch (error.code) {
    // case ErrorCode.E_UNKNOWN:
    case ErrorCode.E_USER_CANCELLED:
      return 'Purchase cancelled';
    case ErrorCode.E_USER_ERROR:
      return 'User error';
    case ErrorCode.E_ITEM_UNAVAILABLE:
      return 'Product unavailable';
    case ErrorCode.E_ALREADY_OWNED:
      return 'Already purchased';
    case ErrorCode.E_DEFERRED_PAYMENT:
      return 'Deferred payment';
    case ErrorCode.E_INTERRUPTED:
      return 'Purchase Interrupted';
    case ErrorCode.E_IAP_NOT_AVAILABLE:
      return 'Purchase not available';
    default:
      return 'Error occurred  while purchasing';
  }
};

class AppMarketplaceIap {
  private showToast = async (message: string, type: 'normal' | 'success' | 'danger' | 'warning' | string) => {
    toast.show(message, {
      type: type,
      placement: 'bottom',
      duration: 2000,
      style: { marginBottom: 80 },
    });
  };

  initiatePurchase = async (params: {productId: string}) => {
    let result;
    try {
      const sku = params.productId;
      store.dispatch(
        modelUpdateAction([
          { selector: ['AppMarketplaceIap', 'isPurchaseInProgress'], newValue: true },
        ]),
      );
      if (_.isEmpty(sku)) {
        this.showToast(`Invalid Sku details`, 'error');
        return;
      }
      const product = await getProducts({ skus: [sku] });
      if (_.isEmpty(product)) {
        this.showToast(`Product not available for purchase`, 'error');
        return;
      }
      if (Platform.OS === 'ios') {
        await requestPurchase({ sku });
      } else if (Platform.OS === 'android') {
        await requestPurchase({ skus: [sku] });
      }
      result = true;
    } catch (error) {
      const errorMessage = getIAPErrorMesssage(error as PurchaseError);
      this.showToast(errorMessage, 'error');
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'paymentError'],
            newValue: errorMessage
          }]
        ));
      result = false;
    } finally {
      store.dispatch(
        modelUpdateAction([
          { selector: ['AppMarketplaceIap', 'isPurchaseInProgress'], newValue: false },
        ]),
      );
    }
    return result;
  };

  initiateSubscription = async (params: {productId: string;}) => {
    let result;
    try {
      store.dispatch(
        modelUpdateAction([
          { selector: ['AppMarketplaceIap', 'isSubscriptionInProgress'], newValue: true },
        ]),
      );

      const sku = params.productId;
      const subscriptions = await getSubscriptions({ skus: [sku] });
      if (_.isEmpty(subscriptions)) {
        this.showToast(`Subscription not available`, 'error');
        return;
      }
      if (Platform.OS === 'ios') {
        await requestSubscription({ sku });
      } else if (Platform.OS === 'android') {
        const offerDetail = subscriptions.find((subscription: Subscription) => subscription.productId === sku);
        if (!offerDetail) {
          this.showToast(`Subscription offering not available`, 'error');
          return;
        }

        await requestSubscription({
          subscriptionOffers: [
            {
              sku: sku,
              offerToken: offerDetail.subscriptionOfferDetails[0]?.offerToken,
            }
          ]
        });
      }
      result = true;
    } catch (error) {
      logger.error('[iap-initiateSubscription] Error initiating subscription', error);
      const errorMessage = getIAPErrorMesssage(error as PurchaseError);
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'paymentError'],
            newValue: errorMessage
          }]))
      this.showToast(errorMessage, 'error');
      result = false;
    } finally {
      store.dispatch(
        modelUpdateAction([
          { selector: ['AppMarketplaceIap', 'isSubscriptionInProgress'], newValue: false },
        ]),
      );
    }
    return result;
  };

  getProducts = async () => {
    try {
      const productIdVals = store.getState().appModel.values.getIn(['AppMarketplaceIap', 'productIds']) || '';
      const productIds = productIdVals?.split(',')?.map(value => value.trim()) || [];
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isProductsLoading'],
            newValue: true
          },
        ]),
      );

      const productSkus = Platform.select({
        ios: productIds,
        android: productIds,
      });

      let products: Product[] = await getProducts({ skus: productSkus });

      products = products.map((product:Product) => {
        return {
          currency: product.currency,
          productId: product.productId,
          title: product.title,
          description: product.description,
          price: product.price,
          localizedPrice: product.localizedPrice,
          countryCode: product.countryCode,
          name: product.name,
          oneTimePurchaseOfferDetails: product.oneTimePurchaseOfferDetails,
          productType: Platform.OS === 'ios' ? product.type : product?.productType,
        }
      })

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'products'],
            newValue: products
          }
        ])
      );
      return products;
    } catch (error) {
      logger.error('[iap-getProducts] Error getting products', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isProductsLoading'],
            newValue: false
          }
        ]))
    }
  };

  getSubscriptions = async () => {
    try {
      const subscriptionIdVals = store.getState().appModel.values.getIn(['AppMarketplaceIap', 'subscriptionIds']) || '';
      const subscriptionIds = subscriptionIdVals?.split(',')?.map(value => value.trim()) || [];

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isSubscriptionsLoading'],
            newValue: true
          },
        ]),
      );

      const subscriptionSkus = Platform.select({
        ios: subscriptionIds,
        android: subscriptionIds,
      });
      let subscriptions: Subscription[] = await getSubscriptions({ skus: subscriptionSkus });

      subscriptions = subscriptions.map((subscription:Subscription) => {
        const priceDetails = subscription?.subscriptionOfferDetails?.find((offer) => offer.offerId == null);
        return {
          currency: Platform.OS === 'ios' ? subscription?.currency : priceDetails?.pricingPhases?.pricingPhaseList?.[0]?.priceCurrencyCode,
          platform: subscription?.platform,
          title: subscription?.title,
          description: subscription?.description,
          productId: subscription?.productId,
          localizedPrice: Platform.OS === 'ios' ? subscription?.price : priceDetails?.pricingPhases?.pricingPhaseList?.[0]?.formattedPrice,
          productType: Platform.OS === 'ios' ? subscription?.type : subscription?.productType,
        }
      })

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'subscriptions'],
            newValue: subscriptions
          }
        ])
      );
      return subscriptions;
    } catch (error) {
      logger.error('[iap-getSubscriptions] Error getting subscriptions', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isSubscriptionsLoading'],
            newValue: false
          }]))
    }

  };

  getAllPurchaseHistory = async () => {
    try {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isPurchaseHistoryLoading'],
            newValue: true
          },
        ]),
      );

      let purchaseHistory = await getPurchaseHistory();

      purchaseHistory = purchaseHistory.map((purchase:Purchase) => {
        return {
          transactionReceipt: purchase.transactionReceipt,
          transactionDate: purchase.transactionDate,
          transactionId: purchase.transactionId,
          productId: purchase.productId,
        }
      })

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'purchaseHistory'],
            newValue: purchaseHistory
          },
        ]),
      );
      return purchaseHistory;
    } catch (error) {
      logger.error('[iap-getAllPurchaseHistory] Error getting purchase history', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isPurchaseHistoryLoading'],
            newValue: false
          },
        ]),
      );
    }
  };

  getCurrentActivePurchases = async () => {
    try {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isCurrentActivePurchasesLoading'],
            newValue: true
          },
        ]),
      );
      let availablePurchases: Purchase[] = await getAvailablePurchases();

      availablePurchases = availablePurchases.map((purchase:Purchase) => {
        return {
          transactionReceipt: purchase.transactionReceipt,
          transactionDate: purchase.transactionDate,
          transactionId: purchase.transactionId,
          productId: purchase.productId
        }
      })

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'availablePurchases'],
            newValue: availablePurchases
          },
        ]),
      );
      return availablePurchases;
    } catch (error) {
      logger.error('[iap-getCurrentActivePurchases] Error getting current active purchases', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isCurrentActivePurchasesLoading'],
            newValue: false
          },
        ]),
      );
    }
  };
}

const appMarketplaceIap = new AppMarketplaceIap();
export default appMarketplaceIap;