import {
  logger,
  modelUpdateAction,
  PluginPropertySettings,
  store
} from "apptile-core";
import {
  Product,
  Subscription
} from 'react-native-iap';
import { availablePurchases, productsListData, subscriptionsListData } from "../index";

export interface IAppMarketplaceIapActionsPluginConfigType {}

export const appMarketplaceIapActionsPluginConfig: IAppMarketplaceIapActionsPluginConfigType = {};

export const appMarketplaceIapActionsPropertySettings: PluginPropertySettings = {};

class AppMarketplaceIap {
  private showToast = async (
    message: string,
    type: "normal" | "success" | "danger" | "warning" | string,
    duration?: number
  ) => {
    toast.show(message, {
      type: type,
      placement: "bottom",
      duration: duration || 2000,
      style: { marginBottom: 80 },
    });
  };

  getProducts = async () => {
    try {
      store.dispatch(
        modelUpdateAction([
          {
          selector: ['AppMarketplaceIap', 'isProductsLoading'],
          newValue: true
        },
      ]),
    );

    await new Promise((resolve) => setTimeout(resolve, 1000));

    store.dispatch(
      modelUpdateAction([
        {
          selector: ['AppMarketplaceIap', "products"],
          newValue: productsListData,
        },
        {
          selector: ['AppMarketplaceIap', 'isProductsLoading'],
          newValue: false
        }
      ])
      );
    } catch (error) {
      logger.error('[iap-getProducts] Error getting products', error);
    }
    return productsListData;
  };

  getSubscriptions = async () => {
    try {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isSubscriptionsLoading'],
            newValue: true
          },
        ]),
      );

      await new Promise((resolve) => setTimeout(resolve, 1000));

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'subscriptions'],
            newValue: subscriptionsListData,
          }
        ])
      );
      return subscriptionsListData;
    } catch (error) {
      logger.error('[iap-getSubscriptions] Error getting subscriptions', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isSubscriptionsLoading'],
            newValue: false
          },
        ]),
      );
    }
  };

  getAllPurchaseHistory = async () => {
    try {
      store.dispatch(
          modelUpdateAction([
            {
            selector: ['AppMarketplaceIap', 'isPurchaseHistoryLoading'],
            newValue: true
          },
        ]),
      );
      const currentPurchasesHistoryData = store.getState().appModel.values.getIn(['AppMarketplaceIap', 'purchaseHistory']) || [];
      await new Promise((resolve) => setTimeout(resolve, 1000));

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'purchaseHistory'],
            newValue: currentPurchasesHistoryData
          }
        ]),
      );
      return currentPurchasesHistoryData;
    } catch (error) {
      logger.error('[iap-getAllPurchaseHistory] Error getting purchase history', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isPurchaseHistoryLoading'],
            newValue: false
          },
        ]),
      );
    }
  };

  getCurrentActivePurchases = async (model: any) => {
    try {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isCurrentActivePurchasesLoading'],
            newValue: true
          },
        ]),
      );

      await new Promise((resolve) => setTimeout(resolve, 1000));

      const currentPurchasesData = model.get('availablePurchases') || [];

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'availablePurchases'],
            newValue: currentPurchasesData,
          }
        ])
      );
      return currentPurchasesData;
    } catch (error) {
      logger.error('[iap-getCurrentActivePurchases] Error getting current active purchases', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isCurrentActivePurchasesLoading'],
            newValue: false
          },
        ]),
      );
    }
  };

  initiatePurchase = async (params: Product) => {
    try {
      const { productId } = params;
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isPurchaseInProgress'],
            newValue: true,
          },
        ]),
      );
    

      await new Promise((resolve) => setTimeout(resolve, 1000));
    
        const model = store.getState().appModel.values.get('AppMarketplaceIap');
        const getCurrentActivePurchases = model.get('availablePurchases');
        const getPurchaseHistory = model.get('purchaseHistory');

        const getCurrentPurchaseDummyData = availablePurchases.filter(purchase => purchase.productId === productId);
        const updatedAvailablePurchases = Array.isArray(getCurrentActivePurchases)
          ? [...getCurrentActivePurchases, ...getCurrentPurchaseDummyData]
          : [...getCurrentPurchaseDummyData];

        const updatedPurchaseHistory = Array.isArray(getPurchaseHistory)
          ? [...getPurchaseHistory, ...getCurrentPurchaseDummyData]
          : [...getCurrentPurchaseDummyData];

        store.dispatch(
          modelUpdateAction([
            {
              selector: ['AppMarketplaceIap', 'availablePurchases'],
              newValue: updatedAvailablePurchases
            },
            {
              selector: ['AppMarketplaceIap', 'purchaseHistory'],
              newValue: updatedPurchaseHistory
            },
          ]),
        );
  
      this.showToast(`Purchase Completed, Note: This is a dummy flow. you can only test In-App Purchase in Real Device`, "success", 4000);
    } catch (error) {
      logger.error('[iap-initiatePurchase] Error initiating purchase', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isPurchaseInProgress'],
            newValue: false,
          },
        ]),
      );
    }
  };

  initiateSubscription = async (params: Subscription) => {
    try {
      const { productId } = params;

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isSubscriptionInProgress'],
            newValue: true,
          },
        ]),
      );

      await new Promise((resolve) => setTimeout(resolve, 1000));

      this.showToast(`Subscription Completed, Note: This is a dummy flow. you can only test In-App Purchase in Real Device`, "success", 4000);

      const model = store.getState().appModel.values.get('AppMarketplaceIap');
      const getCurrentActivePurchases = model.get('availablePurchases');
      const getPurchaseHistory = model.get('purchaseHistory');

      const getCurrentPurchaseDummyData = availablePurchases.filter(purchase => purchase.productId === productId);
      const updatedAvailablePurchases = Array.isArray(getCurrentActivePurchases)
        ? [...getCurrentActivePurchases, ...getCurrentPurchaseDummyData]
        : [...getCurrentPurchaseDummyData];

      const updatedPurchaseHistory = Array.isArray(getPurchaseHistory)
        ? [...getPurchaseHistory, ...getCurrentPurchaseDummyData]
        : [...getCurrentPurchaseDummyData];

      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'availablePurchases'],
            newValue: updatedAvailablePurchases
          },
          {
            selector: ['AppMarketplaceIap', 'purchaseHistory'],
            newValue: updatedPurchaseHistory
          },
        ]),
      );


    } catch (error) {
      logger.error('[iap-initiateSubscription] Error initiating subscription', error);
    } finally {
      store.dispatch(
        modelUpdateAction([
          {
            selector: ['AppMarketplaceIap', 'isSubscriptionInProgress'],
            newValue: false,
          },
        ]),
      );
    }
  };
}

const appMarketplaceIap = new AppMarketplaceIap();
export default appMarketplaceIap;