{"compilerOptions": {"target": "esnext", "module": "commonjs", "lib": ["es2017", "dom"], "jsx": "react-native", "moduleResolution": "node", "paths": {"*": ["./types/*", "./node_modules/@types/*"], "apptile-core": ["../apptile-core/sdk.ts"], "react-native": ["../apptile-app/node_modules/react-native"], "apptile-shopify": ["../apptile-shopify"], "react": ["../apptile-app/node_modules/react"], "@supabase/supabase-js": ["../apptile-app/node_modules/@supabase/supabase-js"], "@react-native-async-storage/async-storage": ["../apptile-app/node_modules/@react-native-async-storage/async-storage"]}}}