export interface IShopifyDatasourceQueryReturnValue<T> {
  data: T;
  rawData: any;
  hasNextPage?: boolean;
  paginationMeta?: any;
  errors: any;
  hasError: boolean;
}

export type IQueryDataType<T> =
  | (T & {
      _raw: any;
    })
  | Array<T & {_raw: any}>;

export type IntegrationPlatformType =
  | 'shopify'
  | 'flits'
  | 'firebaseAnalytics'
  | 'rechargePayments'
  | 'stamped'
  | 'productFilterSearch'
  | 'gorgias'
  | 'ApptileMFAuth'
  | 'apptileMFAuthentication'
  | 'stampedReviews'
  | 'stampedRewards'
  | 'localWishlist'
  | 'apptileSkinCare'
  | 'storifyMe'
  | 'shopFlo'
  | 'orderLimiter'
  | 'judgeMe'
  | 'yagiOrderCancellable'
  | 'shopifyMultipass'
  | 'msg91'
  | 'moEngage'
  | 'searchanize'
  | 'judgeMe'
  | 'aitrillion'
  | 'instagramMedia'
  | 'snackShackPartner'
  | 'joleneAuth'
  | 'googleMaps'
  | 'simplyOTPLogin'
  | 'lively'
  | 'discourse'
  | 'wizzySearch'
  | 'nectorRewards'
  | 'vaccinationTracker'
  | 'HRTech'
  | 'okendoReviews'
  | 'AppMarketplaceIap' 
  | 'yotpoReviews';

export interface DatasourcePluginConfig {
  secretsConfigured: boolean;
}

export interface IBaseCredentials {
  appId: string;
  proxyUrl?: string;
  platformType: string;
}

export interface IRechargePaymentsCredentials extends IBaseCredentials {
  apiVersion: string;
}

export interface IShopifyCredentials extends IBaseCredentials {
  storeName: string;
  storefrontAccessToken: string;
  shopManagerApiUrl: string;
  appId: string;
}

export interface IProductFilterAndSearchCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  shopUrl: string;
}

export interface IStampedCredentials extends IBaseCredentials {
  storeUrl: string;
  publicKey: string;
  storeHash: string;
  apiBaseUrl: string;
  privateKey: string;
}

export interface IFlitsCredentials extends IBaseCredentials {
  userId: string;
}

export interface IGorgiasCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  apiKey: string;
  username: string;
}

export interface IApptileMFAuthCredentials extends IBaseCredentials {
  apiBasePath: string;
  sourceEntityId: string;
}

export interface IStorifyMeCredentials extends IBaseCredentials {
  accountId: string;
  apiKey: string;
  env: string;
}

export interface IOrderLimiterCredentials {
  maximumOrderValue: string;
  maximumOrderItems: string;
  maximumItemQuantity: string;
}

export interface IShopFloCredentials extends IBaseCredentials {
  merchantId: string;
}

export interface ISearchanizeCredentials extends IBaseCredentials {
  apiKey: string;
}
export interface IJudgeMeCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  shopDomain: string;
  apiToken: string;
}
export interface IApptileSkinCareCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  apiAccessKey: string;
}

export interface IYagiOrderCancellableCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  shopDomain: string;
}

export interface ISimplyOTPLoginCredentials extends IBaseCredentials {
  apiBaseUrl: string;
  shopDomain: string;
}

export interface IDiscourseCredentials {
  apiBaseUrl: string;
}

export interface ILivelyCredentials {
  brandId: string;
}

export interface IHRTechCredentials {
  apiBaseUrl: string;
  apiKey: string;
}

export interface IOkendoCredentials {
  apiBaseUrl: string;
  okendoUserId: string;
}
export interface IYotpoCredentials {
  apiBaseUrl: string;
  appKey: string;
}

export type IDatasourceCredentialTypes =
  | IRechargePaymentsCredentials
  | IShopifyCredentials
  | IProductFilterAndSearchCredentials
  | IFlitsCredentials
  | IStampedCredentials
  | IGorgiasCredentials
  | IStorifyMeCredentials
  | IShopFloCredentials
  | IJudgeMeCredentials
  | IApptileSkinCareCredentials
  | IOrderLimiterCredentials
  | IYagiOrderCancellableCredentials
  | ISimplyOTPLoginCredentials
  | IDiscourseCredentials
  | IHRTechCredentials
  | IOkendoCredentials
  | IYotpoCredentials;
