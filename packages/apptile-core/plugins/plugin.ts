import {FetchPolicy} from '@apollo/client';
import Immutable from 'immutable';
import _ from 'lodash';
import React from 'react';
import {WidgetAnimationConfig} from '../common/Animations/apptileAnimationTypes';
import {LayoutRecord, PluginConfig, PluginSubType, PluginType, PropType, WidgetType} from '../common/datatypes/types';
import {Selector} from '../common/DependencyGraph/types';
import {Editors, PluginEditor, PluginEditorsConfig, PropertyEditorConfig} from '../common/EditorControlTypes';
import {RootState} from '../store/RootReducer';
import {WidgetStyleEditorOptions} from '../styles/types';
import {IActionMetadata, PluginAction} from './triggerAction';
import {WidgetProps} from './widgets/widget';

export type onPluginUpdateFn =
  | null
  | ((
      state: RootState,
      pluginId: string,
      pageKey: string,
      instance: number | null,
      userTriggered: boolean,
      pageLoad: boolean,
      options?: AppPageTriggerOptions,
    ) => any);

export type CachePolicy = FetchPolicy;
export type PluginConfigMigrationFn = undefined | ((pluginConfig: PluginConfig) => PluginConfig)

export interface AppPageTriggerOptions {
  focusTrigger?: boolean;
  additionalScope?: any;
  triggeredById?: string;
  getNextPage?: boolean;
  paginationMeta?: any;
  inputOptionsVariables?: any;
  cachePolicy?: CachePolicy;
}

export type WidgetOptions<ConfigType> = {
  propertySettings: PluginPropertySettings;
  widgetStyleConfig?: WidgetStyleEditorOptions;
  pluginListing?: PluginListingSettings;
  docs?: any;
  themeProfileSel: string[];
  animations?: WidgetAnimationConfig;
  configMigration?: PluginConfigMigrationFn;
};
interface PluginInfo {
  plugin: any;
  config: unknown;
  onPluginUpdate: onPluginUpdateFn;
  propertySettings: PluginPropertySettings;
  widgetStyleConfig?: WidgetStyleEditorOptions;
  pluginListing: PluginListingSettings;
  editors?: PluginEditorsConfig<any>;
  actions?: PluginAction[];
  docs?: any;
  mask?: string[];
  themeProfileSel?: string[];
  animations?: WidgetAnimationConfig;
  configMigration?: PluginConfigMigrationFn;
  manifest: null|{bundle: string; name: string; repository: string; id: string};
}

export interface PluginBaseProps {
  id: string;
  pluginType: PluginSubType;
  pageKey?: string;
}

export type PluginModelType = Immutable.Map<string, any>;

export const EventTriggerIdentifier = 'TRIGGER';
export const TriggerActionIdentifier = 'ACTION';

export type PluginPropertySetting = {
  updatesProps?: string[];
  updatesPropsAsync?: string[];
  getValue?: (model: Immutable.Map<string, unknown> | undefined, renderedValue: unknown, selector: Selector) => unknown;
  type?: typeof EventTriggerIdentifier | typeof TriggerActionIdentifier;
  actionMetadata?: IActionMetadata;
  forceEvaluation?: boolean;
};
export type PluginPropertySettings = {[p: string]: PluginPropertySetting};

type sdkPluginMeta= {
  directoryName: string;
};

// FIXME: This should probably move to editor part and only types need to be imported in.
export const COMMONLY_USED_PLUGIN_TYPES = [
  'TextInputWidget',
  'ButtonWidget',
  'TextWidget',
  'ContainerWidget',
  'ImageWidget',
  'QueryPlugin',
] as const;
// FIXME: This should probably move to editor part and only types need to be imported in.
export type CommonPluginsType = (typeof COMMONLY_USED_PLUGIN_TYPES)[number];

// FIXME: This should probably move to editor part and only types need to be imported in.
export const PLUGIN_LISTING_SECTIONS = ['Inputs', 'Display', 'Layout', 'Data', 'Integrations'] as const;
// FIXME: This should probably move to editor part and only types need to be imported in.
export type PluginListingSection = (typeof PLUGIN_LISTING_SECTIONS)[number];

export interface PluginListingSettings {
  labelPrefix: string;
  name: string;
  type: PropType<PluginConfig, 'type'>;
  description: string;
  defaultHeight: number | 'auto';
  defaultWidth: number | 'auto';
  section: PluginListingSection;
  icon: string;
  layout: Partial<LayoutRecord>;
  hidden?: () => boolean;
  tags?: string[];
  manifest?: null|sdkPluginMeta;
}

export type PluginModelChange = {
  selector: string | string[];
  newValue: unknown;
};

export type PluginUpdateConfig = {
  selector: string[];
  pluginConfig: PluginConfig;
};

export type UnknownModel = Immutable.Map<string, unknown>;

export const PluginRegistry: {[s: string]: PluginInfo} = {};
window.PluginRegistry = PluginRegistry;
export const RegisteredPlugins = new Set<string>();

export function registerPlugin(
  type: string,
  plugin: any,
  config: any,
  onPluginUpdate: onPluginUpdateFn,
  editors: PluginInfo['editors'],
  widgetOptions: WidgetOptions<any>,
  actions: PluginAction[],
  manifest: null | {bundle: string; name: string; id: string}
) {
  PluginRegistry[type] = {
    plugin,
    config,
    onPluginUpdate,
    editors,
    propertySettings: widgetOptions.propertySettings,
    widgetStyleConfig: widgetOptions.widgetStyleConfig ?? undefined,
    pluginListing: widgetOptions.pluginListing ?? undefined,
    docs: widgetOptions.docs ?? undefined,
    actions: actions,
    themeProfileSel: widgetOptions.themeProfileSel ?? undefined,
    animations: widgetOptions.animations ?? undefined,
    configMigration: widgetOptions.configMigration ?? undefined,
    manifest
  };

  RegisteredPlugins.add(type);
}

export function registerDatasource(dsName: string, dsModel: any) {
  const actions = makeActionSelectorConfig(dsModel?.options?.propertySettings);
  registerPlugin(
    dsName,
    dsModel,
    connectConfig(dsModel.config),
    dsModel.onPluginUpdate,
    dsModel.editors, // Fill editors.
    dsModel.options,
    actions,
    null
  );
}

export const registerWidget: typeof registerPlugin = (type, ...rest) => {
  registerPlugin(type, ...rest);
};

export const setMask = (type: string, mask: readonly string[]) => {
  PluginRegistry[type].mask = mask;
};

export const connectConfig = (config: any) => (options: any) => {
  return Immutable.Map(config).merge(options);
};

export const GetRegisteredPluginInfo = (type: string): PluginInfo | undefined => {
  const pluginInfo = PluginRegistry[type];
  return pluginInfo ?? undefined;
};

export const GetRegisteredWidget = (type: string): React.Component<WidgetProps> => {
  const pluginInfo = PluginRegistry[type];
  if (pluginInfo) {
    return pluginInfo.plugin;
  }

  logger.error(`Widget for the type ${type} not found.`);
  return null!;
};

export const GetRegisteredPlugin = (type: string): any => {
  const pluginInfo = PluginRegistry[type];
  if (pluginInfo) {
    return pluginInfo.plugin;
  }

  logger.error(`Plugin Model for the type ${type} not found.`);
  return null!;
};

export const GetRegisteredConfig = (type: string) => {
  const result = PluginRegistry[type];
  let configGenerator: (args?: any) => Immutable.Map<string, any>;
  if (result && result.config) {
    configGenerator = result.config;
  } else {
    configGenerator = () => Immutable.Map<string, any>();
  }
  return configGenerator;
};

export const GetRegisteredOnPluginUpdate = (type: string) => {
  const result = PluginRegistry[type];
  if (result && result.onPluginUpdate) {
    return result.onPluginUpdate;
  } else {
    return null;
  }
};

export const GetRegisteredPluginConfigMigrationFn = (type: string) => {
  const result = PluginRegistry[type];
  if (result && result.configMigration) {
    return result.configMigration;
  } else {
    return null;
  }
};

export const MaskResolver = (type: string) => (template: any) => {
  const plugin = PluginRegistry[type];
  if (plugin && plugin.mask) {
    const keySet = Immutable.Set(plugin.mask);
    return template.filter((v: any, k: any) => {
      return keySet.has(k);
    });
  } else {
    return template;
  }
};

export const resolvePluginPropertySettings = (pluginType: PluginSubType | string): PluginPropertySettings => {
  const pluginInfo = PluginRegistry[pluginType];
  if (pluginInfo && pluginInfo.propertySettings) {
    return pluginInfo.propertySettings;
  }
  return null!;
};

export const resolvePluginEditorsConfig = (pluginType: PluginSubType | string): PluginEditorsConfig<any> => {
  const pluginInfo = PluginRegistry[pluginType];
  if (pluginInfo && pluginInfo.editors) {
    return pluginInfo.editors;
  }
  return {};
};

// export const isPluginInfoLoaded = (pluginType: PluginSubType|string): boolean => {
//   return !!PluginRegistry[pluginType];
// }

export const resolvePluginPropertyEditor = (
  pluginType: PluginSubType | string,
  propertyName: string,
): PropertyEditorConfig<any> | null => {
  const pluginEditors = resolvePluginEditorsConfig(pluginType);
  for (let [section, editors] of Object.entries(pluginEditors)) {
    for (let propEditor of editors) {
      if (propEditor.name === propertyName) return propEditor;
    }
  }
  return null;
};

export const resolvePluginListing = (type: string): PluginListingSettings | undefined => {
  const result = PluginRegistry[type];
  let listing = result?.pluginListing ?? undefined;
  if (result?.manifest && listing) {
    listing.manifest = result.manifest;
  }
  return listing;
};

export const resolvePluginDocs = (type: string): any => {
  const result = PluginRegistry[type];
  return result?.docs ?? undefined;
};

export const resolvePluginActions = (type: string): any => {
  const result = PluginRegistry[type];
  return result?.actions ?? undefined;
};

export const resolvePluginAnimations = (type: WidgetType): WidgetAnimationConfig | undefined => {
  const result = PluginRegistry[type];
  return result?.animations ?? undefined;
};

export const connectPlugin = <
  PluginName extends PluginType,
  WidgetExtendedConfig,
  WidgetModelProperties extends readonly (keyof WidgetExtendedConfig)[],
>(
  pluginName: PluginName,
  plugin: any,
  config: any,
  onPluginUpdate: onPluginUpdateFn,
  editors: PluginInfo['editors'],
  widgetOptions: WidgetOptions,
  mask?: WidgetModelProperties,
) => {
  const eventsEditor = makeEventsEditorConfig(widgetOptions.propertySettings);
  if (eventsEditor) {
    editors = {...editors, event: eventsEditor};
  }
  const actions = makeActionSelectorConfig(widgetOptions.propertySettings);
  registerPlugin(pluginName, plugin, connectConfig(config), onPluginUpdate, editors, widgetOptions, actions);

  // TODO: Revisit the need
  if (mask) {
    setMask(pluginName, mask as string[]);
  }
  return plugin;
};

export const makeEventsEditorConfig = (propertySettings: PluginPropertySettings | undefined): Editors<any> | null => {
  const triggers: string[] = [];
  if (!propertySettings) {
    return null;
  }
  Object.entries(propertySettings).forEach(([event, propertySetting]) => {
    if (propertySetting.type === EventTriggerIdentifier) {
      triggers.push(event);
    }
  });

  const eventEditorConfig: Editors<any> = [];

  eventEditorConfig.push(getEventsEditorConfig(triggers));
  if (triggers.length < 1) return null;
  return eventEditorConfig;
};

export const getEventsEditorConfig = (triggers: string[]): PluginEditor<any> => {
  return {
    type: 'eventsEditor',
    name: 'events',
    props: {
      label: 'Events',
      triggers,
    },
  };
};

export const makeActionSelectorConfig = (propertySettings: PluginPropertySettings | undefined): PluginAction[] => {
  const actions: PluginAction[] = [];
  if (!propertySettings) {
    return actions;
  }
  Object.entries(propertySettings).forEach(([action, propertySetting]) => {
    if (propertySetting.type === TriggerActionIdentifier) {
      actions.push({name: action, metadata: propertySetting.actionMetadata});
    }
  });

  return actions;
};
