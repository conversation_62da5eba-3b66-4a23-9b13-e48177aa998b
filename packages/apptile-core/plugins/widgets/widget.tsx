import Immutable from 'immutable';
import _, {toInteger} from 'lodash';
import React, {useEffect, useRef, useState} from 'react';
import {Pressable, Text, View, ScrollView, Platform} from 'react-native';
import {connect, useDispatch, useSelector} from 'react-redux';
import {modelUpdateAction} from '../../actions/AppModelActions';
import {triggerPageEvent} from '../../actions/DispatchActions';
import Icon from '../../icons';
import {
  ImageRecord,
  ImmutableMapType,
  PluginConfig,
  PluginNamespace,
  PluginSubType,
  WidgetType,
} from '../../common/datatypes/types';
import {Editors, PluginEditor, PluginEditorsConfig} from '../../common/EditorControlTypes';
import useCallbackRef from '../../common/utils/useCallBackRef';
import {selectAppConfig, selectPluginConfig} from '../../selectors/AppConfigSelector';
import {
  EMPTY_PLUGINMODEL_MAP,
  makePluginModelSelector,
  StaticPluginModelSelector,
  selectPageModel,
} from '../../selectors/AppModelSelector';
import {getPlatformStyles, generateTypographyByPlatform, evaluateStylesMap} from '../../styles/styleUtils';
import {IDefaultThemeContext} from '../../styles/theme/context';
import {usePluginModelStyles} from '../../views/containers/ThemeContainer';
import {
  connectConfig,
  GetRegisteredConfig,
  GetRegisteredPluginInfo,
  GetRegisteredWidget,
  makeActionSelectorConfig,
  makeEventsEditorConfig,
  onPluginUpdateFn,
  PluginBaseProps,
  PluginModelChange,
  PluginModelType,
  PluginRegistry,
  registerWidget,
  setMask,
  UnknownModel,
  WidgetOptions,
} from '../plugin';
import {getShadowStyle} from './common/shadowUtils';
import {isTypographyStyleSheet as isTypo} from '../../styles/styleUtils';
import {strsel} from '../../common/DependencyGraph/types';
import {connectAnimatedWidget} from '../../common/Animations/animationWidgetWrapper';
import {createPluginModelFromConfig} from '../../sagas/AppModelSaga';
import { store } from '../../store';

export interface WidgetBaseProps extends PluginBaseProps {
  key?: string;
}

export const connectWidget = <
  WidgetName extends WidgetType,
  WidgetExtendedConfig,
  WidgetModelProperties extends readonly (keyof WidgetExtendedConfig)[],
>(
  widgetName: WidgetName,
  Widget: React.ForwardRefExoticComponent<WidgetProps & React.RefAttributes<any>>,
  config: any,
  onPluginUpdate: onPluginUpdateFn,
  editors: PluginEditorsConfig<any>,
  widgetOptions: WidgetOptions<any>,
  mask?: WidgetModelProperties,
): React.ComponentType<WidgetProps> => {
  try {
    const connectedConfig = connectConfig(config);
    // const connectedAnimatedWidget = connectAnimatedWidget(Widget);
    Widget.displayName = 'pluginRegistry_' + widgetName;
    const connectedWidgetWithRedux = connectWidgetWithRedux(Widget);
    connectedWidgetWithRedux.displayName = widgetName + '_redux';
    const WidgetWithReduxAndErrorBoundary = wrapErrorBoundary(connectedWidgetWithRedux, widgetName);
    WidgetWithReduxAndErrorBoundary.displayName = widgetName + '_errBndr';

    const styleEditorsConfig: Editors<any> = widgetOptions.widgetStyleConfig || [];

    const stylesEditor: PluginEditor<'stylesEditor'> = {
      type: 'stylesEditor',
      name: 'style',
      props: {
        label: 'stylesEditor',
        editors: styleEditorsConfig,
      },
    };

    const eventsEditor = makeEventsEditorConfig(widgetOptions.propertySettings);
    if (eventsEditor) {
      editors = {...editors, event: eventsEditor};
    }

    const actions = makeActionSelectorConfig(widgetOptions.propertySettings);

    const editorWithStyle = styleEditorsConfig.length ? {...editors, style: [stylesEditor]} : editors;

    registerWidget(
      widgetName,
      WidgetWithReduxAndErrorBoundary,
      connectedConfig,
      onPluginUpdate,
      editorWithStyle,
      widgetOptions,
      actions,
    );

    if (mask) {
      setMask(widgetName, mask as string[]);
    }

    return WidgetWithReduxAndErrorBoundary;
  } catch (err) {
    console.error("Failed to register widget: ", widgetName, err);
  }
};

type ModelWithInstances = Immutable.Map<number, UnknownModel>;

const getWidgetModelFromConfig = (
  pageKey: string,
  widgetConfig: PluginConfig,
  cachedModel: Immutable.OrderedMap<string, any>,
  instance: number | undefined,
): Immutable.OrderedMap<string, any> => {
  let pluginModel = instance === undefined ? cachedModel : cachedModel?.get(instance);
  if (pluginModel) return pluginModel?.set('pageKey', pageKey);
  var pluginModelFromConfig = createPluginModelFromConfig(pageKey, widgetConfig);
  return pluginModelFromConfig;
};

const connectWidgetWithReduxFn = connect(
  (state, ownProps) => {
    const {id, pageKey, instance, cachedModel, config, isDynamic} = ownProps;
    let model;
    if (instance === undefined) {
      model = state.appModel?.getModelValue([pageKey, 'plugins', id]);
      if (!model) model = cachedModel;
    } else {
      model = state.appModel?.getModelValue([pageKey, 'plugins', id, isDynamic ? instance : 0]);
      if (!model) model = cachedModel?.get(isDynamic ? instance : 0);
    }
    const pluginType = id === '' ? 'ContainerWidget' : (config?.get('subtype') as string);
    const widgetConfigCreator = GetRegisteredConfig(pluginType);
    const widgetConfig = widgetConfigCreator(config);
    return {widgetModel: model, widgetConfig};
  },
  null,
  null,
  {
    forwardRef: true,
    areStatesEqual: (nextState, prevState, nextOwnProps, prevOwnProps) => {
      if (nextState.appModel === prevState.appModel) return true;
      const {id, pageKey, instance} = nextOwnProps;
      if (instance === undefined) {
        const oldModel = prevState.appModel?.getModelValue([pageKey, 'plugins', id]);
        const newModel = nextState.appModel?.getModelValue([pageKey, 'plugins', id]);
        return oldModel === newModel;
      } else {
        const oldModel = prevState.appModel?.getModelValue([pageKey, 'plugins', id, instance]);
        const newModel = nextState.appModel?.getModelValue([pageKey, 'plugins', id, instance]);
        return oldModel === newModel;
      }
    },
  },
);
const connectWidgetWithRedux = (Widget: React.ComponentType<WidgetProps>): React.FC<any> => {
  const WidgetWiredWithReduxState: React.FC<WidgetOuterProps> = React.forwardRef((props: WidgetOuterProps, ref) => {
    const {id, pageKey, instance, isDynamic, widgetConfig, widgetModel, cachedModel} = props;
    // logger.info(`Rerender Widget : ${pageKey}, ${id}, ${instance} `);
    let model = EMPTY_PLUGINMODEL_MAP;
    let config;
    const pluginType = id === '' ? 'ContainerWidget' : (widgetConfig?.get('subtype') as string);
    const namespace = widgetConfig?.namespace;

    // const widgetConfigCreator = GetRegisteredConfig(pluginType);
    // const config = widgetConfigCreator(widgetConfig);

    // if (isDynamic) {
    //   model = useSelector((state: RootState) => StaticPluginModelSelector(state, pageKey, id, instance));
    //   // if (instance !== undefined) {
    //   //   model = model?.get(instance);
    //   // }
    //   model =
    //     model && model.size > 0
    //       ? model
    //       : getWidgetModelFromConfig(pageKey, widgetConfig, cachedModel, instance) || EMPTY_PLUGINMODEL_MAP;
    // } else {
    //   model = widgetConfig
    //     ? getWidgetModelFromConfig(pageKey, widgetConfig, cachedModel, instance)
    //     : EMPTY_PLUGINMODEL_MAP;
    // }
    if (widgetModel) model = widgetModel;
    config = widgetConfig;
    const animations = config?.get('animations');

    const dispatch = useDispatch();

    const modelUpdate = useCallbackRef((changes: PluginModelChange[]) => {
      const modelSelector = instance === undefined ? [pageKey, 'plugins', id] : [pageKey, 'plugins', id, instance];
      dispatch(
        modelUpdateAction(
          changes.map(({selector, newValue}) => {
            return {
              selector: modelSelector.concat(selector),
              newValue,
            };
          }),
          instance,
        ),
      );
    });

    const triggerEvent = useCallbackRef((event: string, params: any) => {
      dispatch(triggerPageEvent(pageKey, id, instance, event, params));
    });

    const getDeviceImage = useCallbackRef((imageId: string) => {
      return GetDeviceImage(imageId);
    });
    const _modelStylesCache = config.get('_modelStyles');
    let modelStyles;
    if (_modelStylesCache) {
      modelStyles = _modelStylesCache;
    } else {
      modelStyles = usePluginModelStyles(config);
    }

    if (!widgetConfig && id !== '') return null;
    if (id !== '' && (!model || !model?.get('id'))) return null;
    if (model && model.get('hidden', false)) return null;

    const widgetProps: WidgetProps = {
      ...props,
      model,
      config,
      namespace,
      modelStyles,
      modelUpdate,
      triggerEvent,
      getDeviceImage,
      animations,
    };

      return <Widget key={props.id} {...widgetProps} ref={ref} />;
  });
  WidgetWiredWithReduxState.displayName = 'WidgetWiredWithReduxState';
  return connectWidgetWithReduxFn(WidgetWiredWithReduxState);
};

export const externalContainerTypes = [];

export const isContainerTypeWidget = (widgetType: string) => {
  return widgetType === 'ContainerWidget' ||
    widgetType === 'ListViewWidget' ||
    widgetType === 'ModalWidget' ||
    widgetType === 'BottomSheetWidget' ||
    widgetType === 'ModuleInstance' ||
    externalContainerTypes.includes(widgetType);
}

const wrapErrorBoundary = <P extends WidgetProps>(Widget: React.ComponentType<P>, widgetType: WidgetType) => {
  const WithWidgetErrorBoundary: React.FC<P> = React.forwardRef((props: P, ref) => {
    const {isDynamic, id, containerId} = props;
    return (
      <WidgetErrorBoundary {...props} widgetType={widgetType}>
        {isDynamic ? (
          <Widget key={id || containerId + '_:' + 'D'} {...props} ref={ref} />
        ) : (
          <Widget key={id || containerId + '_:' + 'S'} {...props} ref={ref} />
        )}
      </WidgetErrorBoundary>
    );
  });

  return WithWidgetErrorBoundary;
};

type WidgetOuterProps = {
  id: string;
  pageKey: string;
  pageId: string;
  instance?: number | null;
  isDynamic?: boolean;
  config: PluginConfig;
  cachedModel?: Immutable.OrderedMap<string, any>;
};
export interface WidgetProps {
  id: string;
  model: PluginModelType;
  config: any;
  namespace?: PluginNamespace;
  isDynamic: boolean;
  modelStyles: any;
  modelUpdate: (changes: PluginModelChange[]) => void;
  // pluginUpdate: (changes: PluginChange[]) => void;
  triggerEvent: (pluginId: string, event?: string) => void;
  getDeviceImage: (imageId: string) => void;
}

type WidgetErrorBoundaryProps = {
  widgetType: WidgetType;
} & Partial<WidgetProps>;

type WidgetErrorBoundaryState = {hasError: boolean; message: string};

export class WidgetErrorBoundary extends React.Component<WidgetErrorBoundaryProps, WidgetErrorBoundaryState> {
  state = {
    hasError: false,
    message: '',
    stack: '',
  };

  componentDidCatch(err: Error, info: React.ErrorInfo) {
    const {id: widgetId, widgetType} = this.props;

    this.setState({
      hasError: true, 
      message: err.message,
      stack: err.stack?.toString()
    });
    // Sentry.withScope(scope => {
    //   scope.setTag('widget', widgetType);

    //   scope.setExtras({
    //     info,
    //     boundaryName: 'wireWidget',
    //     editMode,
    //     fetching,
    //     largeScreen,
    //     namespace: namespace?.getNamespace(),
    //     position: position?.toJSON(),
    //     widgetId,
    //   });
    //   Sentry.captureException(err);
    logger.error(err, widgetId, widgetType);
    // });
  }

  handleAttemptFix() {
    const reduxState = store.getState();
    const isChatRunning = reduxState?.ai?.isChatRunning ?? false;
    if (isChatRunning) {
      alert("Please wait for the current run to finish!")
    } else {
      store.dispatch({
        type: 'EDITOR_OPEN_CHAT_VIEW'
      });
      setTimeout(() => {
        const prompterInput = document.querySelector('#prompter-input');
        if (prompterInput) {
          prompterInput.innerHTML = `I'm getting this error: <span style="color: #ff9e64;">${this.state.message}</span>, 
          in the plugin: <span style="color: #9ece6a">${this.props?.widgetType}</span> 
          in screen: <span style="color: #9ece6a">${this.props?.pageId}</span>
          <br/>          
          <div style="color: gray">
          Here is the stacktrace im getting:
          <br/>
          <pre>${this.state.stack}</pre>
          </div>`;
        }
      }, 500)
      // dispatch action to open chat window
      // trigger the chat from the dom
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <ScrollView
          style={{
            maxWidth: '100%',
            maxHeight: 600,
          }}
        >
          <View style={{
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center',
              minHeight: 400
            }}
          >
            {/* <Button title="reload" onPress={() => this.setState({hasError: false})}>
              reload{' '}
            </Button> */}
            <View style={{flexDirection: 'column', alignItems: 'center'}}>
              <Icon 
                name="warning" 
                size={50} 
                iconType="FontAwesome" 
                style={{
                  color: 'red', 
                  fontWeight: 'bold',
                  marginTop: 150,
                  marginBottom: 30
                }}
              />
              <Text 
                style={{
                  fontSize: 20, 
                  fontWeight: 'bold', 
                }}
              >
                Uh oh
              </Text>
            </View>
            <Text style={{
              fontSize: 20, 
              fontWeight: '300',
              maxWidth: 240,
              textAlign: 'center'
            }}>
              Looks like we are facing an error in {this.props?.widgetType}
            </Text>
            <View style={{
              backgroundColor: "#F2F0F0",
              paddingLeft: 15,
              paddingRight: 15,
              marginTop: 50,
              marginBottom: 50,
              borderRadius: 4
            }}>
              <Text 
                style={{
                  marginTop: 10, 
                  marginBottom: 10, 
                  color: 'red',
                  fontWeight: '200'
                }}
              > 
                {this.state.message}
              </Text>
            </View>
            <Pressable 
              nativeID="attempt-fix-button"
              style={{
                padding: 16, 
                backgroundColor: "#1060E0",
                borderRadius: 25,
                fontSize: 16,
                fontWeight: "500" 
              }}
              onPress={this.handleAttemptFix.bind(this)}
            >
              <Text style={{color: "white", width: 100, textAlign: "center"}}>Attempt Fix</Text>
            </Pressable>
          </View>
        </ScrollView>
      );
    }
    if (Platform.OS === "web" && this.props.id) {
      return (
        <View nativeID={'boundary-' + this.props.id}>
          {this.props.children}
        </View>
      );
    } else {
      return this.props.children;
    }
  }
}

export const renderWidget = (type: WidgetType, pageKey: string, id: string) => {
  const WidgetClass = GetRegisteredWidget(type);

  return <WidgetClass pageKey={pageKey} id={id} />;
};

export const resolveWidgetStyleConfig = (pluginType: PluginSubType | string) => {
  const pluginInfo = GetRegisteredPluginInfo(pluginType);
  if (pluginInfo && pluginInfo.widgetStyleConfig) {
    return pluginInfo.widgetStyleConfig;
  }
  return null!;
};

export const resolveWidgetThemeKeySelector = (pluginType: PluginSubType | string): string[] | undefined => {
  const pluginInfo = GetRegisteredPluginInfo(pluginType);
  return pluginInfo?.themeProfileSel ?? undefined;
};

export const getWidgetsWithThemes = (): typeof PluginRegistry => {
  return _.pickBy(PluginRegistry, (pluginInfo, pluginName) => {
    return pluginInfo.themeProfileSel && !_.isEmpty(pluginInfo.themeProfileSel);
  });
};

export const GetWidgetStyleProps = (
  pluginType: PluginSubType | string,
  themeEvaluator: IDefaultThemeContext['themeEvaluator'],
  widgetStyles: any,
) => {
  const themeProfileSel = resolveWidgetThemeKeySelector(pluginType);

  const widgetStylesMap = widgetStyles ? widgetStyles.toJS() : {};
  const profile = widgetStylesMap?._profile ?? 'default';
  const themeKeySelector = themeProfileSel ? themeProfileSel.concat(profile) : null;
  const themeKey = themeKeySelector ? strsel(themeKeySelector) : null;

  const themeStyles = themeKey ? (themeEvaluator(themeKey) !== themeKey ? themeEvaluator(themeKey) : {}) : {};

  const evalThemeStyles = evaluateStylesMap(themeStyles, themeEvaluator);
  const evalWidgetStyles = evaluateStylesMap(widgetStylesMap, themeEvaluator);
  const mergedStyles = _.defaultsDeep({}, evalWidgetStyles, evalThemeStyles);

  const {elevation, ...configStyles} = mergedStyles;
  const _elevation = isNaN(toInteger(elevation)) ? 0 : toInteger(elevation);
  const shadowStyles = getShadowStyle(_elevation);

  const modelStyles = _.transform(
    Object.assign(configStyles, shadowStyles),
    (result, value, key) => {
      if (_.isNil(value) || value === '') return true;
      result[key] = isTypo(value) ? generateTypographyByPlatform(value) : value;
      return true;
    },
    {},
  );

  return getPlatformStyles(modelStyles);
};

export const GetDeviceImage = (assetId: string) => {
  const appConfig = useSelector(selectAppConfig);
  const imageRecord = appConfig.getImageId(assetId);

  const getOptimalImage = (layoutSize: string) => {
    return getOptimalImageSize(imageRecord, layoutSize);
  };
  return {imageRecord, getOptimalImage};
};

export const getOptimalImageSize = (imageRecord: ImageRecord, layoutSize: string) => {
  if (!imageRecord) {
    return null;
  }
  const variants = imageRecord.get('variants');
  if (!variants) {
    return imageRecord;
  }

  const [width, height] = layoutSize.split('x');
  const numWidth = Number(width);
  const numHeight = Number(height);
  const dimToCompare: number = numWidth > numHeight? numWidth : numHeight;
  const bCompareWidth = width > height? true: false;
  let variantSizes = variants.valueSeq().map((variant: any) => variant);

  if (!variantSizes) {
    return imageRecord;
  }

  const sortedVariants = variantSizes.sort((prev, current) => {
    return prev.width - current.width;
  });

  const optimalImage = sortedVariants.reduce(function (prev, curr) {
    if(bCompareWidth){
      const prevDiff = Math.abs(dimToCompare - prev.width);
      const currDiff = Math.abs(dimToCompare - curr.width);
      return currDiff < prevDiff ? curr : prev;
    } else {
      const prevDiff = Math.abs(dimToCompare - prev.height);
      const currDiff = Math.abs(dimToCompare - curr.height);
      return currDiff < prevDiff ? curr : prev;
    }
  });
  return optimalImage ? optimalImage : imageRecord;
};
