import {SagaIterator} from '@redux-saga/types';
import * as Immutable from 'immutable';
import _, {isObjectLike} from 'lodash';
import {
  all,
  call,
  delay,
  fork,
  join,
  put,
  putResolve,
  select,
  spawn,
  take,
  takeEvery,
  takeLatest,
  takeLeading,
} from 'redux-saga/effects';
import {DestroyPageModel, InitPageModelPayload, modelUpdateAction, ModelUpdateAction} from '../actions/AppModelActions';
import {
  DispatchAction,
  DispatchActions,
  DispatchEmptyAction,
  navigateToScreen,
  PluginDeletePayload,
  PluginPropertyNamespaceUpdate,
  PluginRenamePayload,
  triggerPageQuery,
  triggerOnPluginUpdate,
  navigateToScreenReset,
  triggerAction,
  TriggerActionPayload,
  commitStageModel,
  updateStageModelWithResults,
  pageBooted,
  forwardModuleEvent,
  ForwardModuleEventPayload,
  TriggerPageEventPayload,
  triggerPageEvent,
  sendAnalyticsEvent,
  TriggerOnPluginUpdatePayload,
} from '../actions/DispatchActions';
import {
  AppConfig,
  AppModelType,
  PageConfig,
  PluginConfig,
  PluginConfigType,
  PageParamConfig,
  PluginIdTypePage,
  PageOptionConfig,
  AppModelRecord,
  EventHandlerConfig,
  PluginNamespaceImpl,
  ImmutableMapType,
} from '../common/datatypes/types';
import {DependencyGraph} from '../common/DependencyGraph/DependencyGraph';
import {IDependencyGraph, ModelChange, objsel, Selector, strsel} from '../common/DependencyGraph/types';
import {evaluateJSBindingString, JSModel} from '../common/JSBinding/JSBindingEvaluator';
import {SafeAny} from '../common/types';
import {
  AppPageTriggerOptions,
  GetRegisteredConfig,
  GetRegisteredOnPluginUpdate,
  // isPluginInfoLoaded,
  makeActionSelectorConfig,
  PluginModelChange,
  resolvePluginPropertySettings,
} from '../plugins/plugin';
import {selectAppConfig, selectPluginConfig} from '../selectors/AppConfigSelector';
import {
  selectAppModel,
  selectPreInitModel,
  selectStageModel,
  selectStagePageModels,
} from '../selectors/AppModelSelector';
import {selectPageConfigForPage} from '../selectors/PageSelector';
import {RootState} from '../store/RootReducer';
import {getNavigationContext} from '../views/navigationContext';
import {addNamespace, getPluginSelector, getPluginSelectorFromSelector} from '../common/datatypes/utils';
import {initModuleInputs, initModuleOutputs} from '../plugins/module/moduleHelpers';
import {ApptileActivePagePayload} from '../actions/ApptileActions';
import {PageEventsList} from '../common/datatypes/PageTypes';
import {EVENT_PARAM_IDENTIFIER} from '../constants/modelConstants';
import {ApptileAnimationsRegistry} from '../common/Animations/apptileAnimationRegistry';
import {validateNaming} from '../common/helpers/validateNaming';
import {InteractionManager, Platform} from 'react-native';
import {buffers, channel, eventChannel} from 'redux-saga';
import {getAppDispatch} from '../common/utils/dispatcher';
import {getAppState} from '../common/utils/getAppState';

function generateInitialDependencyGraph(appModel: AppModelType, appConfig: AppConfig, firstLoad = false) {
  const globalPlugins = appConfig.get('plugins');
  const globalScopeIdentifiers = Array.from(globalPlugins.keys());
  const dependencyGraph = new DependencyGraph(globalScopeIdentifiers);
  if (appConfig?._cachedDependencyGraph?._transpiledBindings) {
    dependencyGraph.hydrateCompiledBindings(appConfig._cachedDependencyGraph._transpiledBindings)
  }

  globalPlugins.forEach((plugin, pluginId) => {
    const model = createPluginModelFromConfig('', plugin);
    dependencyGraph.addPlugin(null, plugin, model);
  });
  globalPlugins.forEach((plugin, pluginId) => {
    const model = createPluginModelFromConfig('', plugin);
    dependencyGraph.updatePlugin(null, plugin, model, plugin.get('namespace'));
  });

  const navConfig = appConfig.get('navigation');
  dependencyGraph.addNavigation(navConfig);
  dependencyGraph.updateNavigation(navConfig);
  return dependencyGraph;
}

export function createPluginModelFromConfig(
  pageKey: string,
  pluginConfig: PluginConfig,
): Immutable.OrderedMap<string, any> {
  const pluginType = pluginConfig.get('subtype');
  const pluginId = pluginConfig.get('id');
  const namespace = pluginConfig.get('namespace');
  const hidden = pluginConfig.getIn(['layout', 'hidden']);
  const analytics = pluginConfig.analytics;
  const configGen = GetRegisteredConfig(pluginType);
  let mergedConfig = configGen(pluginConfig.config);
  mergedConfig = mergedConfig.merge({pageKey, pluginType, id: pluginId, hidden, namespace}).delete('style');
  if (analytics) {
    mergedConfig = mergedConfig.merge({analytics});
  }
  return mergedConfig;
}

export function sortPluginsByContainerDepth(
  plugins: Immutable.OrderedMap<string, PluginConfig>,
  rootContainerId: string = '',
): Immutable.OrderedMap<string, PluginConfig> {
  // first pass, build a map of pluginId to its child pluginIds
  const pluginChildren: {[key: string]: string[] | undefined} = {
    rootContainerId: [],
  };
  plugins.forEach((plugin: PluginConfig) => {
    const containerId = plugin.layout ? plugin.layout.container : '';
    const childrenArr = pluginChildren[containerId];
    if (childrenArr) {
      childrenArr.push(plugin.id);
    } else {
      pluginChildren[containerId] = [plugin.id];
    }
  });

  // second pass, build a map of pluginId to its depth
  const pluginDepths: {[key: string]: number} = {
    rootContainerId: 0,
  };
  let nextPlugins: [string, number][] = [[rootContainerId, 0]];
  // this is really a while loop, but we use a for-loop for safety
  // against a malformed appTemplate. we expect an iteration for
  // each plugin, + 1 more for "root" (which isn't a plugin)
  for (let i = 0; i < plugins.size + 1; i++) {
    const next = nextPlugins.pop();
    if (!next) break;

    const [pluginId, depth] = next;
    pluginDepths[pluginId] = depth;

    const childrenArr = pluginChildren[pluginId];
    if (childrenArr) {
      nextPlugins = nextPlugins.concat(childrenArr.map(c => [c, depth + 1]));
    }
  }
  if (nextPlugins.length > 0) {
    logger.info('Unexpected leftover plugins in sort. This app may be malformed.');
  }

  // now sort based on the depths computed
  return plugins.sort((p1, p2) => pluginDepths[p1.id] - pluginDepths[p2.id]);
}

export function* execOnPluginUpdate(
  plugin: PluginIdTypePage,
  instance: number | null,
  userTriggered: boolean,
  pageLoad: boolean,
  options?: AppPageTriggerOptions,
) {
  const onPluginUpdate = GetRegisteredOnPluginUpdate(plugin.pluginType);
  let modelChangesToApply: PluginModelChange[] = [];
  if (onPluginUpdate) {
    // logger.time(`onPluginUpdate for Plugin: ${plugin.id}`);
    yield spawn(function* () {
      const state: RootState = yield select();
      const task = yield fork(
        onPluginUpdate,
        state,
        plugin.id,
        plugin.pageKey,
        instance,
        userTriggered,
        pageLoad,
        options,
      );
      const onPluginUpdateResult: any = yield join(task);

      if (onPluginUpdateResult && typeof onPluginUpdateResult === 'object' && 'modelUpdates' in onPluginUpdateResult) {
        const modelChanges = onPluginUpdateResult.modelUpdates.map(modelUpdate => {
          return {
            selector: plugin.pageKey ? [plugin.pageKey, 'plugins'].concat(modelUpdate.selector) : modelUpdate.selector,
            newValue: modelUpdate.newValue,
          };
        });
        modelChangesToApply = modelChangesToApply.concat(modelChanges);
      }

      if (modelChangesToApply.length) {
        yield spawn(modelUpdateSaga, modelChangesToApply, instance);
        // logger.info(`execOnPluginUpdate:: Resolved PLUGIN_MODEL_UPDATE ${plugin.id}`);
      }
    });

    // logger.timeEnd(`onPluginUpdate for Plugin: ${plugin.id}`);
  }
}

export function* handleTriggerOnPluginUpdate(action: DispatchAction<TriggerOnPluginUpdatePayload>): SagaIterator {
  const {plugin, instance, userTriggered, pageLoad, options} = action.payload;
  try {
    const onPluginUpdate = GetRegisteredOnPluginUpdate(plugin.pluginType);
    if (onPluginUpdate) {
      yield* execOnPluginUpdate(plugin, instance, userTriggered, pageLoad, options);
    }
  } catch (e) {
    logger.error(e);
  }
}

export function* generateInitialAppModel(appConfig: AppConfig, appModel: AppModelType): AppModelType {
  let newModel = appModel;
  const dependencyGraph = yield call(generateInitialDependencyGraph, newModel, appConfig, true);
  newModel = newModel.setDependencyGraph(dependencyGraph);
  const renderOrder = newModel.dependencyGraph
    .topologicalSort()
    .filter((selector: Selector) => !_.isNil(newModel.dependencyGraph.lookupDynamicString(selector)));
  const scratchMemory: JSModel = {
    $global: {},
    unresolved: true,
    $context: {},
    hasCurrentPage: false,
    currentPage: {},
    hasIndex: false,
    i: 0,
  };
  const contextId = newModel.evalContextStack.createEvaluationContext();
  let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);

  for (const selector of renderOrder) {
    const binding = newModel.dependencyGraph.lookupBinding(selector);
    newModel = evaluateJSBindingString(
      selector,
      selector,
      binding.dynamicString,
      newModel.dependencyGraph,
      newModel,
      scratchMemory,
      contextId,
      newModel.dependencyGraph.lookupNamespace(selector),
      binding.binding.compiled,
    );
  }

  const evalResults = evalContext.getResults();
  newModel = newModel.setAllValues(evalResults);
  return newModel;
}
export function* initAppModel(action: DispatchAction<{ generateCache: boolean; }|undefined>): SagaIterator {
  try {
    let newModel;
    let pureModel: AppModelType;
    const appModel = yield select(selectAppModel);
    const appConfig = yield select(selectAppConfig);
    if (Platform.OS === 'web' || __DEV__ || action.payload?.generateCache) {
      pureModel = yield call(generateInitialAppModel, appConfig, new AppModelRecord());
      yield putResolve({
        type: DispatchActions.UPDATE_PREINIT_APP_MODEL,
        payload: {
          appModel: pureModel,
          desc: 'InitAppModel: preInit',
        },
      });
      pureModel = yield call(initPurePageModels);
      yield putResolve({
        type: DispatchActions.UPDATE_PREINIT_APP_MODEL,
        payload: {
          appModel: pureModel,
          desc: 'initPurePageModels: preInit',
        },
      });
      yield call(hydratePluginModelCaches);
    }
    logger.info(`[DEBUG] initAppModel: Called`);

    // logger.info(`[DEBUG]generateInitialAppModel: Called`);
    // logger.time(`generateInitialAppModel`);
    newModel = yield call(generateInitialAppModel, appConfig, appModel);
    // logger.timeEnd(`generateInitialAppModel`);
    if (Platform.OS === 'web' || __DEV__ || action.payload?.generateCache) {
      newModel = newModel.setDependencyGraph(pureModel?.dependencyGraph);
    } else {
      const dependencyGraph = new DependencyGraph(appConfig._cachedDependencyGraph.globalScopeIdentifiers);
      dependencyGraph.deserialize(appConfig._cachedDependencyGraph);
      newModel = newModel.setDependencyGraph(dependencyGraph);
    }
    // }
    yield put({
      type: DispatchActions.UPDATE_STAGE_MODEL,
      payload: {
        appModel: newModel.initializeModel(),
        desc: 'InitAppModel: preInit',
      },
    });
    // newModel = yield select(selectAppModel);
    yield spawn(function* () {
      const globalPlugins = appConfig.get('plugins');
      logger.info(`Global Plugin execOnPluginUpdate`);
      logger.time(`Global Plugin execOnPluginUpdate`);
      const globalPluginUpdates = [];
      for (let plugin of globalPlugins.toList().toJS()) {
        // logger.info(`Calling Global Plugin execOnPluginUpdate: ${plugin.id}`);
        globalPluginUpdates.push(
          call(execOnPluginUpdate, {id: plugin.id, pluginType: plugin.subtype, pageKey: ''}, null, false, true),
        );
        // logger.info(`Called Global Plugin execOnPluginUpdate: ${plugin.id}`);
      }
      yield all(globalPluginUpdates);
      logger.timeEnd(`Global Plugin execOnPluginUpdate`);
      // yield call(initGlobalPageModels);
      yield put(commitStageModel());
      yield take(DispatchActions.FINALIZE_COMMIT_APP_MODEL);
      yield spawn(function* () {
        yield put({type: DispatchActions.INITIALIZE_STAGE_MODEL});
        yield put(commitStageModel());
      });
    });
  } catch (e) {
    logger.error(`initAppModel: Error: ${e}`);
    yield put({
      type: DispatchActions.FETCH_APPCONFIG_ERROR,
      payload: {
        e,
      },
    });
  }
}

function* hydratePluginModelCaches() {
  const appConfig: AppConfig = yield select(selectAppConfig);
  const pageConfigs = appConfig.pages;
  let pureModel = yield select(selectPreInitModel);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: appConfig.set(
      'pages',
      pageConfigs.map((pageConfig, pageId) => {
        return pageConfig.set(
          'plugins',
          pageConfig.plugins.map((pluginConfig, pluginId) => {
            // pureModel.dependencyGraph.mergeNodeData([pageId, 'plugins', pluginId], {
            //   _cachedModel: pureModel.getModelValue([pageId, 'plugins', pluginId]),
            // });
            return pluginConfig.set('_cachedModel', pureModel.getModelValue([pageId, 'plugins', pluginId]));
          }),
        );
      }),
    ),
  });
}

export function* hydratePluginModelCachesForPage(hydratePageId: string) {
  const appConfig: AppConfig = yield select(selectAppConfig);
  const pageConfigs = appConfig.pages;
  let pureModel = yield select(selectPreInitModel);
  pureModel = yield call(initPurePageModelFromConfig, pureModel, appConfig, hydratePageId, hydratePageId, {});
  yield putResolve({
    type: DispatchActions.UPDATE_PREINIT_APP_MODEL,
    payload: {
      appModel: pureModel,
      desc: 'initPurePageModelCachesForPage: ' + hydratePageId,
    },
  });
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: appConfig.set(
      'pages',
      pageConfigs.map((pageConfig, pageId) => {
        if(pageId === hydratePageId)
          return pageConfig.set(
            'plugins',
            pageConfig.plugins.map((pluginConfig, pluginId) => {
              // pureModel.dependencyGraph.mergeNodeData([pageId, 'plugins', pluginId], {
              //   _cachedModel: pureModel.getModelValue([pageId, 'plugins', pluginId]),
              // });
              return pluginConfig.set('_cachedModel', pureModel.getModelValue([pageId, 'plugins', pluginId]));
            }),
          );
        else return pageConfig;
      }),
    ),
  });
}

function* initPurePageModels() {
  const appConfig = yield select(selectAppConfig);
  const pageConfigs = appConfig.pages;
  let pureModel = yield select(selectPreInitModel);
  for (let [pageId, pageConfig] of pageConfigs) {
    pureModel = yield call(initPurePageModelFromConfig, pureModel, appConfig, pageId, pageId, {});
  }
  return pureModel;
}

export function* initGlobalPageModels() {
  logger.info(`[DEBUG] initGlobalPageModels`);
  const appConfig = yield select(selectAppConfig);
  const pageConfigs = appConfig.pages;
  for (let [pageId, pageConfig] of pageConfigs) {
    logger.info(`[DEBUG] checking ${pageId}`);
    if (pageConfig.type === 'Loader') {
      appModel = yield call(initPageModelFromConfig, null, appConfig, pageId, pageId, {});
      yield put({
        type: 'INIT_UPDATE_PAGE_MODEL',
        payload: {
          pageId,
          pageKey: pageId,
          context: {},
        },
      });
    }
  }
}

export function getCachedPluginModelsFromPageConfig(pageConfig: PageConfig) {
  let cachedPluginsModel = pageConfig.get('_cachedPluginModels');
  /*
  const fixEventIndexes = (eventsModel: Immutable.Map) => {
    return eventsModel?.mapKeys(key => {
      if (/^\+?\d+$/.test(key)) return parseInt(key);
      return key;
    });
  };

  cachedPluginsModel = cachedPluginsModel?.map(cachePluginModel => {
    let pluginInListView = false;
    cachePluginModel = cachePluginModel.mapKeys(key => {
      if (/^\+?\d+$/.test(key)) {
        pluginInListView = true;
        return parseInt(key);
      }
      return key;
    });
    if (!pluginInListView) {
      cachePluginModel = cachePluginModel.set('events', fixEventIndexes(cachePluginModel.get('events')));
    } else {
      cachePluginModel = cachePluginModel.map(instanceModel => {
        return instanceModel.set('events', fixEventIndexes(instanceModel.get('events')));
      });
    }
    return cachePluginModel;
  });
  */
  return cachedPluginsModel;
}

function createSamplePageModelFromConfig(pageConfig: PageConfig) {
  const pageId = pageConfig?.pageId;
  const pageParams = pageConfig?.pageParams;
  const pageOptions = pageConfig?.pageOptions;
  const pageKey = '';

  const params = pageParams.map((paramConfig: PageParamConfig, paramName: string) => {
    return paramConfig.isRequired ? paramConfig.defaultValue : '';
  });

  const pageoptions = pageOptions?.map((optionConfig: PageOptionConfig) => {
    return optionConfig.isRequired ? optionConfig.defaultValue : '';
  });
  let eventProps = {};
  _.forEach(PageEventsList, evName => {
    eventProps[evName] = '';
  });
  const pageModel = Immutable.Map(pageConfig)
    .merge({pageId, pageKey, params, options: pageOptions, ...eventProps})
    .delete('plugins')
    .delete('pageParams')
    .delete('pageOptions')
    .delete('_cachedPluginModels')
    .delete('_cachedDependencyGraph')
    // .delete('_transpiledBindings');
  return pageModel;
}

function* initPurePageModelFromConfig(
  useAppModel: AppModelType | undefined,
  appConfig: AppConfig,
  pageId: string,
  pageKey: string,
  context: any,
) {
  const dispatch = getAppDispatch();
  const pageConfig: PageConfig = appConfig.pages.get(pageId);
  if (!pageConfig) throw Error('Missing page config: ' + pageId);
  let appModel = useAppModel ? useAppModel : yield select(selectStageModel);
  const dependencyGraph: IDependencyGraph = appModel.get('dependencyGraph');
  const pluginsConfigMap = pageConfig.get('plugins');
  let cachedPluginsModel = getCachedPluginModelsFromPageConfig(pageConfig);
  let pluginModelValues = Immutable.OrderedMap<string, any>();

  const pageParams = pageConfig?.pageParams;
  const pageOptions = pageConfig?.pageOptions;

  const params = pageParams.map((paramConfig: PageParamConfig, paramName: string) => {
    return paramConfig.isRequired ? paramConfig.defaultValue : '';
  });

  const pageoptions = pageOptions?.map((optionConfig: PageOptionConfig) => {
    return optionConfig.isRequired ? optionConfig.defaultValue : '';
  });
  const {params: contextParams, attachmentOptions, ...restContext} = context;
  let newParams = params?.merge(contextParams);
  const options = pageoptions?.merge(attachmentOptions);
  let eventProps = {};
  _.forEach(PageEventsList, evName => {
    eventProps[evName] = '';
  });

  const pageModel = Immutable.Map(pageConfig)
    .merge({pageId, pageKey, params: newParams, options, ...restContext, ...eventProps})
    .delete('plugins')
    .delete('pageParams')
    .delete('pageOptions');
  // .set('plugins', Immutable.OrderedMap());
  dependencyGraph.addPage(pageKey, pageConfig, pageModel);
  dependencyGraph.updatePage(pageKey, pageConfig, pageModel);

  appModel = appModel.setModelValue([pageKey], pageModel.set('plugins', cachedPluginsModel ?? Immutable.Map()));
  // Init DepGraph in model.
  appModel = batchInitModelForPlugins(appConfig, appModel, pluginsConfigMap, pageKey);

  let {contextId, newModel} = yield call(evaluatePageModelWithContext, appModel, pageKey, true);
  let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
  newModel = newModel.mergeEvalResults(evalContext.getResults());
  const listViewConfigs = pluginsConfigMap.filter(pluginConfig => pluginConfig.subtype === 'ListViewWidget');
  listViewConfigs.forEach(pluginConfig => {
    const lvId = pluginConfig.id;
    const lvInstances = newModel.getModelValue([pageKey, 'plugins', lvId, 'instances'], 0);
    newModel = newModel.setModelValue(
      [pageKey, 'plugins', lvId, 'data'],
      _.range(0, lvInstances).map(i => {
        return {i};
      }),
    );
  });
  appModel = newModel;
  return appModel;
}

function* initCompletePageModelFromConfig(
  useAppModel: AppModelType | undefined,
  appConfig: AppConfig,
  pageId: string,
  pageKey: string,
  context: any,
) {
  const dispatch = getAppDispatch();
  const pageConfig: PageConfig = appConfig.pages.get(pageId);
  if (!pageConfig) throw Error('Missing page config: ' + pageId);
  let appModel = useAppModel ? useAppModel : yield select(selectStageModel);
  const dependencyGraph: IDependencyGraph = appModel.get('dependencyGraph');
  const pluginsConfigMap = pageConfig.get('plugins');
  let cachedPluginsModel = getCachedPluginModelsFromPageConfig(pageConfig);
  let cachedDependencyGraph = pageConfig.get('_cachedDependencyGraph');
  let pluginModelValues = Immutable.OrderedMap<string, any>();

  const pageParams = pageConfig?.pageParams;
  const pageOptions = pageConfig?.pageOptions;

  const params = pageParams.map((paramConfig: PageParamConfig, paramName: string) => {
    return paramConfig.isRequired ? paramConfig.defaultValue : '';
  });

  const pageoptions = pageOptions?.map((optionConfig: PageOptionConfig) => {
    return optionConfig.isRequired ? optionConfig.defaultValue : '';
  });
  const {params: contextParams, attachmentOptions, ...restContext} = context;
  let newParams = params?.merge(contextParams);
  const options = pageoptions?.merge(attachmentOptions);
  let eventProps = {};
  _.forEach(PageEventsList, evName => {
    eventProps[evName] = '';
  });

  const pageModel = Immutable.Map(pageConfig)
    .merge({pageId, pageKey, params: newParams, options, ...restContext, ...eventProps})
    .delete('plugins')
    .delete('pageParams')
    .delete('pageOptions')
    .delete('_cachedPluginModels')
    .delete('_cachedDependencyGraph');
  // .set('plugins', Immutable.OrderedMap());
  dependencyGraph.addPage(pageKey, pageConfig, pageModel);
  dependencyGraph.updatePage(pageKey, pageConfig, pageModel);

  appModel = appModel.setModelValue([pageKey], pageModel.set('plugins', cachedPluginsModel ?? Immutable.Map()));
  // Init DepGraph in model.
  if (!useAppModel && cachedDependencyGraph) {
    dependencyGraph.deserialize(cachedDependencyGraph);
  } else {
    appModel = batchInitModelForPlugins(appConfig, appModel, pluginsConfigMap, pageKey);
  }

  if (!useAppModel) {
    // Not just generating AppModel so evaluate bindings
    if (cachedPluginsModel) {
      let {contextId, newModel} = yield call(reevaluatePageKeysWithContext, appModel, pluginsConfigMap, pageKey);
      commitResultsContextToStore(newModel, contextId);
      dispatch(pageBooted(pageKey, pageId));
      appModel = newModel;
    } else {
      let {contextId, newModel} = yield call(evaluatePageModelWithContext, appModel, pageKey);
      commitResultsContextToStore(newModel, contextId);
      dispatch(pageBooted(pageKey, pageId));
      appModel = newModel;
    }
    // yield put(commitStageModel());
  } else {
    let {contextId, newModel} = yield call(evaluatePageModelWithContext, appModel, pageKey, true);
    let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
    newModel = newModel.mergeEvalResults(evalContext.getResults());
    const listViewConfigs = pluginsConfigMap.filter(pluginConfig => pluginConfig.subtype === 'ListViewWidget');
    listViewConfigs.forEach(pluginConfig => {
      const lvId = pluginConfig.id;
      const lvInstances = newModel.getModelValue([pageKey, 'plugins', lvId, 'instances'], 0);
      newModel = newModel.setModelValue(
        [pageKey, 'plugins', lvId, 'data'],
        _.range(0, lvInstances).map(i => {
          return {i};
        }),
      );
    });
    appModel = newModel;
  }

  return appModel;
}

function* initPageModelFromConfig(
  useAppModel: AppModelType | undefined,
  appConfig: AppConfig,
  pageId: string,
  pageKey: string,
  context: any,
) {
  const dispatch = getAppDispatch();
  const pageConfig: PageConfig = appConfig.pages.get(pageId);
  if (!pageConfig) throw Error('Missing page config: ' + pageId);
  let appModel = useAppModel ? useAppModel : yield select(selectStageModel);
  const dependencyGraph: IDependencyGraph = appModel.get('dependencyGraph');
  const pluginsConfigMap = pageConfig.get('plugins');

  const pageParams = pageConfig?.pageParams;
  const pageOptions = pageConfig?.pageOptions;

  const params = pageParams.map((paramConfig: PageParamConfig, paramName: string) => {
    return paramConfig.isRequired ? paramConfig.defaultValue : '';
  });

  const pageoptions = pageOptions?.map((optionConfig: PageOptionConfig) => {
    return optionConfig.isRequired ? optionConfig.defaultValue : '';
  });
  const {params: contextParams, attachmentOptions, ...restContext} = context;
  let newParams = params?.merge(contextParams);
  const options = pageoptions?.merge(attachmentOptions);
  let eventProps = {};
  _.forEach(PageEventsList, evName => {
    eventProps[evName] = '';
  });

  const pageModel = Immutable.Map(pageConfig)
    .merge({pageId, pageKey, params: newParams, options, ...restContext, ...eventProps})
    .delete('plugins')
    .delete('pageParams')
    .delete('pageOptions')
    .delete('_cachedPluginModels')
    .delete('_cachedDependencyGraph');
  // .set('plugins', Immutable.OrderedMap());
  let pluginModelValues: ImmutableMapType = pageConfig.plugins.map((pluginConfig, pluginId) => {
    const ancestorLVSel = dependencyGraph.getAncestorListViewSelector(strsel([pageId, 'plugins', pluginId]));
    if (ancestorLVSel) {
      return pluginConfig._cachedModel?.map((instanceModel, instanceIdx) => {
        return instanceModel?.set('pageKey', pageKey);
      });
    }
    return pluginConfig._cachedModel?.set('pageKey', pageKey);
  });

  appModel = appModel.setModelValue([pageKey], pageModel.set('plugins', pluginModelValues));
  appModel = appModel.setPageKeyToId(pageKey, pageId);
  dispatch(pageBooted(pageKey, pageId));
  let {contextId, newModel} = yield call(evaluatePageEvalsWithContext, appModel, pageKey);
  commitResultsContextToStore(newModel, contextId);
  return appModel;
}

export function batchInitModelForPlugins(
  appConfig: AppConfig,
  appModel: AppModelType,
  pluginsConfigMap: Immutable.OrderedMap<string, PluginConfig>,
  pageKey: string,
  rootContainerId = '',
): AppModelType {
  // logger.time('batchInitModelForPlugins took');
  const depGraph: IDependencyGraph = appModel.get('dependencyGraph');
  const modulesCache = appConfig.get('modules');

  const sortedPlugins = sortPluginsByContainerDepth(pluginsConfigMap, rootContainerId);

  sortedPlugins.forEach((pluginConfig, pluginId) => {
    const pluginConfigModel = createPluginModelFromConfig(pageKey, pluginConfig);
    depGraph.addPlugin(pageKey, pluginConfig, pluginConfigModel);
  });
  sortedPlugins.forEach((pluginConfig, pluginId) => {
    const pluginConfigModel = createPluginModelFromConfig(pageKey, pluginConfig);
    depGraph.updatePlugin(pageKey, pluginConfig, pluginConfigModel, pluginConfig.get('namespace'));
  });

  const modulePlugins = sortedPlugins.filter(pluginConfig => {
    if (pluginConfig.subtype === 'ModuleInstance') return true;
  });

  modulePlugins.forEach((pluginConfig, pluginId) => {
    initModuleOutputs(modulesCache, pageKey, pluginConfig, pluginConfig.id, depGraph);
    initModuleInputs(modulesCache, pluginsConfigMap, pageKey, pluginConfig, pluginConfig.id, depGraph);
  });

  let newModel = appModel.setDependencyGraph(depGraph);
  // logger.timeEnd('batchInitModelForPlugins took');
  return newModel;
}

export function* evaluatePageEvalsWithContext(
  appModel: AppModelType | undefined,
  pageKey: string,
  useSuppliedModel: boolean = false,
): {contextId: number; newModel: AppModelType} {
  const pageId = appModel?.getPageId(pageKey);
  const renderOrder = appModel.dependencyGraph
    .evalOrderSort()
    .filter((selector: Selector) => selector[0] === pageId)
    .map(sel => {
      return sel[0] === pageId ? [pageKey].concat(sel.slice(1)) : sel;
    });
  logger.info(`evaluatePageEvalsWithContext for ${renderOrder.length} Selectors`);
  let newModel = appModel;
  const contextId = newModel.evalContextStack.createEvaluationContext();
  let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
  evalContext.pushEvalResult({selector: [pageKey], value: newModel.getModelValue([pageKey])});
  newModel = yield call(evaluateAppModelChanges, useSuppliedModel ? newModel : undefined, renderOrder, contextId);
  return {contextId, newModel};
}

export function* evaluatePageModelWithContext(
  appModel: AppModelType | undefined,
  pageKey: string,
  useSuppliedModel: boolean = false,
): {contextId: number; newModel: AppModelType} {
  const renderOrder = appModel.dependencyGraph
    .topologicalSort()
    .filter((selector: Selector) => selector[0] === pageKey);
  logger.info(`evaluatePageModelWithContext for ${renderOrder.length} Selectors`);
  let newModel = appModel;
  const contextId = newModel.evalContextStack.createEvaluationContext();
  let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
  evalContext.pushEvalResult({selector: [pageKey], value: newModel.getModelValue([pageKey])});
  newModel = yield call(evaluateAppModelChanges, useSuppliedModel ? newModel : undefined, renderOrder, contextId);
  return {contextId, newModel};
}

export function* reevaluatePageKeysWithContext(
  appModel: AppModelType | undefined,
  pluginsConfigMap: Immutable.OrderedMap<string, PluginConfig>,
  pageKey: string,
): {contextId: number; newModel: AppModelType} {
  let renderOrder = pluginsConfigMap.keySeq().map(pluginId => [pageKey, 'plugins', pluginId, 'pageKey']);
  const evaluationOrder = appModel.dependencyGraph
    .topologicalSort()
    .filter((selector: Selector) => selector[0] === pageKey)
    .filter((selector: Selector) => {
      return appModel.dependencyGraph.isJSBindingString(selector);
    });
  renderOrder = renderOrder.concat(evaluationOrder);
  const pluginsWithActions = pluginsConfigMap.filter(
    (pluginConfig, pluginId) =>
      !_.isEmpty(makeActionSelectorConfig(resolvePluginPropertySettings(pluginConfig?.subtype))),
  );
  const actionsSelectors: Selector[] = [];
  pluginsWithActions.forEach((pluginConfig, pluginId) => {
    const pluginActions = makeActionSelectorConfig(resolvePluginPropertySettings(pluginConfig?.subtype));
    pluginActions.forEach(action => actionsSelectors.push([pageKey, 'plugins', pluginId, action.name]));
  });
  renderOrder = renderOrder.concat(actionsSelectors);
  logger.info(`reevaluatePageKeysWithContext for ${renderOrder.size} Selectors`);
  let newModel = appModel;
  const contextId = newModel.evalContextStack.createEvaluationContext();
  let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
  evalContext.pushEvalResult({selector: [pageKey], value: newModel.getModelValue([pageKey])});
  newModel = yield call(evaluateAppModelChanges, undefined, renderOrder.toArray(), contextId);
  // logger.info(`reevaluatePageKeysWithContext Model evaluated for (${contextId}) `);
  return {contextId, newModel};
}

export function* generatePurePageModelFromConfig(appConfig: AppConfig, pageId: string) {
  const preInitModel = yield select(selectPreInitModel);
  let pureModel = yield call(initCompletePageModelFromConfig, preInitModel, appConfig, pageId, pageId, {});
  yield put({
    type: DispatchActions.UPDATE_PREINIT_APP_MODEL,
    payload: {
      appModel: pureModel,
      desc: 'initPageModelFromConfig',
    },
  });
  return pureModel;
}

export function commitResultsContextToStore(appModel: AppModelType, contextId: number) {
  const dispatch = getAppDispatch();
  let evalContext = appModel.evalContextStack.getEvaluationContext(contextId);
  const results = evalContext.getResults();
  if (results.length) {
    dispatch(updateStageModelWithResults(results));
  }
}

export function* initPageModelSaga(action: DispatchAction<InitPageModelPayload>): SagaIterator {
  try {
    const dispatch = getAppDispatch();
    const {pageId, pageKey, context} = action.payload;
    logger.info(`initPageModelSaga(${pageKey}) Started`);
    // logger.time(`initPageModelSaga(${pageKey}) took`);

    let appConfig = yield select(selectAppConfig);
    
    yield* initPageModelFromConfig(undefined, appConfig, pageId, pageKey, context);

    // logger.time(`initPageModelSaga(${pageKey}) took`, 'Page Model from Config');
    // logger.timeEnd(`initPageModelSaga(${pageKey}) took`);
    yield spawn(function* () {
      // Commit now to show cached state/init state with loaders/placeholders.
      // yield put({type: DispatchActions.FINALIZE_COMMIT_APP_MODEL, payload: {appModel: newModel}});
      yield put({
        type: 'INIT_UPDATE_PAGE_MODEL',
        payload: {
          pageId,
          pageKey,
          context: undefined,
        },
      });
    });
  } catch (e) {
    logger.error(e);
    yield put({
      type: DispatchActions.INIT_PAGE_MODEL_ERROR,
      payload: {
        e,
      },
    });
  }
}

export function* initUpdatePageModelSaga(action: DispatchAction<InitPageModelPayload>): SagaIterator {
  try {
    const {pageId, pageKey} = action.payload;
    logger.info(`[DEBUG] initUpdatePageModelSaga called.`);
    yield call(flushModelEvalQueue);
    let pageConfig: PageConfig = yield select(selectPageConfigForPage, pageId);
    for (let [pluginId, pluginConfig] of pageConfig.plugins) {
      const onPluginUpdate = GetRegisteredOnPluginUpdate(pluginConfig.subtype);
      if (onPluginUpdate) {
        yield put(
          triggerOnPluginUpdate({id: pluginId, pluginType: pluginConfig.subtype, pageKey: pageKey}, null, false, true),
        );
      }
    }
    yield put(triggerPageEvent(pageKey, null, undefined, 'onLoad'));
    yield put(commitStageModel());
  } catch (e) {
    yield put({
      type: DispatchActions.INIT_PAGE_MODEL_ERROR,
      payload: {
        e,
      },
    });
  }
}

export function* destroyPageModelSaga(action: DispatchAction<DestroyPageModel>): SagaIterator {
  try {
    // logger.time('destroyPageModelSaga');
    const {pageId, pageKey} = action.payload;
    yield call(InteractionManager.runAfterInteractions);
    const appModel = yield select(selectStageModel);
    const appConfig = yield select(selectAppConfig);
    // ApptileAnimationsRegistry.unRegisterPageAnimationRefs(pageKey);
    // let depGraph = destroyPageModelFromConfig(appModel, appConfig, pageId, pageKey);
    let newModel = yield select(selectStageModel);

    // newModel = newModel.setDependencyGraph(depGraph);
    newModel = newModel.deleteModelValue([pageKey]);
    newModel = newModel.deletePageKeyToId(pageKey);
    yield put({
      type: DispatchActions.UPDATE_STAGE_MODEL,
      payload: {
        appModel: newModel,
        desc: 'destroyPageModel',
        meta: {pageId, pageKey},
      },
    });
    yield put(commitStageModel());
    // logger.timeEnd('destroyPageModelSaga');
  } catch (e) {
    // logger.timeEnd('destroyPageModelSaga');
    yield put({
      type: DispatchActions.DESTROY_PAGE_MODEL_ERROR,
      payload: {
        e,
      },
    });
  }
}

function* focusPageHandlerSaga(action: DispatchAction<ApptileActivePagePayload>): SagaIterator {
  try {
    const {activePageId, activePageKey} = action.payload;
    yield call(flushModelEvalQueue);
    const appConfig = yield select(selectAppConfig);
    // Wait for model to commit to AppModel before calling focus callbacks.
    const appModel: AppModelType = yield select(selectAppModel);

    const pageModel = appModel.getModelValue([activePageKey]);
    logger.info("[LIFECYCLE] Page focus attempt: " + activePageKey);
    if (pageModel && pageModel?.get('pageKey') === activePageKey) {
      const pageConfig: PageConfig = appConfig.pages.get(activePageId);
      // logger.info('Executing Page Focus callback', activePageKey);
      yield put(triggerPageEvent(activePageKey, null, undefined, 'onFocus'));
      let firstPlugin;
      for (let plugin of pageConfig.plugins.toList().toArray()) {
        firstPlugin = plugin;
        if (plugin.type === 'query' || (plugin.type === 'state' && plugin.subtype !== 'ModuleProperty')) {
          logger.info("OnUpdate check", plugin.type, plugin.subtype, plugin.id);
          yield put(
            triggerOnPluginUpdate(
              {id: plugin.id, pluginType: plugin.subtype, pageKey: activePageKey},
              null,
              false,
              false,
              {focusTrigger: true},
            ),
          );
        }
      }
      logger.info("[LIFECYCLE] Page focus success: " + activePageKey);
      // if (firstPlugin && firstPlugin.get.call) {
      //   // This wait is there to let the external widget load
      //   while (!isPluginInfoLoaded(firstPlugin.get('subtype'))) {
      //     yield delay(2000);
      //   }
      //   yield put({
      //     type: DispatchActions.SELECT_PLUGIN, 
      //     payload: [activePageKey, firstPlugin.get('id')]
      //   });
      // }
    } else {
      logger.info("[LIFECYCLE] Page focus retry " + action.payload.retryIndex);
      if ((!pageModel || !pageModel?.get('pageKey')) && (action.payload.retryIndex == undefined || action.payload.retryIndex < 5)) {
        logger.info('Delaying focus callback ' + JSON.stringify(action));

        if (!action.payload.retryIndex) {
          action.payload.retryIndex = 1;
        } else {
          action.payload.retryIndex += 1;
        }

        const exponentialBackoffCoefficients = [2, 4, 8, 16, 32];
        yield delay(200 * exponentialBackoffCoefficients[action.payload.retryIndex]);
        yield put(action);
      } else {
        console.error("[LIFECYCLE] FOCUS TRIGGER FAILURE!!", pageModel?.get('pageKey'), " retried times: ", action.payload.retryIndex);
      }
    }
  } catch (e) {
    logger.error(e);
  }
}

function propagateChanges(
  dependants: Selector[],
  evalContextId: number | undefined = undefined,
) {
  return evaluateAppModelChanges(undefined, dependants, evalContextId);
}

const AppModelEvalQueue = [];
async function flushModelEvalQueue() {
  return Promise.all(AppModelEvalQueue);
}
async function evaluateAppModelChanges(
  appModel: AppModelType | undefined,
  selectors: Selector[],
  evalContextId: number | undefined = undefined,
): AppModelType {
  if (!appModel) {
    appModel = selectStageModel(getAppState());
    let evalContext = appModel.evalContextStack.getEvaluationContext(evalContextId);
    const evalResults = evalContext.getResults();
    appModel = appModel.setAllValues(evalResults);
  }
  return evaluateAppModelChangesBatch(appModel, selectors, evalContextId);
}

function evaluateAppModelChangesBatch(
  appModel: AppModelType,
  selectors: Selector[],
  evalContextId: number | undefined = undefined,
): AppModelType {
  // logger.time(`evaluateAppModelChanges ${evalContextId} (${selectors?.length}) `);
  // logger.info('[DEBUG]evaluateAppModelChanges: chunked:', evalContextId, chunkedSelectors.length);

  let model = appModel;
  const scratchMemory: JSModel = {
    $global: {},
    unresolved: true,
    $context: {},
    hasCurrentPage: false,
    currentPage: {},
    hasIndex: false,
    i: 0,
  };

  let evalContext;
  if (process.env.IS_WEB_ENV) {
    if (evalContextId) evalContext = model.evalContextStack.getEvaluationContext(evalContextId);
  }
  const validPageKeys = new Set(model.pageKeysToId?.keys());

  for (const selector of selectors) {
    // logger.info('[DEBUG]evaluateAppModelChanges: evaluating:', evalContextId, selector);
    // if (model.dependencyGraph.hasNode(selector)) {
    let binding, graphSelector;
    if (validPageKeys.has(selector[0])) {
      graphSelector = [model.pageKeysToId.get(selector[0])].concat(selector.slice(1));
      binding = model.dependencyGraph.lookupBinding(graphSelector);
    } else {
      binding = model.dependencyGraph.lookupBinding(selector);
      graphSelector = selector;
    }
    model = evaluateJSBindingString(
      graphSelector,
      selector,
      binding.dynamicString,
      model.dependencyGraph,
      model,
      scratchMemory,
      evalContextId,
      model.dependencyGraph.lookupNamespace(selector),
      binding.binding.compiled,
    );
    // }
  }

  if (process.env.IS_WEB_ENV) {
    if (evalContext) {
      const evalResults = evalContext.getResults();
      model = appModel.setAllValuesNoJSModel(evalResults);
    }
  }

  // logger.timeEnd(`evaluateAppModelChanges ${evalContextId} (${selectors?.length}) `);
  return model;
}

export function* modelUpdateSaga(
  changesets: ModelChange[],
  instance: number | undefined,
  runOnUpdate: boolean = false,
): SagaIterator {
  const appModel: AppModelType = yield select(selectStageModel);
  const appConfig: AppConfig = yield select(selectAppConfig);
  // logger.info(`Executing modelUpdate for Changest: `, changesets);
  let newModel = appModel;
  // for (const {selector, newValue} of changesets) {
  //   newModel = newModel.setModelValue(selector, newValue);
  // }

  let selectors = changesets.map(cs => cs.selector);
  if (instance !== undefined) {
    selectors = selectors.map(selector => {
      const pluginSel = getPluginSelectorFromSelector(selector);
      const selInstance = selector[pluginSel?.length];
      if (selInstance == instance) {
        return selector.slice(0, pluginSel?.length).concat(selector.slice(pluginSel?.length + 1));
      } else {
        return selector;
      }
    });
  }
  const validPageKeys = new Set<string>(appModel.pageKeysToId.keys());
  const validPageIds = new Set<string>(appModel.pageKeysToId.values());
  const pageIdsToPageKeys: Record<string, Set<string>> = new Map<string, Set<string>>();
  appModel.pageKeysToId.map((pageId, pageKey) => {
    if (pageIdsToPageKeys[pageId] === undefined) {
      pageIdsToPageKeys[pageId] = new Set<string>();
    }
    pageIdsToPageKeys[pageId].add(pageKey);
  });
  const allPageIds = new Set<string>(appConfig.pages.keys());
  const excludePageIds = new Set<string>();
  allPageIds.forEach(val => {
    if (!validPageIds.has(val)) excludePageIds.add(val);
  });
  const graphSelectors = [];
  for (const selector of selectors) {
    if (!validPageKeys.has(selector[0])) {
      graphSelectors.push(selector);
    } else {
      graphSelectors.push([appModel.pageKeysToId.get(selector[0])].concat(selector.slice(1)));
    }
  }
  // const graphDependants = selectors
  //   .map(selector => {
  //     return _.map([...newModel.dependencyGraph.getDependantsOf(selector)], node => node.selector);
  //   })
  //   .reduce((dependantsSet, subDependants) => {
  //     subDependants.forEach(dep => dependantsSet.add(dep));
  //     return dependantsSet;
  //   }, new Set<Selector>());

  // const dependantSelStr = new Set<string>();
  // _.map([...dependants], selector => dependantSelStr.add(strsel(selector)));

  const graphDependants: Set<Selector> = new Set<Selector>();
  const graphDependentSelStrings: Set<string> = new Set<string>();
  const modelDependants: Set<Selector> = new Set<Selector>();
  for (const selector of graphSelectors) {
    const dependantNodesSet = newModel.dependencyGraph.getDependantsOf(selector);
    for (const depNode of dependantNodesSet) {
      const depSel = depNode.selector;
      if (!excludePageIds.has(depSel[0])) {
        graphDependants.add(depSel);
        graphDependentSelStrings.add(strsel(depSel));
      }
    }
  }

  // const globalSelectors = selectors.filter(sel => !validPageKeys.has(sel[0]));

  let pageSelectorKey: string = null;
  let pageSelectorId: string = null;
  if (validPageKeys.has(selectors[0][0])) {
    pageSelectorKey = selectors[0][0];
    pageSelectorId = appModel.pageKeysToId.get(pageSelectorKey);
  }

  // const dependants = new Set<Selector>();
  // const dependantSelStrings = new Set<string>();

  // if (pageSelectorKey) {
  //   const pageSelectors = selectors
  //     .filter(sel => validPageKeys.has(sel[0]))
  //     .map(sel => [pageSelectorId].concat(sel.slice(1)));
  //   const pageSelDependants = pageSelectors
  //     .map(selector => {
  //       let selDeps = newModel.dependencyGraph.getDependantsOf(selector);
  //       const arrayDeps = [];
  //       for (let dep of selDeps) {
  //         if (!excludePageIds.has(dep.selector[0])) {
  //           arrayDeps.push(dep.selector);
  //         }
  //       }
  //       return arrayDeps;
  //     })
  //     .reduce((dependants, subDependants) => {
  //       subDependants.forEach(dep => dependants.add([pageSelectorKey].concat(dep.slice(1))));
  //       return dependants;
  //     }, new Set<Selector>());
  //   _.map([...pageSelDependants], selector => {
  //     dependantSelStrings.add(strsel(selector));
  //   });
  //   // console.log('pageSelDependants', pageSelDependants);

  //   pageSelDependants.forEach(sel => {
  //     dependants.add([pageSelectorKey].concat(sel.slice(1)));
  //   });
  // } else {
  //   const globalDependants = globalSelectors
  //     .map(selector => {
  //       let selDeps = newModel.dependencyGraph.getDependantsOf(selector);
  //       const arrayDeps = [];
  //       for (let dep of selDeps) {
  //         if (!excludePageIds.has(dep.selector[0])) {
  //           arrayDeps.push(dep.selector);
  //         }
  //       }
  //       return arrayDeps;
  //     })
  //     .reduce((dependants, subDependants) => {
  //       subDependants.forEach(dep => dependants.add(dep));
  //       return dependants;
  //     }, new Set<Selector>());

  //   _.map([...globalDependants], selector => {
  //     dependants.add(selector);
  //     dependantSelStrings.add(strsel(selector));
  //   });
  //   // console.log('globalDependants', globalDependants);
  // }

  const contextId = newModel.evalContextStack.createEvaluationContext();
  let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
  for (const {selector, newValue} of changesets) {
    evalContext.pushEvalResult({selector, value: newValue});
  }

  const evalOrderSelectors = appModel.dependencyGraph.evalOrderSort();
  const modelDependantsSel = [];
  if(!pageSelectorKey) {
    // console.log('dependantSelStrings', dependantSelStrings);
    for (const evalSel of evalOrderSelectors) {
      if (graphDependentSelStrings.has(strsel(evalSel))) {
        if (validPageIds.has(evalSel[0])) {
          for (const pageKey of pageIdsToPageKeys[evalSel[0]]) {
            const selToAdd = [pageKey].concat(evalSel.slice(1));
            modelDependants.add(selToAdd);
            modelDependantsSel.push(selToAdd);
          }
        } else {
          modelDependants.add(evalSel);
          modelDependantsSel.push(evalSel);
        }
      }
    }
  } else {
    for (const evalSel of evalOrderSelectors) {
      if (graphDependentSelStrings.has(strsel(evalSel))) {
        if (validPageIds.has(evalSel[0])) {
            const selToAdd = [pageSelectorKey].concat(evalSel.slice(1));
            modelDependants.add(selToAdd);
            modelDependantsSel.push(selToAdd);
        } else {
          modelDependants.add(evalSel);
          modelDependantsSel.push(evalSel);
        }
      }
    }
  }
  // console.log('modelDependantsSel', modelDependantsSel);
  newModel = yield call(evaluateAppModelChanges, undefined, modelDependantsSel, contextId);
  // logger.info(`Executing modelUpdate Start Propogation for Changset(${contextId}): `, selectors);
  // logger.time(`modelUpdate Propogation for Changset(${contextId}): `);
  // logger.info(`Executing modelUpdate Propogating changes for Changset(${contextId}): `, Array.from(dependantSelStr));
  // newModel = yield call(propagateChanges, newModel, dependantSelStrings, contextId);

  // // console.log('dependantSelStrings', dependantSelStrings);
  // const dependantSelectors = appModel.dependencyGraph
  //   .evalOrderSort()
  //   .filter((selector: Selector) =>
  //     dependantSelStrings.has((pageSelectorKey ? [pageSelectorKey].concat(selector.slice(1)) : selector).join('.')),
  //   )
  //   .map((selector: Selector) => {
  //     return pageSelectorKey ? [pageSelectorKey].concat(selector.slice(1)) : selector;
  //   });
  // // console.log('dependantSelectors', dependantSelectors);
  // newModel = yield call(evaluateAppModelChanges, undefined, dependantSelectors, contextId);

  evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
  const results = evalContext.getResults();
  if (results.length) {
    // yield spawn(function* () {
    yield put(updateStageModelWithResults(results));
    // });
  }
  // evalContext.clearStack();
  // logger.timeEnd(`modelUpdate Propogation for Changset(${contextId}): `);
  // logger.info(`Executing modelUpdate End Propogation for Changset(${contextId}): `, changesets);

  // logger.time(`modelUpdate onPluginUpdates for Changset(${contextId}): `);
  const changedPlugins = new Set(changesets.map(({selector}) => strsel(getPluginSelectorFromSelector(selector))));

  const dependantTemplates = new Set(
    Array.from(modelDependants)
      .map((selector: Selector) => {
        return strsel(selector.length > 2 && selector[1] == 'plugins' ? selector.slice(0, 3) : [selector[0]]);
      })
      .filter((sel: string) => !changedPlugins.has(sel)),
  );

  for (let depSelStr of dependantTemplates) {
    const depSel = objsel(depSelStr);
    const id = depSel.length > 2 ? depSel[2] : depSel[0];
    const pageKey = depSel.length > 2 ? depSel[0] : '';
    const pluginType = newModel.getPluginModel(pageKey, id)?.get('pluginType');
    // yield call(execOnPluginUpdate, {id, pluginType, pageKey}, null, false, false);
    const onPluginUpdate = GetRegisteredOnPluginUpdate(pluginType);
    if (onPluginUpdate) {
      yield put(triggerOnPluginUpdate({id, pluginType, pageKey}, null, false, false));
    }
  }

  if (runOnUpdate) {
    for (let changedSelStr of changedPlugins) {
      const depSel = objsel(changedSelStr);
      const id = depSel.length > 2 ? depSel[2] : depSel[0];
      const pageKey = depSel.length > 2 ? depSel[0] : '';
      const pluginType = newModel.getPluginModel(pageKey, id)?.get('pluginType');
      // yield call(execOnPluginUpdate, {id, pluginType, pageKey}, null, false, false);
      const onPluginUpdate = GetRegisteredOnPluginUpdate(pluginType);
      if (onPluginUpdate) {
        yield spawn(function* () {
          yield put(triggerOnPluginUpdate({id, pluginType, pageKey}, null, false, false));
        });
      }
    }
  }
  yield spawn(function* () {
    yield put(commitStageModel());
  });
  // logger.timeEnd(`modelUpdate onPluginUpdates for Changset(${contextId}): `);
}

export function* updatePluginModelSaga(action: DispatchAction<ModelUpdateAction>): SagaIterator {
  try {
    const wait = yield call(
      modelUpdateSaga,
      action.payload.changesets,
      action.payload.instance,
      action.payload.runOnUpdate,
    );
    return new Promise(resolve => resolve(wait));
  } catch (e) {
    logger.error(e);
  }
}

function getSelectorsFromUpdate(selector: Selector, object: any): any {
  if (Immutable.isCollection(object) || Immutable.isRecord(object)) {
    if (Immutable.isRecord(object)) object = Immutable.fromJS(object?.toJS());
    return object.reduce((selectors, v, k) => {
      const childSelector = selector.concat([k]);
      return selectors.union(getSelectorsFromUpdate(childSelector, v));
    }, Immutable.Set());
  } else {
    return Immutable.Set([selector]);
  }
}

export function* addPluginDone(
  action: DispatchAction<{
    pageId: string;
    pluginId: string;
    pluginConfig: PluginConfigType<any>;
  }>,
) {
  const {pageId, pluginId, pluginConfig} = action.payload;
  let appModel: AppModelType = yield select(selectStageModel);
  if (!appModel.dependencyGraph) return;

  const pageKeys: string[] = appModel.pageKeysToId
    .mapEntries(([key, id]) => {
      return id == pageId ? [key, id] : undefined;
    })
    .toArray()
    .map(([k, _]) => k);

  if (pluginConfig) {
    for (const pageKey of pageKeys) {
      const pluginConfigModel = createPluginModelFromConfig(pageKey, pluginConfig);
      appModel.dependencyGraph.addPlugin(pageId, pluginConfig, pluginConfigModel);
      appModel.dependencyGraph.updatePlugin(pageId, pluginConfig, pluginConfigModel, pluginConfig.get('namespace'));
      yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, pluginConfigModel);
      appModel = yield select(selectStageModel);
      const ancestorListViewId = appModel.dependencyGraph.getAncestorListViewSelector(`${pageKey}.plugins.${pluginId}`);
      if (ancestorListViewId) {
        const listViewModel = appModel.getModelValue([pageKey, 'plugins', ancestorListViewId]);
        const listViewId = listViewModel.get('id');
        const lvType = listViewModel.get('pluginType');
        yield call(execOnPluginUpdate, {id: listViewId, pluginType: lvType, pageKey}, null, false, false);
      }
    }
  }
}

export function* movePluginDone(
  action: DispatchAction<{
    pageId: string;
    pluginId: string;
    pluginConfig: PluginConfigType<any>;
  }>,
) {
  const {pageId, pluginId, pluginConfig} = action.payload;
  let appModel: AppModelType = yield select(selectAppModel);
  if (!appModel.dependencyGraph) return;

  const pageKeys: string[] = appModel.pageKeysToId
    .mapEntries(([key, id]) => {
      return id == pageId ? [key, id] : undefined;
    })
    .toArray()
    .map(([k, _]) => k);

  if (pluginConfig) {
    for (const pageKey of pageKeys) {
      const pluginConfigModel = createPluginModelFromConfig(pageKey, pluginConfig);
      appModel.dependencyGraph.updatePlugin(pageId, pluginConfig, pluginConfig.config, pluginConfig.get('namespace'));
      yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, pluginConfigModel);
    }
  }
}

export function* renamePlugin(action: DispatchAction<PluginRenamePayload>) {
  try {
    const {pageId, pluginId, newPluginId, namespace} = action.payload;
    if (pluginId === newPluginId || !validateNaming(newPluginId)) return;

    let appModel: AppModelType = yield select(selectAppModel);
    if (!appModel.dependencyGraph) return;

    let newModel = appModel;

    if (!pageId) {
      const isPluginAlreadyExist = !!newModel.getModelValue(['plugins', newPluginId]);
      if (isPluginAlreadyExist) return;

      newModel = newModel.dependencyGraph.renamePlugin('', pluginId, newPluginId);
      let pluginModelValues: Immutable.Map<string, any> = newModel.getModelValue(['plugins']);

      pluginModelValues = pluginModelValues
        .mapKeys(k => (k === pluginId ? newPluginId : k))
        .setIn([newPluginId, 'id'], newPluginId);
      newModel = newModel.setModelValue(['', 'plugins'], pluginModelValues);
    } else {
      const pageKeys: string[] = appModel.pageKeysToId
        .mapEntries(([key, id]) => {
          return id == pageId ? [key, id] : undefined;
        })
        .toArray()
        .map(([k, _]) => k);
      let renamedId = newPluginId;
      if (namespace) {
        renamedId = addNamespace(namespace, newPluginId);
      }
      for (const pageKey of pageKeys) {
        const isPluginAlreadyExist = !!newModel.getModelValue([pageKey, 'plugins', renamedId]);
        if (isPluginAlreadyExist) break;

        newModel.dependencyGraph.renamePlugin(pageKey, pluginId, renamedId);
        let pluginModelValues = newModel.getModelValue([pageKey, 'plugins']);
        pluginModelValues = pluginModelValues
          .mapKeys(k => (k === pluginId ? renamedId : k))
          .updateIn([renamedId], value => {
            if (Immutable.isMap(value) && value.has('id')) return value.set('id', renamedId);
            else if (Immutable.isMap(value))
              return value.map(listValue => {
                if (Immutable.isMap(listValue) && listValue.has('id')) return listValue.set('id', renamedId);
                return listValue;
              });
          });
        newModel = newModel.setModelValue([pageKey, 'plugins'], pluginModelValues);
      }
    }
    yield put({
      type: DispatchActions.UPDATE_STAGE_MODEL,
      payload: {
        appModel: newModel,
        desc: 'renamePlugin',
        meta: {...action.payload},
      },
    });
    yield put(commitStageModel());
  } catch (e) {
    logger.error(e);
  }
}

export function* handleDeletePlugin(action: DispatchAction<PluginDeletePayload>) {
  const {pageId, pluginIds} = action.payload;
  let appModel: AppModelType = yield select(selectAppModel);
  if (!appModel.dependencyGraph) return;

  let newModel = appModel;

  const pageKeys: string[] = appModel.pageKeysToId
    .mapEntries(([key, id]) => {
      return id == pageId ? [key, id] : undefined;
    })
    .toArray()
    .map(([k, _]) => k);

  newModel = pluginIds.reduce((state: AppModelType, id: any) => {
    pageKeys.forEach(pageKey => state.dependencyGraph.deletePlugin(pageKey, id));
    if (pageId) {
      return pageKeys.reduce((st: AppModelType, pageKey: any) => {
        return st.deleteModelValue([pageKey, 'plugins', id]);
      }, state);
    } else {
      return state.deleteModelValue([id]);
    }
  }, newModel);
  yield put({
    type: DispatchActions.UPDATE_STAGE_MODEL,
    payload: {
      appModel: newModel,
      desc: 'handleDeletePlugin',
      meta: {...action.payload},
    },
  });
  yield put(commitStageModel());
}

export function* updatePluginPropertyNamespace(action: DispatchAction<PluginPropertyNamespaceUpdate>) {
  let appModel: AppModelType = yield select(selectAppModel);
  if (!appModel.dependencyGraph) {
    return;
  }
  const {pageKey, pluginId, update, namespace} = action.payload;

  appModel.dependencyGraph.updatePlugin(pageKey);
}

export function* recalculatePluginConfig(
  pageId: string,
  pluginId: string,
  pluginConfig: PluginConfig,
  update: SafeAny,
) {
  try {
    let appModel: AppModelType = yield select(selectStageModel);
    if (!appModel.dependencyGraph) return;
    // logger.time(`recalculatePluginConfig: pageId ${pageId}, pluginId ${pluginId}`);
    // logger.info(`[DEBUG] Begin recalculatePluginConfig: pageId ${pageId}, pluginId ${pluginId}`);
    const isGlobalPlugin = !pageId;
    const pageKeys: string[] = appModel.pageKeysToId
      .mapEntries(([key, id]) => {
        return id == pageId ? [key, id] : undefined;
      })
      .toArray()
      .map(([k, _]) => k);

    update = Immutable.isImmutable(update) ? update : Immutable.fromJS(update);
    if (pluginConfig) {
      if (isGlobalPlugin) {
        appModel.dependencyGraph.updatePlugin(null, pluginConfig, update, pluginConfig.get('namespace'));
      } else {
        pageKeys.forEach(pageKey => {
          const pageId = appModel.pageKeysToId.get(pageKey);
          appModel.dependencyGraph.updatePlugin(pageId, pluginConfig, update, pluginConfig.get('namespace'));
        });
      }
    }

    let possibleSelectors = Immutable.Set<Selector>();
    if (!isGlobalPlugin) {
      pageKeys.forEach(pageKey => {
        const pluginSel = pageKey ? [pageKey, 'plugins', pluginId] : [pluginId];
        possibleSelectors = possibleSelectors.union(getSelectorsFromUpdate(pluginSel, update));
      });
    } else {
      possibleSelectors = possibleSelectors.union(getSelectorsFromUpdate([pluginId], update));
    }

    let validSelectors = Immutable.Set<string>();
    if (!isGlobalPlugin) {
      pageKeys.forEach(pageKey => {
        const pageId = appModel.pageKeysToId.get(pageKey);
        const pluginSel = pageKey ? [pageId, 'plugins', pluginId] : [pluginId];
        validSelectors = validSelectors.union(
          appModel.dependencyGraph.getObjectSelectors(pluginSel).map((s: any) => {
            s[0] = pageKey;
            return strsel(s);
          }),
        );
      });
    } else {
      validSelectors = validSelectors.union(
        appModel.dependencyGraph.getObjectSelectors([pluginId]).map((s: any) => strsel(s)),
      );
    }

    let selectors = possibleSelectors.filter((s: string[]) => validSelectors.has(strsel(s)));

    let newModel = appModel;
    let contextId = newModel.evalContextStack.createEvaluationContext();
    newModel = yield call(evaluateAppModelChanges, undefined, selectors.toArray(), contextId);
    let evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
    let results = evalContext.getResults();
    if (results.length) {
      yield put(updateStageModelWithResults(results));
    }

    // Make the selectors be pageId selectors instead of pageKey selectors to look in depgraph
    const pageKeyToIdMap = appModel.pageKeysToId;
    const pageIdToKeys = new Map();
    const pageIdSelectors = selectors.map(selector => {
        const pageKey = selector[0];
        const id = pageKeyToIdMap.get(pageKey);
        if (id) {
          const pageIdSelector = [id].concat(selector.slice(1));
          if (!pageIdToKeys.has(id)) {
            const pageKeys = new Set();
            pageKeys.add(pageKey);
            pageIdToKeys.set(id, pageKeys);
          } else {
            pageIdToKeys.get(id).add(pageKey);
          }
          return pageIdSelector;
        } else {
          return selector;
        }
    });

    // find all dependants of pageIdSelectors
    const dependants = pageIdSelectors
      .map(selector => {
        return _.map([...newModel.dependencyGraph.getDependantsOf(selector)], node => node.selector);
      })
      .reduce((dpndnts, subDependants) => {
        subDependants.forEach((dep: Selector) => {
          dpndnts.add(strsel(dep));
        });
        return dpndnts;
      }, new Set<string>())

    // find the order in which the dependants have to be evaluated
    const dependantSelectors = appModel.dependencyGraph
      .evalOrderSort()
      .filter((selector: Selector) => dependants.has(selector.join('.')));

    // Convert all the sorted selectors that need to be evaluated into pagekey selectors
    const allSelectors = [];
    for (let index = 0; index < dependantSelectors.length; ++index) {
      const dep = dependantSelectors[index];
      const id = dep[0];
      if (pageIdToKeys.has(id)) {
        for (let pageKey of pageIdToKeys.get(id)) {
          allSelectors.push([pageKey].concat(dep.slice(1)));
        }
      } else {
        allSelectors.push(dep);
      }
    }

    contextId = newModel.evalContextStack.createEvaluationContext();
    newModel = yield call(propagateChanges, allSelectors, contextId);
    evalContext = newModel.evalContextStack.getEvaluationContext(contextId);
    results = evalContext.getResults();
    if (results.length) {
      yield put(updateStageModelWithResults(results));
    }
    yield put(commitStageModel());

    // Call onPluginupdate on all instances of this plugin.
    for (const pageKey of pageKeys) {
      yield call(execOnPluginUpdate, {id: pluginId, pluginType: pluginConfig.subtype, pageKey}, null, false, false);
    }

    const changedPlugins = Immutable.Set(
      selectors.map((selector: string[]) => {
        if (selector.length > 2 && selector[1] == 'plugins')  {
          let truncatedSel = selector.slice(0, 3);
          truncatedSel[0] = pageKeyToIdMap.get(selector[0], selector[0]);
          return truncatedSel;
        } else {
          return [pageKeyToIdMap.get(selector[0], selector[0])];
        }
      }),
    );

    const dependantTemplates = Immutable.Set(
        dependants.map((selector: string) => {
          const firstDot = selector.indexOf(".")
          const secondDot = selector.indexOf(".", firstDot + 1)
          const thirdDot = selector.indexOf(".", secondDot + 1)
          let truncatedSelector: string;
          if (firstDot >= 0 && secondDot >= 0) {
              const firstSubs = selector.substring(firstDot + 1, secondDot)
              if (firstSubs == "plugins" && thirdDot >= 0) {
                  truncatedSelector = selector.substring(0, thirdDot);
              } else if (firstSubs == "plugins"){
                  truncatedSelector = selector;
              } else {
                  truncatedSelector = selector.substring(0, firstDot)
              }
          } else if (firstDot >= 0) {
              truncatedSelector = selector.substring(0, firstDot);
          } else {
              truncatedSelector = selector;
          }
          if (!changedPlugins.has(truncatedSelector)) {
              return truncatedSelector
          } else {
              return ''
          }
      })
    )
    .delete('');


    const emptySet = new Set();
    for (let depSelStr of dependantTemplates) {
      const depSel = objsel(depSelStr);
      const id = depSel.length > 2 ? depSel[2] : depSel[1];
      const pageKeySet = depSel.length > 2 ? pageIdToKeys.get(depSel[0], emptySet) : emptySet;
      for (let pageKey of pageKeySet) {
        const pluginType = newModel.getPluginModel(pageKey, id)?.get('pluginType');
        yield call(execOnPluginUpdate, {id, pluginType, pageKey}, null, false, false);
      }
    }
    yield put(commitStageModel());
    // logger.timeEnd(`recalculatePluginConfig: pageId ${pageId}, pluginId ${pluginId}`);
    // logger.info(`[DEBUG] END recalculatePluginConfig: pageId ${pageId}, pluginId ${pluginId}`);
  } catch (e) {
    logger.warn(`[DEBUG] EXCEPTION in recalculatePluginConfig: pageId ${pageId}, pluginId ${pluginId}`);
    // logger.timeEnd(`recalculatePluginConfig: pageId ${pageId}, pluginId ${pluginId}`);
    logger.error(e);
  }
}

function* commitAppModelSaga(action: DispatchEmptyAction) {
  yield delay(20);
  logger.info('[DEBUG] commitAppModelSaga');
  const appModel: AppModelType = yield select(selectAppModel);
  const stageModel: AppModelType = yield select(selectStageModel);
  // if (appModel !== stageModel) {
  //   if (Date.now() - stageModel.lastCommitTimestamp > 150) {

  yield put({
    type: DispatchActions.FINALIZE_COMMIT_APP_MODEL,
    payload: {
      appModel: stageModel,
    },
  });

  // }
}

/**
 * pluginId: source pluginId of event
 * @param action
 */
function* eventHandlerSaga(action: DispatchAction<TriggerPageEventPayload>): SagaIterator {
  const {pluginId, pageKey, instance, event, eventParams} = action.payload;
  //logger.info(`Event received: `, pluginId, pageKey, instance, event, eventParams);

  try {
    // const appModel: AppModelType = yield select(selectAppModel);
    // const pageModels = yield select(selectPageModels);
    const appModel: AppModelType = yield select(selectStageModel);
    const pageModels = yield select(selectStagePageModels);

    const pageId = appModel.pageKeysToId.get(pageKey);
    const isPageEvent = !pluginId;

    let sourceEntity =
      instance == null
        ? appModel.getPluginModel(pageKey, pluginId)
        : appModel.getPluginModel(pageKey, pluginId).get(instance);
    if (!pluginId) sourceEntity = appModel.getModelValue([pageKey]);

    if (!sourceEntity) return;

    const events: Immutable.Map<number, EventHandlerConfig> | Immutable.List<EventHandlerConfig> = sourceEntity
      .getIn(['events'])
      ?.filter(ev => ev.get('label') === event);
    if (!events) return;

    for (let [evIdx, eventConfigModel] of events.toKeyedSeq()) {
      if (!eventConfigModel) return;
      const eventConfigModelJS = eventConfigModel.toJS();

      const {
        type,
        isGlobalPlugin,
        prop: pluginPropertyName,
        value: incomingValue,
        method,
        hasCondition,
        condition,
      } = eventConfigModelJS;
      
      let {
        screenName,
      } = eventConfigModelJS;

      let modelParams = Object.assign({}, eventConfigModelJS.params);
      let params = _.mapValues(modelParams, pVal => {
        if (!_.isEmpty(pVal)) {
          if (typeof pVal === 'string') {
            if (pVal.startsWith(EVENT_PARAM_IDENTIFIER + '.')) {
              return _.get(eventParams, pVal.replace(EVENT_PARAM_IDENTIFIER + '.', ''));
            }
          }
        }
        return pVal;
      });
      let value = incomingValue;
      if (typeof incomingValue === 'string') {
        if (incomingValue.startsWith(EVENT_PARAM_IDENTIFIER + '.')) {
          value = _.get(eventParams, value.replace(EVENT_PARAM_IDENTIFIER + '.', ''));
        }
      }
      if (screenName.startsWith(EVENT_PARAM_IDENTIFIER + '.')) { 
        screenName = _.get(eventParams, screenName.replace(EVENT_PARAM_IDENTIFIER + '.', ''));
      }
              

      if (hasCondition) {
        if (!condition) {
          yield put({
            type: DispatchActions.APPPAGE_EVENT_HANDLER_FINISHED,
            payload: {pluginId, pageKey, instance, event, params},
          });
          continue;
        }
      }

      const prop = pluginPropertyName ? pluginPropertyName : 'value';
      const eventPluginId = eventConfigModel.get('pluginId');
      const pluginSelector =
        isGlobalPlugin && method !== 'forwardModuleEvent'
          ? [eventPluginId]
          : getPluginSelector(pageKey, eventPluginId, isPageEvent ? undefined : sourceEntity.get('namespace'));
      const pluginModel: Immutable.OrderedMap<string, any> = appModel.getModelValue(pluginSelector);
      const pluginConfig = yield select(
        selectPluginConfig,
        isGlobalPlugin && method !== 'forwardModuleEvent' ? null : pageId,
        eventPluginId,
      );

      switch (method) {
        case 'navigate':
          yield put(navigateToScreen(screenName, params));
          break;
        case 'navigateReset':
          yield put(navigateToScreenReset(screenName, params));
          break;
        case 'navigateBack':
          yield put({type: DispatchActions.APPNAV_GO_BACK});
          break;
        case 'setValue':
          const ancestorListViewSel = appModel.dependencyGraph.getAncestorListViewSelector(pluginSelector.join('.'));

          if (!ancestorListViewSel || instance !== undefined) {
            const changesets: ModelChange[] = [
              {
                selector: pluginSelector.concat([prop]),
                newValue: value,
              },
            ];
            yield put(modelUpdateAction(changesets, undefined, true));
          } else {
            const listViewModel = appModel.getModelValue(getPluginSelector(pageKey, ancestorListViewSel));
            const numInstances = listViewModel.get('instances');
            for (let inst = 0; inst < numInstances; inst++) {
              const changesets: ModelChange[] = [
                {
                  selector: pluginSelector.concat([inst, prop]),
                  newValue: value,
                },
              ];
              yield put(modelUpdateAction(changesets, inst, true));
            }
          }
          // yield put(
          //   triggerOnPluginUpdate(
          //     {
          //       id: pluginModel?.get('id'),
          //       pluginType: pluginModel?.get('pluginType'),
          //       pageKey: pluginModel?.get('pageKey'),
          //     },
          //     null,
          //     false,
          //     false,
          //   ),
          // );
          break;
        case 'triggerToast':
          const toastTypes = ['success', 'error', 'warning', 'info'];
          if (_.isEmpty(value) || _.isEmpty(params.type) || !toastTypes.includes(params.type)) break;

          const pickOffset = (placement: 'top' | 'bottom', offset?: string) => {
            const styleKey = placement === 'top' ? 'marginTop' : 'marginBottom';
            return {[styleKey]: offset ? Number(offset) : 10};
          };

          toast.show(value, {
            type: params.type,
            placement: params.placement,
            duration: params.duration ? Number(params.duration) : 1000,
            style: pickOffset(params.placement, params.offset),
          });
          break;
        case 'setPageTitle':
          const currentNavContext = getNavigationContext();
          if (currentNavContext?.getState()?.type !== 'tab') currentNavContext?.setOptions({title: value});
          break;
        case 'triggerAction':
          const payload: TriggerActionPayload = {
            pluginConfig,
            pluginModel,
            pluginSelector,
            eventModelJS: {...eventConfigModelJS, params},
          };
          yield put(triggerAction(payload));
          break;
        case 'forwardModuleEvent':
          const forwardPayload: ForwardModuleEventPayload = {
            pluginConfig,
            pageKey,
            pluginId,
            instance,
            eventModelJS: {...eventConfigModelJS, params},
          };
          yield put(forwardModuleEvent(forwardPayload));
          break;
        case 'sendPageAnalytics':
        case 'sendTrackAnalytics':
          yield put(sendAnalyticsEvent(type === 'page' ? 'page' : 'track', value, params));
          break;
        case 'executeQuery':
        case 'getNextPage':
        default:
          yield put(
            triggerPageQuery({
              selector: pluginSelector,
              options:
                method === 'getNextPage'
                  ? {
                      getNextPage: true,
                      paginationMeta: pluginModel.get('paginationMeta'),
                      inputOptionsVariables: params,
                    }
                  : {inputOptionsVariables: params},
            }),
          );
          break;
      }
    }
    yield put({
      type: DispatchActions.APPPAGE_EVENT_HANDLER_FINISHED,
      payload: {pluginId, pageKey, instance, event},
    });
  } catch (e) {
    logger.error(e);
    yield put({
      type: DispatchActions.APPPAGE_EVENT_HANDLER_ERROR,
      payload: {
        e,
        pluginId,
        pageKey,
        instance,
        event,
      },
    });
  }
}

/**
 * remove the event dependency by from and to selector
 * and then re-attach the dep for remaining events again to handle
 * edge case when dep is already if there are more then 1 event of simmiliar kind and one of them gets deleted it delete the dep for both
 * @param pageId
 * @param pluginId
 * @param deletedSelector ie deletedSelector =  ['events', 0]
 * @returns
 */
export function* eventDeleteDependencyHandler(pageId: string, pluginId: string, deletedSelector: string[] | undefined) {
  let appModel: AppModelType = yield select(selectAppModel);
  if (!appModel.dependencyGraph) return;
  if (!(deletedSelector && deletedSelector.length > 1)) return;

  const pluginConfig = yield select(selectPluginConfig, pageId, pluginId);
  const deletedEventConfig = pluginConfig.config.getIn(deletedSelector)
    ? pluginConfig.config.getIn(deletedSelector).toJS()
    : {};
  if (!deletedEventConfig) return;

  const pageKeys: string[] = appModel.pageKeysToId
    .mapEntries(([key, id]) => {
      return id == pageId ? [key, id] : undefined;
    })
    .toArray()
    .map(([k, _]) => k);

  let newModel = appModel;
  if (pluginConfig) {
    for (const pageKey of pageKeys) {
      // remove the event dependency by from and to selector
      newModel.dependencyGraph.removeEventDependency(pageKey, pluginId, deletedEventConfig);
      // 1. Deleting all events from DependencyGraph
      newModel.dependencyGraph.removeEvents(pageKey, pluginId);
    }
  }

  // 2. deletingMode values from jsModel
  if (pageId) {
    newModel = pageKeys.reduce((st: AppModelType, pageKey: any) => {
      return st.deleteModelValue([pageKey, 'plugins', pluginId].concat(['events']));
    }, newModel);
  } else {
    newModel = newModel.deleteModelValue([pluginId].concat(['events']));
  }
  yield put({
    type: DispatchActions.UPDATE_STAGE_MODEL,
    payload: {
      appModel: newModel,
      desc: 'eventDependencyHandler',
      meta: {pageId, pluginId, deletedSelector},
    },
  });

  // Iterating through the pageKeys to events using updatePlugin in depGraph and pluginConfig
  // recalculatePluginConfig events to update JSmodel as well
  for (const pageKey of pageKeys) {
    const pluginConfigModel = createPluginModelFromConfig(pageKey, pluginConfig);
    appModel.dependencyGraph.updatePlugin(pageKey, pluginConfig, pluginConfig.config, pluginConfig.get('namespace'));
    yield call(recalculatePluginConfig, pageId, pluginId, pluginConfig, {
      events: pluginConfigModel.get('events').toJS(),
    });
  }
}

export default function* appModelSagas(): SagaIterator {
  yield all([
    takeLatest(DispatchActions.INIT_APP_MODEL, initAppModel),
    takeEvery(DispatchActions.INIT_UPDATE_PAGE_MODEL, initUpdatePageModelSaga),
    takeEvery(DispatchActions.DESTROY_PAGE_MODEL, destroyPageModelSaga),
    takeEvery(DispatchActions.PLUGIN_MODEL_UPDATE, updatePluginModelSaga),
    // takeEvery(DispatchActions.PLUGIN_TRIGGER_ON_UPDATE, handleTriggerOnPluginUpdate),
    takeEvery(DispatchActions.ADD_PLUGIN_DONE, addPluginDone),
    takeEvery(DispatchActions.PLUGIN_MOVE_DONE, movePluginDone),
    takeEvery(DispatchActions.PLUGIN_RENAME, renamePlugin),
    takeEvery(DispatchActions.DELETE_PLUGIN, handleDeletePlugin),
    takeEvery(DispatchActions.UPDATE_PLUGIN_PROPERTY_NAMESPACE, updatePluginPropertyNamespace),
    // takeEvery(DispatchActions.APPPAGE_EVENT_HANDLER, eventHandlerSaga),
    takeEvery(DispatchActions.APPTILE_FOCUS_PAGE, focusPageHandlerSaga),
    takeLatest(DispatchActions.COMMIT_APP_MODEL, commitAppModelSaga),
  ]);
}

export const PageModelChannel = channel(buffers.expanding(3));

// export const PageInitEmitter = (() => {
//   var onInitCb = null;
//   return {
//     initPage: initPageAction => {
//       if (onInitCb) onInitCb(initPageAction);
//     },
//     onInit: cb => {
//       onInitCb = cb;
//     },
//   };
// })();

// function createInitPageChannel(pageEmitter) {
//   return eventChannel(emit => {
//     pageEmitter.onInit(initPageAction => {
//       emit(initPageAction);
//     });
//     return () => {};
//   });
//   // while (true) {
//   //   const payload = yield take(chan);
//   //   yield call(initPageModelSaga, payload);
//   // }
// }

// export function* appModelPageSagas(): SagaIterator {
//   const initPageChannel = yield call(createInitPageChannel, PageInitEmitter);
//   while (true) {
//     try {
//       // An error from socketChannel will cause the saga jump to the catch block
//       const payload = yield take(initPageChannel);
//       yield fork(initPageModelSaga, payload);
//     } catch (err) {
//       logger.error(err);
//       // socketChannel is still open in catch block
//       // if we want end the socketChannel, we need close it explicitly
//       // socketChannel.close()
//     }
//   }
//   // const chan = yield channel();
//   // yield fork(handleRequest, chan);
//   // while (true) {
//   //   const action = yield take(DispatchActions.INIT_PAGE_MODEL);
//   //   yield put(chan, action);
//   // }
// }

// export function* appModelPageSagas(): SagaIterator {
//   while (true) {
//     try {
//       // An error from socketChannel will cause the saga jump to the catch block
//       const payload = yield take(PageModelChannel);
//       yield spawn(initPageModelSaga, payload);
//     } catch (err) {
//       logger.error(err);
//     }
//   }
// }

export function* appModelPageSagas() {
  // create 3 worker 'threads'
  for (var i = 0; i < 3; i++) {
    yield fork(handleInitPageRequest, PageModelChannel);
  }

  while (true) {
    const action = yield take('INIT_PAGE_MODEL');
    yield put(PageModelChannel, action);
  }
}

async function waitForEventLoop(): Promise<void> {
  return new Promise<void>((resolve, reject) => {
    setTimeout(() => resolve(), 100);
  });
}

function* handleInitPageRequest(chan) {
  while (true) {
    const action = yield take(PageModelChannel);
    // logger.info('Recieved INIT_PAGE_MODEL on channel', action);
    try {
      yield call(waitForEventLoop);
      yield* initPageModelSaga(action);
    } catch (e) {
      logger.error(e);
    }
  }
}

export const PluginUpdateChannel = channel(buffers.expanding(20));
function* handleTriggerPluginUpdateRequest(chan) {
  while (true) {
    const action = yield take(PluginUpdateChannel);
    // logger.info('Recieved PLUGIN_TRIGGER_ON_UPDATE on channel', action?.payload?.plugin?.id);
    try {
      yield* handleTriggerOnPluginUpdate(action);
    } catch (e) {
      logger.error(e);
    }
  }
}
export function* appModelTriggerSagas(): SagaIterator {
  for (var i = 0; i < 20; i++) {
    yield fork(handleTriggerPluginUpdateRequest, PluginUpdateChannel);
  }

  while (true) {
    const action = yield take(DispatchActions.PLUGIN_TRIGGER_ON_UPDATE);
    yield put(PluginUpdateChannel, action);
  }
  // yield all([takeEvery(DispatchActions.PLUGIN_TRIGGER_ON_UPDATE, handleTriggerOnPluginUpdate)]);
}

export function* appEventsHandlerSagas(): SagaIterator {
  yield all([takeEvery(DispatchActions.APPPAGE_EVENT_HANDLER, eventHandlerSaga)]);
}
