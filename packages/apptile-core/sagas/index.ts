import {SagaIterator} from '@redux-saga/types';
import {spawn, all, call} from 'redux-saga/effects';
import apptileSaga from './ApptileSaga';
import appConfigSagas from './AppConfigSaga';
import appModelSagas, {appEventsHandlerSagas, appModelPageSagas, appModelTriggerSagas} from './AppModelSaga';
import eventHandlersSaga from './EventHandlersSaga';
import apptileThemeSagas from './ApptileThemeSaga';

const sagas = [
  apptileSaga,
  appConfigSagas,
  appModelPageSagas,
  appModelTriggerSagas,
  appEventsHandlerSagas,
  appModelSagas,
  eventHandlersSaga,
  apptileThemeSagas,
];

export const numRootSagasStarted = {
  current: 0
};

export function* rootSaga(childSagas = sagas): SagaIterator {
  numRootSagasStarted.current = 0;
  yield all(
    childSagas.map(saga => {
      const result = spawn(function* () {
        while (true) {
          try {
            yield call(saga);
            break;
          } catch (e) {
            logger.error(e);
          }
        }
      });
      numRootSagasStarted.current += 1;
      // logger.info("Spawned saga: ", saga, numRootSagasStarted.current);
      return result;
    }),
  );
}
