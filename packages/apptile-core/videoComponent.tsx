import NativeVideo from 'react-native-video';
import {memo} from "react";

// The memoization below is knowingly not respecting all props 
// because re-renders of video component causes re-buffering 
// Buffering of videos served through network uri seems to cause
// ram overuse crashes on android
export const Video = memo(NativeVideo, (prevProps, nextProps) => {
    const videoSourceChanged = (prevProps.source?.uri != nextProps.source?.uri);
    const pausedStateChanged = prevProps.paused != nextProps.paused;
    const muteStateChanged = prevProps.muted != nextProps.muted;
    const arePropsEqual = !(videoSourceChanged || pausedStateChanged || muteStateChanged);
    return arePropsEqual;
})
// export const Video = NativeVideo;
