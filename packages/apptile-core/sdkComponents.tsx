import { Provider as ReduxProvider } from 'react-redux';
import {ToastProvider} from 'apptile-core';
import {PortalProvider} from '@gorhom/portal';
import React, {useState, useMemo, useEffect} from 'react';
import { useSelector, shallowEqual } from 'react-redux';
import {View, Text, SafeAreaView, Alert, Linking} from 'react-native';
import _ from 'lodash';
import { store } from './store';
import { FontsProvider } from './fonts/fontsProvider';
import {
  ApptileAnimationsContextProvider, 
} from './common/Animations/apptileAnimationRegistry';
import ThemeContainer from './views/containers/ThemeContainer';
import { createNavigatorsFromConfig } from './views/navigation';
import { navConfigSelector, pageConfigsSelector } from './selectors/AppConfigSelector';
import { apptileNavigationSelector } from './selectors/AppModelSelector';
import { navigationContext } from './sagas/EventHandlersSaga';
import { EmbeddedAppPageContainer } from './views/screen/AppScreenContainer';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import getConfigValue from './lib/RNGetValues';
import {getAppStartAction} from './actions/AppLaunchAction';
import {numRootSagasStarted,} from './sagas';
import {addCustomEventListener} from './common/utils/CustomEventSystem';
import {LINKING_SETTINGS_KEY, LINKING_SETTINGS_LINKS_KEY} from './common/datatypes/LinkingTypes';
import {getLinkingConfig} from './common/navigation/NavigationHelpers';
import {getLocalStorageItem} from './lib/fileStorage';
import { LocalStorage } from './sdk';
import type { RootState } from './store/RootReducer';

export const JSBINDING_REPLACE_REGEX = /{{([\s\S]*?)}}/g;

global.GetRegisteredNativePage = () => null;

export function ApptileWrapper({children, onNavigationEvent, noNavigatePaths}) {
   useEffect(() => {
     for (let screenName of noNavigatePaths) {
      navigationContext.blockPath(screenName);
     }

     navigationContext.addListener('navigate', onNavigationEvent)
     return () => {
      navigationContext.removeListener('navigate', onNavigationEvent);
     };
   }, [onNavigationEvent]);

   return (
    <SafeAreaProvider>
      <ReduxProvider store={store}>
        <FontsProvider>
            <ApptileAnimationsContextProvider>
              <ThemeContainer>
                <PortalProvider>
                  {children}
                  <ToastProvider />
                </PortalProvider>
              </ThemeContainer>
            </ApptileAnimationsContextProvider>
        </FontsProvider>
      </ReduxProvider>
    </SafeAreaProvider>
  );
 
}

export function ApptileAppRoot() {
  const navConfig = useSelector(navConfigSelector);
  const pages = useSelector(pageConfigsSelector);
  const rootNavigator = navConfig?.get('rootNavigator');
  const apptileNavigation = useSelector(apptileNavigationSelector);
  const childNavigators = useMemo(() => {
    if (!!rootNavigator) {
      return createNavigatorsFromConfig(rootNavigator, apptileNavigation, {}, pages);
    } else {
      return (
        <SafeAreaView>
            <Text>Root navigators not found</Text>
        </SafeAreaView>
      );
    }
  }, [rootNavigator, apptileNavigation, pages]);

  return childNavigators;
}

export function getScreenFromNocode(selector: string[], screenProps) {
  return function({navigation, route}) {
    let navRootConfig = useSelector(navConfigSelector);
    if (navRootConfig) {
      navRootConfig = navRootConfig.get('rootNavigator');
      const apptileNavigationModel = useSelector(apptileNavigationSelector);
      const screenConfigSelector = [];
      for (let index = 0; index < selector.length; ++index) {
        screenConfigSelector.push('screens');
        screenConfigSelector.push(selector[index]);
      }

      const screenConfig = navRootConfig.getIn(screenConfigSelector);
      const screenModel = _.get(apptileNavigationModel, screenConfigSelector);
      if (screenConfig && screenModel) {
        return (
          <EmbeddedAppPageContainer
            key={`${screenConfig.name}_${screenModel?.screen}`}
            screen={screenModel}
            navigation={navigation}
            route={route}
            {...screenProps}
          >
          </EmbeddedAppPageContainer>
        );
      } else {
        return (
          <View>
            <Text>Could not create screen from config - {JSON.stringify(screenConfigSelector)}</Text>
            <Text>screenConfig : {screenConfig}</Text>
            <Text>screenModel : {screenModel}</Text>
          </View>
        );
      }
    } else {
      console.error("Failed to get rootNavigator from nocode layer. Maybe you didn't initialize the nocode layer.");
      return (
        <View><Text>"Failed to get rootNavigator from nocode layer. Maybe you didn't initialize the nocode layer."</Text></View>
      )
    }
  }
}

export function useStartApptile(initAnalytics) {
  function reactNavigationDeeplinkSubscriber(listener) {
    logger.info('[SDK] running subscribe for navigationcontainer')
    // Don't know why this delay was added but its causing a bad experience now
    // let timeoutId: ReturnType<typeof setTimeout>;
    // const delayedListener = (url: string) => {
    //   timeoutId = setTimeout(() => {
    //     listener(url);
    //     logger.info('redirected to', url);
    //   }, 2000);
    // };

    const customListener = addCustomEventListener('deeplink_request', (url: string) => {
      logger.info('[SDK] GOT DEEP-LINK redirect from eventbus: ', url);
      // delayedListener(url)
      listener(url);
    });

    const linkingListener = Linking.addEventListener('url', ({url}) => {
      logger.info('RECIEVED DEEP-LINK URL: ', url);
      return listener(url)
    });
    // Remove this as it looks like it is for firbase messaging
    // const unsubscribeNotificationListener = onNotificationOpenedApp(remoteMessage => {
    //   const url = getUrlFromRemoteMessage(remoteMessage);
    //   if (url) listener(url);
    // });
    return () => {
      logger.info('[SDK] removing deeplink listener')
      // if (timeoutId) clearTimeout(timeoutId);
      customListener.remove();
      linkingListener.remove();
      // unsubscribeNotificationListener();
    };
  }

  const [localState, setLocalState] = useState({
    isDownloading: true,
    appId: null,
    hasUpdate: "notavailable", // "yes", "no"
    updateDownloaded: "notavailable", // "yes", "no"
    theme: {
      primary: "#000000", 
      background: "#ffffff", 
      card: "#ffffff", 
      text: "#000000", 
      border: "#ffffff",
      notification: "#000000",
    },
    linking: {
      prefixes: [],
      config: {
        initialRouteName: "NocodeRoot",
      },
      // Remove this as it looks like its for firebase messaging
      // async getInitialURL() {
      //   const url = await Linking.getInitialURL();
      //   if (url) return url;
      //   const remoteMessage = await getInitialNotification();
      //   if (remoteMessage) return getUrlFromRemoteMessage(remoteMessage);
      // },
      subscribe: reactNavigationDeeplinkSubscriber
    }
  });
  useEffect(() => {
    let lastTheme = {...localState.theme}
    let updateCount = 0;
    let timeoutId = null;
    let unsubscribe = null;
    let hasStartedOnce = false;
    
    logger.info("starting app");
    // NativeDevSettings.setIsDebuggingRemotely(false);
    
    function tryToDispathStart(action) {
      if (timeoutId !== null) {
        clearTimeout(timeoutId);
      }

      if (numRootSagasStarted.current >= 8 && !hasStartedOnce) {
        hasStartedOnce = true;
        logger.info("dispatching appstart");
        // DEFENSIVE BULLSHIT
        setTimeout(() => {
          store.dispatch(action)
          unsubscribe = store.subscribe(() => {
            const state = store.getState()
            const model = state.apptileTheme
            if (model.modelInitialized) {
              const themeValue = model.themeValue;
              const primary = _.get(themeValue, 'colors.navPrimary');       
              const background = _.get(themeValue, 'colors.navBackground');
              const card = _.get(themeValue, 'colors.navCard');
              const text = _.get(themeValue, 'colors.navText');
              const border = _.get(themeValue, 'colors.navBorder');
              const notification = _.get(themeValue, 'colors.navBadge');
      
              const settings = state.appConfig.current?.settings;
              if (settings) {
                Promise.all([
                  getConfigValue('APPTILE_URL_SCHEME'),
                  getConfigValue('APPTILE_APP_HOST'),
                  getConfigValue('APPTILE_APP_HOST_2')
                ])
                .then(([urlScheme, appHost, appHost2]) => {
                  const linkingPrefixes = [];  
                  if (urlScheme) {
                    linkingPrefixes.push(urlScheme);
                  }
                  if (appHost) {
                    linkingPrefixes.push(appHost);
                  }
                  if (appHost2) {
                    linkingPrefixes.push(appHost2);
                  }
      
                  const apptileNavigation = state.appModel.jsModel?.ApptileNavigation;
      
                  const linkingSettings = settings.get(LINKING_SETTINGS_KEY)
                  const linkingLinks = linkingSettings.getSettingValue(LINKING_SETTINGS_LINKS_KEY);
                  const linkingConfig = getLinkingConfig(apptileNavigation, linkingPrefixes, linkingLinks)
      
                  setLocalState(prev => {
                    linkingConfig.config = linkingConfig.config || {};
                    linkingConfig.config.initialRouteName = "NocodeRoot"
                    let hasLinkingChanged = prev.linking.prefixes.length !== linkingConfig.prefixes.length;
                    if (hasLinkingChanged && linkingConfig.prefixes.length > 0) {
                      // linkingConfig.subscribe = reactNavigationDeeplinkSubscriber;
                      const result = {
                        ...prev,
                        linking: {
                          prefixes: linkingConfig.prefixes,
                          config: {
                            screens: {
                              NocodeRoot: {
                                screens: linkingConfig.config.screens,
                              },
                              NativeUtils: "nativeutils",
                              AdminPage: "adminpage"
                            }
                          },
                          subscribe: reactNavigationDeeplinkSubscriber
                        }
                      }
                      logger.warn("[SDK] Next linking " + JSON.stringify(result.linking, null, 2));
                      return result;
                    } else {
                      return prev;
                    }
                  })
                })
                .catch(err => {
                  logger.info("[SDK] failed to get linking prefixes from info.plist", err);
                });
              } else {
                logger.info("[SDK] Linking settings not found!");
              }
      
              let hasChanges = false
              hasChanges = hasChanges || (lastTheme.primary !== primary);
              hasChanges = hasChanges || (lastTheme.background !== background);
              hasChanges = hasChanges || (lastTheme.card !== card);
              hasChanges = hasChanges || (lastTheme.text !== text);
              hasChanges = hasChanges || (lastTheme.border !== border);
              hasChanges = hasChanges || (lastTheme.notification !== notification);
      
              if (hasChanges) {
                lastTheme = {
                  primary,
                  background,
                  card,
                  text,
                  border,
                  notification
                }
                setLocalState(prev => {
                  return {
                    ...prev,
                    theme: lastTheme
                  };
                })
                updateCount++;
              }
      
              if (updateCount > 15) {
                unsubscribe()
              }
            } else {
              logger.info("[SDK] model is not initialized!")
            }
          });
        }, 50);
          
          
        
      } else {
        logger.info("Waiting for sagas to start");
        timeoutId = setTimeout(() => tryToDispathStart(action), 100);
      }
    }
    Promise.all([
      getConfigValue('IS_PREVIEW_APP'),
      getConfigValue('APP_ID')
    ])
      .then(([isPreviewApp, appId]) => {
        LocalStorage.setNamespace(appId);
        if (isPreviewApp === 'YES') {
          logger.info("Returning appId from localStorage.json");
          // read localStorage.json and get appId
          return getLocalStorageItem('appId').then(appId => [appId, true]);
        } else {
          return [appId, false]
        }
      })
      .then(([appId, isPreviewApp]: [string|undefined, boolean]) => { 
        setLocalState(prev => {
          return {
            ...prev,
            appId: appId
          }; 
        });
        logger.info("starting app with appId", appId);
        if (appId) {
          return getAppStartAction(appId, isPreviewApp)
            // .then(startAction => ({startAction, appId}))
        } else {
          logger.info('failed to get appstart action');
          throw new Error("Cannot launch app without APP_ID. Make sure its present in strings.xml or info.plist. It should get inserted via apptile.config.json");
        }
      })
      // .then(({startAction, appId}) => {
      .then(startAction => {
        if (startAction.hasError) {
          logger.info("Error ocurred while trying to get config: ", startAction);
          Alert.alert("Couldn't load app. Please restart.");
        }
        // last ditch effort to make the app load everytime
        logger.info('dispatching appstart action');
        tryToDispathStart(startAction.action);
        setLocalState(prev => {
          return {
            ...prev,
            isDownloading: false
          };
        });
        return startAction.updateCheckResult
      })
      .then(({hasUpdate, updateProgressPromise}) => {
        setLocalState(prev => {
          return {
            ...prev,
            hasUpdate: hasUpdate ? "yes" : "no",
            updateDownloaded: "no"
          };
        });
        if (hasUpdate) {
          return updateProgressPromise.then(() => true);
        } else {
          return false;
        }
      })
      .then((hasUpdate) => {
        if (hasUpdate) {
          setLocalState(prev => {
            return {
              ...prev,
              updateDownloaded: "yes"
            };
          });
        }
        initAnalytics();
      })
      .catch((err) => {
        logger.error("Failure when starting app: ", err);
        initAnalytics();
      });

    return () => {
      try {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        if (unsubscribe) {
          unsubscribe();
        }
      } catch(err) {
        console.error("Failed to unsubscribe from store");
      }
    };
  }, []);
  return localState;
}

export function networkLog(logLine: string) {
  try {
    fetch('http://192.168.11.174:3100/plugin-server/cli/createLog', {
      method: 'GET',
      headers: {
        'X-Telemetry-Log': JSON.stringify(logLine)
      }
    });
    console.log('Sent request for log', logLine);
  } catch(err) {
    console.error("Failed to send networkLog: ", logLine);
  }
}

export async function getSupabaseClient() {
  const startTime = Date.now();
  const maxWaitTime = 5000; // 5 seconds
  let attempt = 0;

  while (Date.now() - startTime < maxWaitTime) {
    const supabaseClient = store.getState().appModel.getIn(['values', 'supabase', 'supabaseClient']);
    if (supabaseClient) {
      return supabaseClient;
    }

    // Exponential backoff: wait time grows with each attempt (e.g., 50ms → 100ms → 200ms...)
    const delay = Math.min(1000, 50 * 2 ** attempt); // cap delay at 1s to avoid long waits
    await new Promise(resolve => setTimeout(resolve, delay));
    attempt++;
  }

  // Timed out without finding a truthy value
  return null;
}

global.networkLog = networkLog;

export function assetIdToImageSrcSet(assetId: string, appConfig: any) {
  let result = null;
  if (appConfig && assetId) {
    const imageRecord = appConfig.getImageId(assetId);
    if (imageRecord) {
      result = imageRecord.get('variants').toList().toJS().sort((a, b) => a.width < b.width ? -1 : 1).map(it => it.fileUrl);
    }
  }
  return result;
}

export {getAppStartAction, checkForOTA, setBundle} from './actions/AppLaunchAction';

export {setLocalStorageItem, getLocalStorageItem} from './lib/fileStorage';

export function useIAPState() {
  const {
    iapClient,
    products,
    subscriptions,
    productsLoading,
    subsLoading,
    availablePurchases,
    availablePurchaseLoading,
    isPurchaseInProgress,
    isSubscriptionInProgress,
    isPurchaseHistoryLoading,
    purchaseHistory
  } = useSelector(
    (state: RootState) => {
      const iapClient = state.appModel.values.getIn(['AppMarketplaceIap', 'iapClient']);
      const products = state.appModel.values.getIn(['AppMarketplaceIap', 'products']);
      const productsLoading = state.appModel.values.getIn(['AppMarketplaceIap', 'isProductsLoading']);
      const subscriptions = state.appModel.values.getIn(['AppMarketplaceIap', 'subscriptions']);
      const subsLoading = state.appModel.values.getIn(['AppMarketplaceIap', 'isSubscriptionsLoading']);
      const availablePurchases = state.appModel.values.getIn(['AppMarketplaceIap', 'availablePurchases']);
      const availablePurchaseLoading = state.appModel.values.getIn(['AppMarketplaceIap', 'isCurrentActivePurchasesLoading']);
      const isPurchaseInProgress = state.appModel.values.getIn(['AppMarketplaceIap', 'isPurchaseInProgress']);
      const isSubscriptionInProgress = state.appModel.values.getIn(['AppMarketplaceIap', 'isSubscriptionInProgress']);
      const isPurchaseHistoryLoading = state.appModel.values.getIn(['AppMarketplaceIap', 'isPurchaseHistoryLoading']);
      const purchaseHistory = state.appModel.values.getIn(['AppMarketplaceIap', 'purchaseHistory']);
      
      return {
        iapClient,
        products,
        subscriptions,
        productsLoading,
        subsLoading,
        availablePurchases,
        availablePurchaseLoading,
        isPurchaseInProgress,
        isSubscriptionInProgress,
        isPurchaseHistoryLoading,
        purchaseHistory
      };
    }, shallowEqual);

  return {
    iapClient,
    products,
    subscriptions,
    productsLoading,
    subsLoading,
    availablePurchases,
    availablePurchaseLoading,
    isPurchaseInProgress,
    isSubscriptionInProgress,
    isPurchaseHistoryLoading,
    purchaseHistory
  };
}