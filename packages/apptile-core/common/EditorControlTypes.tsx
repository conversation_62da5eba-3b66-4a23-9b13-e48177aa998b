import Immutable from 'immutable';
// import type {pluginEditorComponents} from '../../web/components/pluginEditorComponents';
import {ApptileAnalyticsEventType} from './ApptileAnalytics/ApptileAnalyticsTypes';
import {
  AppConfig,
  ImmutableMapType,
  NavigatorConfig,
  PageConfig,
  PluginConfigType,
  ScreenConfig,
} from './datatypes/types';
import {Selector} from './DependencyGraph/types';

export interface EditorProps<ConfigType = any> {
  pluginSelector: string[];
  navComponentSelector: string[];
  pageId: string;
  appConfig: AppConfig;
  selectedPluginConfig: PluginConfigType<ConfigType>;
  selectedNavComponent: ScreenConfig | NavigatorConfig;
  selectedPageId: string;
  selectedPageConfig: PageConfig;
  inModule: boolean;
  target: string;
  pluginConfigDeleteEvent: (pluginId: string, pageId: string, selector: string[] | {[propName: string]: any}) => void;
  pluginConfigUpdate: (pluginId: string, pageId: string, update: string | {[propName: string]: any}) => void;
  pluginConfigEventUpdate: (pluginId: string, pageId: string, update: string | {[propName: string]: any}) => void;
  debouncedPluginConfigUpdate: (pluginId: string, pageId: string, update: string | {[propName: string]: any}) => void;
  pluginLayoutUpdate: (pluginId: string, pageId: string, update: string | {[propName: string]: any}) => void;
  pluginStyleUpdate: (pluginId: string, pageId: string, update: string | {[propName: string]: any}) => void;
  pluginUpdateId: (pluginId: string, pageId: string, newPluginId: string) => void;
  pluginDelete: (pluginId: string, pageId: string) => void;
  navConfigUpdate: (navSelector: string[], propertyName: string, valaue?: any) => void;
  debouncedNavConfigUpdate: (navSelector: string[], propertyName: string, valaue?: any) => void;
  navUpdateName: (navSelector: string[], newNavName: string) => void;
  navComponentDelete: (navSelector: string[]) => void;
  updatePageId: (pageId: string, newPageId: string) => void;
  updatePageConfig: (pageId: string, update: any) => void;
  deletePageConfig: (pageId: string) => void;
  debouncedUpdatePageConfig: (pageId: string, update: any) => void;
  createModuleFromWidget: (widgetId: string, pageId: string) => void;
  reorderEventUpward: (pluginId: string, pageId: string, eventIndex: number) => void;
  reorderEventDownward: (pluginId: string, pageId: string, eventIndex: number) => void;
}

export type PropertyEditorTypes = string; // keyof typeof pluginEditorComponents;

// export const PropertyEditorControlsList: Partial<Record<PropertyEditorTypes | string, string>> = {
export const PropertyEditorControlsList: Partial<Record<string, string>> = {
  codeInput: 'Autocomplete Text Input',
  formatInput: 'Formatted Text Input',
  quadInput: '4+1 Quad Input',
  borderRadiusEditor: 'Border Radius Control',
  paddingEditor: 'Padding Values',
  marginEditor: 'Margin Values',
  borderEditor: 'Border Values',
  checkbox: 'Toggle Checkbox',
  rangeSliderInput: 'Range Slider',
  typographyInput: 'Typography Control',
  iconChooserInput: 'Icon Chooser',
  radioGroup: 'Button Group',
  colorInput: 'Color Input',
  alignmentEditor: 'Alignment Editor',
  aspectRatio: 'Aspect Ratio Control',
  // stylesEditor,
  // layoutEditor,
  // eventsEditor,
  // queryBuilder,
  // pageParamsEditor,
  // moduleEditor,
  assetEditor: 'Image Asset Input',
  cloudinaryEditor: 'Cloudinary Asset Editor',
  listEditor: 'Display Image List Editor',
  dropDown: 'Dropdown',
  // pageIdSelector,
  // loaderIdSelector,
  // screenSelector,
  // regexInput,
  // headerIdSelector,
  // analyticsEditor,
  jsonInput: 'JSON Input',
  customList: 'Custom List',
  numericInput: 'Numeric size input(dp/%)',
  shopifyCollectionHandleControl: '[Shopify] Collection handle picker',
  shopifyProductHandleControl: '[Shopify] Product handle picker',
  shopifyCollectionIdControl: '[Shopify] Collection Id picker',
  shopifyBlogHandleControl: '[Shopify] Blog handle picker',
  shopifyBlogControl: '[Shopify] Blog picker',
  dateAndTimeInput: 'Date And Time Input',
};

export type PropertyEditorCustomProps = {
  codeInput: {
    placeholder?: string | number;
    docs?: string;
    // validator?: Validator;
    validationPattern?: string;
    validationError?: string;
    mode?: string;
    singleLine?: boolean;
    noOfLines?: number;
  };
  formatInput: {
    placeholder?: string | number;
    docs?: string;
    validationPattern?: string;
    validationError?: string;
    prefix?: string;
    suffix?: string;
    mode?: string;
    singleLine?: boolean;
  };
  aspectRatio: {};
  iconChooserInput: {
    placeholder?: string | number;
    iconTypeProp?: string;
  };
  regexInput: {
    placeholder?: string;
  };
  checkbox: {
    checkedValue?: boolean | string | number;
    uncheckedValue?: boolean | string | number;
  };
  radioGroup: {
    options?: string[] | {icon: string; text: string; value: string}[];
    disableBinding?: boolean;
    allowDeselect?: boolean;
  };
  typographyInput: {
    placeholder?: string;
    disableExport?: boolean;
  };
  quadInput: {
    placeholder?: string;
    options: [string, string, string, string];
    layout?: 'plus' | 'square';
  };
  borderRadiusEditor: {
    options: [string, string, string, string];
  };
  trblValuesEditor: {
    placeholder?: string;
    options: [string, string, string, string];
  };
  textDecorationControl: {};
  textTransformControl: {};
  alignmentEditor: {
    placeholder?: string;
  };
  valueMinMaxEditor: {
    placeholder?: string;
    options: [string, string];
  };
  colorInput: {
    placeholder?: string;
  };
  layoutEditor: {
    isContainer?: boolean;
  };
  stylesEditor: {
    editors: Editors<any>;
  };
  eventsEditor: {
    triggers: string[];
  };
  queryEditor: {};
  mfAuthQueryEditor: {};
  mfAuthenticationQueryEditor: {};
  moduleEditor: {};
  assetEditor: {
    urlProperty: string;
    assetProperty: string;
    sourceTypeProperty: string;
    disableBinding: boolean;
  };
  cloudinaryEditor: {
    urlProperty: string;
    disableBinding: boolean;
  };
  listEditor: {
    disableAdd?: boolean;
    minLength?: string;
    maxLength?: string;
    // TODO(gaurav): Not sure where to put this, but this type is in the web folder
    // shopifyType?: ShopifyEntityTypes;
    shopifyType?: string;
  };
  dropDown: {
    options: string[] | Record<string, any>[];
    valueKey?: string;
    nameKey?: string;
  };
  pageIdSelector: {
    disableBinding?: boolean;
  };
  loaderIdSelector: {
    disableBinding?: boolean;
  };
  screenSelector: {
    disableBinding?: boolean;
  };
  headerIdSelector: {
    disableBinding?: boolean;
  };
  analyticsEditor: {
    defaultType?: ApptileAnalyticsEventType;
  };
  animationsEditor: {};
  rangeSliderInput: {
    placeholder?: string;
    maxRange?: string;
    minRange?: string;
    steps?: string;
  };
  numericInput: {
    placeholder?: string;
    unit?: 'dp' | '%';
    noUnit?: boolean;
  };
  jsonInput: {
    placeholder?: string;
  };
  editorSectionHeader: {};
  customList: {
    placeholder?: string;
    schema?: Record<string, string>[];
  };
  shopifyCollectionHandleControl: {};
  shopifyProductHandleControl: {};
  shopifyCollectionIdControl: {};
  shopifyBlogHandleControl: {};
  shopifyBlogControl: {};
  dateAndTimeInput: {
    disableBinding?: boolean;
  };
  richTextControl: {};
  customData: {};
};

export interface PropertyEditorBaseProps {
  label: string;
}

export interface PropertyEditorConfig<ConfigType, Type extends PropertyEditorTypes = PropertyEditorTypes> {
  type: Type;
  name: string;
  defaultValue?: any;
  props: PropertyEditorBaseProps &
    (Type extends keyof PropertyEditorCustomProps ? PropertyEditorCustomProps[Type] : {});
  hidden?: (pluginConfig: ImmutableMapType<ConfigType>) => boolean;
}

export type PluginEditor<ConfigType> = PropertyEditorConfig<ConfigType>;

export type Editors<ConfigType> = PluginEditor<ConfigType>[];

export type PluginEditorsConfig<ConfigType> = {
  basic?: Editors<ConfigType>;
  advanced?: Editors<ConfigType>;
  style?: Editors<ConfigType>;
  layout?: Editors<ConfigType>;
  event?: Editors<ConfigType>;
  module?: Editors<ConfigType>;
  query?: Editors<ConfigType>;
  analytics?: Editors<ConfigType>;
  animations?: Editors<ConfigType>;
};

// Plugin Editor Control Props.

interface EntityEditorCallbackProps {
  onChange: (val: any, debounced?: boolean, remove?: boolean) => void;
  onCustomPropChange: (key: string, val: any, debounced?: boolean, remove?: boolean) => void;
  onUpdateRawValue: (val: any) => void;
}

interface EntityEditorBaseProps {
  pluginId: string;
  pageId: string;
  config: Immutable.Map<string, any> | Immutable.Record<any>;
  entityConfig: PageConfig | PluginConfigType<any> | ScreenConfig | NavigatorConfig;
}

export interface ThemeEditorBaseProps {
  themeIndication?: boolean;
  getThemeObj?: (sel: string[]) => {path: string; value: any} | {path?: string; value?: any};
}

export interface PropertyEditorProps<CustomProps = {}>
  extends EntityEditorCallbackProps,
    EntityEditorBaseProps,
    ThemeEditorBaseProps {
  configProps: PropertyEditorBaseProps & CustomProps;
  name: string;
  value: any;
  id: any;
  defaultValue?: any;
  configPathSelector: Selector;
}

export interface EntityEditorProps extends EntityEditorBaseProps {
  editor: PropertyEditorConfig<any>;
  configPathSelector: Selector;
}

export interface ThemeEntityEditorProps {
  editor: Omit<PropertyEditorConfig<any>, 'hidden'>;
  configPathSelector: string[];
}
