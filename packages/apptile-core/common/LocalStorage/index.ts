import AsyncStorage from '@react-native-async-storage/async-storage';

class LocalStorage {
  namespace: Promise<string>;
  namespaceResolver: null | ((namespace: string) => void) = null;
  constructor() {
    this.namespace = new Promise<string>((resolve) => {
      this.namespaceResolver = resolve;
    });
  }

  setNamespace = (namespaceVal: string) => {
    if (this.namespaceResolver) {
      this.namespaceResolver(namespaceVal);
      this.namespaceResolver = null;
    } else {
      this.namespace = Promise.resolve(namespaceVal);
    }
  };
  getKey = async (key: string) => {
    const namespace = await this.namespace;
    return `${namespace}_${key}`
  };
  setValue = async (key: string, value: string | Record<string, any>) => {
    // logger.time(`setValue_${key}`);
    if (!key) return;
    let finalValue = '';
    try {
      if (value) {
        finalValue = typeof value === 'string' ? value : JSON.stringify(value);
      }
      const namespacedKey = await this.getKey(key);
      await AsyncStorage.setItem(namespacedKey, finalValue);
    } catch (e) {
      logger.error(`setValue_${key}`, e);
    } finally {
      // logger.timeEnd(`setValue_${key}`, value);
    }
  };
  hasKey = async (key: string) => {
    const namespacedKey = await this.getKey(key);
    if (!namespacedKey) {
      return false;
    } else {
      const value = await AsyncStorage.getItem(namespacedKey);
      if (value === null) { // According to async-storage docs, null or string can be used to determine presence of keys
        return false;
      } else {
        return true;
      }
    }
  };
  getValue = async (key: string): Promise<string | number | Record<string, any> | any[] | null> => {
    // logger.time(`getValue_${key}`);
    if (!key) return null;
    const namespacedKey = await this.getKey(key);
    let value = await AsyncStorage.getItem(namespacedKey);
    try {
      value = value && JSON.parse(value);
    } catch (e) {
      // not a json, IGNORE
    } finally {
      // logger.timeEnd(`getValue_${key}`, value);
    }
    return value;
  };

  removeKey = async (key: string) => {
    try{
      if (!key) return 
      const namespacedKey = await this.getKey(key);
      await AsyncStorage.removeItem(namespacedKey)
    }catch (e){
      logger.error(`removeKey_${key}`, e);
    }
  }
  removeItem = async (key: string) => {
    // logger.time(`setValue_${key}`);
    if (!key) {
      logger.error(`removeItem_${key}`);
      return
    };
    try {
      const namespacedKey = await this.getKey(key);
      await AsyncStorage.removeItem(namespacedKey);
    } catch (e) {
      logger.error(`removeItem_${key}`, e);
    } finally {
      // logger.timeEnd(`setValue_${key}`, value);
    }
  };

  // Code used to divide sting in chunks and save in local-storage to avoid 6mb limit on android
  // BUT should be avoided and use Filesystem instead
  // async setValueInChunks(key: string, value: string): Promise<void> {
  //   for (let i = 0; i < 4; i++) {
  //     await this.setValue(`${key}_chunk${i}`, value.slice((i * value.length) / 4, ((i + 1) * value.length) / 4));
  //   }
  // }
  // async getValueFromChunks(key: string): Promise<string> {
  //   let value = '';
  //   for (let i = 0; i < 4; i++) {
  //     const str = await this.getValue(`${key}_chunk${i}`);
  //     if (str) value += str;
  //   }
  //   return value;
  // }
}

const localStorage = new LocalStorage();
export default localStorage;
