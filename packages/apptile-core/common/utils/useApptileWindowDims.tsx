import {Platform, useWindowDimensions} from 'react-native'

export function useApptileWindowDims() {
  let {height, width} = useWindowDimensions();
  // I am knowingly not bothering to update the values when the app-canvas resizes
  // Using a useLayoutEffect will make things too expensive and its just not worth it
  // since the scaling on the web is just a cosmetic effect
  if (Platform.OS === 'web') {
    const appCanvas = document.querySelector('#app-canvas');
    let canvasInternalNode = appCanvas?.querySelector('div:nth-child(2)');
    if (!canvasInternalNode) {
      canvasInternalNode = appCanvas?.querySelector('div');
    }
    const rect = canvasInternalNode?.getBoundingClientRect();
    if (appCanvas) {
      const transforms = appCanvas.computedStyleMap().get('transform');
      const scale = transforms[0].x.value;
      width = rect.width / scale;
      height = rect.height / scale;
    } else {
      width = 393;
      height = 784;
    }
  } 
  return {width, height};
}
