import {createNavigationContainerRef} from '@react-navigation/native';

export const apptileNavigationRef = createNavigationContainerRef();
// window.apptileNavigationRef = apptileNavigationRef;

export function navigate(route, params) {
  logger.info("Navigating to: ", route, params);
  if (apptileNavigationRef.isReady()) {
    apptileNavigationRef.navigate(route, params);
  } else {
    logger.info('Skipping Navigation until Navigator is ready !', route, params);
  }
}

export function navDispatch(action) {
  if (apptileNavigationRef.isReady()) {
    apptileNavigationRef.dispatch(action);
  } else {
    logger.info('Skipping Navigation until Navigator is ready !', route, params);
  }
}

export function goBack() {
  if (apptileNavigationRef.isReady()) {
    if (apptileNavigationRef.canGoBack()) {
      apptileNavigationRef.goBack();
    }
  } else {
    logger.info('Skipping Navigation until Navigator is ready !', route, params);
  }
}
