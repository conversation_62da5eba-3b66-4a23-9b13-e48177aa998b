import React from 'react';
import {Platform, StyleSheet, Text, View} from 'react-native';
import {CurrentScreenContext, CustomModal, MaterialCommunityIcons, getPlatformStyles} from 'apptile-core';
import {Portal} from '@gorhom/portal';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();


const styles = StyleSheet.create({
  closeIcon: {
    fontSize: 20,
    color: 'black',
  },
  iconWrapper: {
    position: 'absolute',
    zIndex: 9,
    top: 0,
    right: 8,
    height: 28,
    width: 28,
    borderRadius: 28,
    backgroundColor: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarTextWrapper: {paddingHorizontal: 10, backgroundColor: '#FFF', paddingVertical: 20, paddingRight: 10},
});

const CalendarWidgetView = ({props}: {props: any}) => {
  const {model, modelUpdate, modelStyles, config} = props;

  const setOpen = !!model.get('setOpen');
  const BACKDROP_COLOR = 'rgba(0,0,0,0.8)';
  const layout = config.get('layout');
  const layoutStyles = getPlatformStyles(layout ? layout.getFlexProperties() : {flex: 5, flexDirection: 'row'});
  const modelPlatformStyles = getPlatformStyles(modelStyles);
  const horizontalAlign = model.get('horizontalAlign', 'left');
  const verticalAlign = model.get('verticalAlign', 'auto');
  const flexDirection = model.get('flexDirection', 'row');
  const setOpenUpdates = {selector: ['setOpen'], newValue: false};

  const closeModal = () => {
    modelUpdate([setOpenUpdates]);
  };

  return (
    <View
      style={[
        layoutStyles,
        modelPlatformStyles,
        {
          textAlign: horizontalAlign,
          textAlignVertical: verticalAlign,
          flexDirection: flexDirection,
        },
      ]}>
      {setOpen && (
        <CurrentScreenContext.Consumer>
          {(screen: any) => (
            <Portal hostName={screen?.isModal ? (model.get('pageKey') as string) : 'root'}>
              {setOpen && (
                <CustomModal
                  position="center"
                  onClose={closeModal}
                  outerComponents={
                    <View style={[styles.iconWrapper, {top: (Platform.OS === 'ios' && !screen?.isModal ? 54 : 0) + 4}]}>
                      <MaterialCommunityIcons name="close" style={[styles.closeIcon]} onPress={closeModal} />
                    </View>
                  }
                  backdropColor={BACKDROP_COLOR}>
                  <View style={styles.calendarTextWrapper}>
                    <Text style={commonStyles.baseText}>Calendar only appears in mobile!</Text>
                  </View>
                </CustomModal>
              )}
            </Portal>
          )}
        </CurrentScreenContext.Consumer>
      )}
    </View>
  );
};

export default CalendarWidgetView;
