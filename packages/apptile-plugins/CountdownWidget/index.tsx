import React, {useEffect, useRef} from 'react';
import {TextInput, Platform} from 'react-native';
import _ from 'lodash';
import humanizeDuration from './humanizeDuration';

import docs from './docs';
import {WidgetStyleEditorOptions} from 'apptile-core';
import {PluginEditorsConfig} from 'apptile-core';
import {connectWidget, WidgetProps} from 'apptile-core';
import {
  EventTriggerIdentifier,
  PluginListingSettings,
  PluginPropertySettings,
  TriggerActionIdentifier,
} from 'apptile-core';
import {getPlatformStyles, mergeWithDefaultStyles} from 'apptile-core';
import {PluginConfig} from 'apptile-core';
import {Selector} from 'apptile-core';
import {modelUpdateAction} from 'apptile-core';

const CountdownWidgetConfig = {
  reset: 'Action_reset',
  key: 'default',
  hasEnded: false,
  isActive: false,
  appendZero: false,
  value: new Date().getTime(),
  interval: 1000,
  yFormat: 'Year,Years',
  moFormat: 'Month,Months',
  wFormat: 'Week,Weeks',
  dFormat: 'Day,Days',
  hFormat: 'Hour,Hours',
  mFormat: 'Minute,Minutes',
  sFormat: 'Second,Seconds',
  msFormat: 'Millisecond,Milliseconds',
  delimiter: ', ',
  units: 'Y,Mo,W,D,H,M,S',
  onCountdownEnd: 'Event_onCountdownEnd',
};

const pluginListing: PluginListingSettings = {
  labelPrefix: 'countdown',
  type: 'widget',
  name: 'Countdown',
  description: 'Display Countdown',
  defaultHeight: 0,
  defaultWidth: 0,
  section: 'Display',
  icon: 'timer',
  layout: [],
};

export const countDownWidgetStyleConfig: WidgetStyleEditorOptions = [
  {
    type: 'colorInput',
    name: 'backgroundColor',
    props: {
      label: 'Background',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'colorInput',
    name: 'color',
    props: {
      label: 'Text Color',
      placeholder: '#HexCode',
    },
  },
  {
    type: 'typographyInput',
    name: 'typography',
    props: {
      label: 'Typography',
      placeholder: 'typography.body',
    },
  },
];

const CountdownWidget = React.forwardRef((props: WidgetProps, forwardedRef: React.LegacyRef<Text>) => {
  const {model, modelUpdate, modelStyles, triggerEvent} = props;

  const key = model.get('key') || 'default';
  const isActive = !!model.get('isActive');
  const appendZero = !!model.get('appendZero');
  const interval = model.get('interval') || 1000;
  const hasEnded = !!model.get('hasEnded');

  const _value = model.get('value');
  // Track the changes in the prop model.value
  const prev_value = useRef(_value);

  let countDownTo: number|string;
  const fiveSecondFromNow = new Date().getTime() + 5000;
  if (_value) {
    if ((typeof _value === 'string') && _value.startsWith('{{')) {
      countDownTo = fiveSecondFromNow;
    } else {
      countDownTo = new Date(_value).getTime();
    }
  } else {
    countDownTo = fiveSecondFromNow;
  }
  // Initialize targetTime to countDownTo on initial render
  const targetTime = useRef(countDownTo);
  const modelUpdateRequestSent = useRef(false);
  const labelRef = useRef<TextInput>(null);

  // if this is a re-render due to a change in model.value we 
  // update the stored ref for targetTime and update the previous 
  // value tracker for model.value
  if (_value != prev_value) {
    targetTime.current = countDownTo;
    prev_value.current = _value;
    modelUpdateRequestSent.current = false;
  }

  const modelPlatformStyles = modelStyles ? getPlatformStyles(modelStyles) : {};
  const {typography, ...restModelPlatformStyles} = modelPlatformStyles;

  let yFormat = model
    .get('yFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (yFormat.length == 0) yFormat = [''];
  let moFormat = model
    .get('moFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (moFormat.length == 0) moFormat = [''];
  let wFormat = model
    .get('wFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (wFormat.length == 0) wFormat = [''];
  let dFormat = model
    .get('dFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (dFormat.length == 0) dFormat = [''];
  let hFormat = model
    .get('hFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (hFormat.length == 0) hFormat = [''];
  let mFormat = model
    .get('mFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (mFormat.length == 0) mFormat = [''];
  let sFormat = model
    .get('sFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (sFormat.length == 0) sFormat = [''];
  let msFormat = model
    .get('msFormat')
    .split(',')
    .filter((e: string) => e.trim());
  if (msFormat.length == 0) msFormat = [''];
  const delimiter = model.get('delimiter');
  const acceptableUnits = ['Y', 'MS', 'W', 'D', 'H', 'M', 'S', 'MS'];
  const units = model
    .get('units')
    .toLowerCase()
    .split(',')
    .filter((e: string) => e.trim() && acceptableUnits.includes(e.toUpperCase()));

  const customHumanizer = humanizeDuration.humanizer({
    language: 'en',
    languages: {
      shortEn: {
        y: (e: number) => (e < 2 ? yFormat[0] : yFormat[yFormat.length - 1]),
        mo: (e: number) => (e < 2 ? moFormat[0] : moFormat[moFormat.length - 1]),
        w: (e: number) => (e < 2 ? wFormat[0] : wFormat[wFormat.length - 1]),
        d: (e: number) => (e < 2 ? dFormat[0] : dFormat[dFormat.length - 1]),
        h: (e: number) => (e < 2 ? hFormat[0] : hFormat[hFormat.length - 1]),
        m: (e: number) => (e < 2 ? mFormat[0] : mFormat[mFormat.length - 1]),
        s: (e: number) => (e < 2 ? sFormat[0] : sFormat[sFormat.length - 1]),
        ms: (e: number) => (e < 2 ? msFormat[0] : msFormat[msFormat.length - 1]),
      },
    },
  });

  useEffect(() => {
    function updateLabel() {
      const timeLeft = targetTime.current - Date.now();
      const formatted = customHumanizer(timeLeft, {
        units,
        maxDecimalPoints: 0,
        includeZero: true,
        appendZero,
        delimiter,
        language: 'shortEn',
      });

      if (Platform.OS !== 'web' && labelRef.current && labelRef.current.setNativeProps) {
        labelRef.current.setNativeProps({
          text: formatted,
        });
      } else if (labelRef.current) {
        labelRef.current.value = formatted;
      }

      if (timeLeft <= 0 && !hasEnded && !modelUpdateRequestSent) {
        modelUpdate([
          {
            selector: ['hasEnded'],
            newValue: true 
          },
        ]);
        triggerEvent('onCountdownEnd');
        modelUpdateRequestSent.current = true;
      }

      return timeLeft;
    }

    updateLabel();

    let intervalId: ReturnType<typeof setInterval>;
    if (isActive) {
      intervalId = setInterval(() => {
        if (updateLabel() <= 0) {
          clearInterval(intervalId);
        }
      }, interval);
    }

    return () => {
      clearInterval(intervalId);
    };
  }, [interval, isActive, hasEnded, targetTime.current, appendZero, delimiter, modelUpdate, modelUpdateRequestSent.current, customHumanizer, labelRef.current]);

  return (
    <TextInput 
      ref={el => {
        if (forwardedRef) {
          forwardedRef.current = el;
        }
        labelRef.current = el;
      }} 
      style={[{textAlign: 'center'}, typography, restModelPlatformStyles]}
      editable={false}
    />
  );
});

const propertySettings: PluginPropertySettings = {
  reset: {
    type: TriggerActionIdentifier,
    getValue(_model, _renderedValue, _selector) {
      return (dispatch: any, config: PluginConfig, model: WidgetProps['model'], selector: Selector, _params: any) => {
        dispatch(modelUpdateAction([{selector: selector.concat(['key']), newValue: Math.random()}], undefined, true));
      };
    },
  },
  key: {
    getValue: (_model, _renderedValue, _selector) => _renderedValue,
  },
  value: {
    getValue: (_model, _renderedValue, _selector) => {
      return _renderedValue;
    },
  },
  isActive: {
    getValue: (_model, _renderedValue, _selector) => !!_renderedValue,
  },
  interval: {
    getValue: (_model, _renderedValue, _selector) => _.toNumber(_renderedValue),
  },
  onCountdownEnd: {
    type: EventTriggerIdentifier,
  },
};

const editors: PluginEditorsConfig<typeof CountdownWidgetConfig> = {
  basic: [
    {
      type: 'checkbox',
      name: 'isActive',
      props: {
        label: 'isActive',
        placeholder: '{{false}}',
      },
    },
    {
      type: 'checkbox',
      name: 'appendZero',
      props: {
        label: 'appendZero',
        placeholder: '{{false}}',
      },
    },
    {
      type: 'dateAndTimeInput',
      name: 'value',
      props: {
        label: 'Date And Time',
        disableBinding: false,
      },
    },
    {
      type: 'codeInput',
      name: 'interval',
      props: {
        label: 'Interval (in ms)',
        placeholder: '{{1000}}',
      },
    },
    {
      type: 'codeInput',
      name: 'yFormat',
      props: {
        label: 'Years Format',
        placeholder: 'Year,Years',
      },
    },
    {
      type: 'codeInput',
      name: 'moFormat',
      props: {
        label: 'Months Format',
        placeholder: 'Month,Months',
      },
    },
    {
      type: 'codeInput',
      name: 'wFormat',
      props: {
        label: 'Weeks Format',
        placeholder: 'Week,Weeks',
      },
    },
    {
      type: 'codeInput',
      name: 'dFormat',
      props: {
        label: 'Days Format',
        placeholder: 'Day,Days',
      },
    },
    {
      type: 'codeInput',
      name: 'hFormat',
      props: {
        label: 'Hours Format',
        placeholder: 'Hour,Hours',
      },
    },
    {
      type: 'codeInput',
      name: 'mFormat',
      props: {
        label: 'Minutes Format',
        placeholder: 'Minute,Minutes',
      },
    },
    {
      type: 'codeInput',
      name: 'sFormat',
      props: {
        label: 'Seconds Format',
        placeholder: 'Second,Seconds',
      },
    },
    {
      type: 'codeInput',
      name: 'msFormat',
      props: {
        label: 'Milliseconds Format',
        placeholder: 'Millisecond,Milliseconds',
      },
    },
    {
      type: 'codeInput',
      name: 'delimiter',
      props: {
        label: 'Delimiter',
        placeholder: ',',
      },
    },
    {
      type: 'codeInput',
      name: 'units',
      props: {
        label: 'Units To Show',
        placeholder: 'Y,Mo,W,D,H,M,S,Ms',
      },
    },
  ],
  layout: [
    {
      type: 'layoutEditor',
      name: 'layout',
      props: {
        label: 'Layout',
        isContainer: false,
      },
    },
  ],
};

export default connectWidget('CountdownWidget', CountdownWidget, CountdownWidgetConfig, () => {}, editors, {
  propertySettings,
  widgetStyleConfig: mergeWithDefaultStyles(countDownWidgetStyleConfig),
  pluginListing,
  docs,
});
