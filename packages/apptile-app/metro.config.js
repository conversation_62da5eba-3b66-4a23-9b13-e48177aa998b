/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */

const path = require('path');
const metroResolver = require('metro-resolver');
const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);

// For pip don't remove
const enableLivelyPIP = false;

const watchFolders = [
  path.resolve(__dirname, 'node_modules'), 
  path.resolve(__dirname, '../apptile-core'),
];

if (enableLivelyPIP) {
  watchFolders.push(path.resolve(__dirname, '../zego-express-engine-reactnative'))
}

const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  resolver: {
    sourceExts: [...defaultConfig.resolver.sourceExts, 'css', 'pcss', 'cjs'],
    resolveRequest: (context, moduleName, platform) => {
      if (moduleName === 'apptile-core') {
        let filePath = path.resolve(__dirname, '../apptile-core/index.ts');
        return {
          filePath,
          type: 'sourceFile',
        };
      } else if (moduleName === 'asset_placeholder-image') {
        let filePaths = [path.resolve(__dirname, 'app/assets/image-placeholder.png')];
        return {
          filePaths,
          type: 'assetFiles',
        };
      } else if (enableLivelyPIP && moduleName === 'zego-express-engine-reactnative') {
        let filePath = path.resolve(__dirname, '../zego-express-engine-reactnative/lib/index.js');
        return {
          filePath,
          type: 'sourceFile'
        };
      }
      return metroResolver.resolve({...context, resolveRequest: null}, moduleName, platform);
    },
    nodeModulesPaths: [path.resolve(__dirname, 'node_modules')],
  },
  watchFolders,
};

module.exports = mergeConfig(defaultConfig, config);
