{"name": "reactnativetsproject", "version": "0.18.19", "private": true, "scripts": {"watchcore": "node -e 'console.log(`watchcore is deprecated`)'", "android": "react-native run-android", "android:release": "react-native run-android --variant release --no-packager", "ios": "react-native run-ios", "ios:release": "react-native run-ios --configuration Release --no-packager", "start": "USE_GLOBAL_PLACEHOLDERS=true react-native start", "web": "export NODE_OPTIONS='--max-old-space-size=8192' && cd web && IS_WEB_ENV=true node ../node_modules/webpack/bin/webpack.js serve  --mode=development --env=development --open https://dev.apptile.local/login --hot --history-api-fallback --progress", "build": "cd web && export NODE_ENV=production && export GENERATE_SOURCEMAP=false && IS_WEB_ENV=true webpack --mode=production --env=production --progress", "run:build": "npm run build && serve -s dist", "test": "IS_WEB_ENV=true node ./node_modules/.bin/jest", "test-debug": "node --inspect ./node_modules/.bin/jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint '*/**/*.{js,jsx,ts,tsx}' --quiet --fix", "bundle:ios": "react-native bundle --dev false --entry-file index.js --bundle-output ./ios/main.jsbundle --assets-dest ./ios --platform ios", "bundle:android": "react-native bundle --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest ./android/app/src/main/res --platform android", "bundle": "npm run bundle:ios && npm run bundle:android", "preview:bundle:android": "npx react-native bundle --platform android --config preview.config.js --dev false --entry-file preview.js --bundle-output android/app/src/main/assets/preview.android.bundle --assets-dest android/app/src/main/res", "preview:bundle:ios": "npx react-native bundle --platform ios --config preview.config.js --dev false --entry-file preview.js --bundle-output ios/preview.bundle", "preview:bundle": "npm run preview:bundle:ios && npm run preview:bundle:android", "preview:start": "npx react-native start --config preview.config.js --entry-file preview.js", "web:generateIconCss": "node-sass ./web/assets/stylesheets/ApptileWebIcons.scss ./web/assets/stylesheets/ApptileWebIcons.css", "web:generateIconComponent": "./node_modules/.bin/generate-icon ./web/assets/stylesheets/ApptileWebIcons.css --prefix=.apptile-web-icon. --componentName=ApptileWebIcons --fontFamily=ApptileWebIcons > ./web/icons/_ApptileWebIcons.tsx", "postinstall": "node scripts/fixupAcorn.js && patch-package"}, "dependencies": {"@apollo/client": "3.10.7", "@babel/plugin-transform-private-methods": "7.24.7", "@babel/plugin-transform-private-property-in-object": "7.24.7", "@babel/standalone": "7.12.9", "@babel/types": "7.24.7", "@codemirror/commands": "^6.6.2", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/matchbrackets": "^0.19.4", "@codemirror/state": "^6.4.1", "@codemirror/view": "^6.33.0", "@dotlottie/react-player": "^1.6.19", "@gorhom/portal": "1.0.14", "@logrocket/react-native": "1.33.7", "@lottiefiles/react-lottie-player": "^3.5.4", "@monaco-editor/react": "^4.6.0", "@native-html/iframe-plugin": "2.6.1", "@ptomasroos/react-native-multi-slider": "2.2.2", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/blur": "4.4.1", "@react-native-community/push-notification-ios": "1.10.1", "@react-native-firebase/analytics": "20.1.0", "@react-native-firebase/app": "20.1.0", "@react-native-firebase/messaging": "20.1.0", "@react-native-masked-view/masked-view": "0.3.1", "@react-navigation/bottom-tabs": "6.5.20", "@react-navigation/drawer": "6.7.0", "@react-navigation/material-top-tabs": "^6.6.14", "@react-navigation/native": "6.1.17", "@react-navigation/native-stack": "6.9.26", "@react-navigation/stack": "6.3.29", "@redux-devtools/serialize": "0.4.2", "@segment/analytics-next": "1.70.0", "@segment/analytics-react-native": "2.19.2", "@segment/sovran-react-native": "1.1.1", "@sentry/react": "^8.13.0", "@sentry/react-native": "5.33.2", "@shopify/app-bridge-react": "^3.1.1", "@shopify/app-bridge-utils": "^3.1.1", "@supabase/supabase-js": "^2.49.4", "@types/babel__standalone": "^7.1.4", "@types/react-native-vector-icons": "6.4.18", "@uiw/react-color": "^2.3.0", "acorn": "8.12.0", "acorn-walk": "8.3.3", "apollo3-cache-persist": "0.14.1", "atrament": "^4.4.1", "axios": "1.7.2", "babel-plugin-transform-inline-environment-variables": "0.4.4", "browser-image-compression": "^2.0.2", "clevertap-react-native": "1.2.1", "clsx": "^1.2.1", "codemirror": "6.0.1", "coloring-palette": "^1.0.0", "crypto-js": "^4.1.1", "css-loader": "^6.6.0", "currency-symbol-map": "^5.1.0", "dependency-graph": "github:samyam-a/dependency-graph", "fontkit": "^2.0.2", "fuse.js": "^6.6.2", "graphql": "^16.9.0", "htmlparser2": "9.0.0", "immutability-helper": "^3.1.1", "immutable": "^4.0.0", "js-sha256": "^0.11.0", "lively-live-stream-rn": "1.5.2", "localforage": "^1.10.0", "localforage-driver-memory": "^1.0.5", "lodash-es": "^4.17.21", "logrocket": "^8.1.0", "millify": "^6.1.0", "moment": "^2.29.4", "normalizr": "^3.6.1", "patch-package": "^8.0.0", "qs": "^6.13.0", "re-resizable": "^6.9.17", "react": "18.2.0", "react-dnd": "^14.0.5", "react-dnd-html5-backend": "^14.1.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.1", "react-helmet": "^6.1.0", "react-hotkeys": "^2.0.0", "react-image-crop": "^10.0.9", "react-native": "0.73.8", "react-native-autolink": "4.1.0", "react-native-calendars": "1.1304.1", "react-native-camera": "4.2.1", "react-native-circular-progress-indicator": "4.4.2", "react-native-compressor": "1.8.25", "react-native-console-time-polyfill": "1.2.3", "react-native-device-info": "9.0.2", "react-native-fast-image": "8.6.3", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.17.1", "react-native-get-random-values": "1.11.0", "react-native-haptic-feedback": "1.14.0", "react-native-iap": "^12.16.2", "react-native-image-pan-zoom": "2.1.12", "react-native-joi": "^0.0.5", "react-native-json-tree": "1.3.0", "react-native-keep-awake": "4.0.0", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-klaviyo": "github:clearsight-dev/react-native-klaviyo-apptile#2.1.0", "react-native-linear-gradient": "2.8.3", "react-native-onesignal": "5.2.0", "react-native-pager-view": "^6.4.1", "react-native-paper": "^5.13.1", "react-native-permissions": "3.3.1", "react-native-popover-view": "5.1.8", "react-native-push-notification": "8.1.1", "react-native-reanimated": "^3.12.1", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.32.0", "react-native-svg": "15.3.0", "react-native-tab-view": "^3.5.2", "react-native-toast-notifications": "3.3.1", "react-native-v8": "2.5.0", "react-native-vector-icons": "10.1.0", "react-native-video": "5.2.0", "react-native-video-controls": "2.8.1", "react-native-web-hooks": "3.0.2", "react-native-webview": "13.10.4", "react-native-zip-archive": "^7.0.1", "react-product-fruits": "^2.2.3", "react-qr-code": "^2.0.7", "react-redux": "7.2.9", "react-router": "^6.2.1", "react-router-dom": "^6.2.1", "react-router-native": "^6.2.1", "react-tiny-popover": "^7.2.4", "react-use-intercom": "^5.0.0", "react-visibility-sensor": "^5.1.1", "react-webcam": "^7.0.1", "redux": "4.2.1", "redux-saga": "1.3.0", "reselect": "4.1.8", "rn-dates": "^0.1.0", "rn-dynamic-fonts": "0.1.2", "rn-fetch-blob": "https://github.com/clearsight-dev/rn-fetch-blob#v0.12.1", "semver": "^7.3.7", "srcset": "^5.0.0", "style-loader": "^3.3.1", "tern": "^0.24.3", "tinycolor2": "^1.4.2", "url-parse": "^1.5.10", "uuid": "^8.3.2", "v8-android-jit": "11.1000.4", "validate-color": "^2.2.1", "validator": "^13.7.0", "video.js": "^8.0.4", "web-vitals": "^3.4.0", "zego-express-engine-reactnative": "3.14.5", "zego-express-engine-webrtc": "^3.4.0"}, "devDependencies": {"@babel/cli": "^7.24.7", "@babel/core": "^7.24.7", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-export-namespace-from": "^7.24.1", "@babel/plugin-transform-flow-strip-types": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@redux-devtools/extension": "^3.2.5", "@redux-saga/types": "^1.1.0", "@types/graphql": "^14.5.0", "@types/jest": "^26.0.23", "@types/lodash": "^4.14.178", "@types/lodash-es": "^4.17.6", "@types/react": "^18.2.6", "@types/react-helmet": "^6.1.5", "@types/react-native": "0.65.0", "@types/react-native-push-notification": "8.1.1", "@types/react-native-video": "5.0.14", "@types/react-router": "^5.1.18", "@types/react-router-dom": "^5.3.3", "@types/react-test-renderer": "^18.3.0", "@types/redux-logger": "^3.0.9", "@types/semver": "^7.3.9", "@types/tinycolor2": "^1.4.3", "@types/uuid": "^8.3.4", "@types/validator": "^13.7.10", "@types/video.js": "^7.3.51", "ajv-errors": "^1.0.1", "apollo": "^2.33.9", "app-icon": "^0.13.2", "babel-jest": "^29.6.3", "babel-loader": "^8.2.3", "babel-plugin-module-resolver": "^4.1.0", "buffer": "^6.0.3", "eslint": "^8.19.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "html-loader": "^4.1.0", "html-webpack-plugin": "^5.3.2", "iconfont-plugin-webpack": "^1.1.4", "imports-loader": "^3.1.1", "jest": "^29.6.3", "lively-feeds-rn": "2.6.9", "lottie-react-native": "6.7.0", "node-sass": "^7.0.3", "prettier": "2.8.8", "raw-loader": "^4.0.2", "react-native-web": "0.19.12", "react-test-renderer": "18.2.0", "redux-logger": "^3.0.6", "serve": "^13.0.2", "typesafe-actions": "^5.1.0", "typescript": "5.0.4", "url-loader": "^4.1.1", "webpack": "^5.51.1", "webpack-cli": "^4.8.0", "webpack-dev-server": "^4.0.0", "webpack-watch-files-plugin": "^1.2.1"}, "engines": {"node": ">=18"}, "overrides": {"@react-native-clipboard/clipboard": {"react-native": "~0.73.0", "react-native-windows": "~0.73.0"}}}