import {BindingError, ModuleCreationParams, ModuleRecord, PluginSubType, Selector} from 'apptile-core';
import Immutable from 'immutable';
import moment from 'moment';
import _ from 'lodash';

export interface ITileCacheInfo {
  isFetched: boolean;
  isFetching: boolean;
  record?: ModuleRecord;
}
const defaultTileCacheInfo: ITileCacheInfo = {
  isFetched: false,
  isFetching: false,
  record: undefined,
};
export class TileCacheInfo extends Immutable.Record(defaultTileCacheInfo, 'tileCacheInfo') {}

interface TilesCacheShape {
  tiles: Immutable.OrderedMap<string, ITileCacheInfo>;
}

const defaultTilesCacheShape: TilesCacheShape = {
  tiles: Immutable.OrderedMap<string, ITileCacheInfo>(),
};

export class TilesCacheRecord extends Immutable.Record(defaultTilesCacheShape, 'tilesCache') {
  constructor(params: Partial<TilesCacheShape>) {
    super(params);
  }

  isTileCached(moduleUUID: string): boolean {
    return this.tiles.has(moduleUUID) && !!this.tiles.get(moduleUUID)?.isFetched;
  }
  initTileCache(moduleUUID: string): TilesCacheType {
    return this.tiles.has(moduleUUID) ? this : this.setIn(['tiles', moduleUUID], new TileCacheInfo({isFetching: true}));
  }
  setTileCache(moduleUUID: string, moduleRecord: ModuleRecord): TilesCacheType {
    return this.setIn(
      ['tiles', moduleUUID],
      new TileCacheInfo({isFetching: false, isFetched: true, record: moduleRecord}),
    );
  }
  getTileCacheRecord(moduleUUID: string): ModuleRecord | undefined {
    return this.isTileCached(moduleUUID)
      ? (this.getIn(['tiles', moduleUUID, 'record']) as ModuleRecord | undefined)
      : undefined;
  }
}

export type TilesCacheType = Omit<
  TilesCacheRecord,
  | 'set'
  | 'update'
  | 'merge'
  | 'mergeDeep'
  | 'mergeWith'
  | 'mergeDeepWith'
  | 'delete'
  | 'setIn'
  | 'updateIn'
  | 'mergeIn'
  | 'mergeDeepIn'
  | 'deleteIn'
  | 'remove'
  | 'removeIn'
>;

export type IconType = 'MaterialCommunityIcons' | 'ApptileWebIcons' | 'MaterialIcons';

export interface EditorStateParams {
  selectedPluginTarget: string;
  selectedPluginConfigSel?: Selector | null;
  selectedNavComponentSel?: Selector | null;
  selectedPageId: string | null;
  selectedPageType: string | null;
  isSaving: boolean;
  isPropertyInspectorOpen: boolean;
  isPluginListingOpen: boolean;
  isThemeEditorOpen: boolean;
  isTilesBrowserOpen: boolean;
  registeredPlugins: Immutable.Set<string>;
  moduleCreationParams: ModuleCreationParams;
  activeAttachmentId: string | undefined;
  activeAttachmentKey: string | undefined;
  bindingErrors: Immutable.Map<string, BindingError>;
  showBindingErrors: boolean;
  showChatView: boolean;
  promptFromLandingPage: string;
  isGeneratingWebPreview: boolean;
  isGeneratingMobilePreview: boolean;
  mobilePreviewStatus: 'idle' | 'loading' | 'success' | 'error';
  isWebPreviewModalOpen: boolean;
  webPreviewLink: string | null;
  generateWebPreviewError: string | null;
  codeEditor: {
    open: boolean;
    appId: string;
    sourceCode: string;
    type: 'navigator'|'plugin';
    artefactName: string;
  };
  bindingEditor: {
    open: boolean;
    binding: string;
    namespace?: string;
    selector: Selector;
    callback: (editorText: string) => void
    evalContext: {
      $pageContext: any;
      $appJSModel: any;
      currentPage: any;
      i: number;
      _: typeof _;
      moment: typeof moment;
    };
  };
}

const defaultEditorParams: EditorStateParams = {
  selectedPluginTarget: "",
  selectedPluginConfigSel: null,
  selectedNavComponentSel: null,
  selectedPageId: null,
  selectedPageType: 'screen',
  isSaving: false,
  isPropertyInspectorOpen: false,
  isPluginListingOpen: true,
  isThemeEditorOpen: false,
  isTilesBrowserOpen: false,
  registeredPlugins: Immutable.Set<PluginSubType>(),
  moduleCreationParams: {
    pageId: undefined,
    pageKey: undefined,
    pluginId: undefined,
    variablesBySelector: {},
    inputSelectorStrings: [],
    modulePluginIds: [],
    moduleParentContainerId: undefined,
    isOpen: false,
  },
  activeAttachmentId: undefined,
  activeAttachmentKey: undefined,
  bindingErrors: Immutable.Map<string, BindingError>(),
  showBindingErrors: process.env.IS_WEB_ENV ? !!localStorage.getItem('showBindingErrors') || false : false,
  showChatView: false,
  promptFromLandingPage: '',
  isGeneratingWebPreview: false,
  isGeneratingMobilePreview: false,
  mobilePreviewStatus: 'idle',
  isWebPreviewModalOpen: false,
  webPreviewLink: null,
  generateWebPreviewError: null,
  codeEditor: {
    open: false,
    appId: '',
    sourceCode: '',
    type: 'plugin',
    artefactName: ''
  },
  bindingEditor: {
    open: false,
    binding: '',
    namespace: '',
    selector: [],
    callback: () => {},
    evalContext: {
      $pageContext: null,
      $appJSModel: null,
      currentPage: null,
      _,
      i: 0,
      moment
    }
  },
};

export class EditorState extends Immutable.Record(defaultEditorParams, 'editor') {
  selectPlugin(pluginConfigSel: Selector) {
    return this.set('selectedNavComponentSel', null)
      .set('selectedPluginConfigSel', pluginConfigSel)
      .set('selectedPageId', null);
  }
  selectPluginWithTarget(sel: Selector, target: string) {
    return this.set('selectedNavComponentSel', null)
      .set('selectedPluginConfigSel', sel)
      .set('selectedPageId', null)
      .set('selectedPluginTarget', target);
  }
  selectNavComponent(navComponentSel: any[]) {
    return this.set('selectedPluginConfigSel', null)
      .set('selectedNavComponentSel', navComponentSel)
      .set('selectedPageId', null);
  }
  selectPage(pageId: string) {
    return this.set('selectedPluginConfigSel', null).set('selectedNavComponentSel', null).set('selectedPageId', pageId);
  }

  selectPageType(pageType: string) {
    return this.set('selectedPageType', pageType);
  }

  deselectPlugin() {
    return this.set('selectedPluginConfigSel', null);
  }

  openOneTab(key: keyof EditorStateParams) {
    const editorTabKeys: Array<keyof EditorStateParams> = [
      'isTilesBrowserOpen',
      'isPluginListingOpen',
      'isThemeEditorOpen',
      'isPropertyInspectorOpen',
    ];

    let result = this;
    for (let index = 0; index < editorTabKeys.length; index++) {
      result = result.set(editorTabKeys[index], false);
    }

    return result.set(key, true);
  }

  recordBindingError(binding: string, payload: BindingError) {
    if (!this.bindingErrors.has(binding)) {
      return this.set('bindingErrors', this.bindingErrors.set(binding, payload));
    } else {
      return this;
    }
  }

  deleteBindingError(binding: string) {
    return this.set('bindingErrors', this.bindingErrors.delete(binding));
  }

  openPropertyInspector() {
    return this.openOneTab('isPropertyInspectorOpen');
  }
  openPluginListing() {
    return this.openOneTab('isPluginListingOpen');
  }
  openThemeEditor() {
    return this.openOneTab('isThemeEditorOpen');
  }
  openTilesBrowser() {
    return this.openOneTab('isTilesBrowserOpen');
  }

  registerPlugins(pluginsList: PluginSubType[]) {
    let registeredPlugins = this.registeredPlugins;
    pluginsList.forEach(pluginType => {
      registeredPlugins = registeredPlugins.add(pluginType);
    });
    return this.set('registeredPlugins', registeredPlugins);
  }
  openModuleCreationDialog() {
    return this.setIn(['moduleCreationParams', 'isOpen'], true);
  }
  closeModuleCreationDialog() {
    return this.setIn(['moduleCreationParams', 'isOpen'], false);
  }
  setModuleCreationParams(params: Partial<ModuleCreationParams>) {
    return this.mergeIn(['moduleCreationParams'], params);
  }

  setActiveAttachmentId(attahcmentId: string) {
    return this.set('activeAttachmentId', attahcmentId);
  }

  setActiveAttachmentKey(attachmentKey: string) {
    return this.set('activeAttachmentKey', attachmentKey);
  }

  toggleBindingError() {
    const currentValue = this.showBindingErrors;
    if (process.env.IS_WEB_ENV) {
      if (!currentValue) {
        localStorage.setItem('showBindingErrors', 'true');
      } else {
        localStorage.setItem('showBindingErrors', '');
      }
    }

    return this.set('showBindingErrors', !currentValue);
  }

  toggleChatView() {
    const currentValue = this.showChatView;
    return this.set('showChatView', !currentValue);
  }

  openChatView() {
    return this.set('showChatView', true);
  }

  closeChatView() {
    return this.set('showChatView', false);
  }

  openBindingEditor(payload: EditorState['bindingEditor']) {
    return this.set('bindingEditor', {...payload});
  }

  closeBindingEditor() {
    return this.set('bindingEditor', {
      open: false,
      binding: '',
      callback: () => {},
      selector: [],
      evalContext: {
        $pageContext: null,
        $appJSModel: null,
        currentPage: null,
        _,
        moment,
      }
    });
  }

  openCodeEditor(payload: EditorState['codeEditor']) {
    return this.set('codeEditor', {...payload});
  }

  closeCodeEditor() {
    return this.set('codeEditor', {
      open: false,
      appId: '',
      sourceCode: '',
      type: 'plugin',
      artefactName: ''
    });
  }

  startGeneratingWebPreview() {
    return this.set('isGeneratingWebPreview', true);
  }

  stopGeneratingWebPreview() {
    return this.set('isGeneratingWebPreview', false);
  }

  startGeneratingMobilePreview() {
    return this
      .set('isGeneratingMobilePreview', true)
      .set('mobilePreviewStatus', 'loading');
  }

  stopGeneratingMobilePreview() {
    return this.set('isGeneratingMobilePreview', false);
  }
  
  resetMobilePreviewStatus() {
    return this.set('mobilePreviewStatus', 'idle');
  }

  openWebPreviewModal() {
    return this.set('isWebPreviewModalOpen', true);
  }

  closeWebPreviewModal() {
    return this.set('isWebPreviewModalOpen', false);
  }

  setWebPreviewLink(link: string | null) {
    return this.set('webPreviewLink', link);
  }

  setGenerateWebPreviewError(error: string | null) {
    return this.set('generateWebPreviewError', error);
  }

  setLandingPagePrompt(prompt: string) {
    return this.set('promptFromLandingPage', prompt);
  }
}
