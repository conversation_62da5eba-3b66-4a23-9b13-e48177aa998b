import {createReducer} from 'apptile-core';
import {
  DispatchAction,
  LOGIN_USER_FINISHED,
  REGISTER_USER_FINISHED,
  USER_EDIT_SUCCESS,
  USER_INIT,
  USER_INIT_ERROR,
  USER_INIT_FETCHED,
  USER_INIT_UNAUTHORIZED,
} from '../actions/editorActions';
import {FetchUserResponse} from '../api/ApiTypes';

export interface AuthUser {
  id?: number;
  email?: string;
  firstname?: string;
  lastname?: string;
  contactNumber?: string;
  role?: string;
  betaAccessEnabled?: boolean;
  betaProgramRunning?: boolean;
  isTileBetaAccessEnabled?: boolean;
}
export interface UserState {
  userFetched: boolean;
  userFetching: boolean;
  userLoggedIn: boolean;
  userRegistered: boolean;
  user: AuthUser | null;
  networkReachable: boolean;
}

const initialState: UserState = {
  userFetched: false,
  userFetching: false,
  userLoggedIn: false,
  user: null,
  userRegistered: false,
  networkReachable: true,
};

export const UserReducer = createReducer<UserState>(
  {
    [USER_INIT]: state => {
      return {...state, userFetching: true};
    },
    [USER_INIT_FETCHED]: (state, action: DispatchAction<FetchUserResponse>) => {
      return {
        ...state,
        ...{user: action.payload},
        ...{
          networkReachable: true,
          userFetched: true,
          userFetching: false,
          userLoggedIn: true,
        },
      };
    },
    [USER_EDIT_SUCCESS]: (state, action: DispatchAction<FetchUserResponse>) => {
      return {
        ...state,
        ...{user: action.payload},
        ...{
          networkReachable: true,
          userFetched: true,
          userFetching: false,
          userLoggedIn: true,
        },
      };
    },
    [USER_INIT_ERROR]: state => {
      return {
        ...state,
        networkReachable: false,
        userFetched: true,
        userFetching: false,
        userLoggedIn: false,
      };
    },
    [USER_INIT_UNAUTHORIZED]: state => {
      return {
        ...state,
        networkReachable: true,
        userFetched: true,
        userFetching: false,
        userLoggedIn: false,
      };
    },
    [LOGIN_USER_FINISHED]: state => {
      return {...state, userLoggedIn: true, networkReachable: true};
    },
    [REGISTER_USER_FINISHED]: state => {
      return {...state, userRegistered: true, networkReachable: true};
    },
  },
  //DefaultState
  initialState,
);
