import {DispatchAction, DispatchActions, DispatchEmptyAction} from 'apptile-core';
import {BindingError, ModuleCreationParams, PluginSubType} from 'apptile-core';
import {createReducer} from 'apptile-core';
import {
  EDITOR_OPEN_MODULE_DIALOG,
  EDITOR_CLOSE_MODULE_DIALOG,
  EDITOR_OPEN_PLUGIN_LISTING,
  EDITOR_OPEN_THEME_EDITOR,
  EDITOR_OPEN_PROPERTY_INSPECTOR,
  EDITOR_SELECT_NAV_COMPONENT,
  EDITOR_SELECT_PAGE,
  REGISTER_PLUGINS,
  SET_MODULE_CREATION_PARAMS,
  EDITOR_SELECTED_PAGE_TYPE,
  EDITOR_SET_ACTIVE_ATTACHMENT_ID,
  EDITOR_SET_ACTIVE_ATTACHMENT_KEY,
  EditorActiveAttachmenIdPayload,
  EditorActiveAttachmentKeyPayload,
  EDITOR_OPEN_TILES_BROWSER,
  EDITOR_RECORD_BINDING_ERROR,
  EDITOR_RESOLVE_BINDING_ERROR,
  EDITOR_TOGGLE_BINDING_ERRORS,
  EDITOR_TOGGLE_CHAT_VIEW,
  EDITOR_OPEN_CODE_EDITOR,
  EDITOR_CLOSE_CODE_EDITOR,
  EDITOR_OPEN_BINDING_EDITOR,
  EDITOR_CLOSE_BINDING_EDITOR,
  EDITOR_OPEN_CHAT_VIEW,
  EDITOR_SET_LANDING_PAGE_PROMPT,
  EDITOR_CLOSE_CHAT_VIEW,
  GENERATE_WEB_PREVIEW_START,
  GENERATE_WEB_PREVIEW_SUCCESS,
  GENERATE_WEB_PREVIEW_FAILED,
  OPEN_WEB_PREVIEW_MODAL,
  CLOSE_WEB_PREVIEW_MODAL,
  SET_WEB_PREVIEW_LINK,
  SET_GENERATE_WEB_PREVIEW_ERROR,
  GENERATE_MOBILE_PREVIEW_START,
  GENERATE_MOBILE_PREVIEW_SUCCESS,
  GENERATE_MOBILE_PREVIEW_FAILED,
  RESET_MOBILE_PREVIEW_STATUS,
} from '../actions/editorActions';
import {EditorState} from '../common/webDatatypes';

const initialState = new EditorState();

const handleOpenPI = (state: EditorState, _) => {
  return state.openPropertyInspector();
};
const handleOpenListing = (state: EditorState, _) => {
  return state.openPluginListing();
};

const handleOpenThemeEditor = (state: EditorState, _) => {
  return state.openThemeEditor();
};

const handleOpenTilesBrowser = (state: EditorState, _) => {
  return state.openTilesBrowser();
};

const handleRegisterPlugins = (state: EditorState, action: DispatchAction<PluginSubType[]>) => {
  return state.registerPlugins(action.payload);
};

const handleSetModuleCreationParams = (state: EditorState, action: DispatchAction<ModuleCreationParams>) => {
  return state.setModuleCreationParams(action.payload);
};
const handleOpenModuleDialog = (state: EditorState, action: DispatchEmptyAction) => {
  return state.openModuleCreationDialog();
};
const handleCloseModuleDialog = (state: EditorState, action: DispatchEmptyAction) => {
  return state.closeModuleCreationDialog();
};

export const EditorReducer = createReducer<EditorState>(
  {
    [DispatchActions.SELECT_PLUGIN]: (state: EditorState, action: DispatchAction<string[]>) => {
      const sel = action.payload;
      return state.selectPlugin(sel);
    },
    [DispatchActions.SELECT_PLUGIN_WITH_TARGET]: (state: EditorState, action: DispatchAction<{selector: string[]; target: string;}>) => {
      const sel = action.payload.selector;
      const target = action.payload.target;
      return state.selectPluginWithTarget(sel, target);
    },
    [EDITOR_SELECT_NAV_COMPONENT]: (state: EditorState, action: DispatchAction<string[]>) => {
      const sel = action.payload;
      return state.selectNavComponent(sel);
    },
    [EDITOR_SELECTED_PAGE_TYPE]: (state: EditorState, action: DispatchAction<string>) => {
      const pageType = action.payload;
      return state.selectPageType(pageType);
    },
    [EDITOR_SET_ACTIVE_ATTACHMENT_ID]: (state: EditorState, action: DispatchAction<EditorActiveAttachmenIdPayload>) => {
      const {activeAttachmentId} = action.payload;
      return state.setActiveAttachmentId(activeAttachmentId);
    },
    [EDITOR_SET_ACTIVE_ATTACHMENT_KEY]: (
      state: EditorState,
      action: DispatchAction<EditorActiveAttachmentKeyPayload>,
    ) => {
      const {activeAttachmentKey} = action.payload;
      return state.setActiveAttachmentKey(activeAttachmentKey);
    },
    [EDITOR_SELECT_PAGE]: (state: EditorState, action: DispatchAction<string>) => {
      const pageId = action.payload;
      return state.selectPage(pageId);
    },
    [EDITOR_OPEN_PROPERTY_INSPECTOR]: handleOpenPI,
    [EDITOR_OPEN_PLUGIN_LISTING]: handleOpenListing,
    [EDITOR_OPEN_THEME_EDITOR]: handleOpenThemeEditor,
    [EDITOR_OPEN_TILES_BROWSER]: handleOpenTilesBrowser,
    [REGISTER_PLUGINS]: handleRegisterPlugins,
    [SET_MODULE_CREATION_PARAMS]: handleSetModuleCreationParams,
    [EDITOR_OPEN_MODULE_DIALOG]: handleOpenModuleDialog,
    [EDITOR_CLOSE_MODULE_DIALOG]: handleCloseModuleDialog,
    [EDITOR_RECORD_BINDING_ERROR]: (
      state: EditorState,
      action: DispatchAction<{
        binding: string;
        error: BindingError;
      }>,
    ) => {
      return state.recordBindingError(action.payload.binding, action.payload.error);
    },
    [EDITOR_RESOLVE_BINDING_ERROR]: (state: EditorState, action: DispatchAction<string>) => {
      return state.deleteBindingError(action.payload);
    },
    [EDITOR_TOGGLE_BINDING_ERRORS]: (state: EditorState) => {
      return state.toggleBindingError();
    },
    [EDITOR_TOGGLE_CHAT_VIEW]: (state: EditorState) => {
      return state.toggleChatView();
    },
    [EDITOR_OPEN_CHAT_VIEW]: (state: EditorState) => {
      return state.openChatView();
    },
    [EDITOR_SET_LANDING_PAGE_PROMPT]: (state: EditorState, action: DispatchAction<string>) => {
      return state.setLandingPagePrompt(action.payload);
    },
    [EDITOR_CLOSE_CHAT_VIEW]: (state: EditorState) => {
      return state.closeChatView();
    },
    [EDITOR_OPEN_CODE_EDITOR]: (state: EditorState, action: DispatchAction<EditorState['codeEditor']>) => {
      return state.openCodeEditor(action.payload);
    },
    [EDITOR_CLOSE_CODE_EDITOR]: (state: EditorState) => {
      return state.closeCodeEditor();
    },
    [EDITOR_OPEN_BINDING_EDITOR]: (state: EditorState, action: DispatchAction<EditorState['bindingEditor']>) => {
      return state.openBindingEditor(action.payload);
    },
    [EDITOR_CLOSE_BINDING_EDITOR]: (state: EditorState) => {
      return state.closeBindingEditor();
    },
    [GENERATE_WEB_PREVIEW_START]: (state: EditorState) => {
      return state
        .set("isGeneratingWebPreview", true)
        .set("webPreviewLink", null)
        .set("generateWebPreviewError", null)
        .set("isWebPreviewModalOpen", false);
    },
    [GENERATE_WEB_PREVIEW_SUCCESS]: (state: EditorState) => {
      return state.stopGeneratingWebPreview();
    },
    [GENERATE_WEB_PREVIEW_FAILED]: (state: EditorState) => {
      return state.stopGeneratingWebPreview();
    },
    [OPEN_WEB_PREVIEW_MODAL]: (state: EditorState) => {
      return state.openWebPreviewModal();
    },
    [CLOSE_WEB_PREVIEW_MODAL]: (state: EditorState) => {
      return state.closeWebPreviewModal();
    },
    [SET_WEB_PREVIEW_LINK]: (state: EditorState, action: DispatchAction<{link: string}>) => {
      return state.setWebPreviewLink(action.payload.link);
    },
    [SET_GENERATE_WEB_PREVIEW_ERROR]: (state: EditorState, action: DispatchAction<{error: string}>) => {
      return state.setGenerateWebPreviewError(action.payload.error);
    },
    [GENERATE_MOBILE_PREVIEW_START]: (state: EditorState) => {
      return state.startGeneratingMobilePreview();
    },
    [GENERATE_MOBILE_PREVIEW_SUCCESS]: (state: EditorState) => {
      return state.stopGeneratingMobilePreview().set('mobilePreviewStatus', 'success');
    },
    [GENERATE_MOBILE_PREVIEW_FAILED]: (state: EditorState) => {
      return state.stopGeneratingMobilePreview().set('mobilePreviewStatus', 'error');
    },
    [RESET_MOBILE_PREVIEW_STATUS]: (state: EditorState) => {
      return state.resetMobilePreviewStatus();
    },
  },
  initialState,
);
