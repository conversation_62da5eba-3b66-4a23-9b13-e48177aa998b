import { createReducer } from 'apptile-core';
import { DispatchAction } from 'apptile-core';
import {
  // ChatHistory,
  // CLEAR_LIVE_MESSAGE,
  // FETCH_CHAT_HISTORY_FAILURE,
  // FETCH_CHAT_HISTORY_REQUEST,
  // FETCH_CHAT_HISTORY_SUCCESS,
  SET_CHAT_RUNNING,
  RUN_CHAT_COMPLETION_FAILURE,
  RUN_CHAT_COMPLETION_REQUEST,
  RUN_CHAT_COMPLETION_SUCCESS,
  // SET_CHAT_HISTORY,
  // UPDATE_LIVE_MESSAGE,
} from '../actions/aiActions';

export interface AIState {
  // chatHistory: ChatHistory | null;
  // liveMessage: string;
  loading: boolean;
  error: string | null;
  // fetchingHistory: boolean;
  // historyError: string | null;
  isChatRunning: boolean;
}

const initialState: AIState = {
  // chatHistory: null,
  // liveMessage: '',
  loading: false,
  error: null,
  // fetchingHistory: false,
  // historyError: null,
  isChatRunning: false
};

export const AIReducer = createReducer<AIState>(
  {
    [SET_CHAT_RUNNING]: (state, action: DispatchAction<boolean>) => ({
      ...state,
      isChatRunning: action.payload
    }),
    [RUN_CHAT_COMPLETION_REQUEST]: (state) => ({
      ...state,
      loading: true,
      error: null,
    }),
    [RUN_CHAT_COMPLETION_SUCCESS]: (state, action: DispatchAction<any>) => ({
      ...state,
      loading: false,
    }),
    [RUN_CHAT_COMPLETION_FAILURE]: (state, action: DispatchAction<string>) => ({
      ...state,
      loading: false,
      error: action.payload,
    }),
    // [FETCH_CHAT_HISTORY_REQUEST]: (state) => ({
    //   ...state,
    //   fetchingHistory: true,
    //   historyError: null,
    // }),
    // [FETCH_CHAT_HISTORY_SUCCESS]: (state, action: DispatchAction<ChatHistory>) => ({
    //   ...state,
    //   chatHistory: action.payload,
    //   fetchingHistory: false,
    // }),
    // [FETCH_CHAT_HISTORY_FAILURE]: (state, action: DispatchAction<string>) => ({
    //   ...state,
    //   fetchingHistory: false,
    //   historyError: action.payload,
    // }),
    // [SET_CHAT_HISTORY]: (state, action: DispatchAction<ChatHistory>) => ({
    //   ...state,
    //   chatHistory: action.payload,
    // }),
    // [UPDATE_LIVE_MESSAGE]: (state, action: DispatchAction<string>) => ({
    //   ...state,
    //   liveMessage: action.payload,
    // }),
    // [CLEAR_LIVE_MESSAGE]: (state) => ({
    //   ...state,
    //   liveMessage: '',
    // }),
  },
  initialState
);
