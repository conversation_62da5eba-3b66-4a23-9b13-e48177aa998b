import React from 'react';
import {StyleSheet, Text, View, Pressable} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {selectSelectedPluginConfig} from '@/root/web/selectors/EditorSelectors';
import {sendTileAnalytics} from '../actions/editorActions';
import {ApptileWebIcon} from '../icons/ApptileWebIcon';
import {Icon} from 'apptile-core';
import theme from '../views/prompt-to-app/styles-prompt-to-app/theme';
import {APPTILE_TILE_ENV} from '../../.env.json';

const styles = StyleSheet.create({
  header: {
    display: 'flex',
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: 8,
    alignItems: 'center',
    paddingTop: 10,
    paddingBottom: 10,
  },
  tile: {
    fontSize: 12,
    marginBottom: 4,
    color: '#B0B0B0',
  },
  tileName: {
    fontWeight: '600',
    wordBreak: 'break-word',
    width: '100%',
    color: theme.TEXT_COLOR,
  },
  delContainer: {
    width: '20%',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  delIcon: {
    // width: 36,
    // height: 36,
    padding: 5,
    // backgroundColor: '#fce5e6',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    // borderColor: '#D80707',
    // borderWidth: 1,
  },
  nameContainer: {
    justifyContent: 'center',
    flex: 1,
  },
});

const TileHeader = (props: any) => {
  const {isDeletable, onDelete, onChat, onEditCode} = props;
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const dispatch = useDispatch();
  if (!moduleConfig) return null;
  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  let title = moduleConfig?.config?.get('moduleName') ?? '';
  let isSDKPlugin = false;
  if (!title && moduleConfig.get('type') === 'widget') {
    title = moduleConfig.get('subtype');
    isSDKPlugin = true;
  }

  return (
    <div style={styles.header}>
      <div style={styles.nameContainer}>
        {/* <Text style={styles.tile}>Tile Name</Text> */}
        <Text style={styles.tileName}>{title}</Text>
      </div>
      <div style={{display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between'}}>
        {APPTILE_TILE_ENV === 'local' || !onEditCode ? (
          <></>
        ) : (
          <Pressable onPress={() => onEditCode(title)} style={{padding: 5}}>
            <Icon name="code" iconType="MaterialIcons" color="#ffffff" size={20} />
          </Pressable>
        )}
        <Pressable onPress={onChat} style={{padding: 5}}>
          <Icon name={'chat'} iconType={'MaterialIcons'} color="#0062ff" size={20} />
        </Pressable>
        {isDeletable && (
          <View style={styles.delIcon}>
            <ApptileWebIcon
              name={'delete'}
              color="#D80707"
              size={20}
              onPress={() => {
                dispatch(sendTileAnalytics(moduleUUID, 'editor:tile_deleted', {}));
                onDelete();
              }}
            />
          </View>
        )}
      </div>
    </div>
  );
};

export default TileHeader;
