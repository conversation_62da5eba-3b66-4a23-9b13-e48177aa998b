import Immutable from 'immutable';
import _, {debounce, update} from 'lodash';
import React, {useCallback, useEffect, useState, useMemo} from 'react';
import {Pressable, StyleSheet, Text, Touchable, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {EventHandlerConfig, ModuleEventHandlerConfig, ScreenConfigParams} from 'apptile-core';
import {EditorProps, PluginEditor, PropertyEditorProps} from '../../../../app/common/EditorControlTypes';
import {selectParentModuleEvents, selectScreensInNav} from '../../../selectors/EditorSelectors';
import {PluginWithGlobalAvailability, selectPageAndGlobalPluginByType} from '../../../selectors/PluginSelectors';
import DropDownControl from '../../../components/controls/DropDownControl';
import CodeInput from '../../../components/controls/CodeInputControl';
import {pluginConfigSetPathValue, pluginConfigUpdatePath} from 'apptile-core';
import {ShopifyItemHandlePicker} from '../../../integrations/shopify/components/ShopifyItemPicker';
import standardScreens, {editableScreens} from '../../../common/screenConstants';
import RadioGroupControl from '@/root/web/components/controls/RadioGroupControl';
import EditorSectionHeader from '@/root/web/components/controls/EditorSectionHeader';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


export interface EditorEventItemProps {
  onEventUpdate: (propName: number, event: EventHandlerConfig) => void;
  eventId: number;
  event: EventHandlerConfig;
  children?: React.ReactNode;
  triggers: string[];
  plugins: PluginWithGlobalAvailability;
  queries: PluginWithGlobalAvailability;
  screens: ScreenConfigParams[];
  rawEvent: ModuleEventHandlerConfig;
}

const EditorEventItem: React.FC<EditorEventItemProps> = props => {
  const {eventId, onEventUpdate, rawEvent} = props;
  let {event} = props;
  const navScreens = useSelector(selectScreensInNav);
  const newEditableScreens: ScreenConfigParams[] = Array(editableScreens.length);
  navScreens
    .filter((s: ScreenConfigParams) => editableScreens.includes(s.name))
    .map(s => newEditableScreens.splice(editableScreens.indexOf(s.name), 1, s));
  const screens = [
    ...newEditableScreens
      .filter((s: ScreenConfigParams) => s)
      .map((s: ScreenConfigParams) => ({
        name: s.title,
        value: s.screen,
        icon: {
          name: s.iconName == 'home' && s.iconType == 'Material Icon' ? 'file-outline' : s.iconName,
          type: s.iconType,
        },
      })),
    ...navScreens
      .filter((s: ScreenConfigParams) => !standardScreens.includes(s.name))
      .map((s: ScreenConfigParams) => ({
        name: s.title,
        value: s.screen,
        icon: {
          name: s.iconName == 'home' && s.iconType == 'Material Icon' ? 'file-outline' : s.iconName,
          type: s.iconType,
        },
      })),
  ];
  const triggers = ['Screen', 'URL'];
  const {method} = event;

  const [link, setLink] = useState(event.get('params').get('link'));

  const trigger = method == 'navigate' ? 'Screen' : 'URL';

  const updateEvent = debounce(event => {
    onEventUpdate(eventId, event);
  }, 200);

  const validateUrl = (url: string) =>
    /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(
      url,
    );

  const eventType = rawEvent.get('type');
  if (rawEvent.get('name') && eventType === 'both') {
    screens.splice(2, 0, {
      name: 'URL',
      value: 'openLinkApptileAction',
      icon: {type: 'MaterialCommunityIcons', name: 'link-variant'},
    });
  }

  if (!event) return null;

  return (
    <View style={styles.eventListItem}>
      {rawEvent.get('name') && eventType ? (
        <View style={styles.editorWindow}>
          <EditorSectionHeader name={''} label={rawEvent.get('name') ?? 'Action'} />
          {eventType != 'link' && (
            <View style={styles.rowLayout}>
              <DropDownControl
                label="Navigate to"
                defaultValue={event.method == 'triggerAction' ? 'openLinkApptileAction' : event.screenName}
                value={event.method == 'triggerAction' ? 'openLinkApptileAction' : event.screenName}
                options={screens}
                nameKey={'name'}
                valueKey={'value'}
                iconKey={'icon'}
                disableBinding={true}
                onChange={function (value: string): void {
                  if (value == 'openLinkApptileAction') {
                    event = event.set('method', 'triggerAction');
                    event = event.set('pluginId', 'Apptile');
                    event = event.set('type', 'action');
                    event = event.set('value', 'openLink');
                    event = event.set('isGlobalPlugin', true);
                    event = event.set('params', event.get('params').set('link', 'https://apptile.com'));
                    setLink('https://apptile.com');
                  } else {
                    event = event.set('method', 'navigate');
                    event = event.set('screenName', value);
                    event = event.set('type', 'page');
                  }
                  updateEvent(event);
                }}
              />
            </View>
          )}
          {(() => {
            switch (method) {
              case 'triggerAction':
                return (
                  <View style={{paddingBottom: validateUrl(link) ? 15 : 0}}>
                    <CodeInput
                      placeholder="Paste Here"
                      value={link}
                      onChange={(value: string) => {
                        setLink(value);
                        if (validateUrl(value)) {
                          event = event.set('params', event.get('params').set('link', value));
                          updateEvent(event);
                        }
                      }}
                      label={eventType === 'link' ? 'Navigate To' : 'URL'}
                      name={'URL'}
                    />
                    {!validateUrl(link) && (
                      <Text style={[commonStyles.baseText, commonStyles.errorText]}>Please Enter A Valid Link</Text>
                    )}
                  </View>
                );
              case 'navigate':
                return (
                  <>
                    {(event.screenName == 'Collection' || event.screenName == 'Product') && (
                      <View style={styles.rowLayout}>
                        <ShopifyItemHandlePicker
                          name={event.screenName}
                          value={event
                            .get('params')
                            .get(event.screenName == 'Product' ? 'productHandle' : 'collectionHandle')?.startsWith('?event.') ? '' : event
                            .get('params')
                            .get(event.screenName == 'Product' ? 'productHandle' : 'collectionHandle')}
                          label={event.screenName}
                          itemType={event.screenName == 'Product' ? 'product' : 'collection'}
                          onChange={value => {
                            const prop = event.screenName == 'Product' ? 'productHandle' : 'collectionHandle';
                            event = event.set('params', event.get('params').set(prop, value));
                            updateEvent(event);
                          }}
                        />
                      </View>
                    )}
                  </>
                );
              default:
                return <></>;
            }
          })()}
        </View>
      ) : (
        <View style={styles.editorWindow}>
          <EditorSectionHeader name={''} label={'Action ' + (eventId + 1)} />
          <View style={styles.editorInputItem}>
            <RadioGroupControl
              label="Navigate to"
              defaultValue={trigger}
              value={trigger}
              options={triggers}
              disableBinding={true}
              onChange={function (value: string): void {
                // logger.info(value);
                if (value == 'Screen') {
                  event = event.set('method', 'navigate');
                  event = event.set('screenName', 'Home');
                  event = event.set('type', 'page');
                } else if (value == 'URL') {
                  event = event.set('method', 'triggerAction');
                  event = event.set('pluginId', 'Apptile');
                  event = event.set('type', 'action');
                  event = event.set('value', 'openLink');
                  event = event.set('isGlobalPlugin', true);
                  event = event.set('params', event.get('params').set('link', 'https://apptile.com'));
                  setLink('https://apptile.com');
                }
                updateEvent(event);
              }}
            />
          </View>
          {(() => {
            switch (method) {
              case 'triggerAction':
                return (
                  <View style={{paddingBottom: validateUrl(link) ? 15 : 0}}>
                    <CodeInput
                      placeholder="URL"
                      value={link}
                      onChange={(value: string) => {
                        setLink(value);
                        if (validateUrl(value)) {
                          event = event.set('params', event.get('params').set('link', value));
                          updateEvent(event);
                        }
                      }}
                      label={'URL'}
                      name={'URL'}
                    />
                    {!validateUrl(link) && (
                      <Text style={[commonStyles.baseText, commonStyles.errorText]}>Please Enter A Valid Link</Text>
                    )}
                  </View>
                );
              case 'navigate':
                return (
                  <>
                    <View style={styles.rowLayout}>
                      <DropDownControl
                        label="Screens"
                        defaultValue={event.screenName}
                        value={event.screenName}
                        options={screens}
                        nameKey={'name'}
                        valueKey={'value'}
                        disableBinding={true}
                        onChange={function (value: string): void {
                          event = event.set('screenName', value);
                          updateEvent(event);
                        }}
                      />
                    </View>
                    {(event.screenName == 'Collection' || event.screenName == 'Product') && (
                      <View style={styles.rowLayout}>
                        <ShopifyItemHandlePicker
                          name={event.screenName}
                          value={event
                            .get('params')
                            .get(event.screenName == 'Product' ? 'productHandle' : 'collectionHandle')?.startsWith('?event.') ? '' : event
                            .get('params')
                            .get(event.screenName == 'Product' ? 'productHandle' : 'collectionHandle')}
                          label={event.screenName}
                          itemType={event.screenName == 'Product' ? 'product' : 'collection'}
                          onChange={value => {
                            const prop = event.screenName == 'Product' ? 'productHandle' : 'collectionHandle';
                            event = event.set('params', event.get('params').set(prop, value));
                            updateEvent(event);
                          }}
                        />
                      </View>
                    )}
                  </>
                );
              default:
                return <></>;
            }
          })()}
        </View>
      )}
    </View>
  );
};

interface EventInspectorProps {
  rawEvents: Immutable.List<ModuleEventHandlerConfig>;
  pluginId: string;
  pageId: string;
  moduleUUID: string;
}

const EventsInspector: React.FC<EventInspectorProps> = ({rawEvents, pluginId, pageId, moduleUUID}) => {
  const {plugins, queries} = useSelector(selectPageAndGlobalPluginByType);
  const screens = useSelector(selectScreensInNav);

  const dispatch = useDispatch();

  const {pluginConfigSetPathValue: setConfig} = useMemo(
    () => bindActionCreators({pluginConfigUpdatePath, pluginConfigSetPathValue}, dispatch),
    [dispatch],
  );

  const onSetRawValue = useCallback(
    (v: unknown) => {
      setConfig(pluginId, pageId, ['config', 'events'], v);
    },
    [setConfig, pluginId, pageId],
  );

  const [events, setEvents] = useState(rawEvents.map((e: ModuleEventHandlerConfig) => e.get('eventHandler')));
  useEffect(() => {
    setEvents(rawEvents.map((e: ModuleEventHandlerConfig) => e.get('eventHandler')));
  }, [rawEvents]);

  if (!rawEvents) return null;

  const onEventUpdate = useCallback(
    (eventIndex: number, event: EventHandlerConfig) => {
      let newEvents = events.set(eventIndex, event);
      // logger.info(newEvents);
      setEvents(newEvents);
      onSetRawValue(newEvents);
    },
    [events, onSetRawValue],
  );

  return (
    <View style={[{flex: 1}, commonStyles.controlContainer]}>
      {events &&
        events.map((event, eventId) => {
          if (
            rawEvents.get(eventId).get('isExposed') &&
            (event.method == 'navigate' ||
              (event.method == 'triggerAction' && event.pluginId == 'Apptile' && event.value == 'openLink'))
          ) {
            return (
              <EditorEventItem
                key={moduleUUID + '_event_' + eventId}
                {...{
                  eventId,
                  event,
                  plugins,
                  queries,
                  screens,
                }}
                rawEvent={rawEvents.get(eventId)}
                onEventUpdate={onEventUpdate}
              />
            );
          }
        })}
    </View>
  );
};

const styles = StyleSheet.create({
  eventListItem: {
    flex: 1,
    flexDirection: 'column',
    flexBasis: 'auto',
    flexGrow: 1,
    overflow: 'hidden',
    marginBottom: 5,
  },
  eventListingLabel: {
    alignContent: 'center',
    padding: 4,
    flex: 1,
    flexBasis: 'auto',
    fontSize: 10,
    color: '#fff',
    backgroundColor: '#0091bc',
    borderRadius: 4,
    marginHorizontal: 4,
    flexWrap: 'wrap',
    flexGrow: 0,
  },
  eventListingMethod: {
    flex: 1,
    flexGrow: 0,
    fontSize: 10,
    lineHeight: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  eventListingDisplayMethod: {
    justifyContent: 'center',
    flex: 1,
    flexWrap: 'wrap',
    flexShrink: 1,
  },
  addEventRow: {
    flexDirection: 'row',
    margin: 2,
    borderRadius: 4,
    justifyContent: 'flex-end',
  },
  eventListingBox: {
    flexDirection: 'row',
    margin: 2,
    borderRadius: 4,
  },
  eventListingLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    overflow: 'scroll',
  },
  actionLinkText: {
    textAlign: 'right',
    flex: 1,
    flexGrow: 0,
    padding: 5,
    color: '#3c92dc',
    flexBasis: 'auto',
  },
  deleteButton: {margin: 2, padding: 2},
  upIcon: {
    borderRadius: 40,
  },
  downIcom: {
    borderRadius: 40,
    marginLeft: 4,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventItemBox: {flexDirection: 'row'},
  editorWindow: {
    flex: 1,
    backgroundColor: '#fff',
    flexBasis: 'auto',
    flexDirection: 'column',
    flexShrink: 0,
  },
  editorInputItem: {flex: 1, flexDirection: 'column'},
  rowLayout: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 11,
    color: '#333',
    marginRight: 5,
  },
});

export default EventsInspector;
