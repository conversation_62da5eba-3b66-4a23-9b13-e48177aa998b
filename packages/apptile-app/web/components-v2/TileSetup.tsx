import React, {useEffect, useRef, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {useSelector} from 'react-redux';
import _ from 'lodash';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {selectSelectedPluginConfig, selectSelectedPluginPageId} from '@/root/web/selectors/EditorSelectors';
import {selectModuleByUUID} from 'apptile-core';
import {
  EventHandlerConfig,
  ModuleEditorConfig,
  ModuleEventHandlerConfig,
  PluginNamespaceImpl,
} from 'apptile-core';
import {addNamespace} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import PluginPropertyEditor from '../views/propertyInspector/components/PluginPropertyEditor';
import {ModuleInput} from './ModuleInput';
import Tabs from './composite/Tabs';
import EventsInspector from './controls/events/EventsInspector';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const styles = StyleSheet.create({
  heading: {
    marginTop: 24,
    marginBottom: 16,
  },
  topSection: {
    marginTop: 8,
    marginBottom: 28,
    width: '100%',
  },
  sectionSeparator: {
    backgroundColor: '#BFBFBF',
    height: 1,
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
  },
  tabWrapperStyle: {
    padding: 0,
    backgroundColor: '#EFEFEF',
  },
  header: {
    marginVertical: 10,
    width: '100%',
    gap: 8,
  },
  tileName: {
    fontSize: 12,
    marginBottom: 4,
    color: '#B0B0B0',
  },
  delContainer: {
    justifyContent: 'flex-end',
    marginLeft: 5,
  },
});

function TileSetup({moduleConfig, pageId}: any) {
  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const moduleRecord = useSelector((state: any) => selectModuleByUUID(state, moduleUUID));
  const editors = moduleRecord?.get('editors')?.toKeyedSeq().toList().toJS();
  const styleEditors = moduleRecord?.get('styleEditors')?.toKeyedSeq().toList().toJS();
  const basicEditors = moduleRecord?.get('basicEditors')?.toKeyedSeq().toList().toJS();
  return (
    <>
      {basicEditors?.map((editorRecord, key) => {
        if (editorRecord?.mandatory) {
          return <PluginPropertyEditorWrapper key={key} {...{pageId, moduleConfig, editorRecord}} />;
        }
      })}
      {editors?.map((editorRecord, key) => {
        if (editorRecord?.mandatory) {
          return <PluginPropertyEditorWrapper key={key} {...{pageId, moduleConfig, editorRecord}} />;
        }
      })}
      {styleEditors?.map((editorRecord, key) => {
        if (editorRecord?.mandatory) {
          return <PluginPropertyEditorWrapper key={key} {...{pageId, moduleConfig, editorRecord}} />;
        }
      })}
    </>
  );
}

const PluginPropertyEditorWrapper = ({pageId, moduleConfig, editorRecord}: any) => {
  let {label, selector, editorType: propEditor} = editorRecord;

  const propertyName = _.last(selector);
  if (!propEditor) {
    propEditor = {
      name: propertyName,
      type: 'codeInput',
      props: {
        label: label,
        placeholder: '',
      },
    };
  }
  propEditor.name = propertyName;
  propEditor.props.label = label;

  const moduleInstanceConfig = moduleConfig.get('config');
  const childNamespace = moduleInstanceConfig.get('childNamespace');

  const moduleNamespace = new PluginNamespaceImpl(
    (moduleConfig.namespace?.getNamespace() ?? []).concat([childNamespace]),
    moduleConfig.id,
  );
  const pluginId = addNamespace(moduleNamespace, selector[0]);
  const pluginConfig = useSelector(state => selectPluginConfig(state, pageId, pluginId));

  const propertyUpdatePath = selector.slice(1, -1);

  return (
    <PluginPropertyEditor
      key={pluginConfig?.id + propEditor.name + label}
      editor={propEditor}
      entityConfig={pluginConfig}
      config={pluginConfig?.getIn(propertyUpdatePath)}
      pageId={pageId}
      pluginId={pluginId}
      configPathSelector={selector}
      inModule={false}
      hideExposePropButton
      isModule
    />
  );
};

const TileSetupWrapper = () => {
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const pageId = useSelector(state => selectSelectedPluginPageId(state));

  const [seeMore, setSeeMore] = useState(true);
  // const [showSeeMore, setShowSeeMore] = useState(true);

  const tileSetupRef = useRef(null);

  const maxHeight = 150;

  const checkForSeeMore = () => {
    // if (
    //   tileSetupRef?.current?.getBoundingClientRect()?.height &&
    //   tileSetupRef?.current?.getBoundingClientRect()?.height < maxHeight
    // )
      // setShowSeeMore(false);
  };

  if (!moduleConfig) return null;
  return (
    <View>
      <View
        style={[!seeMore ? {maxHeight: maxHeight, overflow: 'hidden'} : {}]}
        ref={tileSetupRef}
        onLayout={checkForSeeMore}>
        {/* <View style={styles.header}>
          <Text style={{fontWeight: '600'}}>Tile Setup</Text>
          <Text style={styles.tileName}>Fill in all the fields to complete tile setup</Text>
        </View> */}
        <TileSetup moduleConfig={moduleConfig} pageId={pageId} />
      </View>
      {/* {showSeeMore && (
        <Pressable onPress={() => setSeeMore(!seeMore)}>
          <Text style={[commonStyles.baseText, {color: theme.CONTROL_ACTIVE_COLOR}]}>
            <MaterialCommunityIcons name={seeMore ? 'chevron-up' : 'chevron-down'} size={14} /> See More
          </Text>
        </Pressable>
      )} */}
    </View>
  );
};

export default TileSetupWrapper;
