import React from 'react';
import {
  GestureResponderEvent,
  Image,
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

import {Icon, MaterialCommunityIcons, Plan} from 'apptile-core';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon';
import { getTheme } from '../../utils/themeSelector';
import Analytics from '@/root/web/lib/segment';
import {IconType} from '../../common/webDatatypes';
import RedDot from './RedDot';
import {useDispatch, useSelector} from 'react-redux';
import {allAvailablePlans} from 'apptile-core';
import {currentPlanFeaturesSelector} from '../../selectors/FeatureGatingSelector';
import {setOpenPremiumModal} from '../../actions/editorActions';
import imagePlanMapping from '../../common/featureGatingConstants';

const theme = getTheme();

type MenuItemP = {
  id: string;
  text: string;
  icon?: string;
  iconType?: IconType;
  isActive?: boolean;
  isComingSoon?: boolean;
  redDot?: boolean;
  onPress?: (event: GestureResponderEvent, id: string) => void;
  premiumBase?: Plan;
  vertical?: boolean;
  textStyles?: StyleProp<TextStyle>;
  wrapperStyles?: StyleProp<ViewStyle>;
};

const styles = StyleSheet.create({
  menuItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 4,
    paddingRight: 4,
    borderRadius: 6,
  },
  menuItemActive: {
    backgroundColor: theme.PRIMARY_OPAQUE_BACKGROUND,
  },
  menuItemIcon: {
    width: 42,
    height: 42,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuItemText: {
    fontFamily: theme.FONT_FAMILY,
    marginLeft: 18,
    fontSize: 16,
  },
  menuItemTextActive: {
    color: theme.PRIMARY_COLOR,
  },
  premiumButton: {
    borderRadius: 6,
    position: 'absolute',
    width: '100%',
    height: '80%',
    justifyContent: 'center',
    alignItems: 'flex-end',
    marginBottom: 12,
    paddingVertical: 4,
    paddingRight: 12,
  },
  wrapper: {
    justifyContent: 'center',
  },
  comingSoonText: {
    color: '#FF9728',
    fontSize: 10,
    border: '1px solid #FF9728',
    marginLeft: '8px',
    borderRadius: 8,
    padding: '4px',
  },
});

type MenuItemProps = MenuItemP;
const MenuItem: React.FC<MenuItemProps> = props => {
  const {
    id,
    text,
    icon,
    iconType,
    isActive,
    onPress,
    redDot,
    premiumBase,
    vertical = false,
    textStyles,
    wrapperStyles,
    isComingSoon,
  } = props;
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isFeatureDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);
  const dispatch = useDispatch();

  const isPremium = premiumBase && !currentPlanFeatures?.includes(premiumBase);

  return (
    <View style={[styles.wrapper, wrapperStyles]}>
      <TouchableOpacity
        style={[
          styles.menuItem,
          vertical && {
            flexDirection: 'column',
            alignItems: 'center',
            paddingVertical: 8,
            paddingHorizontal: 4,
            minHeight: 70,
          },
          vertical && isActive ? styles.menuItemActive : null,
        ]}
        onPress={e => onPress?.(e, id)}>
        <View style={styles.menuItemIcon}>
          {iconType === 'ApptileWebIcons' ? (
            <ApptileWebIcon name={icon} size={23} color={isActive ? '#1060E0' : theme.DEFAULT_COLOR} />
          ) : (
            <Icon iconType={iconType} size={23} name={icon} color={isActive ? '#1060E0' : theme.DEFAULT_COLOR} />
          )}
        </View>
        <Text
          nativeID={id ? id : ''}
          numberOfLines={vertical ? 2 : undefined}
          ellipsizeMode={vertical ? 'tail' : undefined}
          style={[
            styles.menuItemText,
            vertical && {
              marginLeft: 0,
              textAlign: 'center',
              fontSize: 12,
              lineHeight: 14,
              marginTop: 4,
              maxWidth: 75,
            },
            isActive ? styles.menuItemTextActive : null,
            textStyles,
          ]}>
          {text}
        </Text>
        {isComingSoon && <Text style={styles.comingSoonText}>Coming Soon</Text>}
        {redDot && <RedDot />}
      </TouchableOpacity>
      {isPremium && isFeatureDisabled && (
        <Pressable
          onPress={() => {
            dispatch(setOpenPremiumModal(true, premiumBase));
            Analytics.track('purchase:upgradePopup_viewed', {
              pricingFlow: 'Analytics',
            });
          }}
          style={styles.premiumButton}>
          <Image
            source={imagePlanMapping[premiumBase]}
            resizeMode="contain"
            style={{width: 78, height: 20, right: 20}}
          />
        </Pressable>
      )}
    </View>
  );
};

// const Menu = React.forwardRef<View, MenuProps>((props, ref) => {
//   const {} = props;
//   return null;
// });

export default MenuItem;
