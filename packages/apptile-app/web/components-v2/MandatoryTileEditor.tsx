import React, {useEffect} from 'react';
import {StyleSheet, Text, View, Image} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import _ from 'lodash';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import theme from '@/root/web/styles-v2/theme';
import {selectSelectedPluginConfig, selectSelectedPluginPageId} from '@/root/web/selectors/EditorSelectors';
import {selectModuleByUUID} from 'apptile-core';
import {
  EventHandlerConfig,
  ModuleEditorConfig,
  ModuleEventHandlerConfig,
  PluginNamespaceImpl,
} from 'apptile-core';
import {addNamespace} from 'apptile-core';
import {selectPluginConfig} from 'apptile-core';
import PluginPropertyEditor from '../views/propertyInspector/components/PluginPropertyEditor';
import {ModuleInput} from './ModuleInput';
import Tabs from './composite/Tabs';
import EventsInspector from './controls/events/EventsInspector';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import VariantSelection from './VariantSelection';
import { EditorRootState } from '../store/EditorRootState';
import {useParams} from 'react-router';
import { fillModuleMandtoryFields } from '../actions/editorActions';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


const styles = StyleSheet.create({
  heading: {
    marginTop: 24,
    marginBottom: 16,
  },
  topSection: {
    marginTop: 8,
    marginBottom: 28,
    width: '100%',
  },
  sectionSeparator: {
    backgroundColor: '#BFBFBF',
    height: 1,
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
  },
  tabWrapperStyle: {
    padding: 0,
    backgroundColor: '#EFEFEF',
  },
  header: {
    flexDirection: 'row',
    marginVertical: 15,
    width: '100%',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  tileName: {
    fontSize: 12,
    marginBottom: 4,
    color: '#B0B0B0',
  },
  delContainer: {
    justifyContent: 'flex-end',
    marginLeft: 5,
  },
  tileLoaderCont: {
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tileLoader: {
    width: 229,
    height: 219,
    backgroundColor: theme.PRIMARY_OPAQUE_BACKGROUND,
    paddingVertical: 58,
    paddingHorizontal: 13,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
  },
  loaderImage: {
    width: 60,
    height: 60,
  },
});

function MandatoryTileEditor({moduleConfig, pageId}: any) {
  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const moduleRecord = useSelector((state: any) => selectModuleByUUID(state, moduleUUID));
  const editors = moduleRecord?.get('editors')?.toKeyedSeq().toList().toJS();
  const styleEditors = moduleRecord?.get('styleEditors')?.toKeyedSeq().toList().toJS();
  const basicEditors = moduleRecord?.get('basicEditors')?.toKeyedSeq()?.toList()?.toJS();
  return (
    <>
      <View style={styles.topSection}>
        <Text style={[styles.heading, commonStyles.heading]}>Mandatory Fields</Text>
        <Text>Fill These Fields Before going forward</Text>
      </View>
      {editors?.map((editorRecord, key) => {
        if (editorRecord?.mandatory) {
          return <PluginPropertyEditorWrapper key={key} {...{pageId, moduleConfig, editorRecord}} />;
        }
      })}
      {basicEditors?.map((editorRecord, key) => {
        if (editorRecord?.mandatory) {
          return <PluginPropertyEditorWrapper key={key} {...{pageId, moduleConfig, editorRecord}} />;
        }
      })}
      {styleEditors?.map((editorRecord, key) => {
        if (editorRecord?.mandatory) {
          return <PluginPropertyEditorWrapper key={key} {...{pageId, moduleConfig, editorRecord}} />;
        }
      })}
    </>
  );
}

const TileLoader = ()=> {
  return <View style={styles.tileLoaderCont}>
     <View style={styles.tileLoader}>
        <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
        <Text style={[commonStyles.heading, {marginTop: 6}]}>SETTING UP YOUR TILE</Text>
     </View>
  </View>
}


const PluginPropertyEditorWrapper = ({pageId, moduleConfig, editorRecord}: any) => {
  let {label, selector, editorType: propEditor} = editorRecord;

  const propertyName = _.last(selector);
  if (!propEditor) {
    propEditor = {
      name: propertyName,
      type: 'codeInput',
      props: {
        label: label,
        placeholder: '',
      },
    };
  }
  propEditor.name = propertyName;
  propEditor.props.label = label;

  const moduleInstanceConfig = moduleConfig.get('config');
  const childNamespace = moduleInstanceConfig.get('childNamespace');

  const moduleNamespace = new PluginNamespaceImpl(
    (moduleConfig.namespace?.getNamespace() ?? []).concat([childNamespace]),
    moduleConfig.id,
  );
  const pluginId = addNamespace(moduleNamespace, selector[0]);
  const pluginConfig = useSelector(state => selectPluginConfig(state, pageId, pluginId));

  const propertyUpdatePath = selector.slice(1, -1);

  return (
    <PluginPropertyEditor
      key={pluginConfig?.id + propEditor.name + label}
      editor={propEditor}
      entityConfig={pluginConfig}
      config={pluginConfig?.getIn(propertyUpdatePath)}
      pageId={pageId}
      pluginId={pluginId}
      configPathSelector={selector}
      inModule={false}
      hideExposePropButton
      isModule
    />
  );
};

const MandatoryTileEditorWrapper = ({isDeletable, onDelete}) => {
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const pageId = useSelector(state => selectSelectedPluginPageId(state));

  const editorOpacity = useSharedValue(0);
  useEffect(() => {
    if (!moduleConfig?.id) return;
    editorOpacity.value = withTiming(0.5, {duration: 380, easing: Easing.bezier(0.25, 0.1, 0.25, 1)}, () => {
      editorOpacity.value = withTiming(1, {duration: 380, easing: Easing.bezier(0.25, 0.1, 0.25, 1)});
    });
  }, [moduleConfig?.id, editorOpacity]);
  const editorAnimation = useAnimatedStyle(() => ({opacity: editorOpacity.value}));

  if (!moduleConfig) return null;
  const moduleInstanceConfig = moduleConfig?.get('config');
  const moduleUUID = moduleInstanceConfig?.get('moduleUUID');
  const {moduleAutoFilling} = useSelector((state: EditorRootState) => state?.platform);
  const tiles = moduleAutoFilling[pageId];
  const tileMandatoryStatus = tiles?.[moduleConfig?.id] ?? {};
  const {filled, filling, failed} = tileMandatoryStatus;
  const {id}= useParams();
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(fillModuleMandtoryFields(pageId, moduleConfig?.id, moduleUUID, false, id, false));
  }, [])
  return (
    <Animated.View style={[{flex: 1, overflow: 'scroll'}, editorAnimation]}>
      <View style={styles.header}>
        <View>
          <Text style={styles.tileName}>Tile Name</Text>
          <Text style={{fontWeight: '600'}}>{moduleConfig?.config?.get('moduleName') ?? ''}</Text>
        </View>
        <View style={styles.delContainer}>
          {isDeletable && (
            <MaterialCommunityIcons name="trash-can-outline" color="#D80707" size={20} onPress={onDelete} />
          )}
        </View>
      </View>
      {!filling ?
        <>
          <View style={styles.sectionSeparator} />
          <MandatoryTileEditor moduleConfig={moduleConfig} pageId={pageId} />
          <VariantSelection key={`${moduleUUID}_Variants_Mandatory`} />
        </> : <TileLoader />
      }
    </Animated.View>
  );
};

export default MandatoryTileEditorWrapper;
