import WebSDKApi from './api/WebSDKApi';
import {pluginEditorComponents, pluginEditorComponentsV2} from './components/pluginEditorComponents';
import {shouldInitWebLogrocket} from './globalvariables';
import React from 'react';
console.log('glvarcheck', shouldInitWebLogrocket);
import websdk from './websdk';
import * as ReactRedux from 'react-redux';
import {main} from './reactLauncher';
import NavigationTree from './components-v2/NavigationTree';

import livelyScript from './lively.txt';
import shoppableFeedScript from './lively-shoppableFeed.txt';
import {selectScreensInNavWithPath} from './selectors/EditorSelectors';
import {addNewPage, navigateToPage} from './actions/editorActions';
import {activeNavigationSelector, deletePageConfig, navComponentDelete} from 'apptile-core';
import {selectMandatoryCheck} from './selectors/EditorModuleSelectors';

window.apptileWebSDK = window.apptileWebSDK || {status: 'notstarted'};
window.apptileWebSDK.React = React;
window.apptileWebSDK.ApptileWebCore = websdk;
window.apptileWebSDK.ReactRedux = ReactRedux;
window.apptileWebSDK.ReactRouter = require('react-router');
window.apptileWebSDK.ReactRouterDom = require('react-router-dom');

loadWebSDKBundle().finally(() => {
  window.addEventListener('keypress', ev => {
    if (ev.ctrlKey && ev.key === 'u') {
      console.log('Loading web sdk bundle');
      loadWebSDKBundle();
    }
  });

  main();
});

const _reloadLively = new Function(livelyScript);
window.reloadLively = () => {
  try {
    _reloadLively();
  } catch (err) {
    logger.error('Failed when reloading lively: ', err);
  }
};

const _reloadShoppableFeeds = new Function(shoppableFeedScript);
window.reloadShoppableFeeds = () => {
  try {
    _reloadShoppableFeeds();
  } catch (err) {
    logger.error('Failed when reloading shoppablefeeds: ', err);
  }
};

const switchShoplazzaPluginEditors = () => {
  if (window.apptileWebSDK.moduleExports) {
    const keys = Object.keys(window.apptileWebSDK.moduleExports?.functions?.externalEditorsV1 ?? {});
    for (let i = 0; i < keys.length; ++i) {
      const key = keys[i];
      pluginEditorComponents[key] = window.apptileWebSDK.moduleExports?.functions.externalEditorsV1[key];
    }
    const keys2 = Object.keys(window.apptileWebSDK.moduleExports?.functions?.externalEditorsV2 ?? {});
    for (let i = 0; i < keys.length; ++i) {
      const key2 = keys2[i];
      pluginEditorComponentsV2[key2] = window.apptileWebSDK.moduleExports?.functions.externalEditorsV2[key2];
    }
  }
};

export const brandOnboardingPageConfig = () => {
  if (window.apptileWebSDK.moduleExports?.components?.brandOnboardingPageConfig) {
    return window.apptileWebSDK.moduleExports?.components?.brandOnboardingPageConfig;
  } else {
    return require('@/root/web/assets/extras/themeOnboardingPage.json');
  }
};

export const navigationTreeComponent = () => {
  if (window.apptileWebSDK.moduleExports?.components?.navigationTreeComponent) {
    const PartnerNavigationTree = window.apptileWebSDK.moduleExports?.components?.navigationTreeComponent;
    return () =>
      React.createElement(PartnerNavigationTree, {
        selectScreensByPath: selectScreensInNavWithPath,
        dispatchAddNewPage: addNewPage,
        dispatchNavigateToPage: navigateToPage,
        dispatchDeletePageConfig: deletePageConfig,
        dispatchDeleteNavComponent: navComponentDelete,
        selectActiveNavigation: activeNavigationSelector,
        selectMandatoryCheck: selectMandatoryCheck,
      });
  } else {
    return () => React.createElement(NavigationTree);
  }
};

export async function loadWebSDKBundle() {
  let cdnLink;
  if (window.PLUGIN_SERVER_URL) {
    cdnLink = `${window.PLUGIN_SERVER_URL}/webSDK/currentWebSdkBundle`;
  } else if (_.isNull(global.WEB_SDK_ARTIFACT_ID) || global.WEB_SDK_ARTIFACT_ID === 'null') {
    cdnLink = await WebSDKApi.fetchCurrentLiveSDKBundle();
  } else {
    cdnLink = await WebSDKApi.fetchBundleWithArtifactId(global.WEB_SDK_ARTIFACT_ID);
  }

  if (cdnLink) {
    if (cdnLink) {
      window.apptileWebSDK.status = 'loading';
      return fetch(cdnLink)
        .then(res => res.text())
        .then(code => {
          const compiled = new Function(code);
          compiled();
          console.log('========= Web SDK bundle loaded =========');
          switchShoplazzaPluginEditors();
          window.apptileWebSDK.status = 'success';
          window.dispatchEvent(new Event('webSDKUpdated'));
        })
        .catch(err => {
          window.apptileWebSDK.status = 'error';
          console.error('error when loading web sdk: ', err);
        });
    } else {
      window.apptileWebSDK.status = 'success';
    }
  } else {
    window.apptileWebSDK.status = 'success';
  }
}
