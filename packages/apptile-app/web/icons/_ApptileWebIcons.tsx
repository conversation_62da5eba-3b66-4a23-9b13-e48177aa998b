/**
 * ApptileWebIcons icon set component.
 * Usage: <ApptileWebIcons name="icon-name" size={20} color="#4F8EF7" />
 */

import createIconSet from 'react-native-vector-icons/lib/create-icon-set';
const glyphMap = {
  "TRBL-button-top": 59905,
  "TRBL-button": 59906,
  "accordion": 59907,
  "alignment-bottom": 59908,
  "alignment-center": 59909,
  "alignment-left": 59910,
  "alignment-middle": 59911,
  "alignment-right": 59912,
  "alignment-top": 59913,
  "analytics-outline": 59914,
  "apptile-live": 59915,
  "back-arrow": 59916,
  "badge": 59917,
  "bell-outline": 59918,
  "book-outline": 59919,
  "border-radius-left": 59920,
  "border-radius": 59921,
  "brands-outline": 59922,
  "button": 59923,
  "calender": 59924,
  "checkbox": 59925,
  "clock-refresh": 59926,
  "clock": 59927,
  "container": 59928,
  "datasource": 59929,
  "delete": 59930,
  "down-arrow": 59931,
  "download-outline": 59932,
  "edit-icon": 59933,
  "expression": 59934,
  "gear-outline": 59935,
  "go-live": 59936,
  "help": 59937,
  "house-outline": 59938,
  "icon": 59939,
  "image-carousel": 59940,
  "image-list": 59941,
  "image": 59942,
  "integration-disconnect": 59943,
  "integrations": 59944,
  "list-view": 59945,
  "local-storage": 59946,
  "magnify": 59947,
  "menu-vertical": 59948,
  "modal": 59949,
  "notifications": 59950,
  "pages-outline": 59951,
  "pages": 59952,
  "percentage-outline": 59953,
  "pills": 59954,
  "pin-fill": 59955,
  "pin-outline": 59956,
  "query": 59957,
  "radio-button": 59958,
  "rich-text": 59959,
  "select": 59960,
  "settings": 59961,
  "slider-range": 59962,
  "small-shop": 59963,
  "snapshots-outline": 59964,
  "star-rating": 59965,
  "state": 59966,
  "switch": 59967,
  "text-input": 59968,
  "text": 59969,
  "themes": 59970,
  "tiles-old": 59971,
  "tiles": 59972,
  "timer": 59973,
  "video-player": 59974,
  "web-view": 59975,
  "your-design": 59976
};

const iconSet = createIconSet(glyphMap, 'ApptileWebIcons', 'ApptileWebIcons.ttf');

export default iconSet;
export const {
  Button,
  getImageSource,
  getImageSourceSync,
} = iconSet;

