import lightTheme from '../styles-v2/theme';
import darkTheme from '../views/prompt-to-app/styles-prompt-to-app/theme';
import lightCommonStyles from '../styles-v2/commonStyles';
import darkCommonStyles from '../views/prompt-to-app/styles-prompt-to-app/commonStyles';
import {APPTILE_TILE_ENV} from '../../.env.json';

export const getTheme = () => {
  if (window.location.href.includes('studio')) {
    return lightTheme;
  }
  return APPTILE_TILE_ENV === 'staging' ? darkTheme : lightTheme;
};

export const getCommonStyles = () => {
  if (window.location.href.includes('studio')) {
    return lightCommonStyles;
  }
  return APPTILE_TILE_ENV === 'staging' ? darkCommonStyles : lightCommonStyles;
};
