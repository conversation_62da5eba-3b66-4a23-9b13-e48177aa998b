import {
  isJ<PERSON><PERSON>ing, 
  NAMESPACE_SPERATOR, 
  GetRegisteredPlugin, 
  Selector, 
  strsel,
  AppConfig,
  AppModelType,
  ModuleEventHandlerConfig,
  NavigatorConfig,
  PageConfig,
  PluginConfigType,
  ScreenConfig,
  selectAppModel
} from 'apptile-core';
import {ApptileMFAuthDsName, ApptileMFAuthenticationDsName} from 'apptile-datasource';
import {DatasourceQueryDetail} from 'apptile-core';
import Immutable from 'immutable';
import _ from 'lodash';
import {createSelector} from 'reselect';

import {EditorRootState} from '../store/EditorRootState';

export const selectSelectedPluginPageId = createSelector(
  (state: EditorRootState) => state.editor.selectedPluginConfigSel,
  (state: EditorRootState) => state.appModel,
  (selector: Selector, appModel: AppModelType) => {
    if (!selector) return null;
    let pageId: string | null = null;
    if (selector.length > 1) {
      pageId = appModel.getPageId(selector[0]);
    }
    return pageId;
  },
);

export const selectCurrentVisiblePageId = (state: EditorRootState) => state.activeNavigation.activePageId;
export const selectCurrentVisiblePageKey = (state: EditorRootState) => state.activeNavigation.activePageKey;

export const selectEditorState = (state: EditorRootState) => state.editor;
export const selectSelectedPluginSelector = createSelector(selectEditorState, editor => editor.selectedPluginConfigSel);
export const selectNavComponentSelector = createSelector(selectEditorState, editor => editor.selectedNavComponentSel);
export const selectSelectedPageId = createSelector(selectEditorState, editor => editor.selectedPageId);

export const selectCurrentAttachmentId = createSelector(selectEditorState, editor => editor.activeAttachmentId);
export const selectCurrentAttachmentKey = createSelector(selectEditorState, editor => editor.activeAttachmentKey);

export const selectSelectedPluginConfig = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  (state: EditorRootState) => state.editor.selectedPluginConfigSel,
  (state: EditorRootState) => state.appModel,
  (appConfig?: AppConfig, selector?: Selector | null, appModel: AppModelType): PluginConfigType<any> => {
    if (!selector) return null;
    let pageId: string;
    if (selector.length > 1) {
      pageId = appModel.getPageId(selector[0]);
      const pluginConfig = appConfig?.getPage(pageId)?.getPluginId(selector[selector.length - 1]);
      return pluginConfig;
    }
    if (selector.length == 1) {
      const pluginConfig = appConfig?.getPlugin(selector[0]);
      return pluginConfig;
    }
    return null;
  },
);

export const selectSelectedNavComponent = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  selectNavComponentSelector,
  (appConfig?: AppConfig, selector?: Selector | null): ScreenConfig | NavigatorConfig => {
    if (!selector) return null;
    return appConfig?.navigation.getConfig(selector);
  },
);

export const selectSelectedPageConfig = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  selectSelectedPageId,
  (appConfig?: AppConfig, selectedPageId?: string | null): PageConfig => {
    if (!selectedPageId) return null;
    return appConfig?.pages.get(selectedPageId);
  },
);

export function getScreensInNavConfig(navConfig: NavigatorConfig): ScreenConfig[] {
  const screens = [];
  navConfig.screens.forEach(config => {
    if (config.type === 'screen') {
      screens.push(config);
    } else {
      const childScreens = getScreensInNavConfig(config);
      childScreens.forEach(screen => screens.push(screen));
    }
  });
  return screens;
}

export function getScreensInNavConfigWithPath(navConfig: NavigatorConfig, path: Array<string>): ScreenConfig[] {
  const screens = [];
  navConfig.screens.forEach(config => {
    if (config.type === 'screen') {
      screens.push({config, path: [...path, config.name]});
    } else {
      const childScreens = getScreensInNavConfigWithPath(config, [...path, config.name]);
      childScreens.forEach(screen => screens.push(screen));
    }
  });

  return screens;
}

export const selectScreensInNav = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  (appConfig?: AppConfig, selector?: Selector | null) => {
    const rootNavigator = appConfig?.navigation.rootNavigator;
    if (!rootNavigator) return [];
    return getScreensInNavConfig(rootNavigator);
  },
);

export const selectScreensInNavWithPath = selector =>
  createSelector(
    (state: EditorRootState) => state.appConfig.current,
    (appConfig?: AppConfig) => {
      const rootNavigator = appConfig?.navigation.rootNavigator;
      if (!rootNavigator) return [];
      return getScreensInNavConfigWithPath(rootNavigator, selector ?? []);
    },
  );

export const selectActiveScreen = createSelector(
  selectScreensInNav,
  (state: EditorRootState) => state.activeNavigation?.activePageId,
  (screens, activePageId) => {
    return screens?.filter((e: ScreenConfig) => e.name === activePageId);
  },
);

export const selectActiveScreenParams = createSelector(
  selectAppModel,
  (state: EditorRootState) => state.activeNavigation?.activePageKey,
  (appModel, activePageKey) => {
    const pageModel = appModel.getModelValue([activePageKey]);
    if (!pageModel) return undefined;
    return pageModel.get('params');
  },
);

export const selectParentModulePlugin = createSelector(
  (state: EditorRootState, pageId: string, pluginId: string) => state.appConfig.current,
  (state: EditorRootState, pageId: string, pluginId: string) => pageId,
  (state: EditorRootState, pageId: string, pluginId: string) => pluginId,
  (appConfig?: AppConfig, pageId: string, pluginId: string): PluginConfigType<any> | null => {
    const pluginConfig: PluginConfigType = pageId
      ? appConfig?.getIn(['pages', pageId, 'plugins', pluginId])
      : appConfig?.getIn([pluginId]);
    if (pageId && pluginConfig && !_.isEmpty(pluginConfig.namespace?.getNamespace())) {
      const namespaceArr = pluginConfig.namespace?.getNamespace();
      const namespacePrefix = namespaceArr.join(NAMESPACE_SPERATOR);
      const pagePlugins = appConfig?.getPagePlugins(pageId);
      for (let [pagePluginId, pagePluginConfig] of pagePlugins) {
        if (
          pagePluginConfig.subtype === 'ModuleInstance' &&
          pagePluginConfig.config.get('childNamespace') === namespacePrefix
        ) {
          return pagePluginConfig;
        }
      }
    }
    return null;
  },
);

export const selectParentModuleEvents = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  selectParentModulePlugin,
  (appConfig?: AppConfig, pagePluginConfig: PluginConfigType<any> | null): string[] => {
    if (pagePluginConfig) {
      const moduleUUID = pagePluginConfig.config.get('moduleUUID');
      const moduleRecord = appConfig?.modules.get(moduleUUID);
      return moduleRecord?.events.slice() || [];
    }
    return [];
  },
);

export const selectIsModuleProperty = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  selectParentModulePlugin,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => nonNamespacedPluginId,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => configPathSelector,
  (
    appConfig?: AppConfig,
    pagePluginConfig: PluginConfigType<any> | null,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ): boolean => {
    if (pagePluginConfig) {
      const moduleUUID = pagePluginConfig.config.get('moduleUUID');
      const moduleRecord = appConfig?.modules.get(moduleUUID);
      if (moduleRecord)
        return moduleRecord.editors.has(strsel([nonNamespacedPluginId].concat(configPathSelector?.slice(1))));
    }
    return false;
  },
);

export const selectIsModuleStyleProperty = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  selectParentModulePlugin,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => nonNamespacedPluginId,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => configPathSelector,
  (
    appConfig?: AppConfig,
    pagePluginConfig: PluginConfigType<any> | null,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ): boolean => {
    if (pagePluginConfig) {
      const moduleUUID = pagePluginConfig.config.get('moduleUUID');
      const moduleRecord = appConfig?.modules.get(moduleUUID);
      if (moduleRecord)
        return moduleRecord.styleEditors.has(strsel([nonNamespacedPluginId].concat(configPathSelector?.slice(1))));
    }
    return false;
  },
);

export const selectIsModuleBasicProperty = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  selectParentModulePlugin,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => nonNamespacedPluginId,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => configPathSelector,
  (
    appConfig?: AppConfig,
    pagePluginConfig: PluginConfigType<any> | null,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ): boolean => {
    if (pagePluginConfig) {
      const moduleUUID = pagePluginConfig.config.get('moduleUUID');
      const moduleRecord = appConfig?.modules.get(moduleUUID);
      if (moduleRecord)
        return moduleRecord.basicEditors?.has(strsel([nonNamespacedPluginId].concat(configPathSelector?.slice(1)))) ?? false;
    }
    return false;
  },
);

export const selectModuleDefaultEvent = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  selectParentModulePlugin,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => nonNamespacedPluginId,
  (
    state: EditorRootState,
    pageId: string,
    pluginId: string,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ) => configPathSelector,
  (
    appConfig?: AppConfig,
    pagePluginConfig: PluginConfigType<any> | null,
    nonNamespacedPluginId: string,
    configPathSelector: Selector,
  ): Immutable.List<ModuleEventHandlerConfig> => {
    if (pagePluginConfig) {
      const moduleUUID = pagePluginConfig.config.get('moduleUUID');
      const moduleRecord = appConfig?.modules.get(moduleUUID);
      if (moduleRecord) return moduleRecord.defaultEventHandlers;
    }
    return Immutable.List();
  },
);

export const selectModuleDefaultEvents = createSelector(
  (state: EditorRootState, moduleUUID: string) => state.appConfig.current,
  (state: EditorRootState, moduleUUID: string) => moduleUUID,
  (appConfig: AppConfig, moduleUUID: string): Immutable.List<ModuleEventHandlerConfig> => {
    if (appConfig) {
      const moduleRecord = appConfig?.modules.get(moduleUUID);
      if (moduleRecord) return moduleRecord.defaultEventHandlers;
    }
    return Immutable.List();
  },
);

export const selectExposedEvent = createSelector(
  (state: EditorRootState, moduleUUID: string, eventId: number) => state.appConfig.current,
  (state: EditorRootState, moduleUUID: string, eventId: number) => moduleUUID,
  (state: EditorRootState, moduleUUID: string, eventId: number) => eventId,
  (appConfig: AppConfig, moduleUUID: string, eventId: number): boolean => {
    if (appConfig) {
      const moduleRecord = appConfig?.modules.get(moduleUUID);
      if (moduleRecord) return moduleRecord.defaultEventHandlers?.get(eventId);
    }
    return null;
  },
);

export const selectAppAssetPlugins = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  (appConfig?: AppConfig): Record<string, string[]> => {
    const assetPluginMapping: Record<string, string[]> = {};
    if (!appConfig) return assetPluginMapping;

    appConfig?.pages
      .toSeq()
      .toMap()
      .forEach((pageConfig, pageId) => {
        pageConfig?.plugins
          .toSeq()
          .toMap()
          .forEach((pluginConfig, pluginId) => {
            const assetId = pluginConfig.getIn(['config', 'assetId']) as string;
            const pluginSelector: string[] = [pageId, 'plugins', pluginId];

            if (assetId && (typeof assetId === 'string' && !isJSBinding(assetId))) {
              assetPluginMapping[assetId] = assetPluginMapping[assetId]
                ? assetPluginMapping[assetId].concat([strsel(pluginSelector)])
                : [strsel(pluginSelector)];
            }
          });
      });
    // TODO: We might need to do above iteration headers as well when headers PR is merged
    // TODO: iterate over the module plugins once tiles PR is merged
    return assetPluginMapping;
  },
);

export const selectQueryPluginDataSourceQueries = createSelector(
  (state: EditorRootState, pageId: string, pluginId: string) => state.appConfig.current,
  (state: EditorRootState, pageId: string, pluginId: string) => pageId,
  (state: EditorRootState, pageId: string, pluginId: string) => pluginId,
  (appConfig?: AppConfig, pageId: string, pluginId: string): Record<string, DatasourceQueryDetail> | null => {
    appConfig?.getPagePlugins;
    const queryPluginConfig: PluginConfigType<any> = pageId
      ? appConfig?.getIn(['pages', pageId, 'plugins', pluginId])
      : appConfig?.getIn([pluginId]);
    const datasourceId = queryPluginConfig?.getIn(['config', 'datasource']) as string;
    if (!datasourceId) return;
    const dsConfig = appConfig.getPlugin(datasourceId);

    if (!dsConfig) return;
    const dsModel = GetRegisteredPlugin(dsConfig.subtype);
    return dsModel?.getQueries();
  },
);

export const selectQueryPluginInputParameters = createSelector(
  (state: EditorRootState, pageId: string, pluginId: string) => state.appConfig.current,
  (state: EditorRootState, pageId: string, pluginId: string) => pageId,
  (state: EditorRootState, pageId: string, pluginId: string) => pluginId,
  (state: EditorRootState, pageId: string, pluginId: string, queryName: string) => queryName,
  (
    appConfig?: AppConfig,
    pageId: string,
    pluginId: string,
    queryName: string,
  ): Record<string, DatasourceQueryDetail> | null => {
    appConfig?.getPagePlugins;
    const queryPluginConfig: PluginConfigType<any> = pageId
      ? appConfig?.getIn(['pages', pageId, 'plugins', pluginId])
      : appConfig?.getIn([pluginId]);
    const datasourceId = queryPluginConfig?.getIn(['config', 'datasource']) as string;
    if (!datasourceId) return;
    // const queryName = queryPluginConfig?.getIn(['config', 'queryName']) as string;
    if (!queryName) return;
    const dsConfig = appConfig.getPlugin(datasourceId);

    if (!dsConfig) return;
    const dsModel = GetRegisteredPlugin(dsConfig.subtype);
    return dsModel.getQueryInputParams(queryName);
  },
);

export const selectApptileMFAuthDsConfig = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  (appConfig?: AppConfig) => {
    const datasourcePluginConfig = appConfig?.plugins?.find(
      (v: any) => v.subtype === ApptileMFAuthDsName,
    ) as PluginConfigType<any>;
    if (!datasourcePluginConfig) return;
    return datasourcePluginConfig;
  },
);

export const selectApptileMFAuthenticationDsConfig = createSelector(
  (state: EditorRootState) => state.appConfig.current,
  (appConfig?: AppConfig) => {
    const datasourcePluginConfig = appConfig?.plugins?.find(
      (v: any) => v.subtype === ApptileMFAuthenticationDsName,
    ) as PluginConfigType<any>;
    if (!datasourcePluginConfig) return;
    return datasourcePluginConfig;
  },
);

export const selectApptileMFAuthDsModel = createSelector(selectApptileMFAuthDsConfig, datasourcePluginConfig => {
  if (!datasourcePluginConfig) return;
  const dsModel = GetRegisteredPlugin(datasourcePluginConfig?.subtype);
  return dsModel;
});

export const selectApptileMFAuthenticationDsModel = createSelector(
  selectApptileMFAuthenticationDsConfig,
  datasourcePluginConfig => {
    if (!datasourcePluginConfig) return;
    const dsModel = GetRegisteredPlugin(datasourcePluginConfig?.subtype);
    return dsModel;
  },
);

export const selectApptileMFAuthDsQueries = createSelector(selectApptileMFAuthDsModel, datasourceModel => {
  if (!datasourceModel) return;
  return datasourceModel?.getQueries();
});

export const selectApptileMFAutenticationhDsQueries = createSelector(
  selectApptileMFAuthenticationDsModel,
  datasourceModel => {
    if (!datasourceModel) return;
    return datasourceModel?.getQueries();
  },
);
