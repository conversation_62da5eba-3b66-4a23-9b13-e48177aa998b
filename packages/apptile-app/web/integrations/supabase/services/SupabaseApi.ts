import axios from 'axios';
import IntegrationsApi from '../../../api/IntegrationsApi';
import {store} from 'apptile-core';
import {EditorRootState} from '../../../store/EditorRootState';
import _ from 'lodash';

// Define the Supabase project interface
export interface SupabaseProject {
  id: string;
  name: string;
  organization_id: string;
  region: string;
  created_at: string;
  status: string;
  isConnected?: boolean;
}

// Define the Supabase API class
export class SupabaseApi {
  private static baseURL = 'https://api.supabase.com';
  static proxyBaseURL = `${window.PLUGIN_SERVER_URL}/cli/supabase-proxy`;
  private static isRefreshing = false;
  private static refreshSubscribers: Array<(token: string) => void> = [];

  /**
   * Subscribe to token refresh
   * @param callback Function to call when token is refreshed
   */
  private static subscribeToRefresh(callback: (token: string) => void) {
    this.refreshSubscribers.push(callback);
  }

  /**
   * Notify all subscribers that token has been refreshed
   * @param token The new access token
   */
  private static notifyRefreshSubscribers(token: string) {
    this.refreshSubscribers.forEach(callback => callback(token));
    this.refreshSubscribers = [];
  }

  /**
   * Refresh the Supabase access token
   * @param appId The Apptile app ID
   */
  static async refreshToken(appId: string): Promise<string> {
    // If already refreshing, return the existing promise
    if (this.isRefreshing) {
      return new Promise<string>(resolve => {
        this.subscribeToRefresh(token => {
          resolve(token);
        });
      });
    }

    this.isRefreshing = true;

    try {
      // Call the refresh token endpoint
      const response = await axios.post(`${this.proxyBaseURL}/refresh-token`, {appId});

      // Extract the new token from the response
      const newToken = response.data.message ? response.data.access_token || '' : '';

      // Notify subscribers about the new token
      this.notifyRefreshSubscribers(newToken);

      this.isRefreshing = false;

      return newToken;
    } catch (error) {
      this.isRefreshing = false;
      console.error('Error refreshing token:', error);
      throw new Error('Failed to refresh token');
    }
  }

  /**
   * Get the current app ID from the Redux store
   */
  static getCurrentAppId(): string {
    // Get the current app ID from the Redux store
    const state = store.getState() as EditorRootState;
    return state.apptile.appId;
  }

  /**
   * List all Supabase projects for the user
   * @param accessToken The Supabase access token
   */
  static async listProjects(accessToken: string): Promise<SupabaseProject[]> {
    try {
      // Use the proxy route instead of direct API call
      const response = await axios.get(`${this.proxyBaseURL}/projects`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error: any) {
      // Check if the error is a 401 Unauthorized
      if (error.response && error.response.status === 401) {
        console.log('Received 401 error, refreshing token...');
        try {
          // Get the current app ID
          const appId = this.getCurrentAppId();

          // Refresh the token
          const newToken = await this.refreshToken(appId);
          console.log('Token refreshed successfully, retrying request');

          // Retry the request with the new token
          const retryResponse = await axios.get(`${this.proxyBaseURL}/projects`, {
            headers: {
              Authorization: `Bearer ${newToken}`,
              'Content-Type': 'application/json',
            },
          });

          return retryResponse.data;
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);
          throw new Error('Failed to refresh token and list Supabase projects');
        }
      }

      // For other errors, just throw
      console.error('Error listing Supabase projects:', error);
      throw new Error('Failed to list Supabase projects');
    }
  }

  /**
   * Get the connected Supabase project for the app
   * @param appId The Apptile app ID
   */
  static async getConnectedProject(appId: string): Promise<SupabaseProject | null> {
    try {
      // Find the Supabase integration
      const supabaseIntegration = await SupabaseApi.getSupabaseIntegration(appId);
      if (!supabaseIntegration) {
        return null;
      }

      //Get credentials
      const creds = (await IntegrationsApi.fetchAppIntegrationSecrets(appId, 'supabase')).data;
      // Return the project details

      //Getting the project name from the projects list
      const projects = await SupabaseApi.listProjects(creds.SUPABASE_ACCESS_TOKEN);

      const currentProject = _.find(projects, {
        id: creds.SUPABASE_PROJECT_REF,
      });

      return {
        id: creds.SUPABASE_PROJECT_REF,
        name: currentProject?.name || 'Supabase Project',
        organization_id: '',
        region: '',
        created_at: '',
        status: 'active',
        isConnected: true,
      };
    } catch (error) {
      console.error('Error getting connected Supabase project:', error);
      return null;
    }
  }

  /**
   * Update the credentials of a connected Supabase project
   * @param appId The Apptile app ID
   * @param credentials The updated credentials
   */
  static async updateProjectCredentials(
    appId: string,
    credentials: {
      SUPABASE_ANON_KEY?: string;
      SUPABASE_PROJECT_REF?: string;
      SUPABASE_ACCESS_TOKEN?: string;
      SUPABASE_REFRESH_TOKEN?: string;
      ACCESS_TOKEN_EXPIRES_IN?: number;
      APPTILE_SUPABASE_CLIENT_ID?: string;
      APPTILE_SUPABASE_CLIENT_SECRET?: string;
    },
  ): Promise<any> {
    try {
      // Update the credentials
      const response = await IntegrationsApi.updateCredentials('supabase', appId, credentials, true);
      return response;
    } catch (error) {
      console.error('Error updating Supabase project credentials:', error);
      throw new Error('Failed to update Supabase project credentials');
    }
  }

  /**
   * Connect to a Supabase project
   * @param appId The Apptile app ID
   * @param project The Supabase project to connect to
   * @param credentials The Supabase credentials
   */
  static async connectToProject(
    appId: string,
    project: SupabaseProject,
    credentials: {
      supabaseAccessToken: string;
      supabaseRefreshToken: string;
      accessTokenExpiresIn: number;
      apptileSupabaseClientId: string;
      apptileSupabaseClientSecret: string;
    },
  ): Promise<any> {
    try {
      // Get the project's API keys using the proxy route
      const apiKeysResponse = await axios.get(`${this.proxyBaseURL}/projects/${project.id}/api-keys`, {
        headers: {
          Authorization: `Bearer ${credentials.supabaseAccessToken}`,
          'Content-Type': 'application/json',
        },
      });

      // Find the anon key
      const anonKey = apiKeysResponse.data.find((key: any) => key.name === 'anon')?.api_key;

      if (!anonKey) {
        throw new Error('Could not find anon key for the project');
      }

      // Prepare the integration data
      const integrationData = {
        platformType: 'supabase',
        credentials: {
          SUPABASE_ANON_KEY: anonKey,
          SUPABASE_PROJECT_REF: project.id,
          SUPABASE_ACCESS_TOKEN: credentials.supabaseAccessToken,
          SUPABASE_REFRESH_TOKEN: credentials.supabaseRefreshToken,
          ACCESS_TOKEN_EXPIRES_IN: credentials.accessTokenExpiresIn,
          APPTILE_SUPABASE_CLIENT_ID: credentials.apptileSupabaseClientId,
          APPTILE_SUPABASE_CLIENT_SECRET: credentials.apptileSupabaseClientSecret,
        },
      };

      // Create the integration
      const response = await IntegrationsApi.createIntegration(appId, integrationData);

      // Mark the project as connected
      project.isConnected = true;

      return response;
    } catch (error) {
      console.error('Error connecting to Supabase project:', error);
      throw new Error('Failed to connect to Supabase project');
    }
  }

  static async getSupabaseIntegration(appId: string): Promise<any> {
    try {
      // Get the integration ID
      const response = await IntegrationsApi.fetchAppIntegrations(appId);
      const integrations = response?.data || [];
      // Find the Supabase integration for this project
      const supabaseIntegration = integrations.find(
        (integration: any) =>
          integration.integrationCode === 'supabase' &&
          _.find(integration.appIntegrations, {platformType: 'supabase'})?.active,
      );
      if (!supabaseIntegration) return null;
      return _.find(supabaseIntegration.appIntegrations, {
        platformType: 'supabase',
      });
    } catch (error) {
      console.error('Error getting Supabase integration:', error);
      return null;
    }
  }
}
