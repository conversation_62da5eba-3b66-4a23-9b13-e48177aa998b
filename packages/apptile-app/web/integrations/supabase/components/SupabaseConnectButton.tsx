import React, {useState, useEffect, useCallback, useRef} from 'react';
import axios from 'axios';
import {SupabaseApi, SupabaseProject} from '../services/SupabaseApi';
import SupabaseConfig from '../config';
import IntegrationsApi from '../../../api/IntegrationsApi';
import {useParams} from 'react-router';
import {useDispatch} from 'react-redux';
import {configureDatasources, fetchAppIntegrations} from '@/root/web/actions/editorActions';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';

interface SupabaseConnectButtonProps {
  onSuccess?: (data?: any) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
  buttonStyle?: React.CSSProperties;
  buttonText?: string;
  appId?: string;
}

/**
 * A button component that allows users to connect their Supabase account
 * and install the Apptile app.
 */
const SupabaseConnectButton: React.FC<SupabaseConnectButtonProps> = ({
  onSuccess,
  onError,
  buttonStyle,
  buttonText = 'Connect to Supabase',
  appId,
}) => {
  // Get the app ID from props or fall back to URL parameters
  const {id: urlAppId, orgId} = useParams();
  // Use the explicitly passed appId if available, otherwise use the one from URL params
  const effectiveAppId = appId || urlAppId;
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [showProjectsModal, setShowProjectsModal] = useState(false);
  const [projects, setProjects] = useState<SupabaseProject[]>([]);
  const [_selectedProject, setSelectedProject] = useState<SupabaseProject | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectedProject, setConnectedProject] = useState<SupabaseProject | null>(null);

  useMountEffect(() => {
    dispatch(fetchAppIntegrations(effectiveAppId as string));
  });

  // Credentials state
  const [credentials, setCredentials] = useState({
    supabaseAccessToken: '',
    supabaseRefreshToken: '',
    accessTokenExpiresIn: 86400,
    apptileSupabaseClientId: '',
    apptileSupabaseClientSecret: '',
  });

  // Check if there's a connected project whenever the app ID changes
  useEffect(() => {
    // Reset connection state when app ID changes
    setIsConnected(false);
    setConnectedProject(null);
    setError(null);
    setStatus(null);

    const checkConnectedProject = async () => {
      try {
        if (!effectiveAppId) return 'App id not found';

        // Set loading state while checking connection
        setIsLoading(true);
        setStatus('Checking Supabase connection...');

        // Check if this app has a connected Supabase project
        const connectedProject = await SupabaseApi.getConnectedProject(effectiveAppId);

        if (connectedProject) {
          const supabaseCreds = (await IntegrationsApi.fetchAppIntegrationSecrets(effectiveAppId, 'supabase')).data;
          if (!supabaseCreds) {
            setIsLoading(false);
            setStatus(null);
            return null;
          }

          // Set the credentials for later use
          setCredentials({
            supabaseAccessToken: supabaseCreds.SUPABASE_ACCESS_TOKEN,
            supabaseRefreshToken: supabaseCreds.SUPABASE_REFRESH_TOKEN,
            accessTokenExpiresIn: supabaseCreds.ACCESS_TOKEN_EXPIRES_IN,
            apptileSupabaseClientId: supabaseCreds.APPTILE_SUPABASE_CLIENT_ID,
            apptileSupabaseClientSecret: supabaseCreds.APPTILE_SUPABASE_CLIENT_SECRET,
          });

          setConnectedProject(connectedProject);
          setIsConnected(true);
          console.log(`App ${effectiveAppId} has connected Supabase project:`, connectedProject.name);
        } else {
          // Explicitly set to not connected when no project is found
          setIsConnected(false);
          setConnectedProject(null);
          console.log(`App ${effectiveAppId} has no connected Supabase project`);
        }
        
        // Clear loading state after check is complete
        setIsLoading(false);
        setStatus(null);
      } catch (err) {
        console.error('Error checking connected project:', err);
        // Reset connection state on error
        setIsConnected(false);
        setConnectedProject(null);
        setIsLoading(false);
        setStatus(null);
      }
    };

    checkConnectedProject();
  }, [effectiveAppId]);

  // Exchange the authorization code for an access token
  const exchangeCodeForToken = useCallback(
    async (code: string, state: string) => {
      setIsLoading(true);
      setError(null);
      setStatus('Exchanging authorization code for access token...');

      try {
        // Verify the state parameter to prevent CSRF attacks
        const savedState = localStorage.getItem('supabase_auth_state');
        if (state !== savedState) {
          throw new Error('Invalid state parameter');
        }

        // Use the proxy route to avoid CORS issues
        const formData = new URLSearchParams();
        formData.append('grant_type', 'authorization_code');
        formData.append('code', code);
        formData.append('client_id', SupabaseConfig.CLIENT_ID);
        formData.append('client_secret', SupabaseConfig.CLIENT_SECRET);
        formData.append('redirect_uri', `${window.location.origin}${SupabaseConfig.REDIRECT_URI}?orgId=${orgId}`);

        // Use the proxy route instead of directly calling the Supabase API
        const proxyUrl = `${window.PLUGIN_SERVER_URL}/cli/supabase-proxy/oauth/token`;

        const tokenResponse = await fetch(proxyUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: formData.toString(),
        });

        if (!tokenResponse.ok) {
          throw new Error(`Failed to exchange code for token: ${tokenResponse.statusText}`);
        }

        const tokenData = await tokenResponse.json();

        // Set the credentials
        const newCredentials = {
          supabaseAccessToken: tokenData.access_token,
          supabaseRefreshToken: tokenData.refresh_token,
          accessTokenExpiresIn: tokenData.expires_in || SupabaseConfig.ACCESS_TOKEN_EXPIRES_IN,
          apptileSupabaseClientId: SupabaseConfig.CLIENT_ID,
          apptileSupabaseClientSecret: SupabaseConfig.CLIENT_SECRET,
        };
        setCredentials(newCredentials);

        try {
          //Also update supabase credentials in the DB
          await SupabaseApi.updateProjectCredentials(effectiveAppId, {
            SUPABASE_ACCESS_TOKEN: tokenData.access_token,
            SUPABASE_REFRESH_TOKEN: tokenData.refresh_token,
            ACCESS_TOKEN_EXPIRES_IN: tokenData.expires_in || SupabaseConfig.ACCESS_TOKEN_EXPIRES_IN,
          });
        } catch (err) {
          console.info('Update failed the db might be empty', err);
        }

        // Fetch the list of Supabase projects
        const projectsList = await SupabaseApi.listProjects(newCredentials.supabaseAccessToken);

        // Check if any project is already connected
        if (connectedProject) {
          // Mark the connected project
          projectsList.forEach(project => {
            if (project.id === connectedProject.id) {
              project.isConnected = true;
            }
          });
        }

        setProjects(projectsList);
        setShowProjectsModal(true);
        setStatus('Select a Supabase project to connect:');
        setIsLoading(false);
      } catch (err) {
        console.error('Error exchanging code for token:', err);
        setError('Failed to authenticate with Supabase');
        setStatus(null);
        setIsLoading(false);
        if (onError && err instanceof Error) onError(err);
      }
    },
    [onError, connectedProject],
  );

  // Listen for the OAuth callback message
  useEffect(() => {
    const handleOAuthCallback = (event: MessageEvent) => {
      // Verify the origin
      if (event.origin !== window.location.origin) return;

      // Check if this is a Supabase auth callback
      if (event.data && event.data.type === 'supabase_auth_callback') {
        const {code, state} = event.data;

        // Exchange the code for an access token
        exchangeCodeForToken(code, state);
      }
    };

    // Add the event listener
    window.addEventListener('message', handleOAuthCallback);

    // Clean up
    return () => {
      window.removeEventListener('message', handleOAuthCallback);
    };
  }, [exchangeCodeForToken]);

  // Function to handle the initial connection to Supabase
  const handleConnect = useCallback(() => {
    setIsLoading(true);
    setError(null);
    setStatus('Connecting to Supabase...');

    try {
      // Generate a random state value for security
      const state = Math.random().toString(36).substring(2, 15);

      // Store the state in localStorage to verify later
      localStorage.setItem('supabase_auth_state', state);

      // Construct the OAuth URL
      const redirectUri = `${window.location.origin}${SupabaseConfig.REDIRECT_URI}?orgId=${orgId}`;

      const oauthUrl = `${SupabaseConfig.OAUTH_AUTHORIZE_URL}?client_id=${
        SupabaseConfig.CLIENT_ID
      }&redirect_uri=${encodeURIComponent(redirectUri)}&state=${state}&response_type=code`;

      // Open the OAuth window
      window.open(oauthUrl, 'Supabase Auth', 'width=600,height=600');
    } catch (err) {
      console.error('Error initiating Supabase OAuth:', err);
      setError('Failed to connect to Supabase');
      setStatus(null);
      setIsLoading(false);
      if (onError && err instanceof Error) onError(err);
    }
  }, [onError]);

  // Function to handle selecting a project
  const handleSelectProject = async (project: SupabaseProject) => {
    // If the project is already connected, don't do anything
    if (project.isConnected) {
      return;
    }

    setSelectedProject(project);
    setIsLoading(true);
    setError(null);

    try {
      // Check if there's already a connected project
      const connectedProj = await SupabaseApi.getConnectedProject(effectiveAppId);

      if (connectedProj) {
        // We're changing the project - update the credentials
        setStatus(`Updating Supabase integration to ${project.name}...`);

        // Get the project's API keys
        const apiKeysResponse = await axios.get(`${SupabaseApi.proxyBaseURL}/projects/${project.id}/api-keys`, {
          headers: {
            Authorization: `Bearer ${credentials.supabaseAccessToken}`,
            'Content-Type': 'application/json',
          },
        });

        // Find the anon key
        const anonKey = apiKeysResponse.data.find((key: any) => key.name === 'anon')?.api_key;

        if (!anonKey) {
          throw new Error('Could not find anon key for the project');
        }

        // Update the credentials
        await SupabaseApi.updateProjectCredentials(effectiveAppId, {
          SUPABASE_ANON_KEY: anonKey,
          SUPABASE_PROJECT_REF: project.id,
          SUPABASE_ACCESS_TOKEN: credentials.supabaseAccessToken,
          SUPABASE_REFRESH_TOKEN: credentials.supabaseRefreshToken,
          ACCESS_TOKEN_EXPIRES_IN: credentials.accessTokenExpiresIn,
          APPTILE_SUPABASE_CLIENT_ID: credentials.apptileSupabaseClientId,
          APPTILE_SUPABASE_CLIENT_SECRET: credentials.apptileSupabaseClientSecret,
        });

        setStatus(`Successfully updated Supabase integration to ${project.name}!`);
      } else {
        // Connect to a new project
        setStatus(`Installing Apptile integration for ${project.name}...`);
        await SupabaseApi.connectToProject(effectiveAppId, project, credentials);
        setStatus(`Successfully installed Apptile integration for ${project.name}!`);
      }
      await IntegrationsApi.toggleAppIntegration(effectiveAppId, 'supabase', true, true);
      //Configure datasources to fetch the new credentials
      if (effectiveAppId) {
        dispatch(configureDatasources(effectiveAppId, true, true));
      }
      // Update the connected project
      setConnectedProject(project);
      setIsConnected(true);

      // Mark this project as connected and update the projects list
      const updatedProjects = projects.map(p => ({
        ...p,
        isConnected: p.id === project.id,
      }));
      setProjects(updatedProjects);

      // Call the success callback
      if (onSuccess) onSuccess();

      // Keep the modal open to show the connected status
      setIsLoading(false);
    } catch (err) {
      console.error('Error connecting to Supabase project:', err);
      setError(`Failed to install Apptile integration for ${project.name}`);
      setStatus(null);
      setIsLoading(false);
      if (onError && err instanceof Error) onError(err);
    }
  };

  // Function to close the modal
  const handleCloseModal = () => {
    setShowProjectsModal(false);
  };

  // Function to disconnect a project
  const handleDisconnectProject = async (project: SupabaseProject, event: React.MouseEvent) => {
    // Stop the event from propagating to the parent (which would select the project)
    event.stopPropagation();

    setIsLoading(true);
    setError(null);
    setStatus(`Disconnecting from ${project.name}...`);

    try {
      const supabaseIntegration = await SupabaseApi.getSupabaseIntegration(effectiveAppId);

      if (!supabaseIntegration) {
        throw new Error('Supabase integration not found');
      }

      // Directly call the toggleAppIntegration API
      await IntegrationsApi.toggleAppIntegration(effectiveAppId, supabaseIntegration.id, false);

      // Update the UI
      setConnectedProject(null);
      setIsConnected(false);

      // Update the projects list
      const updatedProjects = projects.map(p => ({
        ...p,
        isConnected: false,
      }));
      setProjects(updatedProjects);

      setStatus(`Successfully disconnected from ${project.name}`);
      setIsLoading(false);
    } catch (err) {
      console.error('Error disconnecting project:', err);
      setError('Failed to disconnect project');
      setStatus(null);
      setIsLoading(false);
      if (onError && err instanceof Error) onError(err);
    }
  };

  // Function to refresh the projects list
  const handleRefreshProjects = async () => {
    setIsLoading(true);
    setError(null);
    setStatus('Refreshing projects...');

    try {
      const projectsList = await SupabaseApi.listProjects(credentials.supabaseAccessToken);

      // Check if any project is already connected
      if (connectedProject) {
        // Mark the connected project
        projectsList.forEach(project => {
          if (project.id === connectedProject.id) {
            project.isConnected = true;
          }
        });
      }

      setProjects(projectsList);
      setStatus('Select a Supabase project to connect:');
      setIsLoading(false);
    } catch (err) {
      console.error('Error refreshing projects:', err);
      setError('Failed to refresh projects');
      setStatus(null);
      setIsLoading(false);
      if (onError && err instanceof Error) onError(err);
    }
  };

  // Function to redirect to Supabase to create a new project
  const handleCreateProject = () => {
    window.open('https://app.supabase.com/new/new-project', '_blank');
  };

  // Render a project item
  const renderProjectItem = (project: SupabaseProject) => {
    const isProjectConnected = project.isConnected;
    const isStatusUnavailable =
      !project.status || project.status.toLowerCase() === 'inactive' || project.status.toLowerCase() === 'error';

    // Handle click on the project item
    const handleProjectClick = () => {
      if (isStatusUnavailable) {
        // If status is unavailable, redirect to Supabase dashboard
        window.open(`https://app.supabase.com/project/${project.id}`, '_blank');
      } else {
        // Otherwise, select the project
        handleSelectProject(project);
      }
    };

    return (
      <div
        key={project.id}
        style={{
          ...(styles.projectItem as React.CSSProperties),
          ...(isProjectConnected ? (styles.connectedProjectItem as React.CSSProperties) : {}),
          ...(isStatusUnavailable ? (styles.unavailableProjectItem as React.CSSProperties) : {}),
        }}
        onClick={handleProjectClick}>
        <div style={styles.projectName as React.CSSProperties}>
          {project.name}
          {isProjectConnected && <span style={styles.connectedBadge as React.CSSProperties}>Connected</span>}
          {isStatusUnavailable && <span style={styles.fixStatusBadge as React.CSSProperties}>Click to Fix</span>}
        </div>
        <div style={styles.projectDetails as React.CSSProperties}>
          <span>Region: {project.region}</span>
          <span>Status: {project.status || 'Unavailable'}</span>
        </div>
        {isProjectConnected && (
          <button
            onClick={e => handleDisconnectProject(project, e)}
            style={styles.disconnectButton as React.CSSProperties}
            disabled={isLoading}>
            Disconnect
          </button>
        )}
      </div>
    );
  };

  // Render the modal with projects
  const renderProjectsModal = () => {
    if (!showProjectsModal) return null;

    return (
      <div style={styles.modalOverlay as React.CSSProperties}>
        <div style={styles.modalContent as React.CSSProperties}>
          <div style={styles.modalHeader as React.CSSProperties}>
            <div style={styles.modalTitle as React.CSSProperties}>Supabase Projects</div>
            <button onClick={handleCloseModal} style={styles.closeButton as React.CSSProperties}>
              <CloseIcon width={16} height={16} />
            </button>
          </div>

          {status && <div style={styles.statusText as React.CSSProperties}>{status}</div>}

          <div style={styles.projectsActions as React.CSSProperties}>
            <button
              onClick={handleCreateProject}
              disabled={isLoading}
              style={styles.createNewButton as React.CSSProperties}
              title="Create new project">
              <span style={{display: 'flex', alignItems: 'center'}}>
                <PlusIcon width={16} height={16} />
                <span style={{marginLeft: 8}}>Create New Project</span>
              </span>
            </button>
            <button
              onClick={handleRefreshProjects}
              disabled={isLoading}
              style={styles.iconButton as React.CSSProperties}
              title="Refresh projects">
              <RefreshIcon width={16} height={16} />
            </button>
          </div>

          {projects.length > 0 && (
            <div style={styles.projectsList as React.CSSProperties}>
              {projects.map(project => renderProjectItem(project))}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div style={styles.container as React.CSSProperties}>
        <button
          onClick={
            isConnected
              ? () => {
                  setShowProjectsModal(true);
                  handleRefreshProjects();
                }
              : handleConnect
          }
          disabled={isLoading}
          style={{
            ...(styles.button as React.CSSProperties),
            ...(isLoading ? (styles.buttonLoading as React.CSSProperties) : {}),
            ...(isConnected ? (styles.connectedButton as React.CSSProperties) : {}),
            ...buttonStyle,
          }}>
          {isLoading ? (
            <>
              <div style={styles.spinner as React.CSSProperties}></div>
              Loading...
            </>
          ) : isConnected ? (
            <>
              <ConnectedIcon width={16} height={16} />
              Connected to {connectedProject?.name}
            </>
          ) : (
            <>
              <DisconnectedIcon width={16} height={16} />
              {buttonText}
            </>
          )}
        </button>

        {status && !showProjectsModal && <div style={styles.statusText as React.CSSProperties}>{status}</div>}
        {error && <div style={styles.errorText as React.CSSProperties}>{error}</div>}

        {renderProjectsModal()}
      </div>
    </>
  );
};

// Simple icon components
const RefreshIcon = ({width = 24, height = 24}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round">
    <path d="M23 4v6h-6" />
    <path d="M1 20v-6h6" />
    <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15" />
  </svg>
);

// Close icon component
const CloseIcon = ({width = 24, height = 24}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

// Plus icon component
const PlusIcon = ({width = 24, height = 24}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round">
    <line x1="12" y1="5" x2="12" y2="19"></line>
    <line x1="5" y1="12" x2="19" y2="12"></line>
  </svg>
);

// Connected icon component
const ConnectedIcon = ({width = 16, height = 16}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round">
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
    <polyline points="22,4 12,14.01 9,11.01"></polyline>
  </svg>
);

// Disconnected icon component
const DisconnectedIcon = ({width = 16, height = 16}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round">
    <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-5c-.6 0-1.2-.2-1.6-.6l-3.8-3.8a2 2 0 0 1 0-2.8l3.8-3.8c.4-.4 1-.6 1.6-.6h5a2 2 0 0 1 2 2v2"></path>
    <path d="M8 12h8"></path>
  </svg>
);

const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
  },
  button: {
    backgroundColor: '#3ECF8E', // Supabase green color
    color: 'white',
    border: 'none',
    borderRadius: 8,
    padding: '12px 20px',
    fontSize: 14,
    fontWeight: 'bold',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 4px rgba(62, 207, 142, 0.2)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    minWidth: 180,
    position: 'relative',
    overflow: 'hidden',
  },
  buttonLoading: {
    backgroundColor: '#2EB77E', // Darker shade of Supabase green
    cursor: 'not-allowed',
    opacity: 0.8,
  },
  connectedButton: {
    backgroundColor: '#06402B', // Dark green for connected state
    border: '2px solid #059669',
    boxShadow: '0 4px 8px rgba(16, 185, 129, 0.3)',
    transform: 'translateY(-1px)',
  },
  disconnectedButton: {
    backgroundColor: '#3ECF8E',
    border: '2px solid transparent',
    '&:hover': {
      backgroundColor: '#2EB77E',
      transform: 'translateY(-1px)',
      boxShadow: '0 4px 8px rgba(62, 207, 142, 0.3)',
    },
  },
  statusText: {
    color: '#4A5568',
    marginTop: 8,
    fontSize: 12,
  },
  errorText: {
    color: '#E53E3E',
    marginTop: 8,
    fontSize: 12,
  },
  modalOverlay: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 24,
    width: '90%',
    maxWidth: 500,
    maxHeight: '80vh',
    overflow: 'auto',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
  },
  modalHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2D3748',
  },
  closeButton: {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    padding: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#718096',
  },
  projectsList: {
    marginTop: 16,
  },
  projectsActions: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  createNewButton: {
    backgroundColor: '#3ECF8E',
    color: 'white',
    borderRadius: 2,
    fontSize: 14,
    padding: 20,
    marginTop: 5,
    fontWeight: 'bold',
    display: 'flex',
    border: 'none',
    cursor: 'pointer',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  iconButton: {
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    padding: 4,
    borderRadius: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#718096',
  },
  noProjectsContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#F7FAFC',
    borderRadius: 4,
    border: '1px solid #E2E8F0',
    marginTop: 16,
  },
  noProjectsText: {
    fontSize: 14,
    color: '#4A5568',
    marginBottom: 16,
  },
  createProjectButton: {
    backgroundColor: '#3ECF8E',
    color: 'white',
    border: 'none',
    borderRadius: 4,
    padding: '8px 16px',
    fontSize: 14,
    fontWeight: 'bold',
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
  },
  projectItem: {
    backgroundColor: '#F7FAFC',
    borderRadius: 4,
    padding: 12,
    marginBottom: 8,
    cursor: 'pointer',
    transition: 'background-color 0.2s ease',
    border: '1px solid #E2E8F0',
    position: 'relative',
  },
  connectedProjectItem: {
    backgroundColor: '#F0FFF4', // Light green background
    border: '1px solid #3ECF8E', // Green border
  },
  unavailableProjectItem: {
    cursor: 'pointer',
    padding: '8px 12px',
  },
  fixStatusBadge: {
    backgroundColor: '#FC8181',
    color: 'white',
    fontSize: 10,
    padding: 5,
    borderRadius: 3,
    marginLeft: 8,
  },
  projectName: {
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  connectedBadge: {
    backgroundColor: '#3ECF8E',
    color: 'white',
    fontSize: 10,
    padding: '2px 6px',
    borderRadius: 10,
    marginLeft: 8,
  },
  disconnectButton: {
    position: 'absolute',
    top: 6,
    right: 8,
    backgroundColor: '#E53E3E',
    color: 'white',
    border: 'none',
    borderRadius: 2,
    padding: 5,
    fontSize: 12,
    cursor: 'pointer',
  },
  projectDetails: {
    display: 'flex',
    justifyContent: 'space-between',
    fontSize: 12,
    color: '#718096',
  },
  spinner: {
    width: 16,
    height: 16,
    border: '2px solid rgba(255, 255, 255, 0.3)',
    borderTop: '2px solid white',
    borderRadius: '50%',
    animation: 'spin 1s linear infinite',
    marginRight: 8,
  },
};

export default SupabaseConnectButton;
