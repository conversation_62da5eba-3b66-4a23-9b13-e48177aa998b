import React, {useEffect} from 'react';
import {StyleSheet} from 'react-native';

/**
 * Supabase callback component to handle the OAuth redirect
 */
const SupabaseCallback: React.FC = () => {
  useEffect(() => {
    // Get the authorization code and state from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    // Send a message to the parent window with the authorization code and state
    if (window.opener) {
      window.opener.postMessage(
        {
          type: 'supabase_auth_callback',
          code,
          state,
        },
        window.location.origin,
      );

      // Close the window
      window.close();
    }
  }, []);

  return (
    <div style={styles.container}>
      <div style={styles.heading}>Connecting to Supabase...</div>
      <div style={styles.subheading}>This window will close automatically once the connection is complete.</div>
    </div>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  heading: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    textAlign: 'center',
  },
  subheading: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default SupabaseCallback;
