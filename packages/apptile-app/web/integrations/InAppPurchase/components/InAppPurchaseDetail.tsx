import React from 'react';
import { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { useParams } from 'react-router-dom';
import IntegrationsApi from '../../../api/IntegrationsApi';
import TextElement from '../../../components-v2/base/TextElement';
import theme from '../../../views/prompt-to-app/styles-prompt-to-app/theme';
import { ApptileWebIcon } from '@/root/web/icons/ApptileWebIcon.web';
import Icon from 'react-native-vector-icons/AntDesign';
import { useDispatch } from 'react-redux';
import {makeToast} from '../../../actions/toastActions';
import { modelUpdateAction, store } from 'apptile-core';

const PLATFORM_TYPE = 'inAppPurchases';

const webStyles = {
  logoImage: { width: 50, height: 50, objectFit: 'contain' },
  bannerImage: { height: '100%', objectFit: 'fit', aspectRatio: '530/621' },
}

const addButtonStyle = {
  borderRadius: 8,
  fontFamily: theme.FONT_FAMILY,
  border: 'none',
  cursor: 'pointer',
  padding: '6px 16px',
  fontSize: 12,
  background: '#005be4',
  color: theme.DEFAULT_COLOR,
  marginLeft: 0,
  height: 30,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};
const deleteIconButtonStyle = {
  borderRadius: 8,
  fontWeight: theme.FONT_WEIGHT_BOLD,
  fontFamily: theme.FONT_FAMILY,
  border: 'none',
  cursor: 'pointer',
  fontSize: 14,
  background: 'transparent',
  color: '#F44336',
  marginLeft: 8,
  padding: 0,
  height: 32,
  width: 32,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};
const inputFieldStyle = {
  width: '100%',
  padding: '0 10px',
  borderRadius: theme.INPUT_BORDER_RADIUS,
  background: theme.INPUT_BACKGROUND,
  border: `1px solid ${theme.INPUT_BORDER}`,
  color: theme.DEFAULT_COLOR,
  fontFamily: theme.FONT_FAMILY,
  fontSize: theme.FONT_SIZE,
  height: 32
};

interface IdListInputProps {
  label: string;
  infoTitle: string;
  infoUrl: string;
  placeholder: string;
  values: string[];
  setValues: (values: string[]) => void;
  addButtonLabel: string;
  inputValue: string;
  setInputValue: (val: string) => void;
  showInput: boolean;
  setShowInput: (show: boolean) => void;
  Icon: any;
  dispatch: any;
}

const IdListInput: React.FC<IdListInputProps> = ({
  label,
  infoTitle,
  infoUrl,
  placeholder,
  values,
  setValues,
  addButtonLabel,
  inputValue,
  setInputValue,
  showInput,
  setShowInput,
  dispatch,
  Icon,
}) => (
  <div style={styles.inputWrapper}>
    <div style={styles.labelStyle}>
      {label}
      <span style={{ marginLeft: 6, verticalAlign: 'middle', cursor: 'pointer' }} title={infoTitle}>
        <Icon
          style={{marginBottom: 5}}
          onPress={() => {
            window.open(infoUrl, '_blank');
          }}
          name="infocirlce" size={10} color={theme.TEXT_SECONDARY_COLOR} />
      </span>
{
!showInput &&
      <button style={{...addButtonStyle, marginLeft:'10px'}} onClick={() => setShowInput(true)}>{addButtonLabel}</button>
}
    </div>
    {showInput && (
      <div style={{display: 'flex', gap: 8, marginBottom: 20, alignItems: 'center', marginTop: 10}}>
        <input
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          style={inputFieldStyle}
          placeholder={placeholder}
        />
        <button
          style={addButtonStyle}
          onClick={() => {
            const val = inputValue.trim();
            if (val && !values.includes(val)) {
              setValues([...values, val]);
              setInputValue('');
              setShowInput(false);
            } else {
              dispatch(makeToast({content: 'Value already exists', appearances: 'warning'}));
            }
          }}
        >Add</button>
      </div>
    )}
    {values.length > 0 && (
      <ul style={{paddingLeft: 0, listStyle: 'none'}}>
        {values.map((id, idx) => (
          <li key={id} style={{display: 'flex', alignItems: 'center', marginBottom: 4}}>
            <span style={{color: theme.DEFAULT_COLOR, fontSize: 14, marginRight: 8}}>●</span>
            <TextElement color="DEFAULT" fontWeight="400" fontSize="md" style={styles.titleStyles}>
              {id}
            </TextElement>
            <button
              style={deleteIconButtonStyle}
              onClick={() => setValues(values.filter((_, i) => i !== idx))}
            >
              <Icon name="delete" size={16} color={theme.DEFAULT_COLOR} />
            </button>
          </li>
        ))}
      </ul>
    )}
  </div>
);

const InAppPurchaseDetail = ({ integration }: { integration: any }) => {
  const params = useParams<{ id?: string }>();
  const appId = params?.id || '';
  const dispatch = useDispatch();
  const [screen, setScreen] = useState<number | null>(0);
  const [subscriptionIdList, setSubscriptionIdList] = useState<string[]>([]);
  const [productIdList, setProductIdList] = useState<string[]>([]);
  const [consumableProductIdList, setConsumableProductIdList] = useState<string[]>([]);
  const [showSubscriptionInput, setShowSubscriptionInput] = useState(false);
  const [showProductInput, setShowProductInput] = useState(false);
  const [showConsumableInput, setShowConsumableInput] = useState(false);
  const [subscriptionInput, setSubscriptionInput] = useState('');
  const [productInput, setProductInput] = useState('');
  const [consumableInput, setConsumableInput] = useState('');
  const [isIntegrated, setIsIntegrated] = useState(false);
  const [loading, setLoading] = useState(true);

  const fetchCredentials = async () => {
    setLoading(true);
    try {
      const integrationResponse: any = await IntegrationsApi.fetchAppIntegration(appId, PLATFORM_TYPE, true);
      const integrationData: any = integrationResponse.data;
      if (integrationData) {
        setSubscriptionIdList((integrationData.credentials?.subscriptionIds || '').split(',').filter((v: string) => v));
        setProductIdList((integrationData.credentials?.productIds || '').split(',').filter((v: string) => v));
        setConsumableProductIdList((integrationData.credentials?.consumableProductIds || '').split(',').filter((v: string) => v));
        if (integrationData.credentials?.subscriptionIds || integrationData.credentials?.productIds || integrationData.credentials?.consumableProductIds) {
          setIsIntegrated(true);
          setScreen(1);
        }
      }
    } catch (e) {
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (appId) fetchCredentials();
  }, [appId]);

  const handleEnable = () => setScreen(1);

  const handleSaveOrUpdate = async () => {
    setLoading(true);

    try {
      if (isIntegrated) {
        const updatePayload = {
          subscriptionIds: subscriptionIdList.join(','),
          productIds: productIdList.join(','),
          consumableProductIds: consumableProductIdList.join(','),
        };
        await IntegrationsApi.saveAppIntegrationCredentials(appId, PLATFORM_TYPE, updatePayload, true);
        dispatch(makeToast({ content: 'In-App purchase Integration updated', appearances: 'success' }));
      } else {
        if (subscriptionIdList?.length > 0 || productIdList?.length > 0 || consumableProductIdList?.length > 0){
          const createPayload = {
            platformType: PLATFORM_TYPE,
            credentials: {
              subscriptionIds: subscriptionIdList.join(','),
              productIds: productIdList.join(','),
              consumableProductIds: consumableProductIdList.join(','),
            }
          };
          await IntegrationsApi.createIntegration(appId, createPayload);
          setIsIntegrated(true);
          dispatch(makeToast({ content: 'In-App purchase Integration created', appearances: 'success' }));
        } else {
          dispatch(makeToast({ content: 'Please add atleast one Subscription Id, Product Id or Consumable Product Id', appearances: 'warning' }));
        }
      }
      store.dispatch(
        modelUpdateAction([
          { selector: ['AppMarketplaceIap', 'productIds'], newValue: productIdList.join(',') },
          { selector: ['AppMarketplaceIap', 'subscriptionIds'], newValue: subscriptionIdList.join(',') },
          { selector: ['AppMarketplaceIap', 'consumableProductIds'], newValue: consumableProductIdList.join(',') },
        ])
      );
    } finally {
      setLoading(false);
    }
  };

  if (screen === null || loading) {
    return (
      <div style={{
        width: '100%',
        height: '50vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <img
          src={require('@/root/web/assets/images/preloader.svg')}
          alt="Loading..."
          style={{ width: 80, height: 80 }}
        />
      </div>
    );
  }

  return (
    <>
      <div style={styles.flexDirectionRow}>
        <div style={styles.flexDirectionRow}>
          {integration?.icon && (
            <div style={styles.logoWrapper}>
              <img src={integration?.icon} style={{...webStyles.logoImage, objectFit: 'contain'}} />{' '}
            </div>
          )}
          <div>
            <TextElement color="DEFAULT" fontWeight="500" fontSize="lg" style={styles.titleStyles}>
              {integration?.title}
            </TextElement>

            <div style={{ marginTop: '15px' }}>
              <TextElement color="DEFAULT" fontWeight="400" fontSize="sm" lineHeight="lg">
                {integration?.excerpt ?? ''}
              </TextElement>
            </div>
          </div>
        </div>

        <button
          style={{
            padding: '10px 24px',
            borderRadius: 8,
            background: '#005be4',
            color: theme.DEFAULT_COLOR,
            border: 'none',
            fontWeight: theme.FONT_WEIGHT_BOLD,
            fontFamily: theme.FONT_FAMILY,
            cursor: 'pointer',
            marginTop: 16,
          }}
          // disabled={(subscriptionIdList?.length === 0 && productIdList?.length === 0 && consumableProductIdList?.length === 0 && screen != 0)}
          onClick={screen === 0 ? handleEnable : handleSaveOrUpdate}>
          {screen === 0 ? 'Enable' : isIntegrated ? 'Update' : 'Save'}
        </button>
      </div>
      <div style={styles.container}>
        {screen === 1 && (
          <div style={{
            width: '60%',
          }}>
            <div style={styles.tagline}>
              Add the Products & Subscriptions below to enable In App Purchase
            </div>
            <IdListInput
              label="Subscription Ids"
              infoTitle="More info about Subscription Ids"
              infoUrl="https://www.tile.dev/"
              placeholder="Enter Subscription Id"
              values={subscriptionIdList}
              setValues={setSubscriptionIdList}
              addButtonLabel="Add"
              inputValue={subscriptionInput}
              setInputValue={setSubscriptionInput}
              showInput={showSubscriptionInput}
              setShowInput={setShowSubscriptionInput}
              Icon={Icon}
              dispatch={dispatch}
            />
            <IdListInput
              label="Product Ids"
              infoTitle="More info about Product Ids"
              infoUrl="https://www.tile.dev/"
              placeholder="Enter Product Id"
              values={productIdList}
              setValues={setProductIdList}
              addButtonLabel="Add"
              inputValue={productInput}
              setInputValue={setProductInput}
              showInput={showProductInput}
              setShowInput={setShowProductInput}
              dispatch={dispatch}
              Icon={Icon}
            />
            <IdListInput
              label="Consumable Product Ids"
              infoTitle="More info about Consumable Product Ids"
              infoUrl="https://www.tile.dev/"
              placeholder="Enter Consumable Product Id"
              values={consumableProductIdList}
              setValues={setConsumableProductIdList}
              addButtonLabel="Add"
              inputValue={consumableInput}
              setInputValue={setConsumableInput}
              showInput={showConsumableInput}
              setShowInput={setShowConsumableInput}
              Icon={Icon}
              dispatch={dispatch}
            />

          </div>
        )}
      </div>
    </>
  );
};

export default InAppPurchaseDetail;

const styles = StyleSheet.create({
  container: {
    minHeight: 400,
  },
  header: {
    fontWeight: theme.FONT_WEIGHT_BOLD,
    color: theme.FONT_COLOR,
    fontFamily: theme.FONT_FAMILY,
    fontSize: 21,
    marginBottom: 12,
  },
  tagline: {
    color: theme.TEXT_SECONDARY_COLOR,
    marginBottom: 24,
    marginTop:30
  },
  inputWrapper: {
    marginBottom: 16,
    marginTop: 16
  },
  inputStyle: {
    width: '100%',
    padding: 10,
    borderRadius: theme.INPUT_BORDER_RADIUS,
    background: theme.INPUT_BACKGROUND,
    border: `1px solid ${theme.INPUT_BORDER}`,
    color: theme.TILE_COLOR,
    marginBottom: 16,
    fontFamily: theme.FONT_FAMILY,
    fontSize: theme.FONT_SIZE,
    marginTop: 2,
  },
  labelStyle: {
    display: 'flex',
    color: theme.FONT_COLOR,
    fontFamily: theme.FONT_FAMILY,
    alignItems: 'center',
    alignContent: 'center',
    marginBottom: 5,
    marginTop: 10
  },
  logoWrapper: {
    width: 50,
    overflow: 'hidden',
    marginRight: 12,
  },
  flexDirectionRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    justifyContent: 'space-between'
  },
  titleStyles: { maxWidth: 220 },
});
