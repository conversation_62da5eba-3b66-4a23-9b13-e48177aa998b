import {select} from 'redux-saga/effects';
import ShopifyShopApi from '../../api/ShopifyShopApi';
import {getShopifyObjectCache} from './ShopifyObjectCache';
import {ShopifyObjectCacheConfigKeys} from './ShopifyObjectCacheTypes';
import {datasourceTypeModelSel} from 'apptile-core';
import {BlogGqls} from 'apptile-shopify';
import {processShopifyGraphqlQueryResponse} from 'apptile-core';
import {TransformGetBlogs} from 'apptile-shopify';
import {useSelector} from 'react-redux';
import { store } from 'apptile-core';
import {CDN_HOST_URL} from '../../../.env.json';
import { getProduct } from 'react-native-device-info';
import { TransformGetProductsPaginatedQuery, TransformSearchCollections } from 'apptile-shopify';
import {CollectionGqls as ProductCollectionGqls} from 'apptile-shopify';

const MILLISECONDS_12_HOURS = 12 * 60 * 60 * 1000;
const MILLISECONDS_2_HOURS = 2 * 60 * 60 * 1000;

const checkBulkTask = async (appId: string, taskId: string) => {
  // logger.info(`Fetching Bulk Task status : ${taskId}`);
  return ShopifyShopApi.fetchBulkStatus(appId, taskId).then(async response => {
    // logger.info(`Bulk Task status : `, response);
    const status = response.data?.status;
    if (status === 'FAILED') {
      logger.error(`Fetching Bulk Task FAILED : ${taskId}`);
    } else if (status === 'COMPLETED' || status === 'EXPIRED') {
      return response.data;
    } else {
      await new Promise(resolve => setTimeout(resolve, 2000));
      return checkBulkTask(appId, taskId);
    }
  });
};

function jsonlToArray(lines: string) {
  var jsonLines = lines.split(/\r?\n|\r|\n/g);
  return jsonLines
    .map(line => {
      try {
        if (line) {
          return JSON.parse(line);
        }
      } catch (e) {
        logger.error('Invalid json object', line);
      }
    })
    .filter(obj => !!obj);
}

const entityConfigKeysMap: {
  [key: string]: {
    [key: string]: ShopifyObjectCacheConfigKeys;
  };
} = {
  PRODUCT: {
    taskId: 'productsBulkTaskId',
    loaded: 'productsLoaded',
    loadedAt: 'productsLoadedAt',
    appId: 'productsAppId',
  },
  COLLECTION: {
    taskId: 'collectionsBulkTaskId',
    loaded: 'collectionsLoaded',
    loadedAt: 'collectionsLoadedAt',
    appId: 'collectionsAppId',
  },
  BLOG: {
    loaded: 'blogsLoaded',
    loadedAt: 'blogsLoadedAt',
    appId: 'blogsAppId',
  },
  APPTILE_ORDERS: {
    taskId: 'apptileOrdersBulkTaskId',
    loaded: 'apptileOrdersLoaded',
    loadedAt: 'apptileOrdersLoadedAt',
    appId: 'apptileOrdersAppId',
  },
};
const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');

export async function getEntityBulkData(entity: 'PRODUCT' | 'COLLECTION' | 'BLOG' | 'APPTILE_ORDERS', appId: string) {
  const entityConfigKeys = entityConfigKeysMap[entity];
  const shopifyObjectCache = await getShopifyObjectCache();

  var isEntityFetched = (await shopifyObjectCache.getConfigValue(entityConfigKeys.loaded)) as boolean;
  var entityFetchedAt = (await shopifyObjectCache.getConfigValue(entityConfigKeys.loadedAt)) as string;
  var appIdDataFetched = (await shopifyObjectCache.getConfigValue(entityConfigKeys.appId)) as string;
  if (isEntityFetched && entityFetchedAt != null && appIdDataFetched === appId) {
    const currentTime = Date.now();
    const lastFetchedTime = new Date(entityFetchedAt as string).getTime();
    const timeDifference = currentTime - lastFetchedTime;
    if (entity === 'BLOG') {
      if (timeDifference < MILLISECONDS_2_HOURS) {
        return await (await getShopifyObjectCache()).getBlogsList();
      }
    } else {
      if (timeDifference < MILLISECONDS_2_HOURS) {
        let entityList;
        if (entity === 'PRODUCT') {
          entityList = await (await getShopifyObjectCache()).getProductsList();
        } else if (entity === 'COLLECTION') {
          entityList = await (await getShopifyObjectCache()).getCollectionsList();
        } else if (entity === 'APPTILE_ORDERS') {
          entityList = await (await getShopifyObjectCache()).getApptileOrdersList();
        }
        // logger.info(`CACHE:Fetch :`, productList);
        return entityList;
      }
    }
  }
  await loadEntityData();

  async function loadEntityData() {
    let entityLoadHandler = null;
    let running = 0;
    if (entity === 'PRODUCT') {
      const getProducts: any = async () => {
        await new Promise((res: any) =>
          setTimeout(() => {
            res();
          }, 1500),
        );
        running += 1;
        const ShopifyDSModel = shopifyModelSel(store.getState());
        const queryRunner = ShopifyDSModel?.get('queryRunner');
        if (queryRunner && queryRunner.runQuery) {
          const countryCode = ShopifyDSModel?.getIn(['shop','paymentSettings','countryCode']) ?? 'US';
          const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_PRODUCTS_FOR_DROPDOWN, {first: 100, query: '', countryCode});
          const {transformedData: queryTransformedData} = processShopifyGraphqlQueryResponse(
            queryResponse,
            {transformer: TransformGetProductsPaginatedQuery},
            ShopifyDSModel?.get('shop'),
            ShopifyDSModel,
          );
          await shopifyObjectCache.clearProductsData();
          await shopifyObjectCache.setProductsList(queryTransformedData);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loaded, true);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loadedAt, Date.now());
          await shopifyObjectCache.setConfigValue(entityConfigKeys.appId, appId);
          return '';
        } else if (
          window.apptileWebSDK.status == 'success' && window.apptileWebSDK.moduleExports?.functions?.fetchBulkProducts
        ) {
          const queryResponse = await window?.apptileWebSDK?.moduleExports?.functions?.fetchBulkProducts();
          await shopifyObjectCache.clearProductsData();
          await shopifyObjectCache.setProductsList(queryResponse);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loaded, true);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loadedAt, Date.now());
          await shopifyObjectCache.setConfigValue(entityConfigKeys.appId, appId);
          return '';
        } else {
          if (running < 10) return getProducts();
          else return '';
        }
      };
      return await getProducts();
    } else if (entity === 'COLLECTION') {
      const getCollections: any = async () => {
        await new Promise((res: any) =>
          setTimeout(() => {
            res();
          }, 1500),
        );
        running += 1;
        const ShopifyDSModel = shopifyModelSel(store.getState());
        const queryRunner = ShopifyDSModel?.get('queryRunner');
        if (queryRunner && queryRunner.runQuery) {
          const countryCode = ShopifyDSModel?.getIn(['shop','paymentSettings','countryCode']) ?? 'US';
          const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_COLLECTIONS_FOR_DROPDOWN, {first: 100, query: '', countryCode});
          const {transformedData} = processShopifyGraphqlQueryResponse(
            queryResponse,
            {transformer: TransformSearchCollections},
            ShopifyDSModel?.get('shop'),
            ShopifyDSModel,
          );
          await shopifyObjectCache.clearCollectionsData();
          await shopifyObjectCache.setCollectionsList(transformedData);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loaded, true);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loadedAt, Date.now());
          await shopifyObjectCache.setConfigValue(entityConfigKeys.appId, appId);
          return '';
        } else if (
          (window.apptileWebSDK.status == 'success' && window.apptileWebSDK.moduleExports?.functions?.fetchBulkCollections)
        ) {
          const queryResponse = await window?.apptileWebSDK?.moduleExports?.functions?.fetchBulkCollections();
          await shopifyObjectCache.clearCollectionsData();
          await shopifyObjectCache.setCollectionsList(queryResponse);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loaded, true);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loadedAt, Date.now());
          await shopifyObjectCache.setConfigValue(entityConfigKeys.appId, appId);
          return '';
        } else {
          if (running < 10) return getCollections();
          else return '';
        }
      };
      return await getCollections();
    } else if (entity === 'APPTILE_ORDERS') {
      entityLoadHandler = ShopifyShopApi.fetchApptileOrders;
      shopifyObjectCache.clearApptileOrdersData();
    } else if (entity === 'BLOG') {
      const getBlogs: any = async () => {
        await new Promise((res: any) =>
          setTimeout(() => {
            res();
          }, 1500),
        );
        running += 1;
        const ShopifyDSModel = shopifyModelSel(store.getState());
        const queryRunner = ShopifyDSModel?.get('queryRunner');
        if (queryRunner && queryRunner.runQuery) {
          const queryResponse = await queryRunner.runQuery('query', BlogGqls.GET_BLOGS, {first: 100});
          const {transformedData} = processShopifyGraphqlQueryResponse(
            queryResponse,
            {transformer: TransformGetBlogs},
            ShopifyDSModel?.get('shop'),
            ShopifyDSModel,
          );
          await shopifyObjectCache.clearBlogsData();
          await shopifyObjectCache.setBlogsList(transformedData);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loaded, true);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loadedAt, Date.now());
          await shopifyObjectCache.setConfigValue(entityConfigKeys.appId, appId);
          return '';
        } else if (
          (window.apptileWebSDK.status == 'success' && window.apptileWebSDK.moduleExports?.functions?.fetchBulkBlogs)
        ) {
          const queryResponse = await window?.apptileWebSDK?.moduleExports?.functions?.fetchBulkBlogs();
          await shopifyObjectCache.clearBlogsData();
          await shopifyObjectCache.setBlogsList(queryResponse);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loaded, true);
          await shopifyObjectCache.setConfigValue(entityConfigKeys.loadedAt, Date.now());
          await shopifyObjectCache.setConfigValue(entityConfigKeys.appId, appId);
          return '';
        } else {
          if (running < 10) return getBlogs();
          else return '';
        }
      };
      return await getBlogs();
    }
    if(entityLoadHandler) await entityLoadHandler(appId).then(async response => {
      logger.info(`Bulk fetch : `, response.data);
      await shopifyObjectCache.setConfigValue(entityConfigKeys.taskId, response.data.id);
      loadDataFromBulkTask(response.data.shopName, 0);
      return [];
    });
  }

  async function loadDataFromBulkTask(shopName: string, retry: number) {
    try {
      const downloadResponse = await fetch(`${CDN_HOST_URL}/shop-items/${shopName}/${entity.toLowerCase()}.json`);
      const jsonData = await downloadResponse.text();
      const entityList = JSON.parse(jsonData);
      if (entity === 'PRODUCT') {
        await shopifyObjectCache.setProductsList(entityList);
      } else if (entity === 'COLLECTION') {
        await shopifyObjectCache.setCollectionsList(entityList);
      } else if (entity === 'APPTILE_ORDERS') {
        await shopifyObjectCache.setApptileOrdersList(entityList);
      }
      await shopifyObjectCache.setConfigValue(entityConfigKeys.loaded, true);
      await shopifyObjectCache.setConfigValue(entityConfigKeys.loadedAt, Date.now());
      await shopifyObjectCache.setConfigValue(entityConfigKeys.appId, appId);

      // logger.info(`productList:`, productList);
      return entityList;
    } catch (err) {
      await new Promise((res: any) =>
        setTimeout(() => {
          res();
        }, 1500),
      );
      if (retry < 20) {
        return loadDataFromBulkTask(shopName, retry + 1);
      } else {
        return getEntityBulkData(entity, appId);
      }
    }
  }
}
