export type ShopifyObjectCacheProduct = {
  id: string;
  handle: string;
  title: string;
  image: string;
};

export type ShopifyObjectCacheApptileOrders = {
  id: string;
  createdAt: string;
  currentTotalPriceSet: {
    shopMoney: {
      amount: string;
      currencyCode: string;
    };
  };
};

export type ShopifyObjectCacheCollection = {
  id: string;
  handle: string;
  title: string;
  image: string;
  productsCount: number;
};

const configKeys = [
  'productsLoaded',
  'productsBulkTaskId',
  'productsLoadedAt',
  'productsAppId',
  'collectionsLoaded',
  'collectionsBulkTaskId',
  'collectionsLoadedAt',
  'collectionsAppId',
  'blogsLoaded',
  'blogsBulkTaskId',
  'blogsLoadedAt',
  'blogsAppId',
  'apptileOrdersLoaded',
  'apptileOrdersBulkTaskId',
  'apptileOrdersLoadedAt',
  'apptileOrdersAppId',
] as const;

export type ShopifyObjectCacheConfigKeys = (typeof configKeys)[number];
