$__iconfont__data: map-merge(if(global_variable_exists('__iconfont__data'), $__iconfont__data, ()), (
	"ApptileWebIcons": (
		"TRBL-button-top": "\ea01",
		"TRBL-button": "\ea02",
		"accordion": "\ea03",
		"alignment-bottom": "\ea04",
		"alignment-center": "\ea05",
		"alignment-left": "\ea06",
		"alignment-middle": "\ea07",
		"alignment-right": "\ea08",
		"alignment-top": "\ea09",
		"analytics-outline": "\ea0a",
		"apptile-live": "\ea0b",
		"back-arrow": "\ea0c",
		"badge": "\ea0d",
		"bell-outline": "\ea0e",
		"book-outline": "\ea0f",
		"border-radius-left": "\ea10",
		"border-radius": "\ea11",
		"brands-outline": "\ea12",
		"button": "\ea13",
		"calender": "\ea14",
		"checkbox": "\ea15",
		"clock-refresh": "\ea16",
		"clock": "\ea17",
		"container": "\ea18",
		"datasource": "\ea19",
		"delete": "\ea1a",
		"down-arrow": "\ea1b",
		"download-outline": "\ea1c",
		"dropDownArrow": "\ea1d",
		"edit-icon": "\ea1e",
		"expression": "\ea1f",
		"gear-outline": "\ea20",
		"go-live": "\ea21",
		"help": "\ea22",
		"house-outline": "\ea23",
		"icon": "\ea24",
		"image-carousel": "\ea25",
		"image-list": "\ea26",
		"image": "\ea27",
		"integration-disconnect": "\ea28",
		"integrations": "\ea29",
		"list-view": "\ea2a",
		"local-storage": "\ea2b",
		"magnify": "\ea2c",
		"menu-vertical": "\ea2d",
		"modal": "\ea2e",
		"notifications": "\ea2f",
		"pages-outline": "\ea30",
		"pages": "\ea31",
		"percentage-outline": "\ea32",
		"pills": "\ea33",
		"pin-fill": "\ea34",
		"pin-outline": "\ea35",
		"query": "\ea36",
		"radio-button": "\ea37",
		"rich-text": "\ea38",
		"select": "\ea39",
		"settings": "\ea3a",
		"slider-range": "\ea3b",
		"small-shop": "\ea3c",
		"snapshots-outline": "\ea3d",
		"star-rating": "\ea3e",
		"state": "\ea3f",
		"switch": "\ea40",
		"text-input": "\ea41",
		"text": "\ea42",
		"themes": "\ea43",
		"tiles-old": "\ea44",
		"tiles": "\ea45",
		"timer": "\ea46",
		"video-player": "\ea47",
		"web-view": "\ea48",
		"your-design": "\ea49"
	)
));


$create-font-face: true !default; // should the @font-face tag get created?

// should there be a custom class for each icon? will be .filename
$create-icon-classes: true !default; 

// what is the common class name that icons share? in this case icons need to have .icon.filename in their classes
// this requires you to have 2 classes on each icon html element, but reduced redeclaration of the font family
// for each icon
$icon-common-class: 'icon' !default;

// if you whish to prefix your filenames, here you can do so.
// if this string stays empty, your classes will use the filename, for example
// an icon called star.svg will result in a class called .star
// if you use the prefix to be 'icon-' it would result in .icon-star
$icon-prefix: '' !default; 

// helper function to get the correct font group
@function iconfont-group($group: null) {
  @if (null == $group) {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  @if (false == map-has-key($__iconfont__data, $group)) {
    @warn 'Undefined Iconfont Family!';
    @return ();
  }
  @return map-get($__iconfont__data, $group);
}

// helper function to get the correct icon of a group
@function iconfont-item($name) {
  $slash: str-index($name, '/');
  $group: null;
  @if ($slash) {
    $group: str-slice($name, 0, $slash - 1);
    $name: str-slice($name, $slash + 1);
  } @else {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  $group: iconfont-group($group);
  @if (false == map-has-key($group, $name)) {
    @warn 'Undefined Iconfont Glyph!';
    @return '';
  }
  @return map-get($group, $name);
}

// complete mixing to include the icon
// usage:
// .my_icon{ @include iconfont('star') }
@mixin iconfont($icon) {
  $slash: str-index($icon, '/');
  $group: null;
  @if ($slash) {
    $group: str-slice($icon, 0, $slash - 1);
  } @else {
    $group: nth(map-keys($__iconfont__data), 1);
  }
  &:before {
    font-family: $group;
    font-style: normal;
    font-weight: 400;
    content: iconfont-item($icon);
  }
}

// creates the font face tag if the variable is set to true (default)
@if $create-font-face == true {
  @font-face {
   font-family: "ApptileWebIcons";
   src: url('../webfonts/ApptileWebIcons.eot'); /* IE9 Compat Modes */
   src: url('../webfonts/ApptileWebIcons.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
      url('../webfonts/ApptileWebIcons.woff') format('woff'), /* Pretty Modern Browsers */
      url('../webfonts/ApptileWebIcons.ttf')  format('truetype'), /* Safari, Android, iOS */
      url('../webfonts/ApptileWebIcons.svg') format('svg'); /* Legacy iOS */
  }
}

// creates icon classes for each individual loaded svg (default)
@if $create-icon-classes == true {
  .#{$icon-common-class} {
    font-style: normal;
    font-weight: 400;

    @each $icon, $content in map-get($__iconfont__data, "ApptileWebIcons") {
      &.#{$icon-prefix}#{$icon}:before {
        font-family: "ApptileWebIcons";
        content: iconfont-item("ApptileWebIcons/#{$icon}");
      }
    }
  }
}
