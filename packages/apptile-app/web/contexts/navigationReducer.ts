// Define the view types
export enum DashboardViewType {
  APPS = 'APPS',
  INTEGRATIONS = 'INTEGRATIONS',
  NOTIFICATIONS = 'NOTIFICATIONS',
  ANALYTICS = 'ANALYTICS',
}

// Define the state type
export interface NavigationState {
  currentView: DashboardViewType;
  previousView: DashboardViewType | null;
}

// Define action types
export type NavigationAction =
  | {type: 'NAVIGATE_TO'; payload: DashboardViewType}
  | {type: 'SET_PREVIOUS_VIEW'; payload: DashboardViewType};

// Initial state
export const initialState: NavigationState = {
  currentView: DashboardViewType.APPS,
  previousView: null,
};

// Reducer function
export function navigationReducer(state: NavigationState, action: NavigationAction): NavigationState {
  switch (action.type) {
    case 'NAVIGATE_TO':
      return {
        ...state,
        previousView: state.currentView,
        currentView: action.payload,
      };
    case 'SET_PREVIOUS_VIEW':
      return {
        ...state,
        previousView: action.payload,
      };
    default:
      return state;
  }
}
