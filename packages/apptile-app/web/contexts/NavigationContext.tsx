import React, {createContext, useContext, useReducer, ReactNode, useCallback} from 'react';
import {DashboardViewType, type NavigationState, navigationReducer, initialState} from './navigationReducer';

// Create the context
interface NavigationContextType {
  state: NavigationState;
  navigateTo: (view: DashboardViewType) => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

// Provider component
interface NavigationProviderProps {
  children: ReactNode;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({children}) => {
  const [state, dispatch] = useReducer(navigationReducer, initialState);

  const navigateTo = useCallback((view: DashboardViewType) => {
    dispatch({type: 'NAVIGATE_TO', payload: view});
  }, []);

  const value = {state, navigateTo};

  return <NavigationContext.Provider value={value}>{children}</NavigationContext.Provider>;
};

// Custom hook for using the navigation context
export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}
