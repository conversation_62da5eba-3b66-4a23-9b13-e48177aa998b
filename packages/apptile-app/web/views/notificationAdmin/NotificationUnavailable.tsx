import React from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {useParams} from 'react-router';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();


const NotificationProviderSupportLinks = {
  klaviyo: {
    login: 'https://www.klaviyo.com/login',
    help: 'https://help.klaviyo.com/hc/en-us/articles/360006653972',
  },
  moEngage: {
    login: 'https://dashboard-01.moengage.com/v4/#/auth',
    help: 'https://help.moengage.com/hc/en-us/articles/208735856-Overview-Mobile-Push#h_01HKJ3FZ15PNJEDZM0H8GYC8X0',
  },
  cleverTap: {
    login: 'https://eu1.dashboard.clevertap.com/',
    help: 'https://docs.clevertap.com/docs/push',
  },
};


export default function NotificationUnavailable() {
  const {notificationId} = useParams();
  const loginText = NotificationProviderSupportLinks[notificationId]?.login;
  const helpText = NotificationProviderSupportLinks[notificationId]?.help;
  return (
    <View style={styles.wrapper}>
      <View style={styles.container}>
        <Text style={[commonStyles.baseText, styles.header]}>
          Your Apptile Push Notification Dashboard is currently disabled.
        </Text>
        <Text style={[commonStyles.baseText, styles.subHeader]}>
          You've chosen to manage push notifications via {notificationId}.{' '}
          {loginText && (
            <>
              Click{' '}
              <Pressable onPress={() => window.open(loginText)}>
                <Text style={[commonStyles.baseText, styles.subHeaderLink]}>here</Text>
              </Pressable>{' '}
              to send push campaigns using {notificationId}.
            </>
          )}
        </Text>
        {helpText && (
          <Text style={[commonStyles.baseText, styles.subHeader]}>
            Need help on sending push notifications on {notificationId}?{' '}
            <Pressable onPress={() => window.open(helpText)}>
              <Text style={[commonStyles.baseText, styles.subHeaderLink]}>Learn more</Text>
            </Pressable>
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    maxWidth: '80%',
  },
  wrapper: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    flex: 1,
  },
  header: {
    fontSize: 20,
    marginBottom: 20,
    color: '#000',
    lineHeight: 25,
    fontWeight: '500',
    textAlign: 'center',
  },
  subHeader: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
    lineHeight: 20,
  },
  subHeaderLink: {
    color: '#0073e6',
    textDecorationLine: 'underline',
    fontSize: 16,
    lineHeight: 20,
  },
});
