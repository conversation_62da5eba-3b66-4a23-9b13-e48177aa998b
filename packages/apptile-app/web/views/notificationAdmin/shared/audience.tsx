import React from 'react';
import {StyleSheet, View} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import Button from '@/root/web/components-v2/base/Button';
import {PushNotificationApi} from '@/root/web/api/PushNotificationApi';
import {useDispatch, useSelector} from 'react-redux';
import {makeToast} from '@/root/web/actions/toastActions';
import {useParams} from '@/root/web/routing.web';
import {isEmpty} from 'lodash';
import {NotificationPaneContext} from '../context';

import {currentPlanFeaturesSelector} from '@/root/web/selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {setOpenPremiumModal} from '@/root/web/actions/editorActions';

import TextElement from '@/root/web/components-v2/base/TextElement';
import DropDownControl from '../../../components/controls/DropDownControl';

const styles = StyleSheet.create({
  rightPaneWrapper: {
    marginHorizontal: 16,
    marginTop: 89,
    flex: 1,
  },
  rightPaneHeading: {
    color: theme.TEXT_COLOR,
    fontSize: 17,
  },
  rightPaneSubHeading: {
    color: '#1D1D1C',
    marginBottom: 15,
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    bottom: 25,
  },
  buttonWidth: {
    width: '45%',
    minHeight: 40,
  },
  solidButton: {
    backgroundColor: '#262626',
    borderColor: '#262626',
  },
  justifyEnd: {
    justifyContent: 'flex-end',
  },
  rightPaneContent: {
    flex: 1,
    marginTop: 45,
  },
});

type IUserSegments = {
  node: INode;
};

type INode = {
  id: string;
  name: string;
  query: string;
};

type SetupAudienceProps = {
  record: Record<string, string>;
  updateRecord: (key: string, value: any) => void;
};

export const SetupAudience: React.FC<SetupAudienceProps> = props => {
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isGated = !currentPlanFeatures.includes(allAvailablePlans.PRO);
  const openToggle = () => {
    dispatch(setOpenPremiumModal(true, allAvailablePlans.PRO));
  };

  const dispatch = useDispatch();
  const param = useParams();

  const {record, updateRecord} = props;
  const {setPane: setActivePane} = React.useContext(NotificationPaneContext);

  const [audienceList, setAudienceList] = React.useState<IUserSegments[]>([]);

  const appID = param.id as string;

  React.useEffect(() => {
    if (isEmpty(appID)) return;
    const fetchUserSegments = async () => {
      try {
        const result = await PushNotificationApi.shopifyUserSegment<IUserSegments[]>(appID);
        setAudienceList(result.data);
      } catch (err) {
        dispatch(
          makeToast({
            content: 'Unable to fetch user segments',
            appearances: 'info',
          }),
        );
      }
    };

    // commenting this because, we are not using this
    // fetchUserSegments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appID]);

  const updateUserSegment = (val: string) => {
    const pickUserSegment = audienceList.find(entry => entry.node.name === val);

    if (pickUserSegment) {
      updateRecord('userSegmentName', pickUserSegment.node.name);
      updateRecord('userSegmentId', pickUserSegment.node.id);
    }
  };

  return (
    <View style={styles.rightPaneWrapper}>
      {/* <TextElement fontWeight="600" style={styles.rightPaneHeading} lineHeight="md">
        Set Up & Audience
      </TextElement> */}
      <View style={styles.rightPaneContent}>
        {/* <View>
          <TextElement fontWeight="600" fontSize="sm" style={styles.rightPaneSubHeading}>
            Who is your audience?
          </TextElement>
          <DropDownControl
            options={[{name: 'All users', value: 'ALL_USERS'}]}
            onChange={val => updateRecord('targetAudienceType', val)}
            value={record.targetAudienceType}
            defaultValue={record.targetAudienceType}
            nameKey={'name'}
            valueKey={'value'}
            disableBinding={true}
          />
        </View> */}
        <Spacer size={20} />
        {/* {record.targetAudienceType && record.targetAudienceType === 'SHOPIFY_USER_SEGMENT' && (
          <View>
            <TextElement fontWeight="600" fontSize="sm" style={styles.rightPaneSubHeading}>
              Select your Shopify user segment
            </TextElement>
            <DropDownControl
              options={audienceList.map(entry => {
                return {name: entry.node.name, value: entry.node.name};
              })}
              onChange={val => updateUserSegment(val)}
              value={record.userSegmentName || ''}
              defaultValue={record.userSegmentName || ''}
              nameKey={'name'}
              valueKey={'value'}
              disableBinding={true}
            />
          </View>
        )} */}
      </View>
      <View style={[styles.buttonContainer, styles.justifyEnd]}>
        <Button
          onPress={() => setActivePane('Design & Content')}
          containerStyles={[styles.buttonWidth, styles.solidButton]}>
          NEXT
        </Button>
      </View>
    </View>
  );
};

type SpacerPros = {
  size?: number;
};

const Spacer: React.FC<SpacerPros> = props => {
  const style = {
    height: props.size || 10,
    width: '100%',
  };

  return <View style={style} />;
};
