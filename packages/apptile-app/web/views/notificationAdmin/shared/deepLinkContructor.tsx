import React, {useEffect, useState} from 'react';
import {View, StyleSheet} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {generateProductDeepLink, generateCollectionDeepLink, generateHomeDeepLink} from '../generateDeepLink';
import {ShopifyItemObjectPicker} from '../../../integrations/shopify/components/ShopifyItemPicker';
import {makeToast} from '@/root/web/actions/toastActions';
import {get} from 'lodash';
import DropDownControl from '../../../components/controls/DropDownControl';
import {apptileStateSelector} from 'apptile-core';
import {getLinkingPrefixesInDevAndWeb} from '@/root/app/common/utils/getLinkingPrefixes';
import {allAvailablePlans} from 'apptile-core';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import TextInput from '../../../components-v2/base/TextInput';

type IDeepLinkConstructor = {
  selectedAction: string;
  updateSelectedAction: any;
  itemValue: string;
  updateRecord: (key: string, value: any) => void;
};

type IProduct = {
  id: string;
  title: string;
  handle: string;
  status: string;
  featuredImage: FeaturedImage;
};

type IExternalLink = {
  link: string;
};

type FeaturedImage = {
  url: string;
};

type ICollection = {
  id: string;
  title: string;
  handle: string;
  image: null;
};

export const DeepLinkConstructor: React.FC<IDeepLinkConstructor> = ({
  selectedAction,
  updateSelectedAction,
  itemValue,
  updateRecord,
}) => {
  const {current: currentAppConfig} = useSelector((state: EditorRootState) => state.appConfig);
  const settingsInfo: [
    {
      screenName: string;
      linkingURL: string;
      params: string[];
    },
  ] = currentAppConfig?.get('settings')?.get('Linking')?.get('values')?.get('links');
  const getPathPrefixFromScreenName = (screenName: string) => {
    const getProductLinkingUrl = settingsInfo.find(setting => setting.screenName === screenName)?.linkingURL;
    const pathPrefix = getProductLinkingUrl ? getProductLinkingUrl?.split('/:')[0] : '';
    return pathPrefix;
  };
  const dispatch = useDispatch();
  const apptileState = useSelector(apptileStateSelector);
  const [loadedPrefix, setLoadedPrefix] = useState<string[]>([]);

  useEffect(() => {
    if (apptileState.appId) {
      getLinkingPrefixesInDevAndWeb(apptileState.appId as string).then(
        prefixes => prefixes.length && setLoadedPrefix(prefixes as string[]),
      );
    }
  }, [apptileState]);

  const updateDeepLink = (
    path: 'product' | 'collection' | 'home' | 'externalLink',
    config?: ICollection | IProduct | IExternalLink,
  ) => {
    if (!loadedPrefix || loadedPrefix.length === 0) {
      dispatch(
        makeToast({
          content: 'To generate deepLink, Please add prefix in your app',
          appearances: 'error',
        }),
      );

      return;
    }
    const deepLinkPrefix = loadedPrefix[0] + '://';

    if (path === 'home') {
      const {meta, deepLinkURL} = generateHomeDeepLink(deepLinkPrefix);
      updateRecord('deepLinkMetaData', {...meta, UIEntity: {action: 'home', data: null}});
      updateRecord('deepLinkUrl', deepLinkURL);
    }

    if (path === 'product' && config) {
      const pathPrefix = getPathPrefixFromScreenName('Product');
      const {meta, deepLinkURL} = generateProductDeepLink(deepLinkPrefix, config.handle, pathPrefix);
      updateRecord('deepLinkMetaData', {...meta, UIEntity: {action: 'product', data: config}});
      updateRecord('imageUrl', config?.image?.url);
      updateRecord('deepLinkUrl', deepLinkURL);
    }

    if (path === 'collection' && config) {
      const pathPrefix = getPathPrefixFromScreenName('Collection');
      const {meta, deepLinkURL} = generateCollectionDeepLink(deepLinkPrefix, config.handle, pathPrefix);
      updateRecord('deepLinkMetaData', {...meta, UIEntity: {action: 'collection', data: config}});
      updateRecord('imageUrl', config?.image?.url);
      updateRecord('deepLinkUrl', deepLinkURL);
    }

    if (path === 'externalLink' && config) {
      updateRecord('deepLinkUrl', config.link);
    }
  };

  return (
    <>
      <DropDownControl
        label="Link To"
        value={selectedAction}
        onChange={val => {
          updateSelectedAction(val);
          if (val === 'home') {
            updateDeepLink('home');
          }
        }}
        options={[
          {name: 'Product', value: 'product', gating: allAvailablePlans.PRO},
          {name: 'Collection', value: 'collection', gating: allAvailablePlans.PRO},
          {name: 'Home', value: 'home'},
          {name: 'External link', value: 'externalLink', gating: allAvailablePlans.PRO},
        ]}
        defaultValue={selectedAction}
        disableBinding={true}
        nameKey={'name'}
        valueKey={'value'}
        gatingKey={'gating'}
      />

      {selectedAction === 'product' && (
        <>
          <View style={styles.spacer} />
          <View style={styles.itemPickerWrapper}>
            <ShopifyItemObjectPicker
              itemType={'product'}
              value={itemValue}
              onChange={val => updateDeepLink('product', val as any)}
              label={'Product'}
              name={''}
            />
          </View>
        </>
      )}
      {selectedAction === 'collection' && (
        <>
          <View style={styles.spacer} />
          <View style={styles.itemPickerWrapper}>
            <ShopifyItemObjectPicker
              itemType={'collection'}
              value={itemValue}
              onChange={val => updateDeepLink('collection', val as any)}
              label={'Collection'}
              name={''}
            />
          </View>
        </>
      )}
      {selectedAction === 'externalLink' && (
        <>
          <View style={styles.spacer} />
          <View style={styles.itemPickerWrapper}>
            <TextInput
              label="External link"
              onChangeText={value =>
                updateDeepLink('externalLink', {
                  link: value,
                })
              }
            />
          </View>
        </>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  itemPickerWrapper: {width: '100%', borderColor: 'transparent'},
  spacer: {
    height: 10,
    width: '100%',
  },
});
