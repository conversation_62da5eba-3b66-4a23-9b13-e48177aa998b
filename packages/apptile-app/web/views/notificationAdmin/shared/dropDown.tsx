import React from 'react';
import theme from '@/root/web/styles-v2/theme';

const webStyle = {
  select: {
    backgroundColor: theme.TERTIARY_BACKGROUND,
    width: '100%',
    padding: 15,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    border: 'none',
    fontFamily: theme.FONT_FAMILY,
    fontSize: 13,
    lineHeight: 15,
    color: '#000',
  },
};

type DropDownProps = {
  value: string;
  options: {label: string; value: string}[];
  onSelect: (value: string) => void;
  disabled?: boolean;
};

export const DropDown: React.FC<DropDownProps> = props => {
  const {value, options, onSelect, disabled} = props;

  return (
    <select disabled={disabled} style={webStyle.select} onChange={sel => onSelect(sel.target.value)} value={value}>
      <option value="" key="(none)">
        --- Select A Value ---
      </option>
      {options.map(item => {
        return (
          <option value={item.value} key={item.value}>
            {item.label}
          </option>
        );
      })}
    </select>
  );
};
