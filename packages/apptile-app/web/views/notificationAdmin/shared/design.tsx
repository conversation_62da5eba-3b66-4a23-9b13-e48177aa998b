import React from 'react';
import {StyleSheet, View, Text, Pressable, Image, ImageSourcePropType} from 'react-native';

import {MaterialCommunityIcons} from 'apptile-core';
import Button from '@/root/web/components-v2/base/Button';

import {get} from 'lodash';
import {DeepLinkConstructor} from './deepLinkContructor';
import {NotificationPaneContext} from '../context';
import {useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';

import TextElement from '@/root/web/components-v2/base/TextElement';
import CodeInputControl from '../../../components/controls/CodeInputControl';
import AssetChooseDialog from '../../../components/controls/assetEditor/assetChooseDialog';
import Tooltip from '../../../components-v2/base/Tooltip/Index';
import { useNotificationPlaygroundContext, validationMap } from '../manualNotification/context';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();


const styles = StyleSheet.create({
  rightPaneWrapper: {
    marginHorizontal: 16,
    marginTop: 89,
    flex: 1,
  },
  rightPaneHeading: {
    fontSize: 17,
    color: theme.TEXT_COLOR,
    paddingBottom: 25,
  },
  rightPaneSubHeading: {
    color: '#1D1D1C',
    marginBottom: 15,
  },
  separator: {
    height: 0.75,
    width: '100%',
    backgroundColor: '#BFBFBF',
    marginVertical: 30,
  },
  contentHeading: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginBottom: 10,
  },
  contentLabel: {
    fontFamily: theme.FONT_FAMILY,
    color: '#1D1DC',
  },
  buttonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    bottom: 0,
    width: '100%',
    paddingVertical: 10,
    paddingHorizontal: 4,
    position: 'absolute',
    backgroundColor: '#ffffff',
  },
  buttonWidth: {
    width: '45%',
    minHeight: 40,
  },
  justifyEnd: {
    justifyContent: 'flex-end',
  },
  solidButton: {
    backgroundColor: '#262626',
    borderColor: '#262626',
  },
  outlineButton: {borderColor: '#262626'},
  outlineButtonText: {
    color: '#262626',
  },
  rightPaneContent: {
    flex: 1,
    flexBasis: 'auto',
    overflow: 'scroll',
    paddingBottom: 45,
    paddingHorizontal: 2,
  },
  imgContainer: {
    position: 'relative',
    width: '100%',
  },
  removeAsset: {
    backgroundColor: '#262626',
    width: 25,
    height: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    alignSelf: 'flex-end',
    position: 'absolute',
    top: 10,
    right: 10,
    zIndex: 1,
  },
  cardImage: {
    width: '100%',
    height: 170,
    borderRadius: 6,
    overflow: 'hidden',
    backgroundColor: '#f1f1f1',
  },
  imageUpload: {
    width: '100%',
    height: 170,
    backgroundColor: '#f1f1f1',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
});

type DesignContentProps = {
  record: Record<string, string>;
  updateRecord: (key: string, value: any) => void;
  disableBackButton?: boolean;
};

export const DesignContent: React.FC<DesignContentProps> = props => {
  const {record, updateRecord} = props;
  const {style, setPane: setActivePane} = React.useContext(NotificationPaneContext);

  const [showPopover, setShowPopover] = React.useState(false);
  const [selectedAsset, setSelectedAsset] = React.useState('');
  const [selectPage, setSelectPage] = React.useState<'product' | 'collection' | 'home'>('home');
  const [selectItemValue, setItemValue] = React.useState('');

  const assetState = useSelector((state: EditorRootState) => state.asset);
  const currentAsset = assetState.assetsById[selectedAsset];
  const [currentStepValid, setCurrentStepValid] = React.useState(true);

  const {current: currentAppConfig} = useSelector((state: EditorRootState) => state.appConfig);
  const settingsInfo: [
    {
      screenName: string;
      linkingURL: string;
      params: string[];
    },
  ] = currentAppConfig?.get('settings')?.get('Linking')?.get('values')?.get('links');

  const getScreenNameFromPathPrefix = (pathPrefix: string) => {
    const getScreenName = settingsInfo.find(setting => setting.linkingURL?.split('/:')[0] === pathPrefix)?.screenName;
    return getScreenName;
  };

  React.useEffect(() => {
    let currentPage = get(record, ['deepLinkMetaData', 'UIEntity', 'action'], null);
    let itemValue = get(record, ['deepLinkMetaData', 'UIEntity', 'data', 'handle'], null);
    const parts = record.deepLinkUrl?.split('://');
    const remaining = parts[1];
    const segments = remaining?.split('/');
    if (!currentPage) {
      if (parts.length > 1) {
        if (segments.length >= 2) {
          currentPage = _.toLower(getScreenNameFromPathPrefix(segments[0]));
        }
      }
    }
    if (!itemValue) {
      if (parts.length > 1) {
        if (segments.length >= 2) {
          itemValue = segments[1];
        }
      }
    }

    if (itemValue) {
      setItemValue(itemValue);
    }
    if (currentPage) {
      setSelectPage(currentPage);
    }
    setCurrentStepValid(validationMap.title(record.title, record) && validationMap.body(record.body, record));
  }, [record]);

  React.useEffect(() => {
    if (currentAsset) {
      updateRecord('imageUrl', currentAsset.thumbUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentAsset]);

  const removeSelectedAsset = () => {
    setSelectedAsset('');
    updateRecord('imageUrl', '');
  };

  return (
    <View style={styles.rightPaneWrapper}>
      <TextElement fontWeight="600" style={styles.rightPaneHeading} lineHeight="md">
        Design & Content
      </TextElement>
      <View style={styles.rightPaneContent}>
        <TextElement style={commonStyles.labelText}>Deeplink</TextElement>
        <View>
          <DeepLinkConstructor
            selectedAction={selectPage}
            updateSelectedAction={setSelectPage}
            itemValue={selectItemValue}
            updateRecord={updateRecord}
          />
        </View>
        {/* <Text style={styles.audienceList}>200 people in this list</Text> */}
        <Separator />
        <TextElement style={commonStyles.labelText}>Content</TextElement>
        <View style={{marginTop: 15}}>
          <View>
            <View style={styles.contentHeading}>
              <TextElement style={styles.contentLabel} fontSize="sm" lineHeight="md">
                Title
              </TextElement>
            </View>
            <CodeInputControl
              value={record.title}
              placeholder="Exclusive collection is calling you 💕"
              onChange={val => updateRecord('title', val)}
              singleLine={false}
              noOfLines={4}
            />
          </View>
          <View style={{marginTop: 30}}>
            <View style={styles.contentHeading}>
              <TextElement style={styles.contentLabel} fontSize="sm" lineHeight="md">
                Body
              </TextElement>
            </View>
            <CodeInputControl
              value={record.body}
              placeholder="Be the first to shop and get 20% off everything. Don't miss it! 👀✌️"
              onChange={val => updateRecord('body', val)}
              singleLine={false}
              noOfLines={4}
            />
          </View>
        </View>
        {style === 'image' && (
          <>
            <Separator />
            <View style={{marginBottom: 30, flexDirection: 'row'}}>
              <View style={commonStyles.labelContainer}>
                <TextElement style={commonStyles.labelText}>Image</TextElement>
              </View>
              {!selectedAsset && !record.imageUrl && (
                <Pressable
                  style={[styles.imageUpload, commonStyles.inputContainer]}
                  onPress={() => {
                    setShowPopover(true);
                  }}>
                  <TextElement style={styles.contentLabel} fontSize="sm" lineHeight="md">
                    + ADD
                  </TextElement>
                </Pressable>
              )}
              {!!record.imageUrl && (
                <View style={[styles.imgContainer, commonStyles.inputContainer]}>
                  <Pressable onPress={removeSelectedAsset} style={styles.removeAsset}>
                    <MaterialCommunityIcons name="close" size={20} color="#FFF" />
                  </Pressable>
                  <Image style={styles.cardImage} resizeMode="cover" source={record.imageUrl as ImageSourcePropType} />
                </View>
              )}
            </View>
          </>
        )}
      </View>
      <AssetChooseDialog
        askURL={false}
        currentAssetId={selectedAsset}
        onSelectAsset={assetId => {
          setSelectedAsset(assetId);
        }}
        onCloseDialog={val => {
          setShowPopover(val);
        }}
        showDialog={showPopover}
      />
      <View style={[styles.buttonContainer, props.disableBackButton && styles.justifyEnd]}>
        {!props.disableBackButton && (
          <Button
            variant="PILL"
            onPress={() => setActivePane('Set Up & Audience')}
            containerStyles={[styles.buttonWidth, styles.outlineButton]}
            textStyles={styles.outlineButtonText}>
            BACK
          </Button>
        )}
        <View style={styles.buttonWidth}>
          <Tooltip
            visible={!currentStepValid}
            tooltipPosition={'Top'}
            tooltip={
              <View style={[styles.tooltip]}>
                <Text style={[commonStyles.baseText, commonStyles.errorText]}>Fill valid values in all fields</Text>
              </View>
            }>
            <Button
              disabled={!currentStepValid}
              onPress={() => setActivePane('Scheduling')}
              containerStyles={[styles.solidButton, !currentStepValid && {backgroundColor: theme.DISABLED_BACKGROUND}]}>
              NEXT
            </Button>
          </Tooltip>
        </View>
      </View>
    </View>
  );
};

const Separator: React.FC = () => {
  return <View style={styles.separator} />;
};
