import React, {useEffect, useState, useCallback} from 'react';
import {View, Text, TextInput, StyleSheet, ActivityIndicator} from 'react-native';
import CheckboxControl from '../../components/controls/CheckboxControl';
import StoreCreditApi from '../../api/StoreCreditApis';
import {useDispatch, useSelector} from 'react-redux';
import {apptileStateSelector, createDeepEqualSelector, datasourceTypeModelSel} from 'apptile-core';
import {makeToast} from '../../actions/toastActions';
import Button from '../../components-v2/base/Button';
import {IStoreCreditCreateSignUpEventRuleData} from '../../api/ApiTypes';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

// Utility to check for missing fields
const checkMissingFields = ({creditAmount, currencyCode}: IStoreCreditCreateSignUpEventRuleData) => {
  const missingFields = [];
  if (!creditAmount) missingFields.push('Credit Amount');
  if (!currencyCode) missingFields.push('Currency Code');
  return missingFields;
};

// Utility to check if states have changed
const hasStateChanged = (initialValues: any, currentValues: any) =>
  initialValues.creditAmount !== currentValues.creditAmount || initialValues.expiryDays !== currentValues.expiryDays;

const StoreCredit = () => {
  const [isEnabled, setIsEnabled] = useState(false);
  const [creditAmount, setCreditAmount] = useState<number | undefined>();
  const [expiryDays, setExpiryDays] = useState<number | undefined>();
  const [initialValues, setInitialValues] = useState({creditAmount: undefined, expiryDays: undefined});
  const [isInitialDataPresent, setIsInitialDataPresent] = useState(false); // Track if initial data exists
  const [isLoading, setLoading] = useState(false);
  const [error, setError] = useState<any | null>(null);

  const appId = useSelector(apptileStateSelector).appId;
  const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const shopifyStoreCurrencySel = createDeepEqualSelector(shopifyModelSel, shopifyDS =>
    shopifyDS?.getIn(['shop', 'paymentSettings', 'currencyCode']),
  );
  const currencyCode = useSelector(shopifyStoreCurrencySel) as string;
  const dispatch = useDispatch();

  // Fetch initial data
  const fetchSignUpEventData = useCallback(async () => {
    setLoading(true);
    try {
      const signUpData = await StoreCreditApi.fetchSignUpEventRule(appId as string);
      const {creditAmount: fetchedCreditAmount, creditExpiryDays: fetchedExpiryDays} = signUpData?.data || {};

      // Set initial data states
      setCreditAmount(fetchedCreditAmount);
      setExpiryDays(fetchedExpiryDays);
      setInitialValues({creditAmount: fetchedCreditAmount, expiryDays: fetchedExpiryDays});

      // Check if initial data was present
      setIsInitialDataPresent(!!fetchedCreditAmount || !!fetchedExpiryDays);

      setIsEnabled(true);
    } catch (e) {
      console.error(e);
      setError(e);
      dispatch(makeToast({content: e?.response?.data?.message || 'Error fetching data', appearances: 'info'}));
    } finally {
      setLoading(false);
    }
  }, [appId, dispatch]);

  useEffect(() => {
    fetchSignUpEventData();
  }, [fetchSignUpEventData]);

  const hasDataChanged = hasStateChanged(initialValues, {creditAmount, expiryDays});
  const missingFields = checkMissingFields({
    creditAmount: creditAmount as number,
    currencyCode,
  });

  const saveData = async () => {
    setLoading(true);

    // If switch is off and initial data exists, delete the rule
    if (!isEnabled && isInitialDataPresent) {
      try {
        await StoreCreditApi.deleteSignUpEventRule(appId as string);
        dispatch(makeToast({content: 'Store credit deleted successfully', appearances: 'success'}));
      } catch (e) {
        console.error(e);
        dispatch(makeToast({content: e?.response?.data?.message || 'Error deleting data', appearances: 'error'}));
      } finally {
        setLoading(false);
      }
      return;
    }

    // If there are missing fields or data hasn't changed, prevent the save
    if (missingFields.length || !hasDataChanged) {
      if (missingFields.length) {
        dispatch(makeToast({content: `Missing fields: ${missingFields.join(', ')}`, appearances: 'error'}));
      }
      setLoading(false);
      return;
    }

    const data: IStoreCreditCreateSignUpEventRuleData = {
      creditAmount: creditAmount as number,
      creditExpiryDays: expiryDays as number,
      currencyCode,
    };

    try {
      if (isInitialDataPresent) {
        // Update data if it already exists
        await StoreCreditApi.updateSignUpEventRule(appId as string, data);
        dispatch(makeToast({content: 'Store credit updated successfully', appearances: 'success'}));
      } else {
        // Create new data if no initial data exists
        await StoreCreditApi.createSignUpEventRule(appId as string, data);
        dispatch(makeToast({content: 'Store credit created successfully', appearances: 'success'}));
      }
    } catch (e) {
      console.error(e);
      dispatch(makeToast({content: e?.response?.data?.message || 'Error saving data', appearances: 'error'}));
    } finally {
      setLoading(false);
    }
  };

  const toggleSwitch = () => setIsEnabled(prevState => !prevState);

  return (
    <View style={styles.wrapper}>
      <View style={styles.headerContainer}>
        <Text style={styles.subHeader}>Configure store credit</Text>
        <Button
          loading={isLoading}
          disabled={
            (!isEnabled && !isInitialDataPresent) || missingFields.length !== 0 || (!hasDataChanged && isEnabled)
          }
          color="CTA"
          onPress={saveData}>
          Save
        </Button>
      </View>

      <View style={styles.container}>
        <View style={styles.switchContainer}>
          <Text style={[commonStyles.baseText, styles.label]}>Provide store credit to customer for sign up</Text>
          <View>
            <CheckboxControl value={isEnabled} onChange={toggleSwitch} />
          </View>
        </View>

        <View>
          <View style={styles.inputWrapper}>
            <View style={styles.inputContainer}>
              <Text style={[commonStyles.baseText, styles.inputLabel]}>Store Credit Amount</Text>
              <View style={styles.currencyContainer}>
                <TextInput
                  style={styles.input}
                  placeholder="Enter amount"
                  keyboardType="numeric"
                  value={creditAmount?.toString()}
                  onChangeText={value => setCreditAmount(Number(value))}
                />

                <Text style={[commonStyles.baseText, styles.inputLabel, styles.currency]}>{currencyCode}</Text>
              </View>
            </View>

            <View style={styles.inputContainer}>
              <View style={styles.expiryDaysHeader}>
                <Text style={[commonStyles.baseText, styles.inputLabel]}>Store Credit Expiry (In days)</Text>
                <View style={styles.expiryDaysContainer}>
                  <CheckboxControl
                    value={!_.isNull(expiryDays)}
                    onChange={() => setExpiryDays(expiryDays ? undefined : 30)}
                  />
                </View>
              </View>
              <View style={styles.currencyContainer}>
                {!_.isNull(expiryDays) && (
                  <>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter number of days"
                      keyboardType="numeric"
                      value={expiryDays?.toString()}
                      onChangeText={value => setExpiryDays(Number(value))}
                    />

                    <Text style={[commonStyles.baseText, styles.inputLabel, styles.currency]}>Days</Text>
                  </>
                )}
              </View>
            </View>
          </View>

          {(!isEnabled || isLoading) && (
            <View style={styles.overlayView}>{isLoading && <ActivityIndicator size="large" />}</View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderWidth: 1,
    borderRadius: 10,
    borderColor: '#E5E5E5',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  overlayView: {
    backgroundColor: 'rgba(250, 250, 250, 0.4)',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
  },
  inputWrapper: {
    padding: 20,
  },
  wrapper: {
    width: '100%',
    marginTop: 32,
  },
  subHeader: {
    fontSize: 16,
    fontWeight: '600',
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 13,
    borderBottomColor: '#E5E5E5',
    borderBottomWidth: 1,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 13,
    marginBottom: 5,
    fontWeight: '500',
  },
  input: {
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#F4F4F4',
  },
  currency: {
    position: 'absolute',
    right: 10,
    top: 10,
  },
  currencyContainer: {
    justifyContent: 'center',
  },
  expiryDaysHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  expiryDaysContainer: {
    scale: 0.7,
  },
});

export default StoreCredit;
