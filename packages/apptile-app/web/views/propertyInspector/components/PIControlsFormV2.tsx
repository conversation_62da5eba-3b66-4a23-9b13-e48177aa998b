import Immutable from 'immutable';
import _ from 'lodash';
import React from 'react';
import {StyleSheet} from 'react-native';
import {EditorProps, PluginEditorsConfig} from 'apptile-core';
import {resolvePluginEditorsConfig} from 'apptile-core';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import ModuleEditor from '../../../components/controls/moduleEditor/moduleEditor';
import PluginPropertyEditor from './PluginPropertyEditor';
import EditorSectionHeader from '@/root/web/components/controls/EditorSectionHeader';
import {getTheme} from '@/root/web/utils/themeSelector';
const theme = getTheme();

interface PIControlsFormProps extends EditorProps {}

const PIControlsForm: React.FC<PIControlsFormProps> = ({...props}) => {
  const {pluginSelector, pageId, target, selectedPluginConfig} = props;
  const pluginId = pluginSelector ? pluginSelector[pluginSelector.length - 1] : null;
  const inModule = !_.isEmpty(selectedPluginConfig?.namespace?.getNamespace());
  const handleMouseLeave = () => {
    const targetBoundariesNodeList = document.querySelectorAll('[id^="hover-target-"]');
    targetBoundariesNodeList.forEach(el => {
      el.setAttribute('style', 'display: none;');
    });
  };

  if (!selectedPluginConfig) {
    return null;
  } else {
    const pluginEditors: PluginEditorsConfig<any> = resolvePluginEditorsConfig(selectedPluginConfig.subtype);
    let relevantEditors = [];
    let restEditors = [];
    if (Array.isArray(pluginEditors.basic)) {
      for (let i = 0; i < pluginEditors.basic.length; ++i) {
        const editor = pluginEditors.basic[i];
        if (editor.targets && editor.targets.includes(target)) {
          let configPathSelector = [selectedPluginConfig.id, 'config', editor.name];
          let configWithProp = selectedPluginConfig.config;
          relevantEditors.push(
            <PluginPropertyEditor
              key={pluginId + editor.name}
              editor={editor}
              entityConfig={selectedPluginConfig}
              config={configWithProp}
              pageId={pageId}
              pluginId={pluginId}
              configPathSelector={configPathSelector}
              isModule={true}
            />,
          );
        } else {
          let configPathSelector = [selectedPluginConfig.id, 'config', editor.name];
          let configWithProp = selectedPluginConfig.config;
          restEditors.push(
            <PluginPropertyEditor
              key={pluginId + editor.name}
              editor={editor}
              entityConfig={selectedPluginConfig}
              config={configWithProp}
              pageId={pageId}
              pluginId={pluginId}
              configPathSelector={configPathSelector}
              isModule={true}
            />,
          );
        }
      }
    }

    return (
      <div style={styles.container} onMouseLeave={handleMouseLeave}>
        {relevantEditors.length > 0 && (
          <CollapsiblePanel
            isOpen={true}
            backgroundStyle={{borderWidth: 0}}
            title="Focussed"
            customHeader={
              <EditorSectionHeader
                label={'Focussed'}
                name={''}
                isPremiumDesabled={false}
                textStyles={{color: theme.TEXT_COLOR}}
              />
            }>
            {relevantEditors}
          </CollapsiblePanel>
        )}
        <div style={{height: 1, backgroundColor: theme.SIDEBAR_BORDER}}></div>
        {restEditors.length > 0 && (
          <CollapsiblePanel
            isOpen={true}
            backgroundStyle={{borderWidth: 0}}
            title="All properties"
            customHeader={
              <EditorSectionHeader
                label={'All properties'}
                name={''}
                isPremiumDesabled={false}
                textStyles={{color: theme.TEXT_COLOR}}
              />
            }>
            {restEditors}
          </CollapsiblePanel>
        )}
        {Object.entries(pluginEditors).map(([section, propEditors], i) => {
          if (section === 'basic') {
            return null;
          }

          return (
            <CollapsiblePanel key={i} title={section} isOpen={true} index={i}>
              {propEditors.map(propEditor => {
                if (propEditor.hidden?.(selectedPluginConfig.config)) return null;

                let configPathSelector = [selectedPluginConfig.id, 'config', propEditor.name];
                let configWithProp = selectedPluginConfig.config;

                switch (propEditor.type) {
                  case 'analyticsEditor':
                    configPathSelector = [selectedPluginConfig.id, propEditor.name];
                    configWithProp = selectedPluginConfig;
                    break;
                  case 'layoutEditor':
                    configPathSelector = [selectedPluginConfig.id, propEditor.name];
                    configWithProp = selectedPluginConfig.layout;
                    break;
                  case 'animationsEditor':
                    configPathSelector = [selectedPluginConfig.id, propEditor.name];
                    configWithProp = selectedPluginConfig.animations;
                    break;
                  case 'moduleEditor':
                    return (
                      <ModuleEditor
                        {...props}
                        key={pluginId + propEditor.name}
                        propertyEditor={propEditor}
                        pageId={pageId}
                        pluginId={pluginId}
                        inModule={inModule}
                        entityConfig={selectedPluginConfig}
                        configPathSelector={configPathSelector}
                      />
                    );
                  case 'stylesEditor':
                    configWithProp = selectedPluginConfig.getIn(['config', 'style'], Immutable.Map());
                    break;
                }
                return (
                  <PluginPropertyEditor
                    key={pluginId + propEditor.name + i}
                    editor={propEditor}
                    entityConfig={selectedPluginConfig}
                    config={configWithProp}
                    pageId={pageId}
                    pluginId={pluginId}
                    configPathSelector={configPathSelector}
                    inModule={inModule}
                  />
                );
              })}
            </CollapsiblePanel>
          );
        })}
      </div>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    fontSize: 12,
  },
  containerRow: {
    flex: 1,
    flexDirection: 'row',
  },
});

export default PIControlsForm;
