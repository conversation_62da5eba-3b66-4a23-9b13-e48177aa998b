import React, {useCallback, useMemo} from 'react';
import {StyleSheet, Text} from 'react-native';
import {EntityEditorProps} from 'apptile-core';
import {pluginEditorComponents, pluginEditorComponentsV2} from '../../../components/pluginEditorComponents';
import {useTheme} from 'apptile-core';
import _, {debounce} from 'lodash';
import {Pressable, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {forwardModulePluginProperty} from '@/root/web/actions/editorActions';
import {selectIsModuleBasicProperty, selectIsModuleProperty, selectIsModuleStyleProperty} from '@/root/web/selectors/EditorSelectors';
import {MaterialCommunityIcons} from 'apptile-core';
import {pluginConfigUpdatePath, pluginConfigSetPathValue} from 'apptile-core';
import {ModuleEditorLocationType} from 'apptile-core';

import {getWidgetsWithThemes} from 'apptile-core';
import Immutable from 'immutable';
import Tooltip from '@/root/web/components-v2/base/Tooltip/Index';

import {useEditorContext} from '@/root/web/context/editorContext';
import {isRemovable} from './themePropertyEditor';
import { CANVAS_SCALE } from '../../prompt-to-app/editor/components/Editor';

import {getTheme} from '@/root/web/utils/themeSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();
const theme = getTheme();
interface PluginPropertyEditorProps extends EntityEditorProps {
  hideExposePropButton?: boolean;
  sendTileAnalytics?: boolean;
  isModule?: boolean;
  onChange?: (value: any) => void;
}

const PluginPropertyEditor: React.FC<PluginPropertyEditorProps> = props => {
  const {
    editor: {name, type, props: configProps, hidden},
    config,
    pageId,
    pluginId,
    configPathSelector,
    entityConfig,
    hideExposePropButton,
    sendTileAnalytics = false,
    isModule = false,
    onChange: onUpdate = () => {},
  } = props;
  const dispatch = useDispatch();
  const inModule = !_.isEmpty(entityConfig?.namespace?.getNamespace());

  const {pluginConfigUpdatePath: updateConfig, pluginConfigSetPathValue: setConfig} = useMemo(
    () => bindActionCreators({pluginConfigUpdatePath, pluginConfigSetPathValue}, dispatch),
    [dispatch],
  );

  const value = config?.get(name);
  const debouncedUpdateConfig = debounce(
    (widgetId, pageId, selector, update, remove) => updateConfig(widgetId, pageId, selector, update, remove),
    300,
  );

  const {forwardModulePluginProperty: setPropertyForModule} = useMemo(
    () => bindActionCreators({forwardModulePluginProperty}, dispatch),
    [dispatch],
  );
  const updatePathSelector = configPathSelector.slice(1, -1);

  const onSetRawValue = useCallback(
    (v: unknown) => {
      setConfig(pluginId, pageId, updatePathSelector.concat([name]), v);
      if (sendTileAnalytics) {
        onUpdate(v);
      }
    },
    [setConfig, pluginId, pageId, updatePathSelector, name, sendTileAnalytics, onUpdate],
  );
  const onChange = useCallback(
    (v: unknown, debounced = false, remove = false) => {
      if (debounced) {
        debouncedUpdateConfig(
          pluginId,
          pageId,
          updatePathSelector,
          remove ? {} : {[name]: v},
          remove ? [name] : undefined,
        );
      } else {
        updateConfig(pluginId, pageId, updatePathSelector, remove ? {} : {[name]: v}, remove ? [name] : undefined);
      }
      if (sendTileAnalytics) {
        onUpdate(v);
      }
    },
    [sendTileAnalytics, debouncedUpdateConfig, pluginId, pageId, updatePathSelector, name, updateConfig, onUpdate],
  );
  const onCustomPropChange = useCallback(
    (key: string, v: unknown, debounced = false, remove = false) => {
      if (debounced) {
        debouncedUpdateConfig(
          pluginId,
          pageId,
          updatePathSelector,
          remove ? {} : {[key]: v},
          remove ? [key] : undefined,
        );
      } else {
        updateConfig(pluginId, pageId, updatePathSelector, remove ? {} : {[key]: v}, remove ? [key] : undefined);
      }
      if (sendTileAnalytics) {
        onUpdate(v);
      }
    },
    [sendTileAnalytics, debouncedUpdateConfig, pluginId, pageId, updatePathSelector, updateConfig, onUpdate],
  );

  const nonNamespacedPluginId = entityConfig?.namespace?.getPluginId();
  const isModuleProp = useSelector(state =>
    selectIsModuleProperty(state, pageId, pluginId, nonNamespacedPluginId, configPathSelector),
  );
  const isModuleStyleProp = useSelector(state =>
    selectIsModuleStyleProperty(state, pageId, pluginId, nonNamespacedPluginId, configPathSelector),
  );
  const isModuleBasicProp = useSelector(state =>
    selectIsModuleBasicProperty(state, pageId, pluginId, nonNamespacedPluginId, configPathSelector),
  );


  const onModulePropChange = useCallback(
    (location: ModuleEditorLocationType) => {
      setPropertyForModule(
        pageId,
        entityConfig?.id,
        [nonNamespacedPluginId].concat(configPathSelector.slice(1)),
        location === 'basic' ? !isModuleBasicProp : location === 'module' ? !isModuleProp : !isModuleStyleProp,
        name,
        location,
      );
    },
    [configPathSelector, isModuleProp, name, nonNamespacedPluginId, pageId, entityConfig?.id, setPropertyForModule],
  );

  if (hidden?.(entityConfig?.config)) {
    return null;
  }

  const editorComponents = isModule ? pluginEditorComponentsV2 : pluginEditorComponents;
  const EditorComponent = editorComponents[type];
  const isStyleConfigPath = /config.style/g.test(configPathSelector.join('.'));
  const handleMouseEnter = () => {
    let targets = [];
    const editorTargets = (props.editor as any).targets || [];
    for (let i = 0; i < editorTargets.length; ++i) {
      if (editorTargets[i].includes("*")) {
        const sanitized = editorTargets[i].replace("*", '');
        targets.push(`[id="${sanitized}"]`);
      } else {
        targets.push("#" + editorTargets[i]);
      }
    }
    if (targets.length > 0) {
      const targetEls = document.querySelectorAll(targets.join(","));
      const targetBoundariesNodeList = document.querySelectorAll('[id^="hover-target-"]');
      const targetBoundaries: HTMLDivElement[] = [];
      targetBoundariesNodeList.forEach(el => {
        el.setAttribute("style", "display: none;");
        targetBoundaries.push(el as HTMLDivElement);
      });

      const canvasRect = {top: 0, left: 0};
      const appCanvas = document.querySelector('#app-canvas');
      if (appCanvas) {
        const rect = appCanvas.getBoundingClientRect();
        canvasRect.left = rect.left;
        canvasRect.top = rect.top;
      }

      targetEls.forEach((targetEl, i) => {
        const rect = targetEl.getBoundingClientRect();
        const boundaryEl = targetBoundaries[i];
        if (boundaryEl) {
          const padding = 6;
          boundaryEl.setAttribute(
            "style",
            `
            position: absolute;
            display: block;
            width: ${(rect.width + 2 * padding) / CANVAS_SCALE}px;
            height: ${(rect.height + 2 * padding) / CANVAS_SCALE}px;
            top: ${((rect.top - canvasRect.top - padding) / CANVAS_SCALE) - 4}px;
            left: ${((rect.left - canvasRect.left - padding) / CANVAS_SCALE) - 4}px;
            border: dashed 2px #007AFF;
            border-radius: 4px;
            pointer-events: none;
            box-sizing: border-box;
          `,
          );
        }
      })
    }
  }

  return (
    <View style={[styles.container, hideExposePropButton ? commonStyles.controlContainer : {}]}>
      {inModule && !hideExposePropButton && (
        <View style={{flexDirection: 'row'}}>
          <View>
            <Pressable style={{padding: 4}} onPress={() => onModulePropChange('basic')}>
              <MaterialCommunityIcons
                color={isModuleBasicProp ? theme.SUCCESS_BACKGROUND : theme.TEXT_COLOR}
                name={isModuleBasicProp ? 'alpha-b-circle' : 'alpha-b-circle-outline'}
                size={14}
              />
            </Pressable>
          </View>
          <View>
            <Pressable style={{padding: 4}} onPress={() => onModulePropChange('module')}>
              <MaterialCommunityIcons
                color={isModuleProp ? theme.SUCCESS_BACKGROUND : theme.TEXT_COLOR}
                name={isModuleProp ? 'alpha-t-circle' : 'alpha-t-circle-outline'}
                size={14}
              />
            </Pressable>
            <Pressable style={{padding: 4}} onPress={() => onModulePropChange('style')}>
              <MaterialCommunityIcons
                color={isModuleStyleProp ? theme.SUCCESS_BACKGROUND : theme.TEXT_COLOR}
                name={isModuleStyleProp ? 'alpha-s-circle' : 'alpha-s-circle-outline'}
                size={14}
              />
            </Pressable>
          </View>
        </View>
      )}
      <div
        style={styles.flex}
        onMouseEnter={handleMouseEnter}
      >
        {isStyleConfigPath ? (
          <ThemePopulatedComponent
            {...props}
            onChange={onChange}
            onCustomPropChange={onCustomPropChange}
            onUpdateRawValue={onSetRawValue}
            isModule={isModule}
          />
        ) : (
          <EditorComponent
            pluginId={entityConfig?.id}
            pageId={pageId}
            configProps={{...configProps}}
            name={name}
            defaultValue={value}
            value={value}
            id={entityConfig?.id + name}
            config={config}
            entityConfig={entityConfig}
            configPathSelector={configPathSelector}
            onChange={onChange}
            onCustomPropChange={onCustomPropChange}
            onUpdateRawValue={onSetRawValue}
          />
        )}
      </div>
    </View>
  );
};

const ThemePopulatedComponent: React.FC<
  EntityEditorProps & {
    onChange: (v: unknown, debounced?: boolean, remove?: boolean) => void;
    onCustomPropChange: (key: string, v: unknown, debounced?: boolean, remove?: boolean) => void;
    onUpdateRawValue: (v: unknown) => void;
    isModule: boolean;
  }
> = ({
  editor: {name, props: configProps, type},
  config,
  pageId,
  configPathSelector,
  entityConfig,
  onChange: onPluginPropChange,
  onCustomPropChange: onPluginCustomPropChange,
  onUpdateRawValue,
  isModule,
}) => {
  const {themeEvaluator} = useTheme();
  const value = config?.get(name);
  const {layout} = useEditorContext();

  const getThemeObj = (sel: string[]) => {
    const styleKeyIdx = _.findIndex(sel, entry => entry === 'style');
    const styleConfigSel = _.filter(
      styleKeyIdx !== -1 ? _.slice(sel, styleKeyIdx + 1) : [],
      entry => !_.isEmpty(entry),
    );

    const pluginSubType = entityConfig?.get('subtype', null);
    const pluginStyleProfile = entityConfig?.getIn(['config', 'style', '_profile']) ?? 'default';

    const widgetsWithThemes = getWidgetsWithThemes();
    const themeProfileSel = pluginSubType ? _.get(widgetsWithThemes, pluginSubType)?.themeProfileSel : [];

    if (!_.isEmpty(themeProfileSel) && _.isArray(themeProfileSel) && !_.isEmpty(styleConfigSel)) {
      const themePath = [...themeProfileSel, pluginStyleProfile, ...styleConfigSel].join('.');
      const themeValue = themeEvaluator(themePath);
      return !_.isEqual(themeValue, themePath)
        ? _.isObject(themeValue)
          ? {path: themePath, value: Immutable.Map(themeValue)}
          : {path: themePath, value: themeValue}
        : {};
    }

    return {};
  };

  let themeIndication =
    /config.style/g.test(configPathSelector.join('.')) && entityConfig?.get('type', null) === 'widget';

  if (layout === 'editorV2') {
    themeIndication = false;
  }

  const isInheritedFromTheme = themeIndication && _.isNil(value) && !!_.get(getThemeObj(configPathSelector), 'value');
  const editorComponents = isModule ? pluginEditorComponentsV2 : pluginEditorComponents;
  const EditorComponent = editorComponents[type];

  const {path: themePath, value: themeValue} = getThemeObj(configPathSelector);

  const onChange = useCallback(
    (v: unknown, debounced = false, remove = false) => {
      if (!remove) {
        remove = isRemovable(v);
      }

      onPluginPropChange(v, debounced, remove);
    },
    [onPluginPropChange],
  );

  const onCustomPropChange = useCallback(
    (key: string, v: unknown, debounced = false, remove = false) => {
      if (!remove) {
        remove = isRemovable(v);
      }
      onPluginCustomPropChange(key, v, debounced, remove);
    },
    [onPluginCustomPropChange],
  );

  return (
    <View style={styles.container}>
      {isInheritedFromTheme && (
        <Tooltip
          visible={true}
          tooltipPosition={'Top'}
          tooltip={
            <View style={[styles.tooltip]}>
              <Text style={[commonStyles.baseText]}>
                Path: <Text style={styles.toolTipText}>{themePath}</Text>
              </Text>
              <Text style={[commonStyles.baseText]}>
                Value: <Text style={styles.toolTipText}>{themeValue}</Text>
              </Text>
            </View>
          }>
          <View style={styles.bubble} />
        </Tooltip>
      )}

      <View style={[styles.flex]}>
        <EditorComponent
          pluginId={entityConfig?.id}
          pageId={pageId}
          configProps={{...configProps}}
          name={name}
          defaultValue={value}
          value={value}
          id={entityConfig?.id + name}
          config={config}
          entityConfig={entityConfig}
          configPathSelector={configPathSelector}
          onChange={onChange}
          onCustomPropChange={onCustomPropChange}
          onUpdateRawValue={onUpdateRawValue}
          themeIndication={themeIndication}
          getThemeObj={getThemeObj}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  bubble: {
    height: 6,
    width: 6,
    backgroundColor: '#FF674D',
    borderRadius: 3,
    margin: 3,
    cursor: 'pointer',
  },
  tooltip: {
    maxWidth: 250,
    flexDirection: 'column',
    gap: 6,
    padding: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.SIDEBAR_BORDER,
    backgroundColor: theme.PANEL_BACKGROUND,
  },
  toolTipText: {
    color: '#FF674D',
    borderWidth: 1,
    backgroundColor: '#404040',
    borderColor: '#3A3A3A',
    padding: 3,
    borderRadius: 4,
    lineHeight: 24,
  },
});

export default PluginPropertyEditor;
