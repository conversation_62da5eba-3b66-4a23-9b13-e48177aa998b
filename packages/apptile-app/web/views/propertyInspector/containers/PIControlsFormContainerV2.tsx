import {connect} from 'react-redux';
import {AppDispatch} from '../../../../app/store';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {selectCurrentVisiblePageId, selectSelectedPluginPageId} from '../../../selectors/EditorSelectors';
import PIControlsForm from '../components/PIControlsFormV2';

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {};
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    pageId: state.editor.selectedPluginConfigSel
      ? selectSelectedPluginPageId(state)
      : selectCurrentVisiblePageId(state),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(PIControlsForm);
