import React, { useEffect } from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import RadioGroupControl from '../../components/controls/RadioGroupControl';
import {Text} from 'react-native';
import DateAndTimeControl from '../../components/controls/DateAndTimeControl';
import Button from '../../components-v2/base/Button';
import {useDispatch, useSelector} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import {checkOverlappingOtas, clearSnapshotState, scheduleOta} from '../../actions/editorActions';
import {
  otaScheduleErrorSelector,
  otaScheduleLoadingSelector,
  otaScheduleStatusSelector,
  overlappingOtasSelector,
} from '../../selectors/SnapshotSelector';
import moment from 'moment';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

export const SnapshotSchedulerForm = ({onCancel}) => {
  const [startDate, setStartDate] = React.useState(new Date());
  const [endDate, setEndDate] = React.useState(moment(new Date()).add(1, 'hour').toDate());
  const [publishNow, setPublishNow] = React.useState(true);
  const dispatch = useDispatch();
  const scheduleStatus = useSelector(otaScheduleStatusSelector);
  const loading = useSelector(otaScheduleLoadingSelector);
  const error = useSelector(otaScheduleErrorSelector);
  const scheduleButtonDisabled = !startDate || !endDate || new Date(endDate) < new Date(startDate) || moment(endDate).diff(moment(startDate), 'minutes') < 10;

  const checkOverlappingOtaFunction = (requiredDate: Date) => dispatch(checkOverlappingOtas(requiredDate));
  
  useEffect(() => {
    checkOverlappingOtaFunction(startDate);
  }, [startDate, endDate, publishNow]);

  useEffect(() => {
    if (publishNow) {
      setStartDate(new Date());
    }
  }, [publishNow]);

  const overlappingSnapshot = useSelector(overlappingOtasSelector);

  const onCancelPress = () => {
    onCancel();
    dispatch(clearSnapshotState());
  };

  const createSchedule = () => {
    const scheduleOtaPayload = {
      startDate,
      endDate,
    };
    if (publishNow) {
      scheduleOtaPayload.startDate = new Date();
    }
    dispatch(scheduleOta(scheduleOtaPayload));
  };

  return (
    <View style={styles.wrapper}>
      {
        <>
          <Text style={[commonStyles.baseText, styles.headerText]}>Schedule your Version</Text>
          <Text style={[commonStyles.baseText, styles.subHeader]}>
            Do you want to publish your version now or later?
          </Text>
          <View style={{width: 191, marginTop: 11}}>
            <RadioGroupControl
              disableBinding
              value={publishNow ? 'now' : 'later'}
              onChange={value => {
                if (value === 'now') {
                  setPublishNow(true);
                } else {
                  setPublishNow(false);
                }
              }}
              label=""
              options={[
                {text: 'Now', value: 'now'},
                {text: 'Later', value: 'later'},
              ]}
            />
          </View>
          <View style={{flexDirection: 'row', flex: 1, gap: 10, marginTop: 21}}>
            {!publishNow && (
              <View style={styles.dateTimeWrapper}>
                <Text style={[commonStyles.baseText, styles.dateTimeHeader]}>Publish from</Text>
                <View style={{width: 191}}>
                  <DateAndTimeControl
                    label=""
                    value={startDate.toUTCString()}
                    onChange={function (value: string): void {
                      setStartDate(new Date(value));
                    }}
                  />
                </View>
              </View>
            )}
            <View style={styles.dateTimeWrapper}>
              <Text style={[commonStyles.baseText, styles.dateTimeHeader]}>Publish till</Text>
              <View style={{width: 191}}>
                <DateAndTimeControl
                  label=""
                  value={endDate.toUTCString()}
                  onChange={function (value: string): void {
                    setEndDate(new Date(value));
                  }}
                />
              </View>
            </View>
          </View>
        </>
      }
      {!_.isEmpty(overlappingSnapshot) && (
        <Text style={[commonStyles.baseText, {color: 'red', fontSize: 14, marginBottom: 10}]}>
          A version already exists for this date and time, covering the period from{' '}
          {new Date(overlappingSnapshot[0]?.startDate)?.toLocaleString('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
          })}{' '}
          to{' '}
          {new Date(overlappingSnapshot[0]?.endDate)?.toLocaleString('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
          })}
          . Proceeding will overwrite the existing version.
        </Text>
      )}
      <View style={{alignItems: 'flex-end'}}>
        {!publishNow ? (
          <Button
            loading={loading}
            onPress={createSchedule}
            disabled={scheduleButtonDisabled}
            containerStyles={{width: 115}}
            color="CTA">
            Schedule
          </Button>
        ) : (
          <Button
            loading={loading}
            onPress={createSchedule}
            disabled={scheduleButtonDisabled}
            containerStyles={{width: 115}}
            color="CTA">
            Publish
          </Button>
        )}
        {scheduleButtonDisabled && !publishNow && (
          <Text style={[commonStyles.baseText, {color: 'red', fontSize: 10}]}>
            Publish from should be greater than publish till
          </Text>
        )}
        {scheduleButtonDisabled && (
          <Text style={[commonStyles.baseText, {color: 'red', fontSize: 10, marginTop: 5}]}>
            Publish till should be at least 10 minutes in the future
          </Text>
        )}
      </View>
      {scheduleStatus && (
        <View style={styles.successScheduleWrapper}>
          <MaterialCommunityIcons name={'check-circle'} color={'#00000'} size={60} />
          <Text style={[commonStyles.baseText, styles.snapshotSuccessHeader]}>Scheduled Successfully</Text>
          {publishNow ? (
            <Text style={[commonStyles.baseText, styles.snapshotSuccessText]}>
              Your version has been scheduled successfully
            </Text>
          ) : (
            <Text style={[commonStyles.baseText, styles.snapshotSuccessText]}>
              Your version is scheduled successfully!
            </Text>
          )}
        </View>
      )}
      <Pressable style={{position: 'absolute', top: 10, right: 10}} onPress={onCancelPress}>
        <MaterialCommunityIcons name={'close'} color={'#000000'} size={20} />
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: 607,
    height: 435,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#000000',
  },
  subHeader: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 20,
  },
  dateTimeWrapper: {
    width: 191,
  },
  dateTimeHeader: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 11,
  },
  successScheduleWrapper: {
    position: 'absolute',
    width: 607,
    height: 435,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  snapshotSuccessText: {color: '#00000', fontSize: 14, fontWeight: '400', marginTop: 6},
  snapshotSuccessHeader: {color: '#00000', fontSize: 15, fontWeight: '500', marginTop: 30},
});
