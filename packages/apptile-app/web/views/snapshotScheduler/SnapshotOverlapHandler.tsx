import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import RadioGroupControl from '../../components/controls/RadioGroupControl';
import {Text} from 'react-native';
import DateAndTimeControl from '../../components/controls/DateAndTimeControl';
import Button from '../../components-v2/base/Button';
import {useDispatch, useSelector} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import {clearSnapshotState, fetchAppBranchesWithScheduledOta, scheduleOta} from '../../actions/editorActions';
import {EditorRootState} from '../../store/EditorRootState';
import {ISnapshot} from '../../api/ApiTypes';
import {apptileStateSelector} from 'apptile-core';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

export interface ISnapshotOverlapHandler {
  onCancel: any;
  overlappingSnapshot: ISnapshot;
  onPublish: any;
}

export const SnapshotOverlapHandler: React.FC<ISnapshotOverlapHandler> = ({
  onCancel,
  overlappingSnapshot,
  onPublish,
}) => {
  const [publishNow, setPublishNow] = React.useState(true);
  const apptileState = useSelector(apptileStateSelector);
  const dispatch = useDispatch();
  const scheduleStatus = useSelector((state: EditorRootState) => state.snapshots.otaScheduleStatus);
  const loading = useSelector((state: EditorRootState) => state.snapshots.otaScheduleLoading);
  const error = useSelector((state: EditorRootState) => state.snapshots.otaScheduleError);
  const onCancelPress = () => {
    onCancel();
    dispatch(clearSnapshotState());
  };
  const createSchedule = () => {
    if (publishNow) {
      onPublish();
      dispatch(fetchAppBranchesWithScheduledOta(apptileState?.appId, apptileState?.forkId));
    } else {
      const scheduleOtaPayload = {
        startDate: new Date(),
      };
      //Add 5 mins to currentsnapshot end date
      const endDate = new Date(overlappingSnapshot?.endDate);
      endDate.setMinutes(endDate.getMinutes() + 5);
      scheduleOtaPayload.startDate = endDate;
      dispatch(scheduleOta(scheduleOtaPayload));
    }
  };

  return (
    <View style={styles.wrapper}>
      {
        <>
          <Text style={[commonStyles.baseText, styles.headerText]}>Publish your app</Text>
          <Text style={[commonStyles.baseText, styles.subHeader]}>
            Do you want to publish your Primary app now or later?
          </Text>
          <View style={{width: 250, marginTop: 11}}>
            <RadioGroupControl
              disableBinding
              value={publishNow ? 'now' : 'later'}
              onChange={value => {
                if (value === 'now') {
                  setPublishNow(true);
                } else {
                  setPublishNow(false);
                }
              }}
              label=""
              options={[
                {text: 'Now', value: 'now'},
                {text: 'After version ends', value: 'later'},
              ]}
            />
          </View>
          <View style={{flexDirection: 'row', flex: 1, gap: 10, marginTop: 21}}>
            <Text style={[commonStyles.baseText, {color: '#1060E0', fontSize: 14}]}>Note: </Text>
            <Text style={[commonStyles.baseText, {color: '#000', fontSize: 14}]}>
              Your version is currently live. Publishing the Primary app now will override the current version.{' '}
            </Text>
          </View>
        </>
      }
      <View style={{alignItems: 'flex-end'}}>
        <Button loading={loading} onPress={createSchedule} containerStyles={{width: 115}} color="CTA">
          Publish
        </Button>
      </View>
      {scheduleStatus && (
        <View style={styles.successScheduleWrapper}>
          <MaterialCommunityIcons name={'check-circle'} color={'#00000'} size={60} />
          <Text style={[commonStyles.baseText, styles.snapshotSuccessHeader]}>Scheduled Successfully</Text>
          {publishNow ? (
            <Text style={[commonStyles.baseText, styles.snapshotSuccessText]}>
              Your version has been scheduled successfully
            </Text>
          ) : (
            <Text style={[commonStyles.baseText, styles.snapshotSuccessText]}>
              Your version is scheduled successfully!
            </Text>
          )}
        </View>
      )}
      <Pressable style={{position: 'absolute', top: 10, right: 10}} onPress={onCancelPress}>
        <MaterialCommunityIcons name={'close'} color={'#000000'} size={20} />
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: 607,
    height: 435,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#000000',
  },
  subHeader: {
    fontSize: 14,
    fontWeight: '400',
    marginTop: 20,
  },
  dateTimeWrapper: {
    width: 191,
  },
  dateTimeHeader: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 11,
  },
  successScheduleWrapper: {
    position: 'absolute',
    width: 607,
    height: 435,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  snapshotSuccessText: {color: '#00000', fontSize: 14, fontWeight: '400', marginTop: 6},
  snapshotSuccessHeader: {color: '#00000', fontSize: 15, fontWeight: '500', marginTop: 30},
});
