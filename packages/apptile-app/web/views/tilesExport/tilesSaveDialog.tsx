import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  Button,
  StyleSheet,
  Text,
  Pressable,
  Image,
  TextInput as TextInputReact,
  TouchableOpacity,
  View,
} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {useDispatch, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {exportTile, updateTileInfo, updateTile} from '../../actions/editorActions';
import TilesApi from '../../api/TilesApi';
import {v4 as uuidv4} from 'uuid';
import {
  selectAppConfig, selectModuleByUUID, MaterialCommunityIcons
} from 'apptile-core';
import CodeInputControl from '../../components/controls/CodeInputControl';
import TextElement from '../../components-v2/base/TextElement';
import AssetChooseDialog from '../../components/controls/assetEditor/assetChooseDialog';
import RadioGroupControl from '../../components/controls/RadioGroupControl';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface TileSaveDialogProps {
  moduleUUID: string;
  onClose: () => void;
}

const TileSaveDialog: React.FC<TileSaveDialogProps> = props => {
  const {onClose, moduleUUID} = props;
  const dispatch = useDispatch();
  const {
    exportTile: exportTileToServer,
    updateTileInfo: updateTileDetails,
    updateTile: updateTileVersion,
  } = useMemo(() => bindActionCreators({exportTile, updateTileInfo, updateTile}, dispatch), [dispatch]);

  const moduleRecord = useSelector(state => selectModuleByUUID(state, moduleUUID));
  const [moduleName, setModuleName] = useState(moduleRecord?.moduleName);
  const [coverImage, setCoverImage] = useState('');
  const [tileDetails, setTileDetails] = useState(null);
  const [tags, setTags] = useState<string[]>([]);
  const [tagsStr, setTagsString] = useState<string>('');
  const [isLoading, setLoading] = useState(true);
  const [isNew, setNew] = useState(true);
  const [isDeleted, setIsDeleted] = useState(false);
  const [imageAssetId, setImageAssetId] = useState('');
  const [showPopover, setShowPopover] = React.useState(false);
  const [secretCode, setSecretCode] = useState('');
  const [variants, setVariants] = useState('');

  useEffect(() => {
    const tags = tagsStr.split(',').map(str => str.trim());
    setTags(tags);
  }, [tagsStr]);
  useEffect(() => {
    setModuleName(moduleRecord?.moduleName);
    setTagsString(moduleRecord?.getTagsString() ?? '');
  }, [moduleRecord]);
  useEffect(() => {
    setModuleName(tileDetails?.name);
    setTagsString(tileDetails?.tags?.join(',') ?? '');
    setCoverImage(tileDetails?.coverImage ?? '');
  }, [tileDetails]);
  useEffect(() => {
    TilesApi.getTileStatus(moduleUUID)
      .then(resp => {
        setTileDetails(resp.data?.tile);
        setLoading(false);
        setNew(false);
        if (resp.data.status === 'deleted') {
          setIsDeleted(true);
        } else {
          setIsDeleted(false);
        }
      })
      .catch(() => {
        setTileDetails(null);
        setIsDeleted(false);
        setLoading(false);
        setNew(true);
      });
  }, [moduleUUID]);

  const saveTile = useCallback(() => {
    if (moduleName && moduleUUID && moduleRecord) {
      exportTileToServer(moduleUUID, {
        id: moduleRecord.moduleUUID,
        name: moduleName,
        coverImage,
        tags,
        secretCode,
      });
    }
  }, [coverImage, exportTileToServer, moduleName, moduleRecord, moduleUUID, secretCode, tags]);
  const saveTileDetails = useCallback(() => {
    if (moduleName && moduleUUID && moduleRecord) {
      updateTileDetails(moduleUUID, {
        id: moduleRecord.moduleUUID,
        name: moduleName,
        coverImage,
        tags,
        secretCode,
      });
    }
  }, [coverImage, moduleName, moduleRecord, moduleUUID, secretCode, tags, updateTileDetails]);
  const exportTileVersion = useCallback(() => {
    if (moduleName && moduleUUID && moduleRecord) {
      const newModuleUUID = uuidv4();
      moduleRecord.set('moduleUUID', newModuleUUID);
      updateTileVersion(moduleUUID, {
        id: newModuleUUID,
        name: moduleName,
        coverImage,
        tags,
        secretCode,
        variants,
      });
    }
  }, [moduleName, moduleUUID, moduleRecord, updateTileVersion, coverImage, tags, secretCode, variants]);

  const appConfig = useSelector(selectAppConfig);

  useEffect(() => {
    const imageRecord = appConfig?.getImageId(imageAssetId);
    const assetSourceValue = imageRecord?.fileUrl ?? null;
    if (assetSourceValue) setCoverImage(assetSourceValue);
  }, [appConfig, imageAssetId]);

  return (
    <View style={styles.dialogStyle}>
      <View style={styles.dialogHeader}>
        <Text>Create Module</Text>
        <TouchableOpacity onPress={onClose} style={[styles.closeButton]}>
          <MaterialCommunityIcons name="close-circle-outline" size={24} />
        </TouchableOpacity>
      </View>
      {isLoading ? (
        <ActivityIndicator />
      ) : (
        <ScrollView style={{flex: 1}} contentContainerStyle={styles.dialogBody}>
          <View style={styles.rowContainer}>
            <CodeInputControl value={moduleName ?? ''} label={'Name'} onChange={setModuleName} />
          </View>
          <View style={styles.rowContainer}>
            <View style={commonStyles.labelContainer}>
              <Text style={commonStyles.labelText}>Cover Image</Text>
            </View>
            <View style={commonStyles.inputContainer}>
              {!coverImage ? (
                <Pressable style={[styles.imageContainer, styles.upload]} onPress={() => setShowPopover(true)}>
                  <TextElement fontSize="md" lineHeight="md" color="PRIMARY">
                    + UPLOAD
                  </TextElement>
                </Pressable>
              ) : (
                <Pressable style={[styles.imageContainer]} onPress={() => setShowPopover(true)}>
                  <Image resizeMode="contain" source={{uri: coverImage}} style={styles.image} />
                </Pressable>
              )}
            </View>
            <AssetChooseDialog
              askURL={false}
              currentAssetId={''}
              onCloseDialog={val => {
                setShowPopover(val);
              }}
              showDialog={showPopover}
              onSelectAsset={setImageAssetId}
            />
          </View>
          <View style={styles.rowContainer}>
            <CodeInputControl value={tagsStr ?? ''} label={'Tags'} onChange={setTagsString} />
          </View>
          <View style={styles.rowContainer}>
            <View style={commonStyles.labelContainer}>
              <Text style={commonStyles.labelText}>Secret Code</Text>
            </View>
            <View style={commonStyles.inputContainer}>
              <TextInputReact
                style={[commonStyles.input, {paddingHorizontal: 4, paddingVertical: 7}]}
                secureTextEntry={true}
                value={secretCode ?? ''}
                onChange={(event: any) => {
                  setSecretCode(event?.target?.value ?? '');
                }}
              />
            </View>
          </View>
          <View style={styles.rowContainer}>
            <RadioGroupControl
              options={[
                {text: "Don't Replicate", value: ''},
                {text: 'Replicate old variants', value: 'replicate'},
                {text: 'Replicate old variants but hide them', value: 'replicateHide'},
              ]}
              disableBinding
              allowDeselect
              value={variants}
              label={'Variants'}
              onChange={setVariants}
            />
          </View>
        </ScrollView>
      )}
      {isDeleted ? (
        <View style={styles.dialogFooter}>
          <Text style={commonStyles.baseText}>This Tile is deleted</Text>
        </View>
      ) : (
        <View style={styles.dialogFooter}>
          {isNew ? (
            <Button disabled={moduleName === ''} title="Export" onPress={saveTile} />
          ) : (
            <>
              <Button disabled={moduleName === ''} title="Save changes" onPress={saveTileDetails} />
              <Button disabled={moduleName === ''} title="Update tile" onPress={exportTileVersion} />
            </>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    // width: 800,
    // minWidth: 400,
    // // height: 'auto',
    minHeight: 600,
    flexBasis: 'auto',
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  dialogHeader: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    padding: 10,
    backgroundColor: '#eee',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#fff',
    padding: 10,
    alignItems: 'stretch',
  },
  textHeading: {
    flex: 1,
    fontSize: 16,
    margin: 10,
  },
  textSubtitle: {
    fontSize: 10,
    fontColor: '#888',
  },
  textInput: {flex: 1, padding: 4, borderColor: '#ccc', borderWidth: 1},
  rowContainer: {
    flex: 0,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    height: 'auto',
    flexBasis: 'auto',
  },
  inputRow: {
    flex: 0,
    flexBasis: 'auto',
    flexDirection: 'row',
    height: 40,
    width: '100%',
    borderRadius: 4,
    padding: 5,
    alignItems: 'center',
    alignContent: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
    // shadowColor: '#000',
    // shadowOffset: {width: 1, height: 2},
    // shadowRadius: 4,
    // shadowOpacity: 0.3,
  },
  inputCell: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
  },
  selectorRow: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    height: 'auto',
    marginBottom: 5,
    flexGrow: 0,
    flexShrink: 0,
  },
  selectorCell: {
    flex: 1,
    flexBasis: '30%',
    width: '30%',
    height: 'auto',
    margin: 4,
  },
  selectorText: {
    fontSize: 10,
  },
  dialogFooter: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    padding: 4,
    paddingLeft: 10,
    paddingRight: 10,
    flexGrow: 0,
    flexShrink: 0,
    borderTopColor: '#ccc',
    borderTopWidth: 1,
    justifyContent: 'flex-end',
  },
  imageContainer: {
    position: 'relative',
    width: 350,
    height: 250,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
    marginVertical: 4,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
});

export default TileSaveDialog;
