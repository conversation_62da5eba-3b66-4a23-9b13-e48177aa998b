import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ActivityIndicator, StyleSheet, Text, TextInput, Pressable, Image, TouchableOpacity, View} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {MaterialCommunityIcons} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import {selectModuleByUUID} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import CodeInputControlV2 from '../../components/controls-v2/CodeInputControl';
import TextElement from '../../components-v2/base/TextElement';
import AssetChooseDialog from '../../components/controls/assetEditor/assetChooseDialog';
import Popover, {PopoverMode} from 'react-native-popover-view';
import Button from '../../components-v2/base/Button';
import {bindActionCreators} from 'redux';
import {exportTileVariant, updateTileVariant, updateTileVariantInfo} from '../../actions/editorActions';
import CheckboxControl from '../../components/controls/CheckboxControl';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface VariantEditDialogProps {
  moduleUUID: string;
  isOpen: boolean;
  variantRecord: any;
  onClose: () => void;
  newVariant: boolean;
}

const VariantEditDialog: React.FC<VariantEditDialogProps> = props => {
  const {isOpen, moduleUUID, variantRecord, newVariant, onClose} = props;
  const dispatch = useDispatch();
  const {
    exportTileVariant: exportVariantToServer,
    updateTileVariantInfo: updateVariantDetails,
    updateTileVariant: updateVariant,
  } = useMemo(
    () => bindActionCreators({exportTileVariant, updateTileVariantInfo, updateTileVariant}, dispatch),
    [dispatch],
  );

  const moduleRecord = useSelector(state => selectModuleByUUID(state, moduleUUID));
  const [variantUUID, setVariantUUID] = useState('');
  const [variantName, setVariantName] = useState('');
  const [variantHidden, setVariantHidden] = useState(false);
  const [coverImage, setCoverImage] = useState('');
  const [imageAssetId, setImageAssetId] = useState('');
  const [showPopover, setShowPopover] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(newVariant ? false : true);
  const [secretCode, setSecretCode] = useState('');

  const appConfig = useSelector(selectAppConfig);

  useEffect(() => {
    const imageRecord = appConfig?.getImageId(imageAssetId);
    const assetSourceValue = imageRecord?.fileUrl ?? null;
    if (assetSourceValue) setCoverImage(assetSourceValue);
  }, [appConfig, imageAssetId]);

  useEffect(() => {
    if (!newVariant) {
      setVariantUUID(variantRecord?.id ?? '');
      setVariantName(variantRecord?.name ?? '');
      setCoverImage(variantRecord?.coverImage ?? '');
    } else {
      setVariantUUID('');
      setVariantName('');
      setCoverImage('');
    }
    setIsLoading(false);
  }, [newVariant, variantRecord]);

  const saveVariant = useCallback(() => {
    if (variantName && coverImage && moduleUUID) {
      exportVariantToServer(moduleUUID, {
        name: variantName,
        coverImage,
        secretCode,
        tileId: moduleUUID,
      });
    }
  }, [coverImage, exportVariantToServer, moduleUUID, secretCode, variantName]);
  const saveVariantDetails = useCallback(() => {
    if (variantName && coverImage && moduleUUID && variantUUID) {
      updateVariantDetails(moduleUUID, {
        id: variantUUID,
        name: variantName,
        coverImage,
        secretCode,
        tileId: moduleUUID,
        hidden: variantHidden,
      });
    }
  }, [coverImage, moduleUUID, secretCode, updateVariantDetails, variantHidden, variantName, variantUUID]);
  const exportVariantVersion = useCallback(() => {
    if (variantName && coverImage && moduleUUID && variantUUID) {
      updateVariant(moduleUUID, {
        id: variantUUID,
        name: variantName,
        coverImage,
        secretCode,
        tileId: moduleUUID,
        hidden: variantHidden,
      });
    }
  }, [variantName, coverImage, moduleUUID, variantUUID, updateVariant, secretCode, variantHidden]);

  return (
    <>
      {isOpen && (
        <div style={{position: 'fixed', zIndex: 1, height: '100%', width: '100%'}}>
          <Popover
            isVisible={isOpen}
            mode={PopoverMode.JS_MODAL}
            onRequestClose={() => {
              onClose();
            }}
            popoverStyle={{flexBasis: 'auto', width: 1000}}
            backgroundStyle={{backgroundColor: 'rgba(0,0,0,0.3)'}}>
            <View style={styles.dialogStyle}>
              <View style={styles.dialogHeader}>
                <Text>Edit Variant For Module</Text>
                <TouchableOpacity onPress={onClose} style={[styles.closeButton]}>
                  <MaterialCommunityIcons name="close-circle-outline" size={24} />
                </TouchableOpacity>
              </View>
              {isLoading ? (
                <ActivityIndicator />
              ) : (
                <ScrollView style={{flex: 1}} contentContainerStyle={styles.dialogBody}>
                  <View style={styles.rowContainer}>
                    <CodeInputControlV2 singleLine value={variantName ?? ''} label={'Name'} onChange={setVariantName} />
                  </View>
                  <View style={styles.rowContainer}>
                    <View style={commonStyles.labelContainer}>
                      <Text style={commonStyles.labelText}>Cover Image</Text>
                    </View>
                    <View style={commonStyles.inputContainer}>
                      {!coverImage ? (
                        <Pressable style={[styles.imageContainer, styles.upload]} onPress={() => setShowPopover(true)}>
                          <TextElement fontSize="md" lineHeight="md" color="PRIMARY">
                            + UPLOAD
                          </TextElement>
                        </Pressable>
                      ) : (
                        <Pressable style={[styles.imageContainer]} onPress={() => setShowPopover(true)}>
                          <Image resizeMode="contain" source={{uri: coverImage}} style={styles.image} />
                        </Pressable>
                      )}
                    </View>
                    <AssetChooseDialog
                      askURL={false}
                      currentAssetId={''}
                      onCloseDialog={val => {
                        setShowPopover(val);
                      }}
                      showDialog={showPopover}
                      onSelectAsset={setImageAssetId}
                    />
                  </View>
                  <View style={styles.rowContainer}>
                    <View style={commonStyles.labelContainer}>
                      <Text style={commonStyles.labelText}>Secret Code</Text>
                    </View>
                    <View style={commonStyles.inputContainer}>
                      <TextInput
                        style={[commonStyles.input, {paddingHorizontal: 4, paddingVertical: 7}]}
                        secureTextEntry={true}
                        value={secretCode ?? ''}
                        onChange={(event: any) => {
                          setSecretCode(event?.target?.value ?? '');
                        }}
                      />
                    </View>
                  </View>
                  <View style={styles.rowContainer}>
                    <CodeInputControlV2
                      singleLine
                      label="Is this variant hidden?"
                      value={variantHidden}
                      onChange={setVariantHidden}
                    />
                  </View>
                </ScrollView>
              )}
              <View style={styles.dialogFooter}>
                {newVariant ? (
                  <Button disabled={variantName === ''} onPress={saveVariant}>
                    Export
                  </Button>
                ) : (
                  <>
                    <Button disabled={variantName === ''} onPress={saveVariantDetails}>
                      Save changes
                    </Button>
                    <Button disabled={variantName === ''} onPress={exportVariantVersion}>
                      Update tile
                    </Button>
                  </>
                )}
              </View>
            </View>
          </Popover>
        </div>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    // width: 800,
    // minWidth: 400,
    // // height: 'auto',
    minHeight: 600,
    flexBasis: 'auto',
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  dialogHeader: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    padding: 10,
    backgroundColor: '#eee',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#fff',
    padding: 10,
    alignItems: 'stretch',
  },
  rowContainer: {
    flex: 0,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    height: 'auto',
    flexBasis: 'auto',
  },
  imageContainer: {
    position: 'relative',
    width: 350,
    height: 250,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
    marginVertical: 4,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  variantItem: {
    width: 150,
    height: 150,
    borderRadius: 8,
    backgroundColor: theme.QUATERNARY_BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dialogFooter: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    padding: 4,
    paddingLeft: 10,
    paddingRight: 10,
    flexGrow: 0,
    flexShrink: 0,
    borderTopColor: '#ccc',
    borderTopWidth: 1,
    justifyContent: 'flex-end',
  },
});

export default VariantEditDialog;
