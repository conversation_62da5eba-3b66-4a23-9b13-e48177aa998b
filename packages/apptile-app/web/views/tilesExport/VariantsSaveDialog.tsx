import React, {useEffect, useLayoutEffect, useState} from 'react';
import {ActivityIndicator, StyleSheet, Text, Pressable, Image, View, TouchableOpacity} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import TilesApi from '../../api/TilesApi';
import VariantEditDialog from './VariantEditDialog';
import {MaterialCommunityIcons} from 'apptile-core';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface VariantsSaveDialogProps {
  moduleUUID: string;
  onClose: () => void;
}

const VariantsSaveDialog: React.FC<VariantsSaveDialogProps> = props => {
  const {onClose, moduleUUID} = props;
  const [tileVariants, setTileVariants] = useState(null);
  const [currentVariant, setCurrentVariant] = useState(null);
  const [newVariant, setNewVariant] = useState(false);
  const [showPopover, setShowPopover] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useLayoutEffect(() => {
    if (moduleUUID) {
      TilesApi.getTileVariants(moduleUUID, true)
        .then(resp => {
          setTileVariants(resp?.data?.items);
          setIsLoading(false);
        })
        .catch(() => {
          setTileVariants(null);
        });
    }
  }, [moduleUUID]);

  return (
    <>
      <VariantEditDialog
        isOpen={showPopover}
        moduleUUID={moduleUUID}
        onClose={() => {
          setShowPopover(false);
        }}
        variantRecord={currentVariant}
        newVariant={newVariant}
      />
      <View style={styles.dialogStyle}>
        <View style={styles.dialogHeader}>
          <Text>Create Variants For Module</Text>
          <TouchableOpacity onPress={onClose} style={[styles.closeButton]}>
            <MaterialCommunityIcons name="close-circle-outline" size={24} />
          </TouchableOpacity>
        </View>
        {isLoading ? (
          <ActivityIndicator />
        ) : (
          <ScrollView style={{flex: 1}} contentContainerStyle={styles.dialogBody}>
            <View style={[styles.rowContainer, {gap: 4, flexWrap: 'wrap', justifyContent: 'center'}]}>
              {Array.isArray(tileVariants) &&
                tileVariants?.map(e => (
                  <Pressable
                    style={[styles.rowContainer, styles.variantItem]}
                    onPress={() => {
                      setCurrentVariant(e);
                      setShowPopover(true);
                    }}>
                    <Image resizeMode="cover" source={{uri: e.coverImage}} style={styles.image} />
                    <Text style={commonStyles.baseText}>{e.name}</Text>
                  </Pressable>
                ))}
              <Pressable
                style={[styles.rowContainer, styles.variantItem]}
                onPress={() => {
                  setCurrentVariant(null);
                  setNewVariant(true);
                  setShowPopover(true);
                }}>
                <Text>+ Add Variant</Text>
              </Pressable>
            </View>
          </ScrollView>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    // width: 800,
    // minWidth: 400,
    // // height: 'auto',
    minHeight: 600,
    flexBasis: 'auto',
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  dialogHeader: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    padding: 10,
    backgroundColor: '#eee',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#fff',
    padding: 10,
    alignItems: 'stretch',
  },
  rowContainer: {
    flex: 0,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    height: 'auto',
    flexBasis: 'auto',
  },
  imageContainer: {
    position: 'relative',
    width: 350,
    height: 250,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#C7C7C7',
    marginVertical: 4,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    flex: 1,
  },
  upload: {justifyContent: 'center', alignItems: 'center'},
  variantItem: {
    width: 120,
    height: 120,
    borderRadius: 8,
    backgroundColor: theme.QUATERNARY_BACKGROUND,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    flexDirection: 'column',
  },
});

export default VariantsSaveDialog;
