import React, {useEffect, useState} from 'react';
import {usePara<PERSON>, useNavigate} from 'react-router';
import LeftPane from './shared/leftPane';
import {ThemeContainer} from 'apptile-core';
import {LayoutChangeEvent, StyleSheet, Text, View} from 'react-native';
import RightPane from './shared/rightPane';
import theme from '../../styles-v2/theme';
import {useDispatch} from 'react-redux';
import _ from 'lodash';
import {Image} from 'react-native';
import {useSelector} from 'react-redux';
import {BrandSettingsTypes} from 'apptile-core';
const BRAND_LOGO_ASSET_ID = BrandSettingsTypes.BRAND_LOGO_ASSET_ID,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY;
import {selectAppSettingsForKey} from 'apptile-core';
import {SettingsConfig} from 'apptile-core';
import {EditorRootState} from '../../store/EditorRootState';
import {fetchAppConfigAndInitPageModel, setOpenPremiumModal, verifyAppForks} from '../../actions/editorActions';
import {ViewerWidgetCanvas} from 'apptile-core';
import {destroyPageModel} from 'apptile-core';
import {apptileStateSelector} from 'apptile-core';
import {themeOnboardingPageId, themeOnboardingPageKey} from '../../common/onboardingConstants';
import Animated, {useAnimatedStyle, useSharedValue, withSpring} from 'react-native-reanimated';
import ModalComponent from '../../components-v2/base/Modal';
import PremiumUpgradeModal from '../featureGating/premiumUpgradeModal';
import {openPremiumModalSelector} from '../../selectors/FeatureGatingSelector';
import {selectCurrentApptileForkId} from '../../selectors/AppSelectors';
import {brandOnboardingPageConfig} from '../../index.web';

const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);

const stepMapping: any = {
  1: 'Customize logo',
  2: 'Colour Palette',
  3: 'Fonts',
};

const eventMappingWithSlug = {
  'customize-logo': 'customizeLogo',
  'colour-palette': 'colourPalette',
  fonts: 'fonts',
};

const pageId = themeOnboardingPageId;
const pageKey = themeOnboardingPageKey;

export const EditorOnboarding: React.FC = () => {
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const logoAssetId = brandSettings.getSettingValue(BRAND_LOGO_ASSET_ID) as string | undefined;
  const {appId, appSaveId} = useSelector(apptileStateSelector);
  const {slug, id, orgId}: {slug: string} = useParams();
  const orgsState = useSelector((state: EditorRootState) => state.orgs);
  const [canvasScale, setCanvasScale] = useState(0.88);
  const av_scale = useSharedValue(0.88);
  const [appHeight] = useState(844);
  const openPremiumModal = useSelector(openPremiumModalSelector);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const currentApptileForkId = useSelector(selectCurrentApptileForkId);
  const pageConfig = brandOnboardingPageConfig();
  const onPremiumModalVisibilityChange = (value: boolean) => {
    if (!value) {
      dispatch(setOpenPremiumModal(false, ''));
    }
  };
  const styleAnimatedScale = useAnimatedStyle(() => {
    return {
      transform: [
        {
          scale: withSpring(av_scale.value, {restDisplacementThreshold: 0.001, restSpeedThreshold: 0.01}),
        },
      ],
    };
  }, [av_scale]);
  const onLayout = (e: LayoutChangeEvent) => {
    const vh = e.nativeEvent.layout.height;
    const sc = (vh * 0.9) / appHeight;
    setCanvasScale(sc);
    logger.info('Canvas Scale: ', sc);
  };
  useEffect(() => {
    av_scale.value = canvasScale;
  }, [av_scale, canvasScale]);

  const isEditorOnboarded = _.get(orgsState, ['appsById', id, 'isEditorOnboarded'], '');

  useEffect(() => {
    return () => {
      dispatch(destroyPageModel(pageId, pageKey));
    };
  }, [dispatch]);

  //Navigating to proper route if its not there
  useEffect(() => {
    if (!Object.values(stepMapping).find(v => _.kebabCase(v) === slug)) {
      navigate('../customize-logo');
    }
    if (isEditorOnboarded) {
      navigate(`/dashboard/${orgId}/app/${id}/brand-settings`);
    }
  }, [id, isEditorOnboarded, navigate, orgId, slug]);

  //Fetching app config
  useEffect(() => {
    if (id && currentApptileForkId && pageConfig) {
      dispatch(fetchAppConfigAndInitPageModel(id, pageId, pageKey, pageConfig));
    }
  }, [id, appSaveId, dispatch, currentApptileForkId, pageConfig]);

  return (
    <>
      {openPremiumModal && (
        <ModalComponent
          onVisibleChange={onPremiumModalVisibilityChange}
          visible={openPremiumModal}
          content={<PremiumUpgradeModal />}
        />
      )}

      <View style={styles.wrapper} onLayout={onLayout}>
        <View style={styles.leftPaneWrapper}>
          <View style={styles.sidebarHeader}>
            <Image
              style={styles.logo}
              source={require('@/root/web/assets/images/apptile_icon.png')}
              resizeMode="contain"
            />
            <Text style={styles.logoText}>AppTile</Text>
          </View>
          <Text style={styles.brandSettingText}>BRAND SETTINGS</Text>

          <LeftPane logoAssetId={logoAssetId} stepMapping={stepMapping} slug={slug} />
        </View>
        <Animated.View style={[styles.mainBlockWrapper, styleAnimatedScale]}>
          <ThemeContainer>
            <ViewerWidgetCanvas {...{pageId, pageKey}} />
          </ThemeContainer>
          {slug === 'customize-logo' && <View style={styles.mainBlockShadow} />}
        </Animated.View>

        <View style={styles.rightPaneWrapper}>
          <RightPane
            eventMappingWithSlug={eventMappingWithSlug}
            logoAssetId={logoAssetId}
            stepMapping={stepMapping}
            slug={slug}
          />
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    height: '100vh',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#ECE9E1',
  },
  rightPaneWrapper: {
    width: '20%',
    height: '100%',
    minWidth: 330,
    backgroundColor: '#FFFFFF',
  },

  sidebarHeader: {
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontWeight: '700',
    fontSize: 22,
    fontFamily: theme.FONT_FAMILY,
  },
  logo: {
    width: 36,
    height: 36,
    marginRight: 10,
  },

  leftPaneWrapper: {
    width: '20%',
    height: '100%',
    minWidth: 290,
    backgroundColor: '#FFFFFF',
    paddingLeft: 40,
  },

  brandSettingText: {
    color: '#888888',
    fontSize: 16,
    fontFamily: theme.FONT_FAMILY,
    marginTop: 52,
    marginBottom: 10,
  },
  mainBlockWrapper: {
    height: 844,
    position: 'relative',
    width: 390,
    overflow: 'hidden',
  },
  mainBlockShadow: {
    position: 'absolute',
    backgroundColor: 'rgba(0,0,0,0.3)',
    width: '100%',
    height: 785,
    bottom: 0,
  },
});
