import UserApi from '../../api/UserApi';

export function emailValidator(email: string) {
  const re = /\S+@\S+\.\S+/;
  if (!email) return "Email can't be empty.";
  if (!re.test(email)) return 'Please enter a valid email address.';
  return '';
}

export function passwordValidator(password: string) {
  if (!password) return "Password can't be empty.";
  if (password.length < 5) return 'Password must be at least 5 characters long.';
  return '';
}

export function firstnameValidator(firstname: string) {
  if (!firstname) return "firstname can't be empty.";
  if (firstname.length < 3) return 'firstname must be at least 5 characters long.';
  return '';
}

/**
 * Handles user logout with configurable navigation behavior
 *
 * @param options Configuration options
 * @param options.redirectUrl URL to redirect to after logout. Default: '/login'
 * @param options.navigate React Router navigate function for SPA navigation. When not provided, falls back to page refresh.
 * @param options.onSuccess Optional callback when logout is successful
 * @param options.onError Optional callback when logout fails
 * @param options.setIsLoggingOut Optional React state setter for tracking logout in progress
 * @returns Promise that resolves when logout completes
 *
 * @example
 * // Basic usage with default settings (will use page refresh)
 * handleLogout();
 *
 * // With React Router navigation
 * const navigate = useNavigate();
 * handleLogout({ navigate });
 *
 * // With custom redirect
 * handleLogout({ redirectUrl: '/dashboard', navigate });
 *
 * // With state tracking
 * handleLogout({ setIsLoggingOut: setIsLoading, navigate });
 *
 * // With success callback
 * handleLogout({
 *   navigate,
 *   onSuccess: () => console.log('Logged out successfully!')
 * });
 */
export async function handleLogout({
  redirectUrl = '/login',
  navigate = null,
  onSuccess = null,
  onError = null,
  setIsLoggingOut = null,
}: {
  redirectUrl?: string;
  navigate?: ((path: string) => void) | null;
  onSuccess?: (() => void) | null;
  onError?: ((error: any) => void) | null;
  setIsLoggingOut?: ((isLogging: boolean) => void) | null;
} = {}) {
  // Set loading state if provided
  if (setIsLoggingOut) {
    setIsLoggingOut(true);
  }

  try {
    console.log('Sending logout request to server');
    const response = await UserApi.logout();

    if (response.data.success) {
      // Reset loading state if provided
      if (setIsLoggingOut) {
        setIsLoggingOut(false);
      }

      console.log('Server successfully processed logout:', response.data.message);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Handle navigation based on whether navigate function is provided
      if (redirectUrl) {
        console.log(`Redirecting to ${redirectUrl}`);
        if (navigate) {
          // Use React Router navigation if provided
          navigate(redirectUrl);
          return {success: true, redirectUrl, navigated: true};
        } else {
          // Fall back to window location (refresh) if no navigate function
          window.location.href = redirectUrl;
          return {success: true, redirectUrl, navigated: false};
        }
      }

      return {success: true};
    } else {
      console.error('Server logout failed:', response.data.message);

      // Reset loading state if provided
      if (setIsLoggingOut) {
        setIsLoggingOut(false);
      }

      // Call error callback if provided
      if (onError) {
        onError(response.data);
      }

      // Return default redirect info
      return {
        success: false,
        error: response.data.message || 'Logout failed',
        redirectUrl: '/login',
      };
    }
  } catch (error) {
    console.error('Error during logout:', error);

    // Reset loading state if provided
    if (setIsLoggingOut) {
      setIsLoggingOut(false);
    }

    // Call error callback if provided
    if (onError) {
      onError(error);
    }

    // Return default redirect info
    return {
      success: false,
      error,
      redirectUrl: '/login',
    };
  }
}
