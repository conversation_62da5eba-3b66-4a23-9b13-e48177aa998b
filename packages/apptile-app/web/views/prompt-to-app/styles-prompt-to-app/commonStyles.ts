import {StyleSheet} from 'react-native';
import theme from './theme';

const commonStyles = StyleSheet.create({
  rightSideBar: {
    width: theme.RIGHT_SIDE_BAR_WIDTH,
  },
  heading: {
    fontFamily: theme.FONT_FAMILY,
    fontWeight: theme.FONT_WEIGHT_BOLDER,
    fontSize: theme.FONT_SIZE_HEADING,
    lineHeight: theme.LINE_HEIGHT_HEADING,
  },
  controlContainer: {
    width: '100%',
  },
  inputContainer: {
    width: '62%',
    alignSelf: 'center',
    textAlignVertical: 'center',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 1,
  },
  input: {
    borderRadius: theme.INPUT_BORDER_RADIUS,
    backgroundColor: theme.INPUT_BACKGROUND,
  },
  inputText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: theme.FONT_SIZE,
    fontWeight: theme.FONT_WEIGHT as
      | 'normal'
      | 'bold'
      | '100'
      | '200'
      | '300'
      | '400'
      | '500'
      | '600'
      | '700'
      | '800'
      | '900',
    lineHeight: theme.LINE_HEIGHT,
    color: theme.CONTROL_INPUT_COLOR,
  },
  labelContainer: {
    width: '38%',
    paddingRight: 16,
    alignSelf: 'center',
    textAlignVertical: 'center',
    flexDirection: 'row',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 1,
  },
  labelText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: theme.FONT_SIZE,
    fontWeight: theme.FONT_WEIGHT as
      | 'normal'
      | 'bold'
      | '100'
      | '200'
      | '300'
      | '400'
      | '500'
      | '600'
      | '700'
      | '800'
      | '900',
    lineHeight: theme.LINE_HEIGHT,
    color: theme.FONT_COLOR,
    overflow: 'hidden',
  },
  baseText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: theme.FONT_SIZE,
    fontWeight: theme.FONT_WEIGHT as
      | 'normal'
      | 'bold'
      | '100'
      | '200'
      | '300'
      | '400'
      | '500'
      | '600'
      | '700'
      | '800'
      | '900',
    lineHeight: theme.LINE_HEIGHT,
    color: theme.CONTROL_INPUT_COLOR,
  },
  errorText: {
    color: theme.CONTROL_INPUT_COLOR,
    padding: 8,
  },
});

export default commonStyles;
