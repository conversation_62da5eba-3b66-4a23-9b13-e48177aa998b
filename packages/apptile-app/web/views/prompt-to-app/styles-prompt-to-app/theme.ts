import {Platform} from 'react-native';

const theme = {
  DEFAULT_COLOR: '#FFFFFF',
  PRIMARY_COLOR: '#1F1F1F',
  SECONDARY_COLOR: '#535353',
  TERTIARY_COLOR: '#070707',
  QUATERNARY_COLOR: '#D6D6D6',
  NEW_TAB_COLOR: '#000000',
  DISABLED_COLOR: '#858585',
  SUCCESS_COLOR: '#FFFFFF',
  WARNING_COLOR: '#FFFFFF',
  ERROR_COLOR: '#FFFFFF',
  TAB_COLOR: '#000000',
  TILE_COLOR: '#000000',
  CTA_COLOR: '#EA599E',
  PREMIUM_COLOR: '#BD00FF',
  ACCENT_COLOR: '#EA599E',
  ACTIVE_BLUE: '#0062FF',
  PURPLE_ACCENT: '#A669E1',

  DEFAULT: '#FFFFFF',
  PRIMARY: '#000000',
  SECONDARY: '#535353',
  TERTIARY: '#070707',
  QUATERNARY: '#D6D6D6',
  DISABLED: '#858585',
  SUCCESS: '#14B8A6',
  WARNING: '#EAB308',
  ERROR: '#FF4C28',
  TAB: '#F9F9F9',
  TILE: '#000000',
  CTA: '#EA599E',
  PREMIUM: '#BD00FF',
  ACCENT: '#EA599E',
  ACTIVE_BLUE_COLOR: '#0062FF',

  DEFAULT_BACKGROUND: '#000000',
  PRIMARY_BACKGROUND: '#E5DAFA',
  SECONDARY_BACKGROUND: '#DAE8FA',
  TERTIARY_BACKGROUND: '#DAFAF7',
  QUATERNARY_BACKGROUND: '#E8E8E8',
  NEW_TAB_BACKGROUND: '#0000000D',
  DISABLED_BACKGROUND: '#D9D9D9',
  SUCCESS_BACKGROUND: '#14B8A6',
  WARNING_BACKGROUND: '#EAB308',
  ERROR_BACKGROUND: '#FF4C28',
  TAB_BACKGROUND: '#FFFFFF',
  TILE_BACKGROUND: '#FFFFFF',
  TILE_LABEL_BACKGROUND: '#F9F9F9',
  CTA_BACKGROUND: '#EA599E',
  PREMIUM_BACKGROUND: '#FAEAFF',

  PRIMARY_OPAQUE_BACKGROUND: '#1060E00D',
  SIDEBAR_BACKGROUND: '#2E2E2E',
  SIDEBAR_BORDER: '#3A3A3A',
  PANEL_BACKGROUND: '#313131',
  TILE_SELECTION_BACKGROUND: '#313131',
  DIVIDER_COLOR: '#3A3A3A',

  EDITOR_GREY_COLOR: '#C8C8C8',
  EDITOR_LIGHT_BLACK_COLOR: '#535353',

  TEXT_COLOR: '#FFFFFF',
  TEXT_SECONDARY_COLOR: '#D8D6D6',

  FONT_FAMILY: "'Plus Jakarta Sans', sans-serif",
  FONT_FAMILY_BODY: 'Plus Jakarta Sans',
  FONT_FAMILY_SECONDARY: 'Be Vietnam Pro',

  PRIMARY_BORDER: '#000000',
  SECONDARY_BORDER: '#3A3A3A',
  TERTIARY_BORDER: '#3A3A3A',
  QUATERNARY_BORDER: '#000000',
  NEW_TAB_BORDER: '#0000000D',
  PREMIUM_BORDER: '#BD00FF1A',
  CTA_BORDER: '#EA599E',
  TILE_BORDER: '#3A3A3A',
  TILE_BORDER_COLOR: '#3A3A3A',
  CONTROL_BORDER: '#3A3A3A',
  grabbable: {
    ...Platform.select({
      web: {
        cursor: 'grab',
      },
    }),
  },
  hoverable: {
    ...Platform.select({
      web: {
        cursor: 'pointer',
      },
    }),
  },
  modal: {
    borderRadius: 21,
    backgroundColor: '#313131',
    border: 'none',
    padding: 10,
  },
  smallModal: {
    borderRadius: 10,
    backgroundColor: '#313131',
    border: 'none',
    padding: 7,
  },

  INPUT_BACKGROUND: '#404040',
  INPUT_BORDER_RADIUS: 8,
  INPUT_BORDER: '#3A3A3A',

  CONTROL_ACTIVE_COLOR: '#A669E1',
  CONTROL_INPUT_COLOR: '#FFFFFF',
  CONTROL_PLACEHOLDER_COLOR: '#A3A3A3',

  FONT_SIZE: 13,
  FONT_SIZE_HEADING: 21,
  FONT_COLOR: '#FFFFFF',
  FONT_WEIGHT: '500',
  FONT_WEIGHT_BOLD: '600',
  FONT_WEIGHT_BOLDER: '700',
  LINE_HEIGHT: 16,
  LINE_HEIGHT_HEADING: 23,
  PRIMARY_HEIGHT: 30,
  PRIMARY_MARGIN: 4,
  FONT_SIZE_ALERT: 16,
  LINE_HEIGHT_ALERT: 19,

  ONBOARDING_FONT_SIZE: 40,
  ONBOARDING_FONT_WEIGHT: '500',

  RIGHT_SIDE_BAR_WIDTH: 360,
} as const;

export default theme;
