import {FlexStyle, StyleProp, ViewStyle} from 'react-native';

type TRBLType = {T?: FlexStyle['top']; R?: FlexStyle['right']; B?: FlexStyle['bottom']; L?: FlexStyle['left']};

export const getAbsoluteFill = ({T, R, B, L}: TRBLType) => {
  const styles: StyleProp<ViewStyle> = {position: 'absolute'};
  if (T) styles.top = T;
  if (R) styles.right = R;
  if (B) styles.bottom = B;
  if (R) styles.left = L;
  return styles;
};

export const getShadowObj = () => ({
  shadowColor: '#000000',
  shadowOffset: {
    width: 0,
    height: 3,
  },
  shadowOpacity: 0.17,
  shadowRadius: 3.05,
  elevation: 4,
});
