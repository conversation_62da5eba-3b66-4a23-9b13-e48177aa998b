import {connect} from 'react-redux';
import {bindActionCreators} from 'redux';
import {changeAppConfig, AppDispatch} from 'apptile-core';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {
  fetchOrgs,
  saveAppState,
  updateApp,
  editorCopyAction,
  editorPasteAction,
  configureDatasources,
  clearDatasourcesCredentials,
  softRestartConfig,
  changeAppContextData,
} from '../../../../actions/editorActions';
import TileEditor from '../components/Editor';
import {initApptileTilesMode} from 'apptile-core';

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return bindActionCreators(
    {
      saveAppState,
      updateApp,
      changeAppConfig,
      fetchOrgs,
      editorCopyAction,
      editorPasteAction,
      configureDatasources,
      clearDatasourcesCredentials,
      softRestartConfig,
      changeAppContextData,
      initApptileTilesMode,
    },
    dispatch,
  );
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    editor: state.editor,
    platform: state.platform,
    orgs: state.orgs,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(TileEditor);
