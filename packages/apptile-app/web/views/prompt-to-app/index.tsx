import React, {useState, useEffect, useRef} from 'react';
import {StyleSheet, View, Text, TouchableOpacity} from 'react-native';
import {useSelector} from 'react-redux';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import PromptInput from './components/PromptInput';
import SuggestionList from './components/SuggestionList';
import BetaSignupUI from './components/BetaSignupUI';
import ParallaxBackground from './components/ParallaxBackground';
import ModalComponent from '@/root/web/components-v2/base/Modal';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import AuthModal from './components/AuthModal';
import {useNavigate} from '@/root/web/routing.web';
import {handleLogout} from '@/root/web/views/auth/authUtils';
import DashboardLeftPane from './dashboard/DashboardLeftPane';
import {NavigationProvider} from '@/root/web/contexts/NavigationContext';

const PromptToApp: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const {userLoggedIn, user} = useSelector((state: EditorRootState) => state.user);
  const navigate = useNavigate();

  // Ref for the dropdown menu
  const dropdownRef = useRef<View>(null);
  const dropdownTriggerRef = useRef<TouchableOpacity>(null);

  // Get orgs and apps from Redux store
  const orgsById = useSelector((state: EditorRootState) => state.orgs.orgsById);
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // If dropdown is open, check if click is outside both the dropdown and trigger
      if (showUserDropdown) {
        const dropdownElement = dropdownRef.current;
        const triggerElement = dropdownTriggerRef.current;

        // Get the native DOM elements
        let dropdownDomNode = null;
        let triggerDomNode = null;

        if (dropdownElement && 'getNode' in dropdownElement) {
          // For React Native Web
          dropdownDomNode = dropdownElement.getNode();
        } else if (dropdownElement) {
          // For regular DOM elements
          dropdownDomNode = dropdownElement;
        }

        if (triggerElement && 'getNode' in triggerElement) {
          // For React Native Web
          triggerDomNode = triggerElement.getNode();
        } else if (triggerElement) {
          // For regular DOM elements
          triggerDomNode = triggerElement;
        }

        // Check if the click was outside both elements
        if (
          dropdownDomNode &&
          triggerDomNode &&
          !dropdownDomNode.contains(event.target as Node) &&
          !triggerDomNode.contains(event.target as Node)
        ) {
          setShowUserDropdown(false);
        }
      }
    }

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserDropdown]);

  // State for apps
  const [userApps, setUserApps] = useState<any[]>([]);
  const [firstOrgId, setFirstOrgId] = useState<string | null>(null);

  // Track if user was initially logged out and then logged in
  const wasLoggedOutRef = useRef<boolean | null>(null);

  // Initial check when component mounts
  useEffect(() => {
    // Only set the initial value once
    if (wasLoggedOutRef.current === null) {
      wasLoggedOutRef.current = !userLoggedIn;
      console.log('Initial login state tracked:', {wasLoggedOut: wasLoggedOutRef.current});
    }
  }, [userLoggedIn]);

  // Fetch the first org ID and its apps
  useEffect(() => {
    if (Object.keys(orgsById).length > 0) {
      // Get the first org ID
      const firstId = Object.keys(orgsById)[0];
      setFirstOrgId(firstId);

      // Get apps for this org
      if (firstId && orgsById[firstId]) {
        const org = orgsById[firstId];
        const appsList = org.apps
          .map(appId => {
            return appId && appsById[appId] ? appsById[appId] : null;
          })
          .filter(Boolean); // Remove null values

        setUserApps(appsList);
      }
    }
  }, [orgsById, appsById]);

  // Handle post-login localStorage settings
  useEffect(() => {
    // Check if we need to set localStorage values
    const needsSetup = () => {
      // If user wasn't initially logged out or is still not logged in, no need to do anything
      if (wasLoggedOutRef.current !== true || !userLoggedIn) {
        return false;
      }

      // Check if localStorage values are already set correctly
      const currentPluginUrl = localStorage.getItem('plugin-server-url');
      const currentVimSetting = localStorage.getItem('enablevim');

      // Only need setup if either value is missing or incorrect
      return currentPluginUrl !== 'https://api.apptile.io/plugin-server' || currentVimSetting !== 'yes';
    };

    // Only proceed if we need to set up localStorage
    if (needsSetup()) {
      console.log('Setting up localStorage after login');

      // Set localStorage items
      localStorage.setItem('plugin-server-url', 'https://api.apptile.io/plugin-server');
      localStorage.setItem('enablevim', 'yes');

      // Reset tracking ref so this doesn't run again
      wasLoggedOutRef.current = false;

      // Add a flag to avoid refresh loops
      localStorage.setItem('setupComplete', 'true');

      console.log('Setup complete, refreshing page once');
    } else if (wasLoggedOutRef.current === true && userLoggedIn) {
      // Just reset the tracking ref without refreshing
      console.log('No localStorage setup needed');
      wasLoggedOutRef.current = false;
    }
  }, [userLoggedIn]);

  // Add this effect to check for stored prompt when component mounts
  useEffect(() => {
    // Check if there's a stored prompt and user is logged in
    const storedPrompt = sessionStorage.getItem('pendingPrompt');
    if (storedPrompt && userLoggedIn) {
      console.log('Found stored prompt after login:', storedPrompt);
      // Set the prompt in the input
      setPrompt(storedPrompt);
      // Clear the stored prompt
      sessionStorage.removeItem('pendingPrompt');
      // Small delay to ensure the UI is updated
      setTimeout(() => {
        // Simulate a submit to create the app
        const sendButton = document.querySelector('[data-testid="send-button"]');
        if (sendButton) {
          (sendButton as HTMLElement).click();
        }
      }, 500);
    }
  }, [userLoggedIn]);

  useEffect(() => {
    const hideIntercom = () => {
      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = 'none';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = 'none';
      }
    };

    // Initial attempt in case Intercom is already loaded
    hideIntercom();

    // Set up MutationObserver to watch for Intercom injection
    const observer = new MutationObserver(hideIntercom);
    observer.observe(document.body, {childList: true, subtree: true});

    // Cleanup
    return () => {
      observer.disconnect();
      // Show Intercom again
      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = '';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = '';
      }
    };
  }, []);

  const handlePromptChange = (value: string) => {
    setPrompt(value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setPrompt(suggestion);
  };

  // This function will be called when the user submits the prompt
  const handlePromptSubmit = () => {
    if (!userLoggedIn) {
      // Show login modal if user is not logged in
      setShowLoginModal(true);

      // Ensure we track that the user started as logged out
      if (wasLoggedOutRef.current === null) {
        wasLoggedOutRef.current = true;
      }
    } else {
      // Handle the prompt submission for logged in users
      // Your existing logic for handling the prompt
    }
  };

  // Toggle sidebar collapse state
  const toggleSidebar = () => {
    setSidebarCollapsed(!isSidebarCollapsed);
  };

  // Navigate to app editor
  const navigateToApp = (app: any) => {
    if (firstOrgId) {
      navigate(`/dashboard/${firstOrgId}/app/${app.uuid}?openChat=true&tiledev=true`);
    }
  };

  const onLogout = async () => {
    if (isLoggingOut) return; // Prevent multiple clicks

    // Close the dropdown
    setShowUserDropdown(false);

    // Call the centralized logout utility function with navigate function
    await handleLogout({
      redirectUrl: '/',
      setIsLoggingOut,
      onSuccess: () => console.log('Logout successful'),
    });
  };

  const handleRequestBetaAccess = () => {
    // Redirect to tile.dev website
    window.open('https://www.tile.dev', '_blank');
  };

  // Check if user has beta access
  const userHasBetaAccess = userLoggedIn ? user?.isTileBetaAccessEnabled !== false : true;

  return (
    <View style={styles.container}>
      {/* Parallax Background */}
      <ParallaxBackground />

      <NavigationProvider>
        <DashboardLeftPane />
      </NavigationProvider>
      {/* Main Content */}
      <View style={[styles.mainContent, isSidebarCollapsed && styles.mainContentExpanded]}>
        <View style={styles.content}>
          {userHasBetaAccess ? (
            <>
              <Text style={styles.title}>What do you want to build?</Text>
              <Text style={styles.subtitle}>Turn your idea into a full-stack application</Text>

              <View style={styles.inputContainer}>
                <PromptInput value={prompt} onChange={handlePromptChange} onSubmit={handlePromptSubmit} />
              </View>

              <View style={styles.suggestionsContainer}>
                <SuggestionList onSuggestionClick={handleSuggestionClick} />
              </View>
            </>
          ) : (
            <BetaSignupUI onRequestAccess={handleRequestBetaAccess} />
          )}
        </View>
      </View>

      {/* Use a portal-like approach for the modal to ensure it appears as an overlay */}
      {showLoginModal && (
        <View style={styles.modalOverlay}>
          <ModalComponent
            visible={true}
            onVisibleChange={setShowLoginModal}
            content={<AuthModal onClose={() => setShowLoginModal(false)} />}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Make transparent to show parallax background
    minHeight: '100vh',
    flexDirection: 'row',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000, // Ensure it appears on top of everything
    backgroundColor: 'transparent', // Let the modal handle its own background
  },
  // Sidebar styles
  sidebar: {
    width: 250,
    backgroundColor: '#0F0F0F',
    height: '100vh',
    flexDirection: 'column',
    transition: 'width 0.2s ease',
    borderRightWidth: 1,
    borderRightColor: '#222',
  },
  sidebarCollapsed: {
    width: 50,
  },
  sidebarHeader: {
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  sidebarHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  collapseButton: {
    padding: 5,
  },
  sidebarContent: {
    flex: 1,
    paddingVertical: 20,
    paddingHorizontal: 16,
    overflowY: 'auto',
    overflowX: 'hidden',
  },
  sidebarSectionTitle: {
    color: '#AAAAAA',
    fontSize: 14,
    marginBottom: 12,
  },
  sidebarItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 8,
  },
  sidebarItemText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginLeft: 8,
  },
  spacer: {
    flex: 1,
    minHeight: 20,
  },
  sidebarFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#222',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  userDetails: {
    marginLeft: 10,
  },
  userEmail: {
    color: '#AAAAAA',
    fontSize: 12,
    maxWidth: 180,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  // Dropdown menu styles
  userDropdown: {
    position: 'absolute',
    top: -50, // Position it above the user info section
    left: 0, // Center better with user info
    width: 150,
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#333',
    overflow: 'visible', // Allow for caret to extend outside the box
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 5,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    justifyContent: 'center',
  },
  dropdownIcon: {
    marginRight: 8,
  },
  dropdownText: {
    color: '#FFFFFF',
    fontSize: 14,
  },

  // Main content styles
  mainContent: {
    flex: 1,
    backgroundColor: 'transparent', // Make transparent to show parallax background
    transition: 'margin-left 0.2s ease',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 1, // Ensure content appears above background
  },
  mainContentExpanded: {
    marginLeft: 0,
  },
  content: {
    width: '100%',
    maxWidth: 800,
    padding: 20,
    alignItems: 'center',
    position: 'relative',
    zIndex: 2, // Higher z-index for better layering
  },
  title: {
    fontSize: 48,
    fontWeight: '600',
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    color: themeColors.EDITOR_ACCENT_COLOR,
    marginBottom: 40,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 32,
  },
  suggestionsContainer: {
    width: '100%',
  },
});

export default PromptToApp;
