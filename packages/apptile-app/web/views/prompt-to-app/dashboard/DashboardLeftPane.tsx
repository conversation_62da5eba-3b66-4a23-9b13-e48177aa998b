import ModalComponent from '@/root/web/components-v2/base/Modal';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import {useNavigate} from '@/root/web/routing.web';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {handleLogout} from '@/root/web/views/auth/authUtils';
import {Icon} from 'apptile-core';
import React, {useEffect, useRef, useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View, Linking, Pressable} from 'react-native';
import {useSelector} from 'react-redux';
import AuthModal from '../components/AuthModal';
import {useNavigation} from '@/root/web/contexts/NavigationContext';
import {DashboardViewType} from '@/root/web/contexts/navigationReducer';

const DashboardLeftPane: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const {userLoggedIn, user} = useSelector((state: EditorRootState) => state.user);
  const {navigateTo} = useNavigation();
  const navigate = useNavigate();

  // Ref for the dropdown menu
  const dropdownRef = useRef<View>(null);
  const dropdownTriggerRef = useRef<TouchableOpacity>(null);

  // Get orgs and apps from Redux store
  const orgsById = useSelector((state: EditorRootState) => state.orgs.orgsById);
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // If dropdown is open, check if click is outside both the dropdown and trigger
      if (showUserDropdown) {
        const dropdownElement = dropdownRef.current;
        const triggerElement = dropdownTriggerRef.current;

        // Get the native DOM elements
        let dropdownDomNode = null;
        let triggerDomNode = null;

        if (dropdownElement && 'getNode' in dropdownElement) {
          // For React Native Web
          dropdownDomNode = dropdownElement.getNode();
        } else if (dropdownElement) {
          // For regular DOM elements
          dropdownDomNode = dropdownElement;
        }

        if (triggerElement && 'getNode' in triggerElement) {
          // For React Native Web
          triggerDomNode = triggerElement.getNode();
        } else if (triggerElement) {
          // For regular DOM elements
          triggerDomNode = triggerElement;
        }

        // Check if the click was outside both elements
        if (
          dropdownDomNode &&
          triggerDomNode &&
          !dropdownDomNode.contains(event.target as Node) &&
          !triggerDomNode.contains(event.target as Node)
        ) {
          setShowUserDropdown(false);
        }
      }
    }

    // Add event listener
    document.addEventListener('mousedown', handleClickOutside);

    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserDropdown]);

  // State for apps
  const [userApps, setUserApps] = useState<any[]>([]);
  const [firstOrgId, setFirstOrgId] = useState<string | null>(null);

  // Track if user was initially logged out and then logged in
  const wasLoggedOutRef = useRef<boolean | null>(null);

  // Initial check when component mounts
  useEffect(() => {
    // Only set the initial value once
    if (wasLoggedOutRef.current === null) {
      wasLoggedOutRef.current = !userLoggedIn;
      console.log('Initial login state tracked:', {wasLoggedOut: wasLoggedOutRef.current});
    }
  }, [userLoggedIn]);

  // Fetch the first org ID and its apps
  useEffect(() => {
    if (Object.keys(orgsById).length > 0) {
      // Get the first org ID
      const firstId = Object.keys(orgsById)[0];
      setFirstOrgId(firstId);

      // Get apps for this org
      if (firstId && orgsById[firstId]) {
        const org = orgsById[firstId];
        const appsList = org.apps
          .map(appId => {
            return appId && appsById[appId] ? appsById[appId] : null;
          })
          .filter(Boolean); // Remove null values

        setUserApps(appsList);
      }
    }
  }, [orgsById, appsById]);

  // Handle post-login localStorage settings
  useEffect(() => {
    // Check if we need to set localStorage values
    const needsSetup = () => {
      // If user wasn't initially logged out or is still not logged in, no need to do anything
      if (wasLoggedOutRef.current !== true || !userLoggedIn) {
        return false;
      }

      // Check if localStorage values are already set correctly
      const currentPluginUrl = localStorage.getItem('plugin-server-url');
      const currentVimSetting = localStorage.getItem('enablevim');

      // Only need setup if either value is missing or incorrect
      return currentPluginUrl !== 'https://api.apptile.io/plugin-server' || currentVimSetting !== 'yes';
    };

    // Only proceed if we need to set up localStorage
    if (needsSetup()) {
      console.log('Setting up localStorage after login');

      // Set localStorage items
      localStorage.setItem('plugin-server-url', 'https://api.apptile.io/plugin-server');
      localStorage.setItem('enablevim', 'yes');

      // Reset tracking ref so this doesn't run again
      wasLoggedOutRef.current = false;

      // Add a flag to avoid refresh loops
      localStorage.setItem('setupComplete', 'true');

      console.log('Setup complete, refreshing page once');
    } else if (wasLoggedOutRef.current === true && userLoggedIn) {
      // Just reset the tracking ref without refreshing
      console.log('No localStorage setup needed');
      wasLoggedOutRef.current = false;
    }
  }, [userLoggedIn]);

  // Add this effect to check for stored prompt when component mounts
  useEffect(() => {
    // Check if there's a stored prompt and user is logged in
    const storedPrompt = sessionStorage.getItem('pendingPrompt');
    if (storedPrompt && userLoggedIn) {
      console.log('Found stored prompt after login:', storedPrompt);
      // Set the prompt in the input
      setPrompt(storedPrompt);
      // Clear the stored prompt
      sessionStorage.removeItem('pendingPrompt');
      // Small delay to ensure the UI is updated
      setTimeout(() => {
        // Simulate a submit to create the app
        const sendButton = document.querySelector('[data-testid="send-button"]');
        if (sendButton) {
          (sendButton as HTMLElement).click();
        }
      }, 500);
    }
  }, [userLoggedIn]);

  const handlePromptChange = (value: string) => {
    setPrompt(value);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setPrompt(suggestion);
  };

  // This function will be called when the user submits the prompt
  const handlePromptSubmit = () => {
    if (!userLoggedIn) {
      // Show login modal if user is not logged in
      setShowLoginModal(true);

      // Ensure we track that the user started as logged out
      if (wasLoggedOutRef.current === null) {
        wasLoggedOutRef.current = true;
      }
    } else {
      // Handle the prompt submission for logged in users
      // Your existing logic for handling the prompt
    }
  };

  // Toggle sidebar collapse state
  const toggleSidebar = () => {
    setSidebarCollapsed(!isSidebarCollapsed);
  };

  // Navigate to app editor
  const navigateToApp = (app: any) => {
    if (firstOrgId) {
      navigate(`/dashboard/${firstOrgId}/app/${app.uuid}?openChat=true&tiledev=true`);
    }
  };

  const onLogout = async () => {
    if (isLoggingOut) return; // Prevent multiple clicks

    // Close the dropdown
    setShowUserDropdown(false);

    // Call the centralized logout utility function with navigate function
    await handleLogout({
      redirectUrl: '/',
      setIsLoggingOut,
      onSuccess: () => console.log('Logout successful'),
    });
  };

  return (
    <View>
      {/* Sidebar - only show when user is logged in */}

      <View style={[styles.sidebar, isSidebarCollapsed && styles.sidebarCollapsed]}>
        <View style={styles.sidebarHeader}>
          <View style={styles.sidebarHeaderContent}>
            {/* {!isSidebarCollapsed && <Text style={styles.logoText}>Projects</Text>} */}
            <TouchableOpacity onPress={toggleSidebar} style={styles.collapseButton}>
              {isSidebarCollapsed ? (
                <Icon size={18} iconType="MaterialIcons" name="chevron-right" style={{color: 'white'}} />
              ) : (
                <Icon size={18} iconType="MaterialIcons" name="chevron-left" style={{color: 'white'}} />
              )}
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.sidebarContent}>
          {!isSidebarCollapsed && (
            <>
              {/* {userApps.length > 0 ? (
                  userApps.map((app, index) => (
                    <TouchableOpacity key={app.uuid} style={styles.sidebarItem} onPress={() => navigateToApp(app)}>
                      <Text style={styles.sidebarItemText} numberOfLines={1} ellipsizeMode="tail">
                        {app.name}
                      </Text>
                    </TouchableOpacity>
                  ))
                ) : (
                  <TouchableOpacity style={styles.sidebarItem}>
                    <Text style={styles.sidebarItemText}>No projects found</Text>
                  </TouchableOpacity>
                )}
                <View style={styles.spacer} /> */}

              {/* Navigation Items */}
              {/* <TouchableOpacity
                  style={styles.sidebarItem}
                  onPress={() => navigateTo(DashboardViewType.INTEGRATIONS)}
                  activeOpacity={0.7}>
                  <Icon
                    iconType="MaterialCommunityIcons"
                    name="connection"
                    size={16}
                    color="#fff"
                    style={styles.socialIcon}
                  />
                  <Text style={styles.sidebarItemText}>Integrations</Text>
                </TouchableOpacity> */}
            </>
          )}
        </View>

        {/* Social Media Links */}
        <View style={styles.socialLinksContainer}>
          {!isSidebarCollapsed ? (
            <>
              <TouchableOpacity
                style={styles.socialItem}
                onPress={() => Linking.openURL('https://x.com/tile_dev')}
                activeOpacity={0.7}>
                <Icon iconType="FontAwesome" name="twitter" size={16} color="#fff" style={styles.socialIcon} />
                <Text style={styles.socialText}>X</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.socialItem}
                onPress={() => Linking.openURL('https://discord.gg/HrTb3CkCDx')}
                activeOpacity={0.7}>
                <Icon
                  iconType="MaterialCommunityIcons"
                  name="discord"
                  size={16}
                  color="#fff"
                  style={styles.socialIcon}
                />
                <Text style={styles.socialText}>Discord</Text>
              </TouchableOpacity>
            </>
          ) : (
            <>
              <TouchableOpacity
                style={styles.socialItemCollapsed}
                onPress={() => Linking.openURL('https://twitter.com/tile_dev')}
                activeOpacity={0.7}>
                <Icon iconType="FontAwesome" name="twitter" size={16} color="#fff" />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.socialItemCollapsed}
                onPress={() => Linking.openURL('https://discord.gg/HrTb3CkCDx')}
                activeOpacity={0.7}>
                <Icon iconType="MaterialCommunityIcons" name="discord" size={16} color="#fff" />
              </TouchableOpacity>
            </>
          )}
        </View>

        {/* Login Button - only show when user is logged out */}
        {!userLoggedIn && (
          <View style={styles.loginContainer}>
            {!isSidebarCollapsed ? (
              <TouchableOpacity style={styles.loginButton} onPress={() => setShowLoginModal(true)} activeOpacity={0.7}>
                <Icon
                  iconType="MaterialCommunityIcons"
                  name="account-outline"
                  size={16}
                  color="#fff"
                  style={styles.loginIcon}
                />
                <Text style={styles.loginText}>Sign in</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={styles.loginButtonCollapsed}
                onPress={() => setShowLoginModal(true)}
                activeOpacity={0.7}>
                <Icon iconType="MaterialCommunityIcons" name="account-outline" size={16} color="#fff" />
              </TouchableOpacity>
            )}
          </View>
        )}

        <View style={styles.sidebarFooter}>
          {!isSidebarCollapsed && userLoggedIn && (
            <View style={styles.userInfo}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>{user?.firstname ? user.firstname.charAt(0).toUpperCase() : '?'}</Text>
              </View>
              <TouchableOpacity
                ref={dropdownTriggerRef}
                style={styles.userDetails}
                onPress={() => setShowUserDropdown(!showUserDropdown)}
                activeOpacity={0.7}>
                <Text style={styles.userEmail}>{user?.email || ''}</Text>

                {/* User dropdown menu with outside click detection */}
                {showUserDropdown && (
                  <View ref={dropdownRef} style={styles.userDropdown}>
                    <TouchableOpacity style={styles.dropdownItem} onPress={onLogout} disabled={isLoggingOut}>
                      <Icon iconType="MaterialIcons" name="logout" size={16} color="#fff" style={styles.dropdownIcon} />
                      <Text style={styles.dropdownText}>Logout</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>

        {isSidebarCollapsed && (
          <>
            {/* Logout Icon */}
            {userLoggedIn && (
              <>
                <Pressable style={styles.collapsedIconButton}>
                  <Text style={styles.avatarText}>
                    {user?.firstname ? user.firstname.charAt(0).toUpperCase() : '?'}
                  </Text>
                </Pressable>

                <Pressable style={styles.collapsedIconButton} onPress={onLogout} disabled={isLoggingOut}>
                  <Icon iconType="MaterialIcons" name="logout" size={16} color="#fff" />
                </Pressable>
              </>
            )}
          </>
        )}
      </View>

      {/* Use a portal-like approach for the modal to ensure it appears as an overlay */}
      {showLoginModal && (
        <View style={styles.modalOverlay}>
          <ModalComponent
            visible={true}
            onVisibleChange={setShowLoginModal}
            content={<AuthModal onClose={() => setShowLoginModal(false)} />}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000, // Ensure it appears on top of everything
    backgroundColor: 'transparent', // Let the modal handle its own background
  },
  // Sidebar styles
  sidebar: {
    width: 250,
    backgroundColor: '#0F0F0F',
    height: '100vh',
    flexDirection: 'column',
    transition: 'width 0.2s ease',
    borderRightWidth: 1,
    borderRightColor: '#222',
  },
  collapsedIconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#222',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
    marginLeft: 'auto',
    marginRight: 'auto',
    display: 'flex',
  },
  sidebarCollapsed: {
    width: 50,
  },
  sidebarHeader: {
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: '#222',
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  sidebarHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  collapseButton: {
    padding: 5,
  },
  sidebarContent: {
    flex: 1,
    paddingVertical: 20,
    paddingHorizontal: 16,
    overflowY: 'auto',
    overflowX: 'hidden',
  },
  sidebarSectionTitle: {
    color: '#AAAAAA',
    fontSize: 14,
    marginBottom: 12,
  },
  sidebarItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 8,
  },
  sidebarItemText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginLeft: 8,
  },
  spacer: {
    flex: 1,
    minHeight: 20,
  },
  // Social media links styles
  socialLinksContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#222',
  },
  socialItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
  },
  socialItemCollapsed: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    paddingVertical: 8,
  },
  socialIcon: {
    marginRight: 8,
  },
  socialText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  // Login button styles
  loginContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#222',
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: '#4285F4',
    // paddingVertical: 10,
    // paddingHorizontal: 16,
    borderRadius: 6,
    justifyContent: 'flex-start',
  },
  loginButtonCollapsed: {
    alignItems: 'center',
    justifyContent: 'center',
    // backgroundColor: '#4285F4',
    width: '36',
    height: '36',
    borderRadius: 18,
    marginLeft: 'auto',
    marginRight: 'auto',
  },
  loginIcon: {
    marginRight: 8,
  },
  loginText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  // User section styles
  sidebarFooter: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#222',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  userDetails: {
    marginLeft: 10,
  },
  userEmail: {
    color: '#AAAAAA',
    fontSize: 12,
    maxWidth: 180,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  // Dropdown menu styles
  userDropdown: {
    position: 'absolute',
    top: -50, // Position it above the user info section
    left: 0, // Center better with user info
    width: 150,
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#333',
    overflow: 'visible', // Allow for caret to extend outside the box
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 5,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    justifyContent: 'flex-start',
  },
  dropdownIcon: {
    marginRight: 8,
    alignSelf: 'center',
  },
  dropdownText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  // Main content styles
  mainContent: {
    flex: 1,
    backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
    transition: 'margin-left 0.2s ease',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mainContentExpanded: {
    marginLeft: 0,
  },
  content: {
    width: '100%',
    maxWidth: 800,
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 48,
    fontWeight: '600',
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    color: themeColors.EDITOR_ACCENT_COLOR,
    marginBottom: 40,
    textAlign: 'center',
  },
  inputContainer: {
    width: '100%',
    marginBottom: 32,
  },
  suggestionsContainer: {
    width: '100%',
  },
});

export default DashboardLeftPane;
