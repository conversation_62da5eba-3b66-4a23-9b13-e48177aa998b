import React, {useCallback, useEffect, useState} from 'react';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {StyleSheet, Text, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {deleteOrgApp} from '../../../actions/editorActions';
import {useNavigate, useParams} from '../../../routing.web';
import Button from '../../../components-v2/base/Button';
import {MaterialCommunityIcons} from 'apptile-core';
import {Dialog} from '@/root/web/layout-v2/SaveAndPublish';
import TextElement from '@/root/web/components-v2/base/TextElement';
import Fuse from 'fuse.js';
import Tooltip from '@/root/web/components-v2/base/SimpleTooltip';
import {Pressable as RNWebPressable, PressableProps as RNWebPressableProps} from 'react-native';
import CreateAppModal from '../../platform/app/CreateAppModal';
import PromptDashboard from '../components/PromptDashboard';
import {useNavigation} from '@/root/web/contexts/NavigationContext';
import IntegrationsPage from '../integrations/IntegrationsPage';
import NotificationsPage from '../notifications/NotificationsPage';
import AnalyticsPage from '../analytics/AnalyticsPage';
import {DashboardViewType} from '@/root/web/contexts/navigationReducer';

export type PressableProps = RNWebPressableProps & {
  onHoverIn: (e: MouseEvent) => void;
  onHoverOut: (e: MouseEvent) => void;
};
export function Pressable(props: PressableProps) {
  return <RNWebPressable {...props} />;
}

const DASHBOARD_FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  minMatchCharLength: 2,
  ignoreLocation: true,
  threshold: 0.1,
  keys: ['name'],
};

const DashboardMain: React.FC = ({}) => {
  const orgsById = useSelector((state: EditorRootState) => state.orgs.orgsById);
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);
  const {
    state: {currentView},
    navigateTo,
  } = useNavigation();

  const dispatch = useDispatch();
  let params = useParams();
  const orgId = params.orgId;
  const org = orgId && orgsById[orgId] ? orgsById[orgId] : null;

  const [showNewAppForm, setShowNewAppForm] = useState(false);
  const [isDeletionDialogVisible, setDeletionDialogVisible] = useState(false);
  const [deletableAppId, setDeletableAppId] = useState('');

  const [fuseSearch, setFuseSearch] = React.useState(new Fuse(Object.values([]), DASHBOARD_FUSE_OPTIONS));

  const [filterText, setFilterText] = useState('');
  const [filteredItems, setFilteredItems] = React.useState(fuseSearch?.search(filterText));

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  useEffect(() => {
    const appsByOrg: any =
      orgId && orgsById[orgId]
        ? orgsById[orgId].apps.map(appId => {
            const app = appId && appsById[appId] ? appsById[appId] : null;
            return app;
          })
        : [];
    setFuseSearch(new Fuse(Object.values(appsByOrg), DASHBOARD_FUSE_OPTIONS));
  }, [orgId, orgsById, appsById]);

  useEffect(() => {
    const fi = fuseSearch.search(filterText);
    setFilteredItems(fi);
  }, [fuseSearch, filterText]);
  const onEditPress = (appId: string) => {
    // navigate(`/dashboard/${orgId}/app/${appId}/dashboard/store`);
    window.location.assign(`/dashboard/${orgId}/app/${appId}`);
  };

  useEffect(() => {
    const hideIntercom = () => {
      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = 'none';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = 'none';
      }
    };

    // Initial attempt in case Intercom is already loaded
    hideIntercom();

    // Set up MutationObserver to watch for Intercom injection
    const observer = new MutationObserver(hideIntercom);
    observer.observe(document.body, {childList: true, subtree: true});

    // Cleanup
    return () => {
      observer.disconnect();
      // Show Intercom again
      const intercomLauncher = document.querySelector('.intercom-lightweight-app');
      if (intercomLauncher) {
        intercomLauncher.style.display = '';
      }
      const intercomFrame = document.getElementById('intercom-frame');
      if (intercomFrame) {
        intercomFrame.style.display = '';
      }
    };
  }, []);

  const confirmAppDeletion = () => {
    if (isDeletionDialogVisible && deletableAppId) {
      onDeleteApp(deletableAppId);
      setDeletionDialogVisible(false);
      setDeletableAppId('');
    }
  };

  const onDeleteApp = (appId: string) => {
    if (orgId) dispatch(deleteOrgApp(orgId, appId));
  };

  const onCreateApp = useCallback(() => {
    setShowNewAppForm(true);
  }, []);

  return (
    <View style={styles.container}>
      {currentView === DashboardViewType.INTEGRATIONS ? (
        <IntegrationsPage onBack={() => navigateTo(DashboardViewType.APPS)} />
      ) : currentView === DashboardViewType.NOTIFICATIONS ? (
        <NotificationsPage onBack={() => navigateTo(DashboardViewType.APPS)} />
      ) : currentView === DashboardViewType.ANALYTICS ? (
        <AnalyticsPage onBack={() => navigateTo(DashboardViewType.APPS)} />
      ) : (
        <>
          <View style={styles.appHeader}>
            <View style={styles.appHeaderItem}>
              <View style={styles.secondaryHeader}>
                <PromptDashboard />
              </View>
            </View>
          </View>
          {showNewAppForm ? (
            <View>
              <CreateAppModal isVisible={showNewAppForm} onClose={() => setShowNewAppForm(false)} />
            </View>
          ) : (
            <>
              {org && org.apps.length !== 0 && <Text style={styles.titleText}>My Apps</Text>}
              <View style={styles.appWrapperBox}>
                {filteredItems && filteredItems.length
                  ? filteredItems.map((appResult, appIdx) => {
                      const app = appResult && appResult?.item ? appResult.item : null;
                      if (!app) return null;
                      return (
                        <Pressable
                          key={appIdx}
                          style={({}) => [
                            {
                              backgroundColor: appIdx === hoveredIndex ? '#e6f1ff' : 'white',
                            },
                            styles.appBox,
                          ]}
                          onHoverIn={() => {
                            setHoveredIndex(appIdx);
                          }}
                          onHoverOut={() => {
                            setHoveredIndex(null);
                          }}
                          onPress={() => onEditPress(app?.uuid)}>
                          <View style={styles.appTitle}>
                            <Text style={{color: '#FAFAFA', fontWeight: '500', fontSize: 18}}>{app?.name}</Text>
                          </View>
                          <View style={styles.appActions}>
                            <Tooltip
                              tooltip="Delete App"
                              position="top"
                              toolTipMenuStyles={{width: '80px', height: 35}}>
                              <Pressable
                                style={styles.iconStyle}
                                onPress={() => {
                                  setDeletableAppId(app?.uuid);
                                  setDeletionDialogVisible(true);
                                  // onDeleteApp(appId)
                                }}
                                onHoverIn={() => {
                                  setHoveredIndex(appIdx);
                                }}
                                onHoverOut={() => {
                                  setHoveredIndex(appIdx);
                                }}>
                                <MaterialCommunityIcons name={'trash-can-outline'} size={19} color="white" />
                              </Pressable>
                            </Tooltip>
                          </View>
                        </Pressable>
                      );
                    })
                  : null}
                {filterText.length === 0 &&
                  org &&
                  org.apps &&
                  org.apps.map((appId, index) => {
                    const app = appId && appsById[appId] ? appsById[appId] : null;
                    if (!app) return null;
                    return (
                      <Pressable
                        key={appId}
                        style={({pressed, hovered}) => [
                          styles.appBox,
                          hovered && styles.appBoxHovered,
                          pressed && styles.appBoxPressed,
                        ]}
                        onPress={() => onEditPress(appId)}
                        onHoverIn={() => {
                          setHoveredIndex(index);
                        }}
                        onHoverOut={() => {
                          setHoveredIndex(null);
                        }}>
                        <View style={styles.appTitle}>
                          <Text style={{color: '#FAFAFA', fontWeight: '500', fontSize: 18}}>{app?.name}</Text>
                        </View>
                        <View style={styles.appActions}>
                          <Pressable
                            style={styles.iconStyle}
                            onPress={() => {
                              setDeletableAppId(appId);
                              setDeletionDialogVisible(true);
                              // onDeleteApp(appId)
                            }}
                            onHoverIn={() => {
                              setHoveredIndex(index);
                            }}
                            onHoverOut={() => {
                              setHoveredIndex(index);
                            }}>
                            <MaterialCommunityIcons name={'trash-can-outline'} size={18} color={'white'} />
                          </Pressable>
                        </View>
                      </Pressable>
                    );
                  })}
                <View style={styles.noappsContainer}>
                  {(filterText.length !== 0 && filteredItems.length === 0) ||
                  (filterText.length === 0 && org?.apps.length === 0) ? (
                    <Text style={styles.notFoundText}>No Apps Found </Text>
                  ) : null}
                </View>
              </View>
            </>
          )}
        </>
      )}

      {isDeletionDialogVisible && (
        <Dialog>
          <TextElement fontWeight="500" color="SECONDARY" style={{fontSize: 18}}>
            Are you sure you want to delete the app?
          </TextElement>
          <View style={{flexDirection: 'row', justifyContent: 'center', width: '100%', marginTop: 30}}>
            <Button onPress={confirmAppDeletion} textStyles={{color: 'white'}} color="ERROR">
              Confirm
            </Button>
            <Button
              containerStyles={{marginLeft: '10px'}}
              textStyles={{color: 'white'}}
              onPress={() => setDeletionDialogVisible(false)}
              color="SUCCESS">
              Cancel
            </Button>
          </View>
        </Dialog>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    padding: 40,
    paddingTop: 20,
    backgroundColor: 'transparent', // Make transparent to show parallax background
    height: '100%',
    overflow: 'scroll',
    scrollbarColor: '#faebd736 #1f1f1f', // Gets rid of that little box in the bottom-right
    scrollbarWidth: 'thin',
    position: 'relative',
  },
  appWrapperBox: {
    flexDirection: 'row',
    marginTop: 30,
    maxHeight: 'calc(100vh - 250px)',
    width: '100%',
    overflowX: 'hidden',
    paddingRight: 15,
    flexWrap: 'wrap',
    position: 'relative',
  },

  appHeader: {
    padding: 8,
    paddingBottom: 10,
    marginTop: 20,
    flexDirection: 'row',
  },
  appHeaderItem: {
    flex: 5,
    alignSelf: 'flex-end',
  },
  secondaryHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  titleText: {
    fontSize: 28,
    fontWeight: '600',
    color: '#FAFAFA',
  },
  appHeaderAction: {
    flex: 1,
  },
  appBox: {
    width: '49%',
    flexShrink: 0,
    flexWrap: 'wrap',
    padding: 15,
    borderWidth: 1,
    borderColor: '#303030',
    borderRadius: 8,
    margin: 4,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#272727',
    transition: 'all 0.2s ease',
  },
  appBoxHovered: {
    backgroundColor: '#2F2F2F',
    transform: [{scale: 1.01}],
    borderColor: '#404040',
  },
  appBoxPressed: {
    backgroundColor: '#242424',
    transform: [{scale: 0.99}],
  },
  appTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#FAFAFA',
  },
  appActions: {
    flexDirection: 'row',
    alignSelf: 'flex-end',
  },
  iconStyle: {
    flex: 1,
    padding: 8,
    borderRadius: 50,
    backgroundColor: '#FF4C28',
    marginLeft: 12,
    borderColor: 'transparent',
    borderWidth: 0,
    width: 35,
    height: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
    transition: 'all 0.2s ease',
    cursor: 'pointer',
  },
  actionText: {
    color: '#2096f3',
    textDecorationLine: 'underline',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#303030',
    backgroundColor: '#1F1F1F',
  },
  textBox: {
    padding: 10,
    paddingLeft: '7px',
    width: '100%',
    backgroundColor: '#121212',
    borderRadius: 10,
    color: '#FAFAFA',
  },
  noappsContainer: {
    width: '100%',
  },
  notFoundText: {
    fontSize: 14,
    color: '#909090',
    textAlign: 'center',
    paddingTop: 100,
  },
});

export default DashboardMain;
