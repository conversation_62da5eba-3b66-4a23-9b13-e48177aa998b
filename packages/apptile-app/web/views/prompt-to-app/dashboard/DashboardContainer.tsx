import {EditorRootState} from '@/root/web/store/EditorRootState';
import theme from '../styles-prompt-to-app/theme';
import React from 'react';
import {StyleSheet, View} from 'react-native';
import {connect} from 'react-redux';
import {selectBetaAccessStatus} from '../../../selectors/WorkspaceSelector';
import AppFooter from '../components/AppFooter';
import DashboardLeftPane from './DashboardLeftPane';
import DashboardMain from './DashboardMain';
import {NavigationProvider} from '@/root/web/contexts/NavigationContext';
import ParallaxBackground from '../components/ParallaxBackground';

const DashboardContainer: React.FC<{betaAccessBlocked: boolean}> = () => {
  return (
    <NavigationProvider>
      <View style={styles.container}>
        <View style={styles.mainContainer}>
          <ParallaxBackground />
          <DashboardLeftPane />
          <View style={styles.platformBodyWrapper}>
            <View style={styles.mainContent}>
              <DashboardMain />
            </View>
            <AppFooter
              links={[
                {label: 'Terms of Use', url: '/terms-of-use'},
                {label: 'Privacy Policy', url: '/privacy-policy'},
              ]}
              containerStyle={styles.footer}
              textStyle={styles.footerText}
              separatorStyle={styles.footerSeparator}
            />
          </View>
        </View>
      </View>
    </NavigationProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100vw',
    height: '100vh',
  },
  mainContainer: {
    flexDirection: 'row',
    height: '100%',
  },
  platformBodyWrapper: {
    flex: 10,
    borderLeftWidth: 1,
    borderLeftColor: theme.CONTROL_BORDER,
    position: 'relative',
    flexDirection: 'column',
  },
  mainContent: {
    flex: 1,
    overflow: 'hidden',
  },
  footer: {
    borderTopWidth: 1,
    borderTopColor: theme.CONTROL_BORDER,
    paddingVertical: 16,
    paddingHorizontal: 20,
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    zIndex: 1,
  },
  footerText: {
    color: theme.TEXT_SECONDARY_COLOR,
    fontSize: 12,
    opacity: 0.8,
  },
  footerSeparator: {
    color: theme.TEXT_SECONDARY_COLOR,
    opacity: 0.5,
  },
});

const mapStateToProps = (state: EditorRootState) => {
  return {
    betaAccessBlocked: selectBetaAccessStatus(state),
  };
};

export default connect(mapStateToProps)(DashboardContainer);
