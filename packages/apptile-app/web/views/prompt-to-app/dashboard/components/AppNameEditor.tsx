import Analytics from '@/root/web/lib/segment';
import AppApi from '@/root/web/api/AppApi';
import {MaterialCommunityIcons} from 'apptile-core';
import React, {useEffect, useState, useRef} from 'react';
import {Pressable, StyleSheet, Text, View, TextInput} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useParams} from '@/root/web/routing.web';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
import {makeToast} from '@/root/web/actions/toastActions';

const commonStyles = getCommonStyles();
// App Name Editor component
const AppNameEditor = () => {
  const params = useParams();
  const dispatch = useDispatch();
  const [isEditing, setIsEditing] = useState(false);
  const [appName, setAppName] = useState('');
  const [originalName, setOriginalName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const inputRef = useRef<TextInput>(null);

  // Get app name from Redux store
  const currentApp = useSelector((state: EditorRootState) => state.orgs?.appsById?.[params.id] || {});

  useEffect(() => {
    if (currentApp?.name) {
      setAppName(currentApp.name);
      setOriginalName(currentApp.name);
    }
  }, [currentApp?.name]);

  const handleEditStart = () => {
    setIsEditing(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  };

  const handleEditComplete = async () => {
    if (isSaving) {
      return;
    }

    // If name is empty, revert to original and exit edit mode.
    if (!appName.trim()) {
      setAppName(originalName);
      setIsEditing(false);
      return;
    }

    // If name hasn't changed, just exit edit mode
    if (appName === originalName) {
      setIsEditing(false);
      return;
    }

    setIsSaving(true);

    try {
      await AppApi.updateBasicAppInfo(params.id, {name: appName});
      setOriginalName(appName);
      dispatch(
        makeToast({
          content: 'App name updated successfully',
          appearances: 'success',
        }),
      );

      // Track analytics event
      Analytics.track('editor:app_name_updated');

      // Refresh app data
      dispatch({
        type: 'FETCH_ORGS',
      });
    } catch (error) {
      console.error('Failed to update app name:', error);
      setAppName(originalName);
      dispatch(
        makeToast({
          content: 'Failed to update app name',
          appearances: 'error',
        }),
      );
    } finally {
      setIsSaving(false);
      setIsEditing(false);
    }
  };

  return (
    <View style={styles.appNameContainer}>
      {isEditing ? (
        <View style={styles.appNameEditContainer}>
          <TextInput
            ref={inputRef}
            style={[commonStyles.baseText, styles.appNameInput]}
            value={appName}
            onChangeText={setAppName}
            onBlur={handleEditComplete}
            onSubmitEditing={handleEditComplete}
            maxLength={32}
            selectTextOnFocus
            editable={!isSaving}
          />
        </View>
      ) : (
        <Pressable onPress={handleEditStart} style={styles.appNameDisplay}>
          <Text style={[commonStyles.baseText, styles.appNameText]} numberOfLines={1} ellipsizeMode="tail">
            {appName || 'Untitled App'}
          </Text>
          <MaterialCommunityIcons name="pencil" size={14} color="#979797" style={styles.appNameEditIcon} />
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  // App name editor styles
  appNameContainer: {
    marginLeft: 16,
    height: 40,
    justifyContent: 'center',
    maxWidth: 200,
  },
  appNameDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    height: 36,
    borderRadius: 6,
  },
  appNameText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'General Sans',
    maxWidth: 180,
  },
  appNameEditIcon: {
    marginLeft: 8,
    opacity: 0.7,
  },
  appNameEditContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#3A3A3A',
    borderRadius: 6,
    backgroundColor: '#121212',
    height: 36,
    paddingHorizontal: 8,
  },
  appNameInput: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'General Sans',
    padding: 0,
    height: 36,
  },
});

export default AppNameEditor;
