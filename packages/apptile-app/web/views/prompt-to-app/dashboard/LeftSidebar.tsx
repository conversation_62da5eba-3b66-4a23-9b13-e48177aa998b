import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {useMatch, useNavigate, useParams} from 'react-router';

import {LeftPane as NotificationLeftPaneContent} from '@/root/web/views/notificationAdmin/shared/leftPane';
import MenuItem from '../../../components-v2/base/Menu';

import {useIsPreview} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import {softRestartConfig, toggleChatView} from '../../../actions/editorActions';
import {selectMandatoryCheck} from '../../../selectors/EditorModuleSelectors';
import theme from '../styles-prompt-to-app/theme';
import {navigationTreeComponent} from '../components/CreateNavTreeComponent';

const styles = StyleSheet.create({
  sidebarContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  sidebarLeft: {
    height: '100%',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    overflow: 'hidden',
    zIndex: 10,
    borderRightWidth: 1,
    borderColor: theme.SIDEBAR_BORDER,
    paddingHorizontal: 2,
  },
  sidebarHeader: {
    height: '8vh',
    minHeight: 'calc(2vh + 48px)',
    paddingVertical: '1vh',
    flexDirection: 'row',
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  logoSmall: {
    width: 36,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  sidebarLeftSecondary: {
    position: 'absolute',
    top: 0,
    left: 80,
    bottom: 0,
    flex: 1,
    height: '100%',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    overflow: 'hidden',
    zIndex: -1,
  },
  sidebarContentContainer: {
    flex: 1,
    overflow: 'scroll',
  },
  leftSidebarWrapper: {width: 270},
  toTilesWrapper: {
    position: 'absolute',
    left: 80,
    top: 0,
    zIndex: -1,
    backgroundColor: '#F8FBF8',
    paddingHorizontal: 30,
    borderRadius: 21,
    height: 200,
    justifyContent: 'center',
    shadowColor: 'rgba(99, 99, 99, 0.45)',
    shadowOffset: {width: 0, height: 0},
    shadowRadius: 40,
  },
  tooltipMenuWrapper: {
    position: 'absolute',
    left: 0,
    top: -17,
    backgroundColor: '#000000',
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    zIndex: 9,
    height: 25,
  },
  leftEditorSidebarText: {
    fontSize: 12,
    color: theme.DEFAULT_COLOR,
  },
  loaderImage: {
    width: 60,
    height: 60,
  },
});

type LeftSidebarProps = {
  mainBar?: string;
  secondaryBar?: string;
  customMainBar?: any;
};

const LeftSidebar: React.FC<LeftSidebarProps> = props => {
  const {mainBar: currentMainBar, secondaryBar: currentSecondaryBar, customMainBar: CustomMainBar} = props;
  const match = useMatch('/dashboard/:orgId/app/:appId/f/:forkId/b/:branchName');

  const [mainBar, setMainBar] = React.useState(currentMainBar ?? '');
  const [secondaryBar, setSecondaryBar] = React.useState(true);

  const isPreview = false;
  const [secondarySidebarZIndex, setSecondarySidebarZIndex] = useState(false);
  const sidebarLeftSecondaryTranslateX = useSharedValue(-450);

  useEffect(() => {
    !secondaryBar && setSecondarySidebarZIndex(false);
    sidebarLeftSecondaryTranslateX.value = withTiming(
      !secondaryBar || isPreview ? -450 : 0,
      {
        duration: 680,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      },
      () => {
        secondaryBar && setSecondarySidebarZIndex(true);
      },
    );
  }, [isPreview, secondaryBar, sidebarLeftSecondaryTranslateX]);
  const sidebarLeftSecondaryAnimation = useAnimatedStyle(() => ({
    transform: [{translateX: sidebarLeftSecondaryTranslateX.value}],
    zIndex: secondarySidebarZIndex ? 9 : -1,
  }));

  const sidebarWidth = useSharedValue(270);
  useEffect(() => {
    sidebarWidth.value = withTiming(isPreview ? 0 : secondaryBar ? 80 : 270, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, secondaryBar, sidebarWidth]);
  const sidebarLeftAnimation = useAnimatedStyle(() => ({
    width: sidebarWidth.value,
    // paddingHorizontal: interpolate(sidebarWidth.value, [0, 80], {extrapolateRight: Extrapolation.CLAMP}),
  }));

  let leftpane = (
    <LeftPaneContent
      mainBar={mainBar}
      setMainBar={setMainBar}
      secondaryBar={secondaryBar}
      setSecondaryBar={setSecondaryBar}
    />
  );

  return (
    <View style={[secondaryBar ? {zIndex: 2} : {}]}>
      <View style={[styles.sidebarContainer, match ? {width: 340} : {}]}>
        <Animated.View style={[styles.sidebarLeft, sidebarLeftAnimation]}>{leftpane}</Animated.View>
      </View>

      <Animated.View
        style={[
          // styles.leftSidebarWrapper,
          styles.sidebarLeftSecondary,
          sidebarLeftSecondaryAnimation,
          {overflow: 'visible'},
        ]}>
        {secondaryBar === 'pages' && (
          <View
            style={[
              styles.sidebarContentContainer,
              {
                width: '300px',
              },
            ]}>
            {React.createElement(navigationTreeComponent())}
          </View>
        )}
      </Animated.View>
    </View>
  );
};

type LeftPaneContentProps = {
  mainBar: string;
  setMainBar: (val: string) => void;
  secondaryBar: string;
  setSecondaryBar: (val: string) => void;
};

const LeftPaneContent: React.FC<LeftPaneContentProps> = props => {
  const {mainBar} = props;

  switch (mainBar) {
    case 'DASHBOARD':
      return <DashBoardLeftPaneContent {...props} />;
    case 'MANUAL_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent />;
    case 'AUTOMATED_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent isAutomatedCampaign />;
    case 'APP_EDITOR':
      return <AppEditorLeftPaneContent {...props} />;
    default:
      return <></>;
  }
};

type AppEditorLeftPaneContentProps = {} & LeftPaneContentProps;

export const AppEditorLeftPaneContent: React.FC<AppEditorLeftPaneContentProps> = props => {
  const {secondaryBar, setSecondaryBar} = props;
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const params = useParams();

  // Track the current active item in the sidebar based on current path
  const [activeItem, setActiveItem] = React.useState<string>('');

  // Set the active item based on the current route and sidebar state
  useEffect(() => {
    // Check if we're on the integrations page
    if (window.location.pathname.includes('/integrations')) {
      setActiveItem('integrations');
    } else if (window.location.pathname.includes('/editor')) {
      setActiveItem('editor');
    } else if (window.location.pathname.includes('/notifications')) {
      setActiveItem('notifications');
    } else if (window.location.pathname.includes('/analytics')) {
      setActiveItem('analytics');
    }

    // For the Pages tab, we need to track it based on the secondaryBar state
    // since it doesn't change the URL but opens a sidebar
    if (secondaryBar === 'pages') {
      setActiveItem('pages');
    }
  }, [params, secondaryBar, window.location.pathname]);

  const mandatoryFields = useSelector(selectMandatoryCheck());

  const toggleChat = () => {
    dispatch(toggleChatView());
  };

  const onSoftRefreshEditor = () => {
    dispatch(softRestartConfig());
  };

  return (
    <View style={[styles.sidebarContentContainer, {overflow: 'hidden'}]}>
      <MenuItem
        id={'dashboard'}
        text={''}
        icon={'back-arrow'}
        iconType="ApptileWebIcons"
        isActive={false}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('dashboard');
          navigate(`/dashboard/${params?.orgId}`);
        }}
      />

      <MenuItem
        id={'editor'}
        text={'Editor'}
        icon={'pencil'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'editor'}
        redDot={mandatoryFields?.check ?? false}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          // navigate(`/dashboard/${params?.orgId}/app/${params?.id}`);
          if (activeItem !== 'editor' && secondaryBar !== 'pages') {
            window.location.href = `/dashboard/${params?.orgId}/app/${params?.id}`;
          } else {
            setActiveItem('editor');
            setSecondaryBar('editor');
          }
        }}
      />

      {activeItem !== 'integrations' && (
        <MenuItem
          id={'pages'}
          text={'Pages'}
          icon={'your-design'}
          iconType="ApptileWebIcons"
          isActive={activeItem === 'pages'}
          redDot={mandatoryFields?.check ?? false}
          vertical={!!secondaryBar}
          textStyles={styles.leftEditorSidebarText}
          onPress={() => {
            if (secondaryBar === 'pages') {
              setSecondaryBar('dashboard');
              setActiveItem('');
            } else {
              setSecondaryBar('pages');
              setActiveItem('pages');
            }
          }}
        />
      )}

      <MenuItem
        id={'integrations'}
        text={'Integrations'}
        icon={'download-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'integrations'}
        redDot={mandatoryFields?.check ?? false}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('integrations');
          // Use standard React Router navigation for other menu items
          navigate(
            `/dashboard/${params?.orgId}/app/${params?.id}/f/${params?.forkId}/b/${params?.branchName}/dashboard/integrations`,
          );
        }}
      />

      <MenuItem
        id={'notifications'}
        text={'Notifications'}
        icon={'bell-outline'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'notifications'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('notifications');
          navigate(
            `/dashboard/${params?.orgId}/app/${params?.id}/f/${params?.forkId}/b/${params?.branchName}/dashboard/notifications`,
          );
        }}
      />

      <MenuItem
        id={'analytics'}
        text={'Analytics'}
        icon={'chart-line'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'analytics'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('analytics');
          navigate(
            `/dashboard/${params?.orgId}/app/${params?.id}/f/${params?.forkId}/b/${params?.branchName}/dashboard/analytics`,
          );
        }}
      />
      <MenuItem
        id={'builds'}
        text={'Builds'}
        icon={'book-outline'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'builds'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('builds');
          navigate(
            `/dashboard/${params?.orgId}/app/${params?.id}/f/${params?.forkId}/b/${params?.branchName}/dashboard/publish`,
          );
        }}
      />
    </View>
  );
};

export default LeftSidebar;
