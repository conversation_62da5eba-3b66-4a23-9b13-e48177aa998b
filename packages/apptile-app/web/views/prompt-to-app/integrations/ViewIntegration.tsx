import { useParams } from '@/root/web/routing.web';
import _ from 'lodash';
import React from 'react';
import { StyleSheet, View, Pressable } from 'react-native';
import TextElement from '../../../components-v2/base/TextElement';
import availableIntegrations from './availableIntegrations.json';
import SupabaseConnectButton from '@/root/web/integrations/supabase';
import theme from '@/root/web/views/prompt-to-app/styles-prompt-to-app/theme';
import InAppPurchaseDetail from '@/root/web/integrations/InAppPurchase/components/InAppPurchaseDetail';

const ViewIntegration = () => {
  const params = useParams();
  const integrationCode = params?.integrationCode;

  let integration = !_.isEmpty(integrationCode)
    ? _.find(availableIntegrations, int => int?.integrationCode == integrationCode)
    : _.first(availableIntegrations);

  return (
    <View style={styles.container}>
      <View style={styles.integrationModalCont}>
        <View style={[{ marginVertical: 40, flex: 1 }, !integration?.images && { width: '100%' }]}>

          {
            integrationCode === 'supabase' &&
            <>
              <View style={styles.flexDirectionRow}>
                <View style={[styles.logoContainer, styles.flexDirectionRow]}>
                  {integration?.icon && (
                    <View style={styles.logoWrapper}>
                      <img src={integration?.icon} style={webStyles.logoImage} />{' '}
                    </View>
                  )}
                  <TextElement color="DEFAULT" fontWeight="500" fontSize="lg" style={styles.titleStyles}>
                    {integration?.title}
                  </TextElement>
                </View>
              </View>

              <View style={styles.descriptionWrapper}>
                <View style={[styles.featureBox, styles.marginTop37]}>
                  <TextElement fontWeight="400" fontSize="sm" lineHeight="lg" color="DEFAULT">
                    {integration?.excerpt ?? ''}
                  </TextElement>
                </View>
                <SupabaseConnectButton />
              </View>
            </>
          }
          {integrationCode === 'apptile-in-app-purchase' && <InAppPurchaseDetail integration={integration} />}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 35,
    paddingVertical: 20,
    alignItems: 'center',
    position: 'relative',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
  },
  integrationModalCont: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: theme.PANEL_BACKGROUND,
    paddingHorizontal: 32,
    borderRadius: 12,
    width: '100%',
    flex: 1,
    borderWidth: 1,
    borderColor: theme.SIDEBAR_BORDER,
  },
  buttonStyle: {
    padding: '10px 24px',
    borderRadius: 8,
    background: theme.CTA_COLOR,
    color: '#fff',
    border: 'none',
    fontWeight: theme.FONT_WEIGHT_BOLD,
    fontFamily: theme.FONT_FAMILY,
    cursor: 'pointer',
    marginTop: 16,
  },
  titleBox: { flex: 1, alignItems: 'flex-start' },
  ctaBox: { flex: 1, alignItems: 'flex-end' },
  integrationFeatures: { flexDirection: 'row', alignItems: 'center' },
  featureBox: { marginBottom: 20, justifyContent: 'flex-start' },
  flexOne: { flex: 1 },
  cardContentIcon: { position: 'relative', width: '100px', height: '50px', marginVertical: 8, borderRadius: 8 },
  integrationIcon: { marginRight: 8 },
  imageSlider: { width: 450 },
  premiumWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(166, 105, 225, 0.2)',
    borderRadius: 3,
    padding: 5,
    marginHorizontal: 10,
    borderWidth: 1,
    borderColor: theme.PURPLE_ACCENT,
  },
  purchaseWrapper: {
    marginVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 30,
    width: '100%',
  },
  purchasePlanWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  purchaseAddOnWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  buttonSubtext: { position: 'absolute', bottom: -25, fontWeight: '500', fontSize: 14, color: theme.TEXT_COLOR },
  purchasedButtonSubtext: { color: theme.SUCCESS_BACKGROUND },
  logoWrapper: {
    width: 50,
    overflow: 'hidden',
    marginRight: 12,
  },
  logoContainer: {},
  flexDirectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    alignContent: 'center',
    justifyContent: 'space-between'
  },
  titleStyles: { maxWidth: 220 },
  marginTop37: { marginTop: 37 },
  descriptionWrapper: { overflow: 'hidden', maxHeight: '90%' },
  loaderImage: {
    width: 150,
    height: 150,
  },
  loaderContainer: {
    width: '100%',
    alignItems: 'center',
    height: '100%',
    justifyContent: 'center',
  },
  iconStyles: { width: 64, height: 64, resizeMode: 'contain' },
});

const webStyles = {
  logoImage: { width: 50, height: 50, objectFit: 'contain' },
  bannerImage: { height: '100%', objectFit: 'fit', aspectRatio: '530/621' },
};

export default ViewIntegration;
