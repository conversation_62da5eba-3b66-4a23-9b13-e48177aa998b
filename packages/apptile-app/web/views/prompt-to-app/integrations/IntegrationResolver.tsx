import _ from 'lodash';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator, StyleSheet, View} from 'react-native';
import {useMatch} from 'react-router';
import {Navigate, useLocation} from 'react-router-dom';
import availableIntegrations from './availableIntegrations.json';
import theme from '@/root/web/views/prompt-to-app/styles-prompt-to-app/theme';

export const IntegrationResolver = ({...rest}) => {
  const location = useLocation();
  const match = useMatch(
    '/dashboard/:orgId/app/:appId/f/:forkId/b/:branchName/dashboard/integrations/connect/:integrationCode',
  );

  const [firstIntegCode, setFirstIntegCode] = useState('');

  useEffect(() => {
    if (_.isEmpty(match?.integrationCode)) {
      const firstId = _.first(availableIntegrations)?.integrationCode;
      setFirstIntegCode(firstId);
    }
  }, [match, firstIntegCode]);

  if (!_.isEmpty(firstIntegCode)) {
    return (
      <Navigate
        replace
        {...rest}
        to={{
          pathname: `./connect/${firstIntegCode}`,
          search: `${location.search}`,
        }}
      />
    );
  } else {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size={'large'} color={theme.ACTIVE_BLUE} />
      </View>
    );
  }
};

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    height: '100%',
    width: '100%',
  },
});
