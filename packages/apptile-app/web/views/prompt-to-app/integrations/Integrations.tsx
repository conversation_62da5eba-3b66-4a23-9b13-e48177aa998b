import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import theme from '../styles-prompt-to-app/theme';
import ListIntegrations from './ListIntegrations';
import LeftSidebar from '../dashboard/LeftSidebar';

interface IntegrationsProps {}

const Integrations: React.FC<IntegrationsProps> = () => {
  return (
    <View style={styles.mainContainer}>
      <LeftSidebar mainBar="APP_EDITOR" />
      <View style={styles.container}>
        <ListIntegrations />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    minHeight: '100vh',
  },
  platformBodyWrapper: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    paddingBottom: 40,
    flex: 1,
  },
  container: {
    flexDirection: 'column',
    width: '100%',
    alignItems: 'center',
    flex: 1,
  },
  campaignBanner: {
    borderRadius: 10,
    marginTop: 30,
  },
  bannerImage: {
    flex: 1,
    aspectRatio: 1044 / 228,
    overflow: 'hidden',
    borderRadius: 10,
    marginHorizontal: '0.66%',
  },
});

export default Integrations;
