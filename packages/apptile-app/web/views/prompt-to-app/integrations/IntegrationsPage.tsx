import React, {useState, useEffect, useRef} from 'react';
import {StyleSheet, View, Text, TouchableOpacity, ScrollView, Modal} from 'react-native';
import {useNavigate} from '@/root/web/routing.web';
import {Icon} from 'apptile-core';
import {useSelector, useDispatch} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {integrations, Integration} from './integrationsMockData';
import {SupabaseConnectButton} from '@/root/web/integrations/supabase';
import {fetchOrgs} from '@/root/web/actions/editorActions';
import {selectWorkspaces} from '@/root/web/selectors/WorkspaceSelector';

interface IntegrationsPageProps {
  onBack?: () => void;
}

const IntegrationsPage: React.FC<IntegrationsPageProps> = ({onBack}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {userLoggedIn} = useSelector((state: EditorRootState) => state.user);
  const workspaces = useSelector(selectWorkspaces);
  const orgsById = useSelector((state: EditorRootState) => state.orgs.orgsById);
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [selectedAppId, setSelectedAppId] = useState<string | null>(null);
  const [selectedAppName, setSelectedAppName] = useState<string | null>(null);

  // Fetch organizations and their apps when component mounts
  useEffect(() => {
    dispatch(fetchOrgs());
    console.log('Fetching organizations...');
  }, [dispatch]);

  // Debug state data
  useEffect(() => {
    console.log('Workspaces:', workspaces);
    console.log('OrgsById:', orgsById);
    console.log('AppsById:', appsById);
  }, [workspaces, orgsById, appsById]);

  // Navigate back to dashboard
  const handleBackToDashboard = () => {
    if (onBack) {
      onBack();
    } else {
      navigate('/');
    }
  };

  // Get all apps from all organizations
  const getAllApps = () => {
    const allApps: Array<{id: string; name: string; orgName: string; orgId: string; isConnected?: boolean}> = [];
    // For demo purposes, we'll assume no apps are connected yet
    // In a real implementation, you would check against a list of connected apps
    const connectedAppIds: string[] = [];

    // Direct approach using appsById similar to DashboardMain
    if (appsById && Object.keys(appsById).length > 0) {
      // Loop through all apps in the appsById object
      Object.entries(appsById).forEach(([appId, app]) => {
        if (app) {
          // Find which org this app belongs to
          let orgName = 'Unknown Org';
          let orgId = '';

          // Check each org to find which one contains this app
          if (orgsById) {
            Object.entries(orgsById).forEach(([id, org]) => {
              if (org.apps && org.apps.includes(appId)) {
                orgName = org.name || 'Unknown Org';
                orgId = id;
              }
            });
          }

          allApps.push({
            id: appId,
            name: app.name || 'Unnamed App',
            orgName: orgName,
            orgId: orgId,
            isConnected: connectedAppIds.includes(appId),
          });
        }
      });
    }

    console.log('All Apps:', allApps);
    return allApps;
  };

  // State for app dropdown
  const [showAppDropdown, setShowAppDropdown] = useState(false);

  // Handle app selection
  const handleSelectApp = (app: {id: string; name: string; orgName?: string; orgId?: string}) => {
    setSelectedAppId(app.id);
    setSelectedAppName(app.name);
    console.log('Selected app:', app);
  };

  // Handle integration selection
  const handleSelectIntegration = (integration: Integration) => {
    setSelectedIntegration(integration);
  };

  // Render error state if user is not logged in
  if (!userLoggedIn) {
    return (
      <View style={styles.container}>
        <Text>Please log in to view integrations</Text>
        <TouchableOpacity onPress={handleBackToDashboard}>
          <View style={styles.backButton}>
            <Icon iconType="MaterialIcons" name="arrow-back" size={20} color="#fff" />
            <Text style={styles.backButtonText}>Back to Dashboard</Text>
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  // Render integration list item
  const renderIntegrationItem = (integration: Integration) => {
    const isSelected = selectedIntegration?.id === integration.id;

    return (
      <TouchableOpacity key={integration.id} onPress={() => handleSelectIntegration(integration)}>
        <View style={[styles.integrationItem, isSelected && styles.selectedIntegrationItem]}>
          <View style={styles.integrationItemIcon}>
            <Icon
              iconType="MaterialCommunityIcons"
              name={integration.icon.name}
              size={20}
              color={integration.icon.color}
            />
          </View>
          <Text style={[styles.integrationItemText, isSelected && styles.selectedIntegrationText]}>
            {integration.name}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  // Render integration details
  const renderIntegrationDetails = () => {
    if (!selectedIntegration) {
      return (
        <View style={styles.noSelectionContainer}>
          <Text style={styles.noSelectionText}>Select an integration from the list</Text>
        </View>
      );
    }

    return (
      <ScrollView>
        <View style={styles.detailsHeader}>
          <View style={styles.detailsIconContainer}>
            <Icon
              iconType="MaterialCommunityIcons"
              name={selectedIntegration.icon.name}
              size={36}
              color={selectedIntegration.icon.color}
            />
          </View>
          <View style={styles.detailsTitleContainer}>
            <Text style={styles.detailsTitle}>{selectedIntegration.name}</Text>
            <Text style={styles.detailsTagline}>{selectedIntegration.description}</Text>
          </View>
          {selectedIntegration.id === 'supabase' ? (
            <View style={styles.supabaseButtonsContainer}>
              <View style={styles.appSelectionRow}>
                <TouchableOpacity
                  style={styles.chooseAppButton}
                  onPress={() => {
                    console.log('Choose App button clicked');
                    console.log('Workspaces:', workspaces);
                    console.log('Apps:', getAllApps());
                    setShowAppDropdown(true);
                  }}
                  disabled={!workspaces || workspaces.length === 0}>
                  <Text style={styles.chooseAppButtonText}>Choose App</Text>
                </TouchableOpacity>

                {selectedAppName && (
                  <View style={styles.selectedAppContainer}>
                    <Text style={styles.selectedAppText}>Selected App: {selectedAppName}</Text>
                  </View>
                )}
              </View>

              <SupabaseConnectButton
                buttonText="Connect to Supabase"
                appId={selectedAppId || undefined}
                onSuccess={() => {
                  // Update the integration status
                  const updatedIntegration = {
                    ...selectedIntegration,
                    status: 'connected' as 'connected' | 'disconnected',
                  };
                  setSelectedIntegration(updatedIntegration);
                }}
                onError={error => {
                  console.error('Supabase connection error:', error);
                }}
                buttonStyle={{
                  backgroundColor: selectedIntegration.status === 'connected' ? '#FF4C4C' : '#4285F4',
                  color: '#FFFFFF',
                  borderRadius: 4,
                  padding: 8,
                  fontSize: 14,
                  fontWeight: '500',
                  minWidth: 150, // Ensure consistent width for both states
                  textAlign: 'center',
                  opacity: selectedAppId ? 1 : 0.5,
                }}
                disabled={!selectedAppId}
              />
              {!selectedAppId ? <Text style={styles.helperText}>Please select an app first</Text> : null}

              {/* App Selection Dropdown */}
              {showAppDropdown && (
                <Modal transparent={true} visible={showAppDropdown} onRequestClose={() => setShowAppDropdown(false)}>
                  <TouchableOpacity
                    style={styles.modalOverlay}
                    activeOpacity={1}
                    onPress={() => setShowAppDropdown(false)}
                  />
                  <View style={styles.appDropdown}>
                    <View style={styles.appDropdownHeader}>
                      <Text style={styles.appDropdownTitle}>Select an App</Text>
                      <TouchableOpacity style={styles.closeButton} onPress={() => setShowAppDropdown(false)}>
                        <Text style={styles.closeButtonText}>×</Text>
                      </TouchableOpacity>
                    </View>
                    <ScrollView style={styles.appDropdownScroll}>
                      {getAllApps().length > 0 ? (
                        getAllApps().map((app, index) => (
                          <TouchableOpacity
                            key={app.id}
                            style={styles.appDropdownItem}
                            onPress={() => {
                              handleSelectApp(app);
                              setShowAppDropdown(false);
                            }}>
                            <View style={styles.appDropdownItemHeader}>
                              <Text style={styles.appDropdownItemText}>{app.name}</Text>
                              {app.isConnected && (
                                <View style={styles.connectedIndicator}>
                                  <Text style={styles.connectedIndicatorText}>Connected</Text>
                                </View>
                              )}
                            </View>
                            <Text style={styles.appDropdownItemOrg}>{app.orgName}</Text>
                          </TouchableOpacity>
                        ))
                      ) : (
                        <View style={styles.noAppsContainer}>
                          <Text style={styles.noAppsText}>No apps available. Please create an app first.</Text>
                        </View>
                      )}
                    </ScrollView>
                  </View>
                </Modal>
              )}
            </View>
          ) : (
            <TouchableOpacity>
              <View
                style={[styles.connectButton, selectedIntegration.status === 'connected' && styles.disconnectButton]}>
                <Text style={styles.connectButtonText}>
                  {selectedIntegration.status === 'connected' ? 'Disconnect' : 'Connect'}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.detailsBody}>
          <Text style={styles.detailsDescription}>{selectedIntegration.longDescription}</Text>

          {selectedIntegration.configOptions && selectedIntegration.configOptions.length > 0 && (
            <View style={styles.configSection}>
              <Text style={styles.configSectionTitle}>Configuration</Text>
              {selectedIntegration.configOptions.map((option, index) => (
                <View key={index} style={styles.configOption}>
                  <Text style={styles.configOptionLabel}>{option.label}</Text>
                  <View style={styles.configOptionInput}>
                    {option.type === 'toggle' ? (
                      <View style={styles.toggleContainer}>
                        <View style={styles.toggleOption}>
                          <View style={styles.toggleIndicator} />
                        </View>
                      </View>
                    ) : (
                      <View style={styles.textInputContainer}>
                        <Text style={styles.textInputPlaceholder}>{option.placeholder || ''}</Text>
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    );
  };

  // Main render
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackToDashboard}>
          <View style={styles.backButton}>
            <Icon iconType="MaterialIcons" name="arrow-back" size={20} color="#fff" />
            <Text style={styles.backButtonText}>Back to Dashboard</Text>
          </View>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Integrations</Text>
      </View>

      <View style={styles.contentContainer}>
        {/* Left sidebar - Integration list (Master view) */}
        <View style={styles.sidebar}>
          <Text style={styles.sidebarTitle}>Available Integrations</Text>
          <ScrollView>{integrations.map(renderIntegrationItem)}</ScrollView>
        </View>

        {/* Right content - Integration details (Detail view) */}
        <View style={styles.detailsContainer}>{renderIntegrationDetails()}</View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0F0F0F',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  backIcon: {
    marginRight: 8,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
  },

  // Master-Detail Layout
  contentContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#0F0F0F',
  },

  // Sidebar (Master View)
  sidebar: {
    width: 250,
    borderRightWidth: 1,
    borderRightColor: '#333',
    paddingRight: 16,
  },
  sidebarTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  integrationsList: {
    flex: 1,
    marginBottom: 16,
  },
  integrationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    backgroundColor: '#1A1A1A',
  },
  selectedIntegrationItem: {
    backgroundColor: '#2A2A2A',
    borderLeftWidth: 3,
    borderLeftColor: '#4285F4',
  },
  integrationItemIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#222',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  integrationItemText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  selectedIntegrationText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },

  // Details Container (Detail View)
  detailsContainer: {
    flex: 1,
    paddingLeft: 24,
  },
  detailsContent: {
    flex: 1,
  },
  detailsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  detailsIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 12,
    backgroundColor: '#222',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  detailsTitleContainer: {
    flex: 1,
  },
  detailsTitle: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 4,
  },
  detailsTagline: {
    color: '#AAAAAA',
    fontSize: 14,
  },
  detailsBody: {
    flex: 1,
  },
  detailsDescription: {
    color: '#DDDDDD',
    fontSize: 14,
    lineHeight: 22,
    marginBottom: 24,
  },

  // Configuration Section
  configSection: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#333',
  },
  configSectionTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  configOption: {
    marginBottom: 16,
  },
  configOptionLabel: {
    color: '#FFFFFF',
    fontSize: 14,
    marginBottom: 8,
  },
  configOptionInput: {
    backgroundColor: '#222',
    borderRadius: 4,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  textInputContainer: {
    height: '100%',
    justifyContent: 'center',
  },
  textInputPlaceholder: {
    color: '#666',
    fontSize: 14,
  },
  toggleContainer: {
    flexDirection: 'row',
  },
  toggleOption: {
    width: 40,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#333',
    padding: 2,
  },
  toggleIndicator: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#666',
  },

  // Connect Button
  connectButtonWrapper: {
    marginLeft: 16,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 150,
  },

  // App Selection Styles
  supabaseButtonsContainer: {
    display: 'flex',
    flexDirection: 'column',
    marginLeft: 16,
  },
  appSelectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  chooseAppButton: {
    backgroundColor: '#6772e5',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginRight: 12,
  },
  chooseAppButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  selectedAppContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedAppText: {
    color: '#DDDDDD',
    fontSize: 14,
    fontWeight: '500',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 9998,
  },
  appDropdown: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 350,
    maxHeight: 450,
    backgroundColor: '#222',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#333',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.5,
    shadowRadius: 8,
    zIndex: 9999,
    elevation: 10,
    transform: [{translateX: -175}, {translateY: -225}],
  },
  appDropdownHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
    position: 'relative',
  },
  appDropdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  appDropdownScroll: {
    maxHeight: 380,
  },
  appDropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  appDropdownItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
  },
  appDropdownItemText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  appDropdownItemOrg: {
    fontSize: 12,
    color: '#AAAAAA',
  },
  closeButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    color: '#AAAAAA',
    fontSize: 20,
    fontWeight: '600',
  },
  connectedIndicator: {
    backgroundColor: '#28a745',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  connectedIndicatorText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  noAppsContainer: {
    padding: 16,
    alignItems: 'center',
  },
  noAppsText: {
    color: '#AAAAAA',
    fontSize: 14,
    textAlign: 'center',
  },
  helperText: {
    color: '#FF4C4C',
    fontSize: 12,
    marginTop: 4,
  },

  connectButton: {
    backgroundColor: '#4285F4',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginLeft: 16,
  },
  disconnectButton: {
    backgroundColor: '#FF4C4C',
  },
  connectButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },

  // No Selection State
  noSelectionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noSelectionText: {
    color: '#666',
    fontSize: 16,
  },

  // Error State
  errorText: {
    color: '#FFFFFF',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
});

export default IntegrationsPage;
