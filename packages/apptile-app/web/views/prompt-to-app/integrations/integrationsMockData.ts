export interface Integration {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  icon: {
    type: string;
    name: string;
    color: string;
  };
  status: 'connected' | 'disconnected';
  configOptions?: {
    name: string;
    type: 'text' | 'password' | 'toggle' | 'select';
    label: string;
    placeholder?: string;
    options?: {label: string; value: string}[];
  }[];
}

export const integrations: Integration[] = [
  {
    id: 'supabase',
    name: 'Supabase',
    description: 'Connect your app to Supabase for database and authentication',
    longDescription:
      'Supabase is an open source Firebase alternative. It provides all the backend services you need to build a product: a Postgres database, Authentication, instant APIs, Edge Functions, Realtime subscriptions, and Storage.',
    icon: {
      type: 'MaterialCommunityIcons',
      name: 'database',
      color: '#4285F4',
    },
    status: 'disconnected',
    // configOptions: [
    //   {
    //     name: 'apiKey',
    //     type: 'password',
    //     label: 'API Key',
    //     placeholder: 'Enter your Supabase API key',
    //   },
    //   {
    //     name: 'projectUrl',
    //     type: 'text',
    //     label: 'Project URL',
    //     placeholder: 'https://your-project.supabase.co',
    //   },
    // ],
  },
  // {
  //   id: 'twilio',
  //   name: 'Twilio',
  //   description: 'Add SMS and messaging capabilities to your app',
  //   longDescription:
  //     'Twilio is a cloud communications platform as a service company. Twilio allows software developers to programmatically make and receive phone calls, send and receive text messages, and perform other communication functions using its web service APIs.',
  //   icon: {
  //     type: 'MaterialCommunityIcons',
  //     name: 'message-text',
  //     color: '#7289DA',
  //   },
  //   status: 'disconnected',
  //   configOptions: [
  //     {
  //       name: 'accountSid',
  //       type: 'text',
  //       label: 'Account SID',
  //       placeholder: 'Enter your Twilio Account SID',
  //     },
  //     {
  //       name: 'authToken',
  //       type: 'password',
  //       label: 'Auth Token',
  //       placeholder: 'Enter your Twilio Auth Token',
  //     },
  //     {
  //       name: 'phoneNumber',
  //       type: 'text',
  //       label: 'Phone Number',
  //       placeholder: '+**********',
  //     },
  //   ],
  // },
  {
    id: 'in-app-purchase',
    name: 'In-App Purchase',
    description: 'Enable in-app purchases for your mobile application',
    longDescription:
      'In-app purchases allow you to sell a variety of items directly within your app, including premium content, digital goods, and subscriptions. This integration helps you implement in-app purchases for both iOS and Android platforms.',
    icon: {
      type: 'MaterialCommunityIcons',
      name: 'shopping',
      color: '#96BF48',
    },
    status: 'disconnected',
    configOptions: [
      {
        name: 'enableIos',
        type: 'toggle',
        label: 'Enable for iOS',
      },
      {
        name: 'enableAndroid',
        type: 'toggle',
        label: 'Enable for Android',
      },
      {
        name: 'productIds',
        type: 'text',
        label: 'Product IDs (comma separated)',
        placeholder: 'product1,product2,product3',
      },
    ],
  },
  // {
  //   id: 'stripe',
  //   name: 'Stripe',
  //   description: 'Process payments and manage subscriptions',
  //   longDescription:
  //     "Stripe is a suite of payment APIs that powers commerce for online businesses of all sizes. Stripe's products include payment processing software and APIs for e-commerce websites and mobile applications.",
  //   icon: {
  //     type: 'MaterialCommunityIcons',
  //     name: 'credit-card',
  //     color: '#6772E5',
  //   },
  //   status: 'disconnected',
  //   configOptions: [
  //     {
  //       name: 'publishableKey',
  //       type: 'text',
  //       label: 'Publishable Key',
  //       placeholder: 'pk_test_...',
  //     },
  //     {
  //       name: 'secretKey',
  //       type: 'password',
  //       label: 'Secret Key',
  //       placeholder: 'sk_test_...',
  //     },
  //     {
  //       name: 'webhookSecret',
  //       type: 'password',
  //       label: 'Webhook Secret',
  //       placeholder: 'whsec_...',
  //     },
  //   ],
  // },
];
