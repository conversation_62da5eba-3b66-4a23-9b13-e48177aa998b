import TextElement from '@/root/web/components-v2/base/TextElement';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, View} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import theme from '@/root/web/views/prompt-to-app/styles-prompt-to-app/theme';

interface Integration {
  id?: string;
  title: string;
  excerpt?: string;
  icon?: string;
  integrationCode?: string;
}

interface IntegrationCardProps {
  integration: Integration;
  isActive?: boolean;
}

export const IntegrationCard: React.FC<IntegrationCardProps> = ({integration, isActive = false}) => {
  const [hover, setHover] = useState(false);
  const [active, setActive] = useState(isActive);
  
  useEffect(() => {
    setActive(isActive);
    console.log('IntegrationCard isActive:', isActive, 'for', integration.title);
  }, [isActive, integration.title]);
  const borderWidthSharedValue = useSharedValue(0);
  const shadowSharedValue = useSharedValue(0);

  useEffect(() => {
    if (hover || active) {
      borderWidthSharedValue.value = 1;
      shadowSharedValue.value = 10;
    } else {
      borderWidthSharedValue.value = 0;
      shadowSharedValue.value = 0;
    }
  }, [hover, active]);

  const withTimingFunction = (animationWidth: any) =>
    withTiming(animationWidth.value, {
      duration: 50,
      easing: Easing.cubic,
    });

  const animatedBorderWidth = useAnimatedStyle(() => {
    return {
      borderWidth: withTimingFunction(borderWidthSharedValue),
      shadowRadius: withTimingFunction(shadowSharedValue),
    };
  }, [borderWidthSharedValue]);

  return (
    <Animated.View
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      style={[styles.integrationCard, animatedBorderWidth, active ? styles.activeCard : null]}>
      <View style={styles.cardHeaderWrapper}>
        <View style={[styles.flexDirectionRow]}>
          {integration?.icon && <Image source={{uri: integration?.icon}} style={styles.iconStyles} />}

          <TextElement
            fontWeight="500"
            style={[{maxWidth: 167}, (hover || active) && styles.hoverColors]}
            fontSize="md"
            color="DEFAULT">
            {integration?.title.replace(/^\s+|\s+$/gm, '') ?? ''}
          </TextElement>
        </View>
      </View>

      <View style={[styles.titleContainer]}>
        <TextElement fontWeight="400" fontSize="sm" lineHeight="xl" color="DEFAULT">
          {integration?.excerpt ?? ''}
        </TextElement>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  integrationCard: {
    height: 212,
    marginBottom: 15,
    borderRadius: 13,
    backgroundColor: theme.PANEL_BACKGROUND,
    padding: 22,
    borderColor: theme.PRIMARY_BACKGROUND,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
  },
  activeCard: {
    borderWidth: 3,
    borderColor: theme.PRIMARY_BACKGROUND,
    // backgroundColor: `${theme.ACTIVE_BLUE}30`, // 30% opacity blue background
    // boxShadow: `0 0 12px ${theme.ACTIVE_BLUE}`, // Stronger glow
    transform: 'scale(1.02)', // Slightly larger
    zIndex: 10, // Ensure it's above other cards
  },
  titleContainer: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  premiumWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F4E4FA',
    borderRadius: 5,
    paddingVertical: 3,
    paddingHorizontal: 6,
    height: 30,
  },
  boxWithShadow: {
    shadowColor: '#CCCCCC',
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 5,
  },
  premiumPriceWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 3,
    paddingVertical: 3,
    paddingHorizontal: 6,
    height: 30,
  },
  connectedIconBox: {flexDirection: 'row', alignItems: 'center', marginTop: 5},
  ctaBox: {alignSelf: 'flex-end', justifyContent: 'flex-end', position: 'absolute', bottom: 20},
  cardContentIcon: {
    position: 'relative',
    width: 70,
    height: 50,
    borderRadius: 8,
  },
  ctaButton: {
    borderRadius: 20,
    width: 100,
  },
  cardHeaderWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 15,
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  flexDirectionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'nowrap',
  },
  hoverColors: {
    color: theme.PRIMARY_BACKGROUND,
  },
  hoverWrapper: {
    borderWidth: 1,
    borderColor: theme.PRIMARY_BACKGROUND,
    shadowColor: '#E5E4E2',
    shadowRadius: 10,
  },
  iconStyles: {width: 32, height: 32, marginRight: 10, resizeMode: 'contain'},
  connectionInfoWrapper: {position: 'absolute', bottom: 25},
  connectButton: {top: 5},
});
