import React from 'react';
import {Route, Routes} from 'react-router';
import Integrations from './Integrations';
import {IntegrationResolver} from './IntegrationResolver';


export const IntegrationRouter: React.FC = () => {
  return (
    <>
      <Routes>
        <Route path="/*" element={<IntegrationResolver />} />
        <Route path="/connect/*" element={<Integrations />} />
        {/* <Route path="/integrations/:integrationId" element={<ViewIntegration />} />
        <Route path="/integrations/:appIntegrationId/configure/*" element={<EditIntegration />} />
        <Route path="/integrations/configure/:platformType" element={<CreateIntegration />} />
        */}
      </Routes>
      {/* <DeleteIntegrationModal /> */}
    </>
  );
};
