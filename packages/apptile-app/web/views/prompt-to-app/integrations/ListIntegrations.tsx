import React, {useState, useEffect} from 'react';
import {FlatList, Pressable, StyleSheet, Text, View} from 'react-native';
import {Route, Routes, useLocation, useNavigate, useParams} from '../../../routing.web';
import availableIntegrations from './availableIntegrations.json';
import {IntegrationCard} from './IntegrationCard';
import ViewIntegration from './ViewIntegration';
import theme from '@/root/web/views/prompt-to-app/styles-prompt-to-app/theme';

interface ListIntegrationsProps {}

const ListIntegrations: React.FC<ListIntegrationsProps> = () => {
  const navigate = useNavigate();
  const params = useParams();
  const [activeIntegrationCode, setActiveIntegrationCode] = useState<string>('');

  // Set the active integration based on the URL params
  useEffect(() => {
    if (params?.integrationCode) {
      setActiveIntegrationCode(params.integrationCode);
      console.log('Active integration set to:', params.integrationCode);
    }
  }, [params?.integrationCode]);

  return (
    <View style={styles.appWrapperBox}>
      <View style={[styles.listContainer, {flex: 1, height: '700px'}]}>
        <FlatList
          key={1}
          scrollEnabled
          contentContainerStyle={{padding: 10}}
          showsVerticalScrollIndicator={false}
          data={availableIntegrations}
          renderItem={({item}) => (
            <Pressable
              onPress={() => {
                setActiveIntegrationCode(item.integrationCode);
                navigate(`../connect/${item.integrationCode}`);
              }}>
              <IntegrationCard integration={item} isActive={activeIntegrationCode === item.integrationCode} />
            </Pressable>
          )}
          keyExtractor={item => item}
          numColumns={1}
        />
      </View>
      <View style={[styles.detailsContainer, {flex: 2}]}>
        <Routes>
          <Route path="/:integrationCode" element={<ViewIntegration />} />
        </Routes>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  appWrapperBox: {
    flexDirection: 'row',
    width: '100%',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
  },
  listContainer: {
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    borderRightWidth: 1,
    borderRightColor: theme.SIDEBAR_BORDER,
  },
  detailsContainer: {
    backgroundColor: theme.SIDEBAR_BACKGROUND,
  },
});

export default ListIntegrations;
