import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import LeftSidebar from '../dashboard/LeftSidebar';
import theme from '../styles-prompt-to-app/theme';

interface NotificationsPageProps {}

const NotificationsPage: React.FC<NotificationsPageProps> = () => {
  return (
    <View style={styles.mainContainer}>
      <LeftSidebar mainBar="APP_EDITOR" />
      <View style={styles.container}>
        <View style={styles.comingSoonContainer}>
          <Text style={styles.comingSoonText}>Coming Soon</Text>
          <Text style={styles.descriptionText}>Notification management features will be available here shortly.</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    minHeight: '100vh',
  },
  container: {
    flex: 1,
    padding: 20,
    // backgroundColor: theme.DEFAULT_BACKGROUND,
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  comingSoonText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.DEFAULT_COLOR,
    marginBottom: 16,
  },
  descriptionText: {
    fontSize: 16,
    color: theme.DEFAULT_COLOR,
    textAlign: 'center',
  },
});

export default NotificationsPage;
