import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import {useNavigate} from '@/root/web/routing.web'; // Assuming this is your navigation hook
import {Icon} from 'apptile-core'; // Assuming Icon component is available

// Theme colors from the app's dark theme (consistent with other pages)
const theme = {
  HEADER_BACKGROUND: '#2E2E2E',
  BORDER_COLOR: '#3A3A3A',
  TEXT_COLOR: '#FFFFFF',
  ACCENT_COLOR: '#0062FF', // Active blue for interactive elements
};

const StaticPageHeader: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/'); // Navigate to the root path
  };

  return (
    <View style={styles.headerContainer}>
      <TouchableOpacity onPress={handleGoHome} style={styles.leftContainer}>
        {/* You can replace Text with an Icon or Logo component if available */}
        {/* <Icon name="arrow-left" size={24} color={theme.TEXT_COLOR} /> */}
        <Text style={styles.logoText}>Tile.Dev</Text> {/* Or your app's name */}
      </TouchableOpacity>
      <View style={styles.rightContainer}>{/* Placeholder for any right-side elements if needed in the future */}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    backgroundColor: theme.HEADER_BACKGROUND,
    borderBottomWidth: 1,
    borderBottomColor: theme.BORDER_COLOR,
    height: 56, // Standard header height
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.TEXT_COLOR,
    marginLeft: 8, // Add margin if using an icon before text
  },
  rightContainer: {
    // Styles for any right-side content
  },
});

export default StaticPageHeader;
