import React from 'react';
import {View, StyleSheet, TouchableOpacity, Text, Linking} from 'react-native';
import theme from '../styles-prompt-to-app/theme';

interface FooterLink {
  label: string;
  url: string;
  onPress?: () => void;
}

interface AppFooterProps {
  links?: FooterLink[];
  containerStyle?: any;
  textStyle?: any;
  separatorStyle?: any;
  backgroundColor?: string;
  showSeparators?: boolean;
}

/**
 * A customizable footer component for the app
 * Can display multiple links with custom styling
 */
const AppFooter: React.FC<AppFooterProps> = ({
  links = [
    {label: 'Terms of Use', url: '/terms-of-use'},
    {label: 'Privacy Policy', url: '/privacy-policy'},
  ],
  containerStyle,
  textStyle,
  separatorStyle,
  backgroundColor = 'transparent',
  showSeparators = true,
}) => {
  const handleLinkPress = (link: FooterLink) => {
    if (link.onPress) {
      link.onPress();
    } else {
      Linking.openURL(link.url);
    }
  };

  return (
    <View style={[styles.container, {backgroundColor}, containerStyle]}>
      <View style={styles.content}>
        {links.map((link, index) => (
          <React.Fragment key={link.label}>
            <TouchableOpacity
              onPress={() => handleLinkPress(link)}
              accessibilityRole="link"
              accessibilityLabel={link.label}>
              <Text style={[styles.linkText, textStyle]}>{link.label}</Text>
            </TouchableOpacity>

            {showSeparators && index < links.length - 1 && <Text style={[styles.separator, separatorStyle]}>•</Text>}
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flexWrap: 'wrap',
  },
  linkText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 12,
    color: theme.CONTROL_INPUT_COLOR,
    opacity: 0.8,
  },
  separator: {
    marginHorizontal: 8,
    fontSize: 8,
    color: theme.CONTROL_INPUT_COLOR,
    opacity: 0.5,
  },
});

export default AppFooter;
