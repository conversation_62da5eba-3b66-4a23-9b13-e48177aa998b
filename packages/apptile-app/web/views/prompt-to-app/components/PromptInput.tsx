import React, {useState, useCallback, useEffect, useMemo, useRef} from 'react';
import {StyleSheet, TextInput, View, Animated, Pressable} from 'react-native';
import {useSelector, useDispatch} from 'react-redux';
import {useNavigate, useParams} from '@/root/web/routing.web';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {selectWorkspaces} from '@/root/web/selectors/WorkspaceSelector';
import {
  createOrgApp,
  verifyAppForks,
  fetchOrgs,
  setLandingPagePrompt,
  openChatView,
} from '@/root/web/actions/editorActions';
import AppConfigApi from '@/root/web/api/AppConfigApi';
import {cloneOpenApp} from '@/root/web/components/pluginServer';
import LoadingOverlay from './LoadingOverlay';
import AppNameGeneratorApi from '@/root/web/api/AppNameGeneratorApi';
import OrgApi from '@/root/web/api/OrgApi';

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit?: () => void; // Add this new prop
}

const PromptInput: React.FC<PromptInputProps> = ({value, onChange, onSubmit}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  let params = useParams();

  const isLoggedIn = useSelector((state: EditorRootState) => state.user.userLoggedIn);
  const workspaces = useSelector(selectWorkspaces);
  // Use a hardcoded default blueprint ID instead of trying to fetch blueprints
  const [isCreatingApp, setIsCreatingApp] = useState(false);
  const [isCloning, setIsCloning] = useState(false);
  const [cloningError, setCloningError] = useState('');
  const [createdAppName, setCreatedAppName] = useState('');
  const [createdOrgId, setCreatedOrgId] = useState('');
  const appsById = useSelector((state: EditorRootState) => state.orgs.appsById);
  // We'll store the current app ID for which we're fetching forks
  const [_currentAppIdForForks, setCurrentAppIdForForks] = useState('');
  const [inputHeight, setInputHeight] = useState(60); // Initial height
  const [contentHeight, setContentHeight] = useState(0);
  const MAX_HEIGHT = 200; // Maximum height in pixels

  // Ref for triggering app creation programmatically
  const createAppRef = useRef<(() => void) | null>(null);

  // Add this effect to fetch workspaces when login status changes
  useEffect(() => {
    if (isLoggedIn) {
      // Fetch organizations/workspaces when user logs in
      dispatch(fetchOrgs());
    }
  }, [isLoggedIn, dispatch]);

  // Handle logged-out user prompt restoration after login
  useEffect(() => {
    if (isLoggedIn) {
      try {
        const storedPrompt = localStorage.getItem('loggedOutUserPrompt');
        if (storedPrompt && storedPrompt.trim()) {
          console.log('Found stored logged-out user prompt:', storedPrompt);

          // Set the prompt in the input
          onChange(storedPrompt);

          // Immediately clean up the stored prompt
          localStorage.removeItem('loggedOutUserPrompt');

          // Small delay to ensure the UI is updated, then trigger app creation
          setTimeout(() => {
            if (createAppRef.current) {
              createAppRef.current();
            }
          }, 500);
        }
      } catch (error) {
        console.error('Error handling logged-out user prompt:', error);
        // Clean up on error to prevent stale data
        try {
          localStorage.removeItem('loggedOutUserPrompt');
        } catch (cleanupError) {
          console.error('Error cleaning up logged-out user prompt:', cleanupError);
        }
      }
    }
  }, [isLoggedIn, onChange]);

  // Modify createNewApp to handle the flow better
  // Fallback function for random name generation
  const generateRandomAppName = () => {
    const adjectives = ['Amazing', 'Brilliant', 'Creative', 'Dynamic', 'Elegant', 'Fantastic', 'Gorgeous'];
    const nouns = ['App', 'Solution', 'Platform', 'Project', 'System', 'Tool', 'Service'];
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    return `${randomAdjective} ${randomNoun} ${Math.floor(Math.random() * 1000)}`;
  };

  // Generate an app name using ChatGPT API or fallback to random generation
  const generateAppName = async () => {
    // If no prompt value, fall back to random name
    if (!value.trim()) {
      return generateRandomAppName();
    }

    // Try to get a name from backend with exponential backoff
    let retryCount = 0;
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    while (retryCount < maxRetries) {
      try {
        // Use the AppNameGeneratorApi class
        const response = await AppNameGeneratorApi.generateAppName(value);

        if (!response || !response.data) {
          throw new Error('Backend API returned empty response');
        }

        const appName = response.data.appName;

        // Verify we got a sensible name (not empty, not too long)
        if (appName && appName.length > 0 && appName.split(' ').length <= 3) {
          return appName;
        } else {
          throw new Error('Generated name was not suitable');
        }
      } catch (error) {
        console.error(`Backend API attempt ${retryCount + 1} failed:`, error);
        retryCount++;

        // If not the last retry, wait before trying again
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, baseDelay * Math.pow(2, retryCount - 1)));
        }
      }
    }

    // If all attempts failed, fall back to random name generation
    console.log('All backend API attempts failed, using random app name');
    return generateRandomAppName();
  };

  let newAppId = '';

  const createNewApp = useCallback(async () => {
    if (!value.trim()) {
      return; // Don't submit empty input
    }
    console.log('Creating new app, isLoggedIn:', isLoggedIn);

    if (!isLoggedIn && onSubmit) {
      // Store the prompt in localStorage before showing the login modal
      try {
        localStorage.setItem('loggedOutUserPrompt', value);
        console.log('Stored logged-out user prompt in localStorage:', value);
      } catch (error) {
        console.error('Error storing logged-out user prompt:', error);
        // Continue with login flow even if storage fails
      }
      console.log('User not logged in, triggering onSubmit');
      onSubmit();
      return;
    }

    if (!workspaces?.length) {
      console.log('No workspaces available');
      return;
    }

    if (isCreatingApp) {
      console.log('Already creating app');
      return;
    }

    if (isLoggedIn && workspaces && workspaces.length > 0 && !isCreatingApp) {
      setIsCreatingApp(true);

      const selectedOrgId = params?.orgId;
      if (!selectedOrgId) {
        setIsCreatingApp(false);
        return;
      }
      console.log(`Selected organization: ID: ${selectedOrgId}`);
      // Use the provided default blueprint ID
      const baseBlueprintId = '377cc13c-add3-4fb7-8c88-9ce2368d1c3f'; //Blank Ai theme

      if (baseBlueprintId) {
        // Create a new app with a name generated from ChatGPT or fallback
        const appName = await generateAppName();

        // Save the app name and org ID to use later
        setCreatedAppName(appName);
        setCreatedOrgId(selectedOrgId);

        // Dispatch action to create the app
        const appCreationResp = await OrgApi.createOrgsApps(selectedOrgId, appName, baseBlueprintId);
        newAppId = appCreationResp?.data?.uuid;
        console.log('App created successfully:', appCreationResp.data);
        redirectAppTo(newAppId, selectedOrgId);
        // We'll navigate in the useEffect that watches for the new app
      } else {
        // If no blueprint is available, just navigate to the dashboard
        navigate(`/dashboard/${selectedOrgId}`);
        setIsCreatingApp(false);
      }
    } else if (!isLoggedIn) {
      // If not logged in, redirect to login page
      navigate('/login');
    }
  }, [isLoggedIn, workspaces, isCreatingApp, dispatch, navigate, onSubmit]);

  // Set up the ref for programmatic triggering
  useEffect(() => {
    createAppRef.current = createNewApp;
  }, [createNewApp]);

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // Prevent default newline
      console.log('Enter key pressed, isLoggedIn:', isLoggedIn);

      if (!value.trim()) {
        return; // Don't submit empty input
      }

      if (!isLoggedIn && onSubmit) {
        // Store the prompt in localStorage before showing the login modal
        try {
          localStorage.setItem('loggedOutUserPrompt', value);
          console.log('Stored logged-out user prompt in localStorage:', value);
        } catch (error) {
          console.error('Error storing logged-out user prompt:', error);
          // Continue with login flow even if storage fails
        }
        onSubmit(); // Call onSubmit for non-logged in users
      } else {
        createNewApp(); // Use existing functionality for logged in users
      }
    }
  };

  const redirectAppTo = (newAppId: string, createdOrgId: string) => {
    // Return the current app ID for which we're fetching forks
    if (newAppId) {
      // Save the app ID for which we're fetching forks
      setCurrentAppIdForForks(newAppId);

      // Verify app forks - this will create a fork if none exists
      dispatch(verifyAppForks(newAppId));

      // Start polling for the app manifest which contains the fork ID
      const pollInterval = 5000; // 5 seconds
      const maxAttempts = 15; // 75 seconds total
      let attempts = 0;

      const pollForManifest = async () => {
        try {
          // Fetch the app manifest which contains the fork ID
          const manifest: any = await AppConfigApi.fetchAppManifest(newAppId);
          console.log('App manifest:', manifest);

          // Check if the manifest has forks
          if (manifest && manifest.forks && manifest.forks.length > 0) {
            // We have a fork! Get the first fork ID
            // const forkId = manifest.forks[0].id;

            // Start the cloning process before navigation
            setIsCloning(true);
            setCloningError('');

            try {
              // Call cloneOpenApp and wait for it to complete
              const cloneResult = await cloneOpenApp(newAppId);

              if (cloneResult.success) {
                console.log('App cloned successfully:', cloneResult.data);
                dispatch(openChatView());
                dispatch(setLandingPagePrompt(value));
                // Store the initial prompt in sessionStorage
                // sessionStorage.setItem('initialChatPrompt', value);
                // console.log('Stored initial prompt in sessionStorage:', value);

                // Navigate to the studio page with the default branch 'main'
                // Add query parameter to indicate that the chat should be opened
                navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
              } else {
                // Cloning failed, but we'll still navigate
                console.error('Cloning failed:', cloneResult.error);
                setCloningError(cloneResult.error || 'Failed to clone app');
                dispatch(openChatView());
                dispatch(setLandingPagePrompt(value));
                // Store the initial prompt in sessionStorage
                // sessionStorage.setItem('initialChatPrompt', value);
                // console.log('Stored initial prompt in sessionStorage:', value);

                // Navigate anyway after a short delay
                setTimeout(() => {
                  navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
                }, 2000);
              }
            } catch (error) {
              // Unexpected error during cloning
              console.error('Unexpected error during cloning:', error);
              setCloningError('Unexpected error during cloning');
              dispatch(openChatView());
              dispatch(setLandingPagePrompt(value));
              // Store the initial prompt in sessionStorage
              // sessionStorage.setItem('initialChatPrompt', value);
              // console.log('Stored initial prompt in sessionStorage:', value);

              // Navigate anyway after a short delay
              setTimeout(() => {
                navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
              }, 2000);
            } finally {
              // Reset state
              setIsCloning(false);
              setIsCreatingApp(false);
              setCreatedAppName('');
              setCreatedOrgId('');
              setCurrentAppIdForForks('');
            }

            return; // Exit the polling
          }
        } catch (error) {
          console.error('Error fetching app manifest:', error);
        }

        // Set up the next poll
        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(pollForManifest, pollInterval);
        } else {
          console.error('Failed to get fork ID after maximum attempts');
          // Navigate to the app dashboard as fallback
          navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
          setIsCreatingApp(false);
          setCreatedAppName('');
          setCreatedOrgId('');
          setCurrentAppIdForForks('');
        }
      };

      // Start polling
      setTimeout(pollForManifest, pollInterval);

      // Don't reset state here - we'll reset it after navigation
    }
  };

  // We don't want to auto-create an app when a suggestion is clicked
  // This was causing multiple apps to be created simultaneously

  // Effect to navigate to the newly created app
  useEffect(() => {
    if (createdAppName && createdOrgId && isCreatingApp && Object.keys(appsById).length > 0) {
      // Find the newly created app by name
      // const newAppId = Object.keys(appsById).find(appId => {
      //   const app = appsById[appId];
      //   return app.name === createdAppName;
      // });

      if (newAppId) {
        // Save the app ID for which we're fetching forks
        setCurrentAppIdForForks(newAppId);

        // Verify app forks - this will create a fork if none exists
        dispatch(verifyAppForks(newAppId));

        // Start polling for the app manifest which contains the fork ID
        const pollInterval = 5000; // 5 seconds
        const maxAttempts = 15; // 75 seconds total
        let attempts = 0;

        const pollForManifest = async () => {
          try {
            // Fetch the app manifest which contains the fork ID
            const manifest: any = await AppConfigApi.fetchAppManifest(newAppId);
            console.log('App manifest:', manifest);

            // Check if the manifest has forks
            if (manifest && manifest.forks && manifest.forks.length > 0) {
              // We have a fork! Get the first fork ID
              const forkId = manifest.forks[0].id;

              // Start the cloning process before navigation
              setIsCloning(true);
              setCloningError('');

              try {
                // Call cloneOpenApp and wait for it to complete
                const cloneResult = await cloneOpenApp(newAppId);

                if (cloneResult.success) {
                  console.log('App cloned successfully:', cloneResult.data);
                  dispatch(openChatView());
                  dispatch(setLandingPagePrompt(value));
                  // Store the initial prompt in sessionStorage
                  // sessionStorage.setItem('initialChatPrompt', value);
                  // console.log('Stored initial prompt in sessionStorage:', value);

                  // Navigate to the studio page with the default branch 'main'
                  // Add query parameter to indicate that the chat should be opened
                  navigate(
                    `/dashboard/${createdOrgId}/app/${newAppId}/f/${forkId}/b/main/dashboard/editor?openChat=true`,
                  );
                } else {
                  // Cloning failed, but we'll still navigate
                  console.error('Cloning failed:', cloneResult.error);
                  setCloningError(cloneResult.error || 'Failed to clone app');
                  dispatch(openChatView());
                  dispatch(setLandingPagePrompt(value));
                  // Store the initial prompt in sessionStorage
                  // sessionStorage.setItem('initialChatPrompt', value);
                  // console.log('Stored initial prompt in sessionStorage:', value);

                  // Navigate anyway after a short delay
                  setTimeout(() => {
                    navigate(
                      `/dashboard/${createdOrgId}/app/${newAppId}/f/${forkId}/b/main/dashboard/editor?openChat=true`,
                    );
                  }, 2000);
                }
              } catch (error) {
                // Unexpected error during cloning
                console.error('Unexpected error during cloning:', error);
                setCloningError('Unexpected error during cloning');
                dispatch(openChatView());
                dispatch(setLandingPagePrompt(value));
                // Store the initial prompt in sessionStorage
                // sessionStorage.setItem('initialChatPrompt', value);
                // console.log('Stored initial prompt in sessionStorage:', value);

                // Navigate anyway after a short delay
                setTimeout(() => {
                  navigate(
                    `/dashboard/${createdOrgId}/app/${newAppId}/f/${forkId}/b/main/dashboard/editor?openChat=true`,
                  );
                }, 2000);
              } finally {
                // Reset state
                setIsCloning(false);
                setIsCreatingApp(false);
                setCreatedAppName('');
                setCreatedOrgId('');
                setCurrentAppIdForForks('');
              }

              return; // Exit the polling
            }
          } catch (error) {
            console.error('Error fetching app manifest:', error);
          }

          // Set up the next poll
          attempts++;
          if (attempts < maxAttempts) {
            setTimeout(pollForManifest, pollInterval);
          } else {
            console.error('Failed to get fork ID after maximum attempts');
            // Navigate to the app dashboard as fallback
            navigate(`/dashboard/${createdOrgId}/app/${newAppId}`);
            setIsCreatingApp(false);
            setCreatedAppName('');
            setCreatedOrgId('');
            setCurrentAppIdForForks('');
          }
        };

        // Start polling
        setTimeout(pollForManifest, pollInterval);

        // Don't reset state here - we'll reset it after navigation
      }
    }
  }, [appsById, createdAppName, createdOrgId, isCreatingApp, navigate, dispatch, newAppId]);

  // We don't need a separate effect to monitor forks anymore
  // The polling in createNewApp handles this

  // Create a pulsating animation for the glittering effect
  const pulseAnim = useMemo(() => {
    const anim = new Animated.Value(0.6);
    Animated.loop(
      Animated.sequence([
        Animated.timing(anim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(anim, {
          toValue: 0.6,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
    ).start();
    return anim;
  }, []);

  // Create a shimmer animation for the glittering effect
  const shimmerAnim = useMemo(() => {
    const anim = new Animated.Value(-100);
    Animated.loop(
      Animated.timing(anim, {
        toValue: 100,
        duration: 3000,
        useNativeDriver: true,
      }),
    ).start();
    return anim;
  }, []);

  // State for hover effect
  const [isHovered, setIsHovered] = useState(false);

  // Create a rotation animation for the loading state
  const spinAnim = useMemo(() => {
    const anim = new Animated.Value(0);
    Animated.loop(
      Animated.timing(anim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      }),
    ).start();
    return anim;
  }, []);

  // Interpolate the spin animation to rotate from 0 to 360 degrees
  const spin = spinAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // SVG Paper Plane icon for the send button
  const SendIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21.7 2.3C21.5 2.1 21.2 2 21 2C20.9 2 20.7 2 20.6 2.1L2.6 9.1C2.3 9.2 2.1 9.5 2 9.8C2 10.1 2.1 10.4 2.3 10.6L7 15.3L15.3 7L16.7 8.4L8.4 16.7L13.2 21.5C13.4 21.7 13.6 21.8 13.9 21.8C14 21.8 14.1 21.8 14.2 21.8C14.5 21.7 14.8 21.5 14.9 21.2L21.9 3.2C22 2.9 21.9 2.5 21.7 2.3Z"
        fill={themeColors.EDITOR_ACCENT_COLOR}
      />
    </svg>
  );

  // Loading spinner icon
  const LoadingIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z"
        fill={themeColors.EDITOR_ACCENT_COLOR + '40'}
      />
      <path d="M12 2V4C16.41 4 20 7.59 20 12H22C22 6.48 17.52 2 12 2Z" fill={themeColors.EDITOR_ACCENT_COLOR} />
    </svg>
  );

  // Add an effect to handle text changes and resize accordingly
  useEffect(() => {
    // When text is deleted, we need to recalculate height
    // This is a fallback since onContentSizeChange might not trigger on deletion
    if (value === '') {
      setInputHeight(60); // Reset to minimum height
      setContentHeight(0);
    }
  }, [value]);

  return (
    <View style={styles.container}>
      <TextInput
        style={[styles.input, {height: Math.min(Math.max(60, inputHeight), MAX_HEIGHT)}]}
        value={value}
        onChangeText={onChange}
        onKeyPress={handleKeyPress}
        onContentSizeChange={e => {
          const newHeight = e.nativeEvent.contentSize.height;
          setContentHeight(newHeight);
          setInputHeight(newHeight);
        }}
        placeholder="Describe your app idea..."
        placeholderTextColor={'#909090'}
        multiline={true}
        autoFocus
      />
      <Pressable
        style={styles.iconContainer}
        onPress={createNewApp}
        disabled={isCreatingApp || isCloning}
        onHoverIn={() => setIsHovered(true)}
        onHoverOut={() => setIsHovered(false)}>
        <Animated.View
          style={[
            styles.sendButtonWrapper,
            {opacity: isCreatingApp || isCloning ? 1 : pulseAnim},
            isCreatingApp || isCloning ? styles.sendButtonLoading : null,
            isHovered && !isCreatingApp && !isCloning ? styles.sendButtonHover : null,
          ]}>
          {/* Shimmer effect overlay */}
          {!isCreatingApp && !isCloning && (
            <Animated.View
              style={[
                styles.shimmerEffect,
                {
                  transform: [{translateX: shimmerAnim}, {skewX: '-20deg'}],
                },
              ]}
            />
          )}

          {isCreatingApp || isCloning ? (
            <Animated.View style={{transform: [{rotate: spin}]}}>
              <LoadingIcon />
            </Animated.View>
          ) : (
            <SendIcon />
          )}
        </Animated.View>
      </Pressable>
      <LoadingOverlay
        visible={isCreatingApp || isCloning}
        message={isCloning ? 'Preparing your development environment...' : 'Creating your application...'}
        error={cloningError}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: '#121212',
    borderRadius: 12,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#303030',
    transition: 'border-color 0.2s ease',
    ':focus-within': {
      borderColor: '#404040',
    },
  },
  input: {
    flex: 1,
    minHeight: 60,
    maxHeight: 200,
    paddingHorizontal: 20,
    paddingVertical: 15,
    fontSize: 16,
    color: '#FAFAFA',
    outlineStyle: 'none',
    backgroundColor: 'transparent',
    transition: 'background-color 0.2s ease',
    ':hover': {
      backgroundColor: 'rgba(255, 255, 255, 0.02)',
    },
  },
  iconContainer: {
    width: 60,
    height: 60,
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  },
  sendButtonWrapper: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#1F1F1F',
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
    borderWidth: 1.5,
    borderColor: '#909090',
    boxShadow: '0 0 10px rgba(144, 144, 144, 0.5), 0 0 15px rgba(144, 144, 144, 0.25)',
    transition: 'all 0.2s ease',
    background: 'linear-gradient(135deg, #1F1F1F, #121212)',
    position: 'relative',
    overflow: 'hidden',
    cursor: 'pointer',
    ':active': {
      transform: 'scale(0.95)',
      boxShadow: '0 0 5px rgba(144, 144, 144, 0.4), 0 0 10px rgba(144, 144, 144, 0.2)',
    },
  },
  sendButtonHover: {
    transform: 'scale(1.05)',
    boxShadow: '0 0 15px rgba(144, 144, 144, 0.6), 0 0 20px rgba(144, 144, 144, 0.3)',
    borderColor: '#FAFAFA',
    backgroundColor: '#272727',
  },
  sendButtonLoading: {
    borderColor: 'rgba(144, 144, 144, 0.5)',
    boxShadow: '0 0 8px rgba(144, 144, 144, 0.4), 0 0 12px rgba(144, 144, 144, 0.2)',
  },
  shimmerEffect: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 30,
    height: '100%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    zIndex: 1,
  },
});

export default PromptInput;
