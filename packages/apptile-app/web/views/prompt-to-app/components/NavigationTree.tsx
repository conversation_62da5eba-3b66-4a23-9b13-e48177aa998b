import React, {useState} from 'react';
import {Pressable, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {navigateToPage} from '@/root/web/actions/editorActions';
import Button from '@/root/web/components-v2/base/Button';
import {activeNavigationSelector, Icon, MaterialCommunityIcons, ScreenConfig} from 'apptile-core';
import _ from 'lodash';
import {premiumScreens} from '../../../common/screenConstants';
import {selectScreensInNavWithPath} from '../../../selectors/EditorSelectors';
import promptTheme from '../styles-prompt-to-app/theme';
import AddCustomPage from '../../../views/editor/components/customPage/AddCustomPage';
import DeletePageDialog from '../../../views/editor/components/customPage/DeletePageDialog';

const styles = StyleSheet.create({
  heading: {
    width: '100%',
    paddingLeft: 24,
    paddingVertical: 16,
  },
  menuItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingVertical: 16,
  },
  menuItemIcon: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 24,
  },
  menuItemText: {
    fontFamily: promptTheme.FONT_FAMILY,
    fontSize: 13,
    fontWeight: '400',
    color: promptTheme.TEXT_COLOR,
    paddingLeft: 8,
  },
  menuItemTextActive: {
    color: promptTheme.ACTIVE_BLUE,
  },
  menuItemActive: {
    backgroundColor: promptTheme.ACTIVE_BLUE + '1A', // 10% opacity
  },
  treeWrapper: {
    paddingLeft: 14,
    borderLeftWidth: 2,
    borderColor: promptTheme.SIDEBAR_BORDER,
  },
  pagesWrapper: {},
  pagesTitleWrapper: {
    width: '100%',
    borderTopColor: promptTheme.SIDEBAR_BORDER,
    borderTopWidth: 1,
    paddingLeft: 24,
    paddingTop: 20,
    paddingBottom: 16,
    justifyContent: 'center',
  },
  defaulPageTitle: {
    fontWeight: '600',
    fontFamily: promptTheme.FONT_FAMILY,
    fontSize: 11,
    color: promptTheme.TEXT_COLOR,
  },
  defaulPageSubTitle: {
    fontWeight: '400',
    fontSize: 12,
    color: promptTheme.TEXT_SECONDARY_COLOR,
    fontFamily: promptTheme.FONT_FAMILY,
    marginTop: 8,
  },
  rootWrapper: {
    width: '100%',
    flex: 1,
  },
  customPageTitleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuTextWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
});

export type NavigationConfig = {
  config: ScreenConfig;
  path: [string];
};

const NavigationTree = () => {
  const screens: any = useSelector(selectScreensInNavWithPath(['/']));

  const [showAddPageDialog, setShowAddPageDialog] = useState(false);

  return (
    <View style={styles.rootWrapper}>
      <View>
        <View style={styles.pagesTitleWrapper}>
          <View style={styles.customPageTitleWrapper}>
            <Text style={styles.defaulPageTitle}>PAGES</Text>

            <View style={{flex: 1}}>
              <Button
                variant="TEXT"
                color="PRIMARY"
                size="MEDIUM"
                disabled={false}
                containerStyles={{paddingVertical: 0}}
                onPress={() => {
                  setShowAddPageDialog(!showAddPageDialog);
                }}>
                + Add Page
              </Button>
            </View>
            {/* <Text style={styles.defaulPageSubTitle}>You can fully customise these pages and add new ones</Text> */}
          </View>
        </View>

        <AddCustomPage showAddPageDialog={showAddPageDialog} />
        {screens?.map((s: NavigationConfig, index: number) => {
          const {path, config} = s;
          return (
            <ScreenItem
              key={config.name}
              screen={config}
              path={path}
              isEditable={true}
              isDeletable={!premiumScreens.includes(config.name)}
              redDot={false}
            />
          );
        })}
      </View>
    </View>
  );
};

const ScreenItem = ({screen: s, path, isEditable, isDeletable, redDot}) => {
  const [showDeletePageDialog, setShowDeletePageDialog] = useState(false);
  const dispatch = useDispatch();
  const activeNavigation = useSelector(activeNavigationSelector);
  const active = activeNavigation?.activePageId === s.screen;

  return (
    <>
      <TouchableOpacity
        style={[styles.menuItem, active ? styles.menuItemActive : null]}
        onPress={() => {
          dispatch(navigateToPage(s.screen, s.type));
        }}>
        <View style={styles.menuItemIcon}>
          {/* <MaterialCommunityIcons name={s.iconName} size={16} {...(active ? {color: promptTheme.ACTIVE_BLUE} : {})} /> */}
          <Icon iconType={s.iconType} name={s.iconName} size={16} {...(active ? {color: promptTheme.ACTIVE_BLUE} : {color: promptTheme.TEXT_COLOR})} />
        </View>
        <View style={styles.menuTextWrapper}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Text style={[styles.menuItemText, active ? styles.menuItemTextActive : null]}>
              {!_.isEmpty(s.title) ? s.title : s.name}{' '}
            </Text>
            {/* {redDot && <RedDo tooltip={'Tile setup incomplete in this page'} />} */}
          </View>
          {isDeletable && (
            <Pressable
              onPress={() => {
                setShowDeletePageDialog(!showDeletePageDialog);
              }}
              style={{paddingRight: 10}}>
              <MaterialCommunityIcons name="trash-can-outline" size={16} color={promptTheme.TEXT_SECONDARY_COLOR} />
            </Pressable>
          )}
        </View>
      </TouchableOpacity>
      <DeletePageDialog showDeletePageDialog={showDeletePageDialog} screen={s} selector={path} />
    </>
  );
};

export default NavigationTree;
