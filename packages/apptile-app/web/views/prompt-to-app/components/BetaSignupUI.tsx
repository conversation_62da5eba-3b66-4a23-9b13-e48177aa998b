import React from 'react';
import {StyleSheet, View, Text, TouchableOpacity} from 'react-native';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';

interface BetaSignupUIProps {
  onRequestAccess: () => void;
}

const BetaSignupUI: React.FC<BetaSignupUIProps> = ({onRequestAccess}) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Text style={styles.icon}>🚀</Text>
        </View>

        <Text style={styles.title}>Join the Beta Program</Text>
        <Text style={styles.subtitle}>
          Get early access to our AI-powered app builder and be among the first to create amazing mobile experiences
          with just a prompt.
        </Text>

        <View style={styles.featuresContainer}>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>✨</Text>
            <Text style={styles.featureText}>AI-powered app generation</Text>
          </View>
          <View style={styles.feature}>
            <Text style={styles.featureIcon}>🎨</Text>
            <Text style={styles.featureText}>Customizable designs</Text>
          </View>
        </View>

        <TouchableOpacity style={styles.requestButton} onPress={onRequestAccess}>
          <Text style={styles.requestButtonText}>Request Beta Access</Text>
        </TouchableOpacity>

        <Text style={styles.disclaimer}>We'll notify you as soon as your beta access is approved.</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  content: {
    width: '100%',
    maxWidth: 600,
    alignItems: 'center',
    textAlign: 'center',
  },
  iconContainer: {
    marginBottom: 24,
  },
  icon: {
    fontSize: 64,
  },
  title: {
    fontSize: 48,
    fontWeight: '600',
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    marginBottom: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 20,
    color: themeColors.EDITOR_ACCENT_COLOR,
    marginBottom: 40,
    textAlign: 'center',
    lineHeight: 28,
  },
  featuresContainer: {
    marginBottom: 40,
    gap: 16,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  featureIcon: {
    fontSize: 20,
  },
  featureText: {
    fontSize: 18,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
  },
  requestButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    marginBottom: 24,
    minWidth: 200,
  },
  requestButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  disclaimer: {
    fontSize: 14,
    color: themeColors.EDITOR_ACCENT_COLOR,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default BetaSignupUI;
