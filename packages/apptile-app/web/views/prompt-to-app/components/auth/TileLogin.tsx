import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import React, {useEffect} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useSelector} from 'react-redux';
import {GoogleSignInButton} from '../../../../components/auth/GoogleSignInButton';
import {useNavigate} from '../../../../routing.web';
import {EditorRootState} from '../../../../store/EditorRootState';
import But<PERSON> from '@/root/web/components-v2/base/Button';

const TileLogin: React.FC = ({}) => {
  // const dispatch = useDispatch();
  const navigate = useNavigate();
  const isLoggedIn = useSelector((state: EditorRootState) => state.user.userLoggedIn);

  // const [email, setEmail] = useState<string>('');
  // const [password, setPassword] = useState<string>('');

  // const [emailError, setEmailError] = useState<string>();
  // const [passwordError, setpasswordError] = useState<string>();
  // const [loginValid, setLoginValid] = useState<boolean>();

  // const onEmailChange = (email: string) => {
  //   if (email.length < 3) return;
  //   const emailErr = emailValidator(email);
  //   setEmailError(emailErr);
  //   setEmail(email);
  //   if (!emailError && !passwordError) {
  //     setLoginValid(true);
  //   }
  // };

  // const onPasswordChange = (pass: string) => {
  //   if (pass.length < 3) return;
  //   const passErr = passwordValidator(pass);
  //   setpasswordError(passErr);
  //   setPassword(pass);
  //   if (!emailError && !passwordError) {
  //     setLoginValid(true);
  //   }
  // };

  // const onLogin = () => {
  //   dispatch(loginUser(email as string, password as string));
  // };

  useEffect(() => {
    if (isLoggedIn) {
      navigate('/dashboard');
    }
  }, [navigate, isLoggedIn]);

  // const renderValidationError = (error: string | undefined) => {
  //   return error ? <Text style={styles.errorText}>{error}</Text> : <></>;
  // };

  return (
    <View style={styles.container}>
      <View>
        <Text style={styles.heading}>Login to Tile</Text>
        <GoogleSignInButton />

        <Button
          onPress={() => {
            navigate('/legacy-login');
          }}
          containerStyles={{width: 200, marginTop: 20}}
          color="CTA">
          Use Legacy Login
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
    height: '100vh',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  topHeader: {flexDirection: 'column', width: '100%', alignSelf: 'center'},
  heading: {
    flex: 1,
    fontWeight: '600',
    fontSize: '18px',
    padding: 14,
    textAlign: 'center',
    color: themeColors.EDITOR_ACCENT_COLOR,
  },
});

export default TileLogin;
