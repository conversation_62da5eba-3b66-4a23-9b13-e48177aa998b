import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Text} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';
import {GoogleSignInButton} from '@/root/web/components/auth/GoogleSignInButton';

interface AuthModalProps {
  onClose: () => void;
}

const AuthModal: React.FC<AuthModalProps> = ({onClose}) => {
  const dispatch = useDispatch();
  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Get auth state from Redux
  const {userLoggedIn} = useSelector((state: any) => state.user);

  // Watch for auth state changes - Handle modal closing and start app creation
  useEffect(() => {
    // When user logs in successfully
    if (isLoading && userLoggedIn) {
      console.log('LOGIN SUCCESS: userLoggedIn is true');
      // Get the stored prompt from sessionStorage
      const storedPrompt = sessionStorage.getItem('pendingPrompt');
      console.log('Retrieved stored prompt:', storedPrompt);
      // Close the modal
      onClose();
      // Simulate an Enter key press to trigger app creation with the stored prompt
      if (storedPrompt) {
        // Find the prompt input element and set its value
        const inputElement = document.querySelector('textarea[placeholder="Describe your app idea..."]');
        if (inputElement) {
          // Create and dispatch an Enter key event
          const enterEvent = new KeyboardEvent('keypress', {
            key: 'Enter',
            code: 'Enter',
            keyCode: 13,
            which: 13,
            bubbles: true,
            cancelable: true
          });
          
          // Dispatch the event to trigger the app creation
          inputElement.dispatchEvent(enterEvent);
        }
      }
    }
  }, [userLoggedIn, isLoading, onClose]);

  // Safety timeout to reset loading state if auth takes too long
  useEffect(() => {
    if (!isLoading) return;

    const timeoutId = setTimeout(() => {
      if (isLoading && !userLoggedIn) {
        console.log('AUTH TIMEOUT: Resetting loading state');
        setIsLoading(false);
      }
    }, 5000);

    return () => clearTimeout(timeoutId);
  }, [isLoading, userLoggedIn]);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Sign in to continue</Text>
      <Text style={styles.subtitle}>Please sign in with your Google account to create an app</Text>

      <View style={styles.googleButtonContainer}>
        <GoogleSignInButton onLoginStart={() => setIsLoading(true)} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
    padding: 24,
    width: 400,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    marginBottom: 24,
    textAlign: 'center',
    opacity: 0.8,
  },
  googleButtonContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
});

export default AuthModal;
