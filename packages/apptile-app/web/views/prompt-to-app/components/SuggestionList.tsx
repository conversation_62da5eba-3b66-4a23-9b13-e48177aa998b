import React from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';

interface SuggestionListProps {
  onSuggestionClick: (suggestion: string) => void;
}

const suggestions = [
  'Create a simple to-do list app',
  'Make a basic weather app',
  // 'Build a simple calculator',
  // 'Design a photo gallery app',
];

const SuggestionList: React.FC<SuggestionListProps> = ({onSuggestionClick}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Try these prompts:</Text>
      <View style={styles.grid}>
        {suggestions.map((suggestion, index) => (
          <Pressable
            key={index}
            style={({pressed, hovered}) => [
              styles.suggestionItem,
              hovered && styles.suggestionHovered,
              pressed && styles.suggestionPressed
            ]}
            onPress={() => onSuggestionClick(suggestion)}>
            <Text style={styles.suggestionText}>{suggestion}</Text>
          </Pressable>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  heading: {
    fontSize: 14,
    fontWeight: '500',
    color: '#909090',
    marginBottom: 16,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  suggestionItem: {
    backgroundColor: '#121212',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#303030',
    flex: 1,
    minWidth: 250,
    cursor: 'pointer',
    transition: 'all 0.2s ease',
  },
  suggestionHovered: {
    backgroundColor: '#1A1A1A',
    borderColor: '#404040',
    transform: [{scale: 1.01}],
  },
  suggestionPressed: {
    backgroundColor: '#0F0F0F',
    transform: [{scale: 0.99}],
  },
  suggestionText: {
    color: '#FAFAFA',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default SuggestionList;
