import {selectAppBranchByName} from '@/root/web/selectors/AppSelectors';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {DEFAULT_BRANCH_NAME} from 'apptile-core';
import _ from 'lodash';
import React, {useEffect, useState} from 'react';
import {ActivityIndicator} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {Navigate, useLocation} from 'react-router-dom';
import {fetchOrgs} from '../../../../web/actions/editorActions';

const type = 'tile';

export const OrgResolver = ({...rest}) => {
  const location = useLocation();

  const orgsById = useSelector((state: EditorRootState) => state.orgs.orgsById);
  const [firstOrgId, setFirstOrgId] = useState<string | null>(null);

  useEffect(() => {
    if (Object.keys(orgsById).length > 0) {
      const firstTileOrgId = _.find(orgsById, {sourcePlatformType: type});
      const firstId = Object.keys(orgsById)[0];
      const selectedOrgId = !_.isEmpty(firstTileOrgId) ? firstTileOrgId.id : firstId;
      if (selectedOrgId) {
        setFirstOrgId(selectedOrgId);
      }
    }
  }, [orgsById]);

  const branchByNames = useSelector(selectAppBranchByName);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchOrgs());
  }, [dispatch]);

  if (!_.isEmpty(firstOrgId)) {
    return (
      <Navigate
        replace
        {...rest}
        to={{
          pathname: `/dashboard/${firstOrgId}`,
          search: `${location.search}`,
        }}
      />
    );
  } else {
    return <ActivityIndicator size={'large'} />;
  }
};
