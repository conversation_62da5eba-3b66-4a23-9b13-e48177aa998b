import React, {useEffect} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useLocation} from 'react-router';
import Button from '@/root/web/components-v2/base/Button';
import Analytics from '@/root/web/lib/segment';
import AuthTopBanner from '../../auth/components/AuthTopBanner';

const AuthFailed: React.FC = ({}) => {
  const location = useLocation();

  useEffect(() => {
    Analytics.track('editor:unAuthorizedScreen_customerUnAuthorized');
  }, []);

  const queryParams: URLSearchParams = new URLSearchParams(location.search);
  const error = queryParams.get('error')
    ? queryParams.get('error')
    : 'Your Authorization request failed. Please retry if this issue still <NAME_EMAIL>';

  return (
    <View style={styles.container}>
      <AuthTopBanner />
      <View style={styles.wrapperStyle}>
        <View style={{alignItems: 'center'}}>
          <Text style={styles.header}>Unauthorized</Text>
          <Text style={styles.descText}>{error}</Text>
          <Button onPress={() => showNewMessage()} containerStyles={{width: 200}} color="CTA">
            Contact us
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start',
    overflow: 'scroll',
  },
  header: {
    fontSize: 40,
    fontWeight: '600',
    margin: 20,
    textAlign: 'center',
    color: '#0091bc',
  },
  descText: {
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 20,
  },
  wrapperStyle: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ecf0f1',
    padding: 40,
    borderRadius: 10,
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
  },
});

export default AuthFailed;
