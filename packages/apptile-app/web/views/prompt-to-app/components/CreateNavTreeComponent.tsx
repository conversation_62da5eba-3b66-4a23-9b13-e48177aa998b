import React from 'react';
import {activeNavigationSelector, deletePageConfig, navComponentDelete} from 'apptile-core';
import {addNewPage, navigateToPage} from '../../../actions/editorActions';
import {selectMandatoryCheck} from '../../../selectors/EditorModuleSelectors';
import {selectScreensInNavWithPath} from '../../../selectors/EditorSelectors';
import NavigationTree from './NavigationTree';

export const navigationTreeComponent = () => {
  if (window.apptileWebSDK.moduleExports?.components?.navigationTreeComponent) {
    const PartnerNavigationTree = window.apptileWebSDK.moduleExports?.components?.navigationTreeComponent;
    return () =>
      React.createElement(PartnerNavigationTree, {
        selectScreensByPath: selectScreensInNavWithPath,
        dispatchAddNewPage: addNewPage,
        dispatchNavigateToPage: navigateToPage,
        dispatchDeletePageConfig: deletePageConfig,
        dispatchDeleteNavComponent: navComponentDelete,
        selectActiveNavigation: activeNavigationSelector,
        selectMandatoryCheck: selectMandatoryCheck,
      });
  } else {
    return () => React.createElement(NavigationTree);
  }
};
