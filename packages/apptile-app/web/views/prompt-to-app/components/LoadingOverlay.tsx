import React from 'react';
import {View, Text, ActivityIndicator, StyleSheet, Modal} from 'react-native';
import {themeColors} from '@/root/web/components/codeEditor/darkTheme';

interface LoadingOverlayProps {
  visible: boolean;
  message?: string;
  error?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({visible, message = 'Creating your app...', error = ''}) => {
  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={() => {
        /* Required prop, does nothing here */
      }}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <ActivityIndicator size="large" color={error ? '#ff4d4f' : themeColors.EDITOR_ACCENT_COLOR} />
          <Text style={styles.message}>{error ? 'Warning' : message}</Text>
          {error ? (
            <>
              <Text style={[styles.subMessage, styles.errorMessage]}>{error}</Text>
              <Text style={styles.subMessage}>Continuing with navigation...</Text>
            </>
          ) : (
            <>
              {/* Displaying some generic steps */}
              <Text style={styles.subMessage}>Generating app structure...</Text>
              <Text style={styles.subMessage}>Setting up initial codebase...</Text>
              <Text style={styles.subMessage}>Please wait, this may take a moment.</Text>
            </>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.75)', // Darker overlay
  },
  container: {
    minWidth: 300, // Ensure minimum width
    padding: 40,
    backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4}, // Increased shadow
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 10, // for Android shadow
    borderWidth: 1,
    borderColor: themeColors.EDITOR_ACCENT_COLOR + '50', // Subtle border
  },
  message: {
    marginTop: 25,
    fontSize: 20, // Larger font
    fontWeight: 'bold',
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    textAlign: 'center',
  },
  subMessage: {
    marginTop: 12,
    fontSize: 15, // Slightly larger
    color: themeColors.EDITOR_FOREGROUND_COLOR + 'a0', // Slightly less faded
    textAlign: 'center',
  },
  errorMessage: {
    color: '#ff4d4f', // Error red color
    fontWeight: '500',
  },
});

export default LoadingOverlay;
