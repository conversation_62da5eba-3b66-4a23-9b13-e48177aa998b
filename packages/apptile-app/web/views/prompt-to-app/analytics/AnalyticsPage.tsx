import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import LeftSidebar from '../dashboard/LeftSidebar';
import theme from '../styles-prompt-to-app/theme';

interface AnalyticsPageProps {}

const AnalyticsPage: React.FC<AnalyticsPageProps> = () => {
  return (
    <View style={styles.mainContainer}>
      <LeftSidebar mainBar="APP_EDITOR" />
      <View style={styles.container}>
        <View style={styles.comingSoonContainer}>
          <Text style={styles.comingSoonText}>Coming Soon</Text>
          <Text style={styles.descriptionText}>Analytics will be available here shortly.</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: theme.SIDEBAR_BACKGROUND,
    minHeight: '100vh',
  },
  container: {
    flex: 1,
    padding: 20,
  },
  comingSoonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  comingSoonText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.TEXT_COLOR,
    marginBottom: 16,
  },
  descriptionText: {
    fontSize: 16,
    color: theme.TEXT_COLOR,
    textAlign: 'center',
  },
});

export default AnalyticsPage;
