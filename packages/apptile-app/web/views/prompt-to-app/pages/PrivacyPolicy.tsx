import React from 'react';
import {ScrollView, View, Text, StyleSheet} from 'react-native';
import StaticPageHeader from '../components/StaticPageHeader';
import theme from '../styles-prompt-to-app/theme';

const PrivacyPolicy: React.FC = () => {
  return (
    <View style={styles.pageContainer}>
      <StaticPageHeader />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Privacy Policy</Text>
        <Text style={styles.subtitle}>Last Updated: May 22, 2025</Text>

        <Text style={styles.heading}>1. Information We Collect</Text>
        <Text style={styles.paragraph}>
          We collect information that you provide directly to us, such as when you create an account, use our services,
          or communicate with us. This may include:
        </Text>
        <View style={styles.list}>
          <Text style={styles.listItem}>• Contact information (name, email address, etc.)</Text>
          <Text style={styles.listItem}>• Account credentials</Text>
          <Text style={styles.listItem}>• Content you create or upload</Text>
          <Text style={styles.listItem}>• Communication preferences</Text>
        </View>

        <Text style={styles.heading}>2. How We Use Your Information</Text>
        <Text style={styles.paragraph}>We use the information we collect to:</Text>
        <View style={styles.list}>
          <Text style={styles.listItem}>• Provide, maintain, and improve our services</Text>
          <Text style={styles.listItem}>• Process transactions and send related information</Text>
          <Text style={styles.listItem}>• Respond to your comments, questions, and requests</Text>
          <Text style={styles.listItem}>• Monitor and analyze trends, usage, and activities</Text>
          <Text style={styles.listItem}>• Detect, investigate, and prevent security incidents</Text>
        </View>

        <Text style={styles.heading}>3. Information Sharing</Text>
        <Text style={styles.paragraph}>
          We do not sell your personal information. We may share your information in the following circumstances:
        </Text>
        <View style={styles.list}>
          <Text style={styles.listItem}>• With service providers who perform services on our behalf</Text>
          <Text style={styles.listItem}>• When required by law or to protect rights and safety</Text>
          <Text style={styles.listItem}>• In connection with a merger, sale, or asset transfer</Text>
        </View>

        <Text style={styles.heading}>4. Data Security</Text>
        <Text style={styles.paragraph}>
          We implement appropriate technical and organizational measures to protect your personal information. However,
          no method of transmission over the internet or electronic storage is 100% secure.
        </Text>

        <Text style={styles.heading}>5. Your Rights</Text>
        <Text style={styles.paragraph}>
          Depending on your location, you may have certain rights regarding your personal information, including:
        </Text>
        <View style={styles.list}>
          <Text style={styles.listItem}>• Access to your personal information</Text>
          <Text style={styles.listItem}>• Correction of inaccurate information</Text>
          <Text style={styles.listItem}>• Deletion of your personal information</Text>
          <Text style={styles.listItem}>• Restriction or objection to processing</Text>
          <Text style={styles.listItem}>• Data portability</Text>
        </View>

        <Text style={styles.heading}>6. Changes to This Policy</Text>
        <Text style={styles.paragraph}>
          We may update this Privacy Policy from time to time. We will notify you of any changes by updating the "Last
          Updated" date at the top of this page.
        </Text>

        <Text style={styles.heading}>7. Contact Us</Text>
        <Text style={styles.paragraph}>
          If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
    backgroundColor: theme.SIDEBAR_BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    paddingBottom: 40, // Add extra padding at the bottom for better scrolling
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.TEXT_COLOR,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.TEXT_SECONDARY_COLOR,
    marginBottom: 24,
    textAlign: 'center',
  },
  heading: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.TEXT_COLOR,
    marginTop: 20,
    marginBottom: 10,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: theme.TEXT_SECONDARY_COLOR,
    marginBottom: 15,
    textAlign: 'justify',
  },
  list: {
    marginLeft: 16,
    marginBottom: 16,
  },
  listItem: {
    fontSize: 16,
    lineHeight: 24,
    color: theme.TEXT_SECONDARY_COLOR,
    marginBottom: 5,
  },
});

export default PrivacyPolicy;
