import React from 'react';
import {ScrollView, View, Text, StyleSheet} from 'react-native';
import StaticPageHeader from '../components/StaticPageHeader';
import theme from '../styles-prompt-to-app/theme';

const TermsOfUse: React.FC = () => {
  return (
    <View style={styles.pageContainer}>
      <StaticPageHeader />
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Terms of Use</Text>
        <Text style={styles.subtitle}>Last Updated: May 22, 2025</Text>

        <Text style={styles.heading}>1. Acceptance of Terms</Text>
        <Text style={styles.paragraph}>
          By accessing or using our services, you agree to be bound by these Terms of Use and all applicable laws and
          regulations. If you do not agree with any of these terms, you are prohibited from using or accessing our
          services.
        </Text>

        <Text style={styles.heading}>2. Use License</Text>
        <Text style={styles.paragraph}>
          Permission is granted to temporarily use our services for personal, non-commercial purposes only. This is the
          grant of a license, not a transfer of title, and under this license you may not:
        </Text>
        <View style={styles.list}>
          <Text style={styles.listItem}>• Modify or copy the materials</Text>
          <Text style={styles.listItem}>• Use the materials for any commercial purpose</Text>
          <Text style={styles.listItem}>• Attempt to decompile or reverse engineer any software</Text>
          <Text style={styles.listItem}>• Remove any copyright or other proprietary notations</Text>
          <Text style={styles.listItem}>• Transfer the materials to another person</Text>
        </View>

        <Text style={styles.heading}>3. Disclaimer</Text>
        <Text style={styles.paragraph}>
          The materials on our website are provided on an 'as is' basis. We make no warranties, expressed or implied,
          and hereby disclaim and negate all other warranties including, without limitation, implied warranties or
          conditions of merchantability, fitness for a particular purpose, or non-infringement of intellectual property.
        </Text>

        <Text style={styles.heading}>4. Limitations</Text>
        <Text style={styles.paragraph}>
          In no event shall we or our suppliers be liable for any damages (including, without limitation, damages for
          loss of data or profit, or due to business interruption) arising out of the use or inability to use our
          services, even if we or an authorized representative has been notified orally or in writing of the possibility
          of such damage.
        </Text>

        <Text style={styles.heading}>5. Revisions and Errata</Text>
        <Text style={styles.paragraph}>
          The materials appearing on our website could include technical, typographical, or photographic errors. We do
          not warrant that any of the materials on our website are accurate, complete, or current. We may make changes
          to the materials contained on our website at any time without notice.
        </Text>

        <Text style={styles.heading}>6. Links</Text>
        <Text style={styles.paragraph}>
          We have not reviewed all of the sites linked to our website and are not responsible for the contents of any
          such linked site. The inclusion of any link does not imply endorsement by us of the site. Use of any such
          linked website is at the user's own risk.
        </Text>

        <Text style={styles.heading}>7. Modifications to Terms of Use</Text>
        <Text style={styles.paragraph}>
          We may revise these terms of use for our website at any time without notice. By using this website, you are
          agreeing to be bound by the then current version of these Terms of Use.
        </Text>

        <Text style={styles.heading}>8. Governing Law</Text>
        <Text style={styles.paragraph}>
          These terms and conditions are governed by and construed in accordance with the laws and you irrevocably
          submit to the exclusive jurisdiction of the courts in that location.
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
    backgroundColor: theme.SIDEBAR_BACKGROUND,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    paddingBottom: 40, // Add extra padding at the bottom for better scrolling
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.TEXT_COLOR,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.TEXT_SECONDARY_COLOR,
    marginBottom: 24,
    textAlign: 'center',
  },
  heading: {
    fontSize: 20,
    fontWeight: '600',
    color: theme.TEXT_COLOR,
    marginTop: 20,
    marginBottom: 10,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 24,
    color: theme.TEXT_SECONDARY_COLOR,
    marginBottom: 15,
    textAlign: 'justify',
  },
  list: {
    marginLeft: 16,
    marginBottom: 16,
  },
  listItem: {
    fontSize: 16,
    lineHeight: 24,
    color: theme.TEXT_SECONDARY_COLOR,
    marginBottom: 5,
  },
});

export default TermsOfUse;
