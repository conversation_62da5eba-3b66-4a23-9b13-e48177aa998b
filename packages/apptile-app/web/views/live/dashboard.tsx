import {allAvailablePlans, Icon} from 'apptile-core';
import LogRocket, {error} from 'logrocket';
import React, {useCallback, useEffect, useState} from 'react';
import {Image, Pressable, ScrollView, StyleSheet, Text, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigate} from 'react-router';
import {setOpenPremiumModal} from '../../actions/editorActions';
import {fetchActiveStreams, fetchPastStreams, updateCreateStreamMeta} from '../../actions/liveSellingActions';
import {makeToast} from '../../actions/toastActions';
import ModalComponent from '../../components-v2/base/Modal';
import TextElement from '../../components-v2/base/TextElement';
import {currentPlanFeaturesSelector, openPremiumModalSelector} from '../../selectors/FeatureGatingSelector';
import {EditorRootState} from '../../store/EditorRootState';
import PremiumUpgradeModal from '../featureGating/premiumUpgradeModal';
import {DeleteStreamConfirmationModal} from './shared/deleteStreamConfirmationModal';
import LiveStreamHeader from './shared/LiveStreamHeader';
import {StreamCard} from './shared/streamCard';
import _ from 'lodash-es';
import NetworkErrorPage from './shared/NetworkErrorPage';
import NoDataFound from './shared/NoDataFound';
import {handleToast} from './shared/CommonError';
import {useSearchParams} from 'react-router-dom';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const PastStreams: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  // const [appId, setAppId] = useState('');
  const appId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const pastStreams = useSelector(state => state.liveSelling.pastStreams);
  const {data, loading, errorMessage, isNetworkError} = pastStreams;

  React.useEffect(() => {
    if (authToken) {
      dispatch(fetchPastStreams());
    }
  }, [authToken, dispatch]);

  // useEffect(() => {
  //   const params = new URLSearchParams(window.location.search);
  //   setAppId(params.get('app-id'));
  // }, []);

  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isPlusDisabled = !currentPlanFeatures.includes(allAvailablePlans.PLUS);

  return (
    <View>
      <View style={[styles.rowLayout, styles.alignCenter, {gap: 5, alignSelf: 'flex-start'}]}>
        <DeleteStreamConfirmationModal />

        <TextElement color="SECONDARY" fontSize="xl" lineHeight="xl" fontWeight="600">
          Your Past Streams
        </TextElement>
        {!_.isEmpty(errorMessage) && !isNetworkError && (
          <Pressable onPress={() => handleToast(errorMessage, dispatch)} style={styles.iconStyle}>
            <Icon
              name={'error'}
              iconType={'MaterialIcons'}
              size={25}
              color={'#AA2217'}
              style={{margin: 0, verticalAlign: 'middle', padding: 10, fontWeight: 300}}
            />
          </Pressable>
        )}
      </View>

      {data.length === 0 && <NoDataFound text={'No past streams found'} />}

      <View style={[styles.streamsList]}>
        {data.map((entry, idx) => (
          <StreamCard
          idUpcoming={false}
            streamInfo={entry}
            heading={entry.streaming_name || '-title-'}
            description={entry.streaming_description || '-description-'}
            key={idx}
            startTime={entry.start_time}
            streaming_duration={entry.streaming_duration}
            productInfo={entry.product_info}
            streamTo={entry.stream_to}
            isActive
            imageUrl={entry.streaming_thumbnail}
            status={'ENDED'}
            onJoinPress={null}
            // onStopPress={() =>
            //   setCurrentDeletion({fileId: entry.recording?.file_info?.file_id, streamingId: entry?.streaming_id})
            // }
            onContainerPress={() => {
              if (isPlusDisabled) {
                dispatch(setOpenPremiumModal(true, allAvailablePlans.PLUS ?? ' ', 'Stream Analytics'));
              } else {
                navigate(
                  `/live-selling/app/${appId}/analytics/stream/${entry?.streaming_id}?shopable-feed-id=${entry?.shoppable_feed_id}`,
                );
              }
            }}
          />
        ))}
      </View>
    </View>
  );
};

const UpcomingStreams: React.FC = ({}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  // const [searchParams, setSearchParams] = useSearchParams();
  // const apptileAppId = searchParams.get('app-id');
  const apptileAppId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);

  let {
    data: inProgressStreams,
    loading: inProgressStreamsLoading,
    errorMessage: inProgressStreamError,
    isNetworkError: inProgressStreamNetowkrErr,
  } = useSelector(state => state.liveSelling?.inProgressStreams);
  let {
    data: upcomingStreams,
    loading: upcomingStreamsLoading,
    errorMessage: upcomingStreamError,
    isNetworkError: upcomingStreamNetowkrErr,
  } = useSelector(state => state.liveSelling?.upcomingStreams);

  React.useEffect(() => {
    if (authToken) {
      dispatch(fetchActiveStreams());
    }
  }, [authToken, dispatch]);

  return (
    <View>
      <View style={[styles.rowLayout, styles.spaceBetween, styles.alignCenter]}>
        <View style={[styles.rowLayout, {alignItems: 'center', justifyContent: 'center', alignSelf: 'flex-start'}]}>
          <TextElement color="SECONDARY" fontSize="xl" lineHeight="xl" fontWeight="600">
            Upcoming Streams
          </TextElement>
          {(upcomingStreamError || inProgressStreamError) &&
            (!_.isEmpty(upcomingStreamError) || !_.isEmpty(inProgressStreamError)) && (
              <Pressable
                onPress={() =>
                  handleToast(
                    !_.isEmpty(upcomingStreamError)
                      ? `At upcoming stream: ${upcomingStreamError}`
                      : `At In Progress stream: ${inProgressStreamError}`,
                    dispatch,
                  )
                }
                style={styles.iconStyle}>
                <Icon
                  name={'error'}
                  iconType={'MaterialIcons'}
                  size={25}
                  color={'#AA2217'}
                  style={{margin: 0, verticalAlign: 'middle', padding: 10, fontWeight: 300}}
                />
              </Pressable>
            )}
        </View>
      </View>

      {(inProgressStreamsLoading || upcomingStreamsLoading) && (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '28px',
          }}>
          <Image source={require('@/root/web/assets/images/preloader-blue.svg')} style={{width: 28, height: 28}} />
        </View>
      )}

      <View style={[styles.streamsList]}>
        {inProgressStreams.map((entry, idx) => (
          <StreamCard
            idUpcoming={true}
            streamInfo={entry}
            heading={entry.streaming_name || '-title-'}
            description={entry.streaming_description || '-description-'}
            key={idx}
            startTime={entry.start_time}
            streaming_duration={entry.streaming_duration}
            productInfo={entry.product_info}
            streamTo={entry.stream_to}
            isActive
            imageUrl={entry.streaming_thumbnail}
            status={'LIVE'}
            onJoinPress={() => navigate(`../live-stream/${entry.streaming_id}/1`)}
            onModeratePress={() => navigate(`../live-stream/${entry.streaming_id}/2`)}
          />
        ))}

        {upcomingStreams.map((entry, idx) => (
          <StreamCard
            idUpcoming={true}
            streamInfo={entry}
            heading={entry.streaming_name || '-title-'}
            description={entry.streaming_description || '-description-'}
            key={idx}
            isActive
            imageUrl={entry.streaming_thumbnail}
            productInfo={entry.product_info}
            streamTo={entry.stream_to}
            status={'SCHEDULED'}
            startTime={entry.start_time}
            streaming_duration={entry.streaming_duration}
            onJoinPress={() => navigate(`../live-stream/${entry.streaming_id}/1`)}
            onModeratePress={() => navigate(`../live-stream/${entry.streaming_id}/2`)}
            onEditPress={() => navigate(`/live-selling/edit-stream/${entry.streaming_id}?app-id=${apptileAppId}`)}
            // onStopPress={() => {
            //   setCurrentDeletion(entry.streaming_id);
            // }}
          />
        ))}
        <Pressable
          style={styles.createStreamCardWrapper}
          onPress={() => navigate(`../create-stream?app-id=${apptileAppId}`)}>
          <View style={styles.plusWrapper}>
            <Icon name="plus" iconType="MaterialCommunityIcons" size={24} color={'#000'} />
          </View>
          <Text
            style={[
              commonStyles.baseText,
              {
                color: '#000',
                fontSize: 16,
                fontWeight: '500',
              },
            ]}>
            Create stream
          </Text>
        </Pressable>
      </View>
    </View>
  );
};

const Dashboard = () => {
  const authToken = useSelector(state => state.liveSelling.auth.authToken);
  const livelyUser = useSelector(state => state.liveSelling.auth.livelyUser);
  const createStremMeta = useSelector((state: EditorRootState) => state.liveSelling.streamCreationMeta);
  const isUpcomingNetworkError = useSelector(state => state.liveSelling.upcomingStreams.isNetworkError);
  const isInPreogressNetworkError = useSelector(state => state.liveSelling.upcomingStreams.isNetworkError);
  const isPastetworkError = useSelector(state => state.liveSelling.upcomingStreams.isNetworkError);

  const dispatch = useDispatch();

  useEffect(() => {
    if (createStremMeta?.error) {
      dispatch(
        makeToast({
          content: createStremMeta?.error,
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
    return () => {
      dispatch(updateCreateStreamMeta({data: '', error: null}));
    };
  }, [createStremMeta?.error, dispatch]);

  useEffect(() => {
    if (livelyUser) {
      const identity = {
        name: livelyUser.user_name,
        company: livelyUser.company_name,
        buildnumber: (window as any).BN_FOR_LOGROCKET,
      };
      LogRocket.identify(livelyUser.user_id, identity);
    }
  }, [livelyUser, dispatch]);

  const fetchStreams = useCallback(async () => {
    if (authToken) {
      dispatch(fetchActiveStreams());
      dispatch(fetchPastStreams());
    }
  }, [authToken, dispatch]);
  const openPremiumModal = useSelector(openPremiumModalSelector);

  const onPremiumModalVisibilityChange = (value: boolean) => {
    if (!value) {
      dispatch(setOpenPremiumModal(false, ''));
    }
  };

  return (
    <>
      {authToken ? (
        <>
          <LiveStreamHeader
            fetchStreams={fetchStreams}
            showActionButtons={isUpcomingNetworkError || isInPreogressNetworkError || isPastetworkError ? false : true}
          />
          {isUpcomingNetworkError || isInPreogressNetworkError || isPastetworkError ? (
            <NetworkErrorPage />
          ) : (
            <>
              <View style={[styles.container]} key={authToken}>
                <ScrollView style={{width: '100%'}} contentContainerStyle={styles.wrapper}>
                  <View style={styles.contentContainer}>
                    <UpcomingStreams />
                    <PastStreams />
                  </View>
                </ScrollView>
              </View>
              {openPremiumModal && (
                <ModalComponent
                  onVisibleChange={onPremiumModalVisibilityChange}
                  visible={openPremiumModal}
                  content={<PremiumUpgradeModal />}
                />
              )}
            </>
          )}
        </>
      ) : (
        <Image source={require('@/root/web/assets/images/preloader-blue.svg')} style={{width: 50, height: 50}} />
      )}
    </>
  );
};

export default Dashboard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    alignItems: 'center',
    flex: 1,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  contentContainer: {
    width: '95%',
    marginBottom: 50,
    paddingVertical: 40,
  },
  infoText: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
    color: theme.TEXT_COLOR,
    lineHeight: 16,
    fontWeight: '500',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  alignCenter: {
    alignItems: 'center',
  },
  streamsList: {
    marginVertical: 30,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  emptyStream: {
    height: 175,
    width: '100%',
    backgroundColor: '#E0D6C6',
    borderRadius: 10,
    marginBottom: 20,
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  createStreamCardWrapper: {
    width: '19%',
    backgroundColor: '#E0D6C6',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 15,
    minHeight: 303,
  },
  plusWrapper: {
    width: 50,
    height: 50,
    borderRadius: 50,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 5,
    width: 100,
    borderColor: '#D92626',
    borderRadius: 24,
    borderWidth: 1,
  },
});
