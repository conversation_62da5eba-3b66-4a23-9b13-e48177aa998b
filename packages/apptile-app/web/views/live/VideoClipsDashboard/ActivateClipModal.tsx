import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {Icon} from 'apptile-core';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const ActivateClipModal = ({onClosePress, setStreamActive}) => {
  return (
    <View style={styles.ModalComponent}>
      <View style={styles.ModalInnerComponent}>
        <View style={styles.Header}>
          <TextElement style={{textAlign: 'left', width: '100%', fontSize: 20}} color="SECONDARY" fontWeight="600">
            Change Featured Video Clip
          </TextElement>
          <Pressable onPress={onClosePress}>
            <Icon
              name="close"
              iconType="AntDesign"
              size={16}
              color={'black'}
              style={{
                margin: 0,
                fontWeight: 300,
              }}
            />
          </Pressable>
        </View>

        <View style={styles.ModalContent}>
          <TextElement style={{textAlign: 'left', width: '100%', fontSize: 15}} color="SECONDARY" fontWeight="500">
            Are you sure you want to change the featured video clip in product page of your app?
          </TextElement>
        </View>
        <View style={styles.ModalNote}>
          <TextElement style={{textAlign: 'left', fontSize: 15}} color="SECONDARY" fontWeight="500">
            <TextElement
              style={{textAlign: 'left', fontSize: 15, color: 'rgba(232, 70, 70, 1)', marginRight: 7}}
              fontWeight="600">
              Note:
            </TextElement>
            Only 1 video clip can be featured per product inside your app.
          </TextElement>
        </View>
        <View style={styles.ButtonContainer}>
          <Pressable
            onPress={onClosePress}
            style={[
              styles.EnableButton,
              {borderWidth: 1, borderColor: 'rgba(111, 111, 111, 1)', backgroundColor: '#ffffff'},
            ]}>
            <TextElement
              fontWeight="500"
              style={[
                commonStyles.baseText,
                {
                  color: 'rgba(111, 111, 111, 1)',
                  fontSize: 13,
                  paddingHorizontal: 15,
                },
              ]}>
              Cancel
            </TextElement>
          </Pressable>
          <Pressable onPress={setStreamActive} style={styles.EnableButton}>
            <TextElement
              fontWeight="500"
              style={[
                commonStyles.baseText,
                {
                  color: '#ffffff',
                  fontSize: 13,
                  paddingLeft: 10,
                  paddingRight: 10,
                  paddingTop: 10,
                  paddingBottom: 10,
                },
              ]}>
              Yes, Feature Clip
            </TextElement>
          </Pressable>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  ModalComponent: {},
  ModalInnerComponent: {
    width: 450,
    marginVertical: 35,
    marginHorizontal: 30,
  },
  Header: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ModalContent: {
    width: '100%',
    marginTop: 30,
  },
  ModalNote: {
    marginTop: 20,
  },
  ButtonContainer: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 30,
  },
  EnableButton: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    backgroundColor: 'rgba(16, 96, 224, 1)',
    height: 30,
  },
});

export default ActivateClipModal;
