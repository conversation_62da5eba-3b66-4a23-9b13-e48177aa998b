import React from 'react';
import {View, StyleSheet, Pressable, Text} from 'react-native';
import DefaultViewerMultiplier from './DefaultViewerMultiplier';
import LiveStreamHeader from '../shared/LiveStreamHeader';
import TextElement from '@/root/web/components-v2/base/TextElement';
import {Icon} from 'apptile-core';
import {useNavigate} from '@/root/web/routing.web';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

export const StreamSettings: React.FC = () => {
  const navigate = useNavigate();
  return (
    <View style={styles.container}>
      <LiveStreamHeader showActionButtons={false} />
      <View style={{paddingHorizontal: 20}}>
        <Pressable onPress={() => navigate(-1)} style={styles.BackButton}>
          <Icon
            name="chevron-left"
            iconType="MaterialCommunityIcons"
            size={24}
            color={'rgba(136, 136, 136, 1)'}
            style={{margin: 0}}
          />
          <Text
            style={[
              commonStyles.baseText,
              {
                fontSize: '14px',
                fontWeight: 500,
                color: 'rgba(136, 136, 136, 1)',
              },
            ]}>
            BACK
          </Text>
        </Pressable>
        <TextElement
          style={{textAlign: 'left', width: '100%', marginTop: 15, fontSize: 22}}
          color="SECONDARY"
          fontWeight="600">
          Stream settings
        </TextElement>
        <DefaultViewerMultiplier />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  BackButton: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    marginTop: 20,
  },
});

export default StreamSettings;
