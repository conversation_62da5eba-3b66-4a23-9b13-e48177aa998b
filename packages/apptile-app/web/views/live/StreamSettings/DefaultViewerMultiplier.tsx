import {makeToast} from '@/root/web/actions/toastActions';
import {LivelyApi} from '@/root/web/api/LivelyApi';
import RadioGroupControl from '@/root/web/components/controls/RadioGroupControl';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const DefaultViewerMultiplier = () => {
  const [viewerMultiplier, setViewerMultiplier] = React.useState<number | null>(null);
  const [currentMeta, setCurrentMeta] = React.useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const authToken = useSelector(state => state.liveSelling?.auth?.authToken);
  const onViewerMultiplierChange = async (value: number) => {
    try {
      await LivelyApi.addMetadataToCompany(authToken, {...currentMeta, default_multiplier: value});
      setViewerMultiplier(value);
    } catch (error) {
      console.error('Error setting viewer multiplier', error);
      dispatch(
        makeToast({
          content: 'Error setting viewer multiplier. Please try again!',
          appearances: 'error',
          duration: 5000,
        }),
      );
    }
  };

  useEffect(() => {
    const fetchViewerMultiplier = async () => {
      const response = await LivelyApi.getCompanyInfo(authToken);
      if (response?.data?.data?.meta?.default_multiplier) {
        setViewerMultiplier(response?.data?.data?.meta?.default_multiplier);
      }
      setCurrentMeta(response?.data?.data?.meta);
      setLoading(false);
    };
    fetchViewerMultiplier();
  }, []);

  return (
    <View style={[styles.container]}>
      <Text
        style={[
          commonStyles.baseText,
          {
            fontSize: 16,
            fontWeight: 500,
            color: 'rgba(136, 136, 136, 1)',
          },
        ]}>
        View Multiplier
      </Text>
      {loading ? (
        <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
      ) : (
        <RadioGroupControl
          disableBinding={true}
          value={viewerMultiplier}
          options={[
            {text: '1x', value: 1},
            {text: '2x', value: 2},
            {text: '3x', value: 3},
            {text: '4x', value: 4},
          ]}
          disabled={loading}
          onChange={onViewerMultiplierChange}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginRight: 20,
    gap: 10,
    width: 310,
    marginTop: 20,
  },
  loaderImage: {
    width: 50,
    height: 50,
  },
});

export default DefaultViewerMultiplier;
