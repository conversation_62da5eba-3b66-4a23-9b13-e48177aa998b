import React, {useEffect, useState} from 'react';
import {View, Text} from 'react-native';
import Button from '../../components-v2/base/Button';
import {DefaultRootState, useSelector} from 'react-redux';
import TextElement from '../../components-v2/base/TextElement';
import {EditorRootState} from '../../store/EditorRootState';
import UserApi from '../../api/UserApi';

export const LiveSellingNewTab: React.FC = () => {
  const [email, setEmail] = useState('');
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);

  const currentIntegration = useSelector(
    (state: DefaultRootState) => state.integration.currentIntegration.appIntegration,
  );
  useEffect(() => {
    if (currentIntegration?.credentials?.livelyLoginEmail) {
      setEmail(currentIntegration?.credentials?.livelyLoginEmail);
    }
  }, [currentIntegration?.credentials?.livelyLoginEmail]);

  return (
    <View style={{flex: 1, justifyContent: 'center', alignItems: 'center', gap: 10}}>
      <TextElement fontSize={'xl'} color={'SECONDARY'}>
        Live selling is only available in new tab
      </TextElement>
      <Button
        color={'CTA'}
        onPress={async () => {
          //TODO: Probably this api call not required. Get it reviwed from @manas const response = await UserApi.fetchUserInit();
          window.open(`${window.location.origin}/live-selling/login?liveLoginEmail=${email}&app-id=${appId}`);
        }}>
        Open Live Dashboard
      </Button>
    </View>
  );
};
