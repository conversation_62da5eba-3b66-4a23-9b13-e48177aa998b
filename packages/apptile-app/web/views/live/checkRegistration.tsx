import React, {useEffect, useState} from 'react';
import {useParams} from 'react-router';
import {Image} from 'react-native';
import Dashboard from './dashboard';
import {DefaultRootState, useDispatch, useSelector} from 'react-redux';
import Login from './login';
import {fetchAppIntegration, saveAppIntegrationCredentials} from '../../actions/editorActions';
import Register from './register';
import {LIVELY_INTEGRATION_CODE} from '../../actions/liveSellingActions';
import { LiveSellingNewTab } from './LiveSellingNewTab';

export const CheckRegistration: React.FC = () => {
  const {id: appId} = useParams();
  const [loading, setLoading] = useState(true);
  const [register, setRegister] = useState(false);
  const dispatch = useDispatch();
  const livelyIntegration = useSelector((state: DefaultRootState) => {
    const allIntegrations = state.integration.appIntegrationsById;
    return allIntegrations[
      Object.keys(allIntegrations).find(e => allIntegrations[e].integrationCode == LIVELY_INTEGRATION_CODE)
    ];
  });
  const authToken = useSelector((state: DefaultRootState) => state.liveSelling.auth.authToken);
  const currentIntegration = useSelector(
    (state: DefaultRootState) => state.integration.currentIntegration.appIntegration,
  );

  useEffect(() => {
    if (livelyIntegration) {
      if (appId && currentIntegration?.platformType != LIVELY_INTEGRATION_CODE) {
        dispatch(fetchAppIntegration(appId, livelyIntegration?.appIntegrations?.[0]?.id));
      } else if (
        currentIntegration?.platformType == LIVELY_INTEGRATION_CODE &&
        !currentIntegration?.credentials?.livelyLoginEmail
      ) {
        setRegister(true);
        setLoading(false);
      } else if (
        currentIntegration?.platformType == LIVELY_INTEGRATION_CODE &&
        !!currentIntegration?.credentials?.livelyLoginEmail
      ) {
        setRegister(false);
        setLoading(false);
      }
    } else {
      setRegister(true);
      setLoading(false);
    }
  }, [
    appId,
    currentIntegration?.credentials?.livelyLoginEmail,
    currentIntegration?.platformType,
    dispatch,
    livelyIntegration,
  ]);

  return loading ? (
    <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 50, height: 50}} />
  ) : register ? (
    <Register/>
  ) : (
    <LiveSellingNewTab />
  );
};
