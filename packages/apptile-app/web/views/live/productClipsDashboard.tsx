import React, { useState, useEffect, useRef, } from 'react';
import { View, Text, StyleSheet, Pressable, ScrollView, Image } from 'react-native';
import LiveStreamHeader from './shared/LiveStreamHeader';
import { useLocation, useNavigate, useParams } from 'react-router';
import { Icon, setActiveBundle } from 'apptile-core';
import TextElement from '../../components-v2/base/TextElement';
import { useDispatch, useSelector } from 'react-redux';
import { LivelyApi } from '../../api/LivelyApi';
import videojs from 'video.js';
import { <PERSON><PERSON>ueryRunner as apolloQueryRunner } from 'apptile-core';
import { makeToast } from '@/root/web/actions/toastActions';
import { ShopifyItemObjectPicker } from '../../integrations/shopify/components/ShopifyItemPicker';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const VideoPlayer = ({ options }) => {
    const videoRef = useRef(null);
    const playerRef = useRef(null);

    useEffect(() => {
        if (videoRef.current && !playerRef.current) {
            const player = videojs(videoRef.current, options, () => {
                console.log('player is ready');
            });
            playerRef.current = player;
        }

        return () => {
            if (playerRef.current) {
                playerRef.current.dispose();
                playerRef.current = null;
            }
        };
    }, [options]);

    return (
        <div style={{ margin: '2.5px', padding: '2.5px', borderRadius: '2.5px' }}>
            <div data-vjs-player style={{ width: '150px', borderRadius: '2.5px' }}>
                <video style={{ borderRadius: '2.5px' }} ref={videoRef} className="video-js vjs-big-play-centered" />
            </div>
        </div>
    );
};
export const ProductClipsDashboard: React.FC = () => {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const authToken = useSelector(state => state.liveSelling?.auth?.authToken);
    const companyId = useSelector(state => state.liveSelling?.auth?.livelyUser?.company_id);
    const [loading, setLoading] = React.useState(false);
    const [productVideoClips, setProductVideoClips] = React.useState([]);
    const [activeProduct, setActiveProduct] = useState();
    const updateProductStreams = async (product: any) => {
        setLoading(true);
        const productVideoChunks = await LivelyApi.getProductStreamClips(
            authToken,
            product?.id.split('/').pop(),
            companyId
        ); 
        const filteredStreams = productVideoChunks?.data?.data?.filter((e) => {
            return e?.url?.includes('live-streaming-video-chunks');
        })
        console.log('filteredStreams',filteredStreams);
        setProductVideoClips(filteredStreams);
    }
    useEffect(()=>{
        updateProductStreams(activeProduct);
    },[activeProduct])
    const [queryRunner, setQueryRunner] = useState(null);
    const setStreamActive = async (productId: string, shoppableFeedId: string) => {
        const response = await LivelyApi.updateProductClipRank(
            authToken,
            productId,
            shoppableFeedId,
            1
        ).then((res) => {
            if (res?.data?.message == 'Success') {
                dispatch(makeToast({
                    content: 'Stream Activated successfully',
                    appearances: 'success',
                }));
            } else {
                dispatch(makeToast({
                    content: 'Something went wrong !!',
                    appearances: 'error',
                }))
            }
        });
        setLoading(false);
        return;
    };
    const setStreamInActive = async (productId: string, shoppableFeedId: string) => {
        const response = await LivelyApi.updateProductClipRank(
            authToken,
            productId,
            shoppableFeedId,
            -1
        ).then((res) => {
            if (res?.data?.message == 'Success') {
                dispatch(makeToast({
                    content: 'Stream InActivated successfully',
                    appearances: 'success',
                }))
            } else {
                dispatch(makeToast({
                    content: 'Something went wrong !!',
                    appearances: 'error',
                }))
            }
        });
        setLoading(false);
        return;
    };
    useEffect(() => {
        if (!authToken) {
            navigate(-1);
        }
        LivelyApi.getShopifyCreds(authToken).then(response => {
            const newQueryRunner = apolloQueryRunner();
            newQueryRunner
                .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, { headers }) => {
                    return {
                        headers: {
                            ...headers,
                            'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
                        },
                    };
                })
                .then(() => {
                    setQueryRunner(newQueryRunner);
                });
        });
    }, [authToken]);
    return (
        <>
            <LiveStreamHeader showActionButtons={false} />
            {true ? (
                <View style={styles.container}>
                    <ScrollView style={{ width: '100%' }} contentContainerStyle={styles.wrapper}>
                        <View style={styles.contentContainer}>
                            <Pressable onPress={() => navigate(-1)} style={styles.BackButton}>
                                <Icon
                                    name="chevron-left"
                                    iconType="MaterialCommunityIcons"
                                    size={24}
                                    color={'rgba(136, 136, 136, 1)'}
                                    style={{ margin: 0 }}
                                />
                                <Text
                                    style={[
                                        commonStyles.baseText,
                                        {
                                            fontSize: '14px',
                                            fontWeight: 500,
                                            color: 'rgba(136, 136, 136, 1)',
                                        },
                                    ]}>
                                    BACK
                                </Text>
                            </Pressable>
                            <TextElement
                                style={{ textAlign: 'left', width: '100%', marginTop: 15 }}
                                color="SECONDARY"
                                fontSize="2xl"
                                lineHeight="xl"
                                fontWeight="600">
                                Manage Product Clips
                            </TextElement>
                            <View style={styles.itemPickerWrapper}>
                                <ShopifyItemObjectPicker
                                    itemType={'product'}
                                    value={activeProduct?.handle || ''}
                                    onChange={val => setActiveProduct(val as any)}
                                    label={'Product'}
                                    name={''}
                                    queryRunner={queryRunner}
                                />
                            </View>
                            <View style={styles.StreamConatiner}>
                            </View>
                            <View>
                                <View>
                                    <TextElement
                                        style={{ textAlign: 'left', width: '100%' }}
                                        color="SECONDARY"
                                        fontSize="xl"
                                        lineHeight="xl"
                                        fontWeight="600">
                                        Your Short Product Video Clips
                                    </TextElement>
                                </View>
                                {productVideoClips && <View style={{ display: 'flex', flexDirection: 'row', overflow: 'scroll' }}>
                                    {productVideoClips.map((entry, idx) => (
                                        <View key={idx} style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
                                            <VideoPlayer key={'video-player'+idx} options={{
                                                controls: true,
                                                responsive: true,
                                                sources: [{
                                                    src: entry.streaming_url.toString(),
                                                    type: 'video/mp4',
                                                }]
                                            }} />
                                            <View style={styles.customerProductCard}>
                                                <Image key={'product-image'+idx} source={{ uri: entry.product_info[0]?.featured_image }} style={{ width: 60, height: 60 }} />
                                                <View style={{ padding: 10, flex: 1 }}>
                                                    <Text numberOfLines={2} style={[commonStyles.baseText, styles.customerLabelText]}>
                                                        {entry.product_info[0]?.product}
                                                    </Text>
                                                </View>
                                                <Pressable onPress={() => {
                                                   entry?.pdp_rank == 1 ? setStreamInActive(entry.product_info[0]?.product_id, entry?._id) :  setStreamActive(entry.product_info[0]?.product_id, entry?._id);
                                                }} style={styles.customerAddButton}>
                                                    <Text style={[commonStyles.baseText, { color: '#FFF', fontSize: 12, fontWeight: '500' }]}>{entry?.pdp_rank == 1 ? 'IN ACTIVE STREAM' : 'ENABLE STREAM'}</Text>
                                                </Pressable>
                                            </View>
                                        </View>
                                    ))}
                                    {productVideoClips.length === 0 && (
                                        <View>
                                            {loading ? (
                                                <Image source={require('@/root/web/assets/images/preloader.svg')} style={{ width: 50, height: 50 }} />
                                            ) : (
                                                <Text>No Video Clips Generated</Text>
                                            )}
                                        </View>
                                    )}
                                </View>}
                            </View>
                        </View>
                    </ScrollView>
                </View>
            ) : (
                <View style={styles.rootContainer}>
                    <Image source={require('@/root/web/assets/images/preloader.svg')} style={{ width: 100, height: 100 }} />
                </View>
            )}
        </>
    );
};

const styles = StyleSheet.create({
    customerProductCardNew: {
        width: '-webkit-fill-available',
        marginRight: 12,
        marginLeft: 22,
        height: 70,
        borderRadius: 12,
        backgroundColor: '#00000040',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        top: 70,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: '#FFFFFF',
        position: 'absolute',
    },
    customerProductCard: {
        position: 'absolute',
        bottom: 30,
        width: 240,
        height: 60,
        borderRadius: 10,
        backgroundColor: '#00000040',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'row',
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: '#A5A5A5',
    },
    customerAddButtonNew: {
        height: 30,
        borderRadius: 27,
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderWidth: 1,
        borderColor: '#000',
        marginRight: 10,
        marginBottom: 4,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    customerAddButton: {
        height: 30,
        borderRadius: 27,
        paddingHorizontal: 10,
        paddingVertical: 6,
        borderWidth: 1,
        borderColor: '#A5A5A5',
        marginRight: 10,
        marginBottom: 4,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#000',
    },
    customerAddButtonV2: {
        height: 35,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 5,
        backgroundColor: '#000',
    },
    customerLabelText: {
        fontSize: 12,
        color: '#FFF',
        overflow: 'hidden',
        fontWeight: '600',
        marginBottom: 5,
    },
    itemPickerWrapper: { width: '100%', borderColor: 'transparent' },
    customerProductCardV2: {
        width: 150,
        borderRadius: 10,
        backgroundColor: '#0005',
        justifyContent: 'center',
        marginTop: 10,
        padding: 10,
        overflow: 'hidden',
        borderWidth: 1,
        borderColor: '#ffffff99',
    },
    rootContainer: {
        width: '100%',
        height: '90%',
        justifyContent: 'center',
        alignItems: 'center',
    },
    container: {
        backgroundColor: theme.PRIMARY_BACKGROUND,
        alignItems: 'center',
        flex: 1,
    },
    wrapper: {
        flexDirection: 'row',
        justifyContent: 'center',
        width: '100%',
    },
    contentContainer: {
        width: '95%',
        marginBottom: 30,
        paddingTop: 20,
    },
    BackButton: {
        backgroundColor: 'transparent',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-start',
        gap: 5,
    },
    StreamConatiner: {
        marginTop: 20,
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
    },
});

export default ProductClipsDashboard;
