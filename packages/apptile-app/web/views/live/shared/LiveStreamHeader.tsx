import {Image, Pressable, StyleSheet, Text, View} from 'react-native';
import React, {useState} from 'react';
import Button from '../../../components-v2/base/Button';
import ModalComponent from '../../../components-v2/base/Modal';
import InfoModal from '../infoModal';
import {logoutFromLively} from '../../../actions/liveSellingActions';
import {useDispatch, useSelector} from 'react-redux';
import {useNavigate} from 'react-router';
import {currentPlanFeaturesSelector} from '../../../selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {setOpenPremiumModal} from '../../../actions/editorActions';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

type LiveStreamHeaderProps = {
  showActionButtons?: boolean;
  fetchStreams?: () => void;
};

const LiveStreamHeader = React.forwardRef<View, LiveStreamHeaderProps>((props: LiveStreamHeaderProps, ref) => {
  const {showActionButtons, fetchStreams} = props;
  const [infoModal, setInfoModal] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isPlusDisabled = !currentPlanFeatures.includes(allAvailablePlans.PLUS);

  const handleLogout = () => {
    dispatch(logoutFromLively());
  };
  const handleRefresh = () => {
    fetchStreams();
  };

  const handleVideoClipButtonNavigate = () => {
    if (isPlusDisabled) {
      dispatch(setOpenPremiumModal(true, allAvailablePlans.PLUS ?? ' ', 'Video Clipping'));
    } else {
      navigate(`/live-selling/video-clips/dashboard`);
    }
  };

  const handleStreamSettingsNavigate = () => {
    navigate(`/live-selling/stream-settings`);
  };

  return (
    <View>
      <ModalComponent
        visible={infoModal}
        modalBackgroundStyles={{backgroundColor: 'rgba(0, 0, 0, 0.1)'}}
        onVisibleChange={setInfoModal}
        content={<InfoModal onClosePress={() => setInfoModal(false)} />}
      />
      <View ref={ref} style={[styles.headerContainer, styles.indicator, styles.justifyCenter]}>
        <View style={styles.contentContainer}>
          <Image style={styles.logo} source={require('@/root/web/assets/icons/apptile-live.svg')} />
          {showActionButtons && (
            <View style={[styles.rowLayout, {gap: 10}]}>
              <Button onPress={() => handleVideoClipButtonNavigate()} color="DEFAULT">
                Video Clips
              </Button>
              <Button onPress={() => handleStreamSettingsNavigate()} color="DEFAULT">
                Stream settings
              </Button>
              <Button icon={'plus'} onPress={() => setInfoModal(true)} color="DEFAULT">
                Add stream to website
              </Button>
              <Button
                icon={'reload'}
                onPress={handleRefresh}
                textStyles={{fontWeight: 500}}
                color="PRIMARY"
                containerStyles={{borderWidth: 0, backgroundColor: 'rgb(217,234,255)'}}>
                Refresh
              </Button>
              <Pressable onPress={handleLogout} style={styles.logoutButton}>
                {/* <Icon name="power" iconType="MaterialCommunityIcons" size={24} color={'#D92626'} /> */}
                <Text
                  style={[
                    commonStyles.baseText,
                    {
                      fontSize: 13,
                      fontWeight: 500,
                      color: 'black',
                    },
                  ]}>
                  Logout
                </Text>
              </Pressable>
            </View>
          )}
        </View>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  alignCenter: {
    alignItems: 'center',
  },
  justifyCenter: {
    justifyContent: 'center',
  },
  rowLayout: {
    flexDirection: 'row',
  },
  logo: {
    width: '185px',
    height: '35px',
    gap: '7px',
  },
  indicator: {
    backgroundColor: theme.DEFAULT_COLOR,
  },
  headerContainer: {
    width: '100%',
    height: 61,
    flexDirection: 'row',
    alignItems: 'center',
  },
  contentContainer: {
    width: '95%',
    paddingVertical: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  joinButton: {
    backgroundColor: theme.PRIMARY_COLOR,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 5,
    width: 100,
    borderColor: '#e5e5e5',
    borderRadius: 24,
    borderWidth: 2,
  },
});

export default LiveStreamHeader;
