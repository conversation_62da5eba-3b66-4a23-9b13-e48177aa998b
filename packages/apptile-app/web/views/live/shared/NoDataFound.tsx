import TextElement from '@/root/web/components-v2/base/TextElement';
import React from 'react';
import {Image, StyleSheet, View} from 'react-native';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const NoDataFound = ({text}) => {
  return (
    <View style={styles.noProducts}>
      <Image style={styles.emptyImage} source={require('../../../assets/images/snapshot-no-result.png')} />
      <TextElement style={[commonStyles.baseText, {fontSize: 13, fontWeight: '500', color: '#000'}]}>
       {text}
      </TextElement>
    </View>
  );
};

const styles = StyleSheet.create({
  noProducts: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 45,
    marginTop: 25,
    width:"100%",
    height:"100%",
    backgroundColor:"white"
  },
  emptyImage: {width: 150, height: 150},
});

export default NoDataFound;
