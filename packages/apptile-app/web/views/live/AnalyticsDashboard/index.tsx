import React, {useState, useEffect} from 'react';
import {View, Text, StyleSheet, Pressable, ScrollView, Image} from 'react-native';
import LiveStreamHeader from '../shared/LiveStreamHeader';
import {useLocation, useNavigate, useParams} from 'react-router';
import {Icon} from 'apptile-core';
import TextElement from '../../../components-v2/base/TextElement';
import StreamDetails from './StreamDetails';
import StreamAnalytics from './StreamAnalytics';
import {useDispatch, useSelector} from 'react-redux';
import {LivelyApi} from '../../../api/LivelyApi';
import LiveStreamAnalyticsApi from '../../../api/LiveStreamAnalyticsApi';
import {ApolloQueryRunner as apolloQueryRunner} from 'apptile-core';
import {GET_SHOP} from 'apptile-shopify';
import {formatDisplayPrice} from 'apptile-shopify';
import {makeToast} from '@/root/web/actions/toastActions';
import _ from 'lodash-es';
import {
  getAnalyticsServerMessage,
  geteErrorMessage,
  handleApiError,
  handleApiErrorWithoutNetwork,
} from '../shared/CommonError';
import {COMMON_ERROR_MESSAGE} from '../../../../app/common/utils/apiErrorMessages/generalMessages';
import NetworkErrorPage from '../shared/NetworkErrorPage';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const AnalyticsDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const params = useParams();
  const appId = useSelector((state: EditorRootState) => state.liveSelling.livelyAppId);
  // const appId = params?.appId;
  const streamId = params?.streamId || '';
  //Get pastStreamId from query params
  const shopableFeedId = new URLSearchParams(useLocation().search).get('shopable-feed-id');
  const authToken = useSelector(state => state.liveSelling.auth.authToken) || '';
  const [streamInfo, setStreamInfo] = useState({
    actual_end_time: '',
    streaming_name: '',
    stream_to: [],
    streaming_thumbnail: '',
    actual_start_time: '',
    streaming_duration: '',
    product_info: [],
  });
  const [streamATC, setStreamATC] = useState<number | string>(0);
  const [streamViews, setStreamViews] = useState<number | string>(0);
  const [streamtotalSales, setStreamTotalSales] = useState<number | string>('0');
  const [orderCount, setOrderCount] = useState<number | string>(0);
  const [avgOrderValue, setAvgOrderValue] = useState<number | string>('0');
  const [commentsCount, setCommentsCount] = useState<number | string>(0);
  const [loading, setLoading] = useState(false);
  const [currencyCode, setCurrencyCode] = useState(null);
  const [hasTimeData, setHasTimeData] = useState(false);
  const [showNote, setShowNote] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState('');
  const [isNetworkError, setIsNetworkError] = useState(false);
  const [commentError, setCommentError] = useState('');
  const [atcError, setAtcError] = useState('');
  const [streamInfoError, setStreamInfoError] = useState('');
  const [reachError, setReachrror] = useState('');
  const [salesError, setSalesError] = useState('');
  const [isRecentStream, setIsRecentStream] = useState(false);

  useEffect(() => {
    const fetchProductTime = async () => {
      await LivelyApi.getLiveStreamProductTime(shopableFeedId as string)
        .then(() => {
          setHasTimeData(true);
        })
        .catch(error => {
          setHasTimeData(false);
        });
    };
    if (shopableFeedId) {
      fetchProductTime();
    } else {
      console.error('shoppable feed id is missing.');
      dispatch(
        makeToast({
          content: COMMON_ERROR_MESSAGE.ERROR.RETRY_ERROR,
          appearances: 'error',
          duration: 3000,
        }),
      );
    }
  }, [shopableFeedId]);

  const fetchStreamInfo = async () => {
    try {
      const streamInfoResult = await LivelyApi.getLiveStreamInfoByID(authToken, streamId);
      const streamData = streamInfoResult?.data.data;
      if (streamData && !_.isEmpty(streamData)) {
        setStreamInfo(streamData);
        setLoading(false);
      } else {
        console.error(`At fetchStreamInfo - ${COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR}`);
        setStreamInfoError(COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR);
      }
      return streamData;
    } catch (error) {
      const errorMessage = geteErrorMessage(error, 'fetchStreamInfo');
      if (errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR) {
        setIsNetworkError(true);
      } else {
        setStreamInfoError(errorMessage);
      }
      // setLoading(false);
    }
  };

  const getComments = async () => {
    try {
      const comments = await LivelyApi.getStreamComments(authToken, streamId, 'app');
      const commentsData = comments?.data?.data;
      if (commentsData && _.isArray(commentsData)) {
        setCommentsCount(commentsData.length);
      } else {
        console.error(`At getComments - ${COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR}`);
        setCommentError(COMMON_ERROR_MESSAGE.ERROR.UNEXPECTED_ERROR);
      }
    } catch (error) {
      const errorMessage = geteErrorMessage(error, 'fetchStreamInfo');
      if (errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR) {
        setIsNetworkError(true);
      } else {
        setCommentError(errorMessage);
      }
    }
  };

  const fetchAnalytics = async () => {
    const atcPromise = LiveStreamAnalyticsApi.getStreamAddToCartEvent(appId, streamId);
    const viewsPromise = LiveStreamAnalyticsApi.getStreamViewsEvent(streamId, 'live_streaming_analytics');
    const salesPromise = LiveStreamAnalyticsApi.getStreamSalesEvent(appId, streamId);
    try {
      const [atc, views, sales] = await Promise.allSettled([atcPromise, viewsPromise, salesPromise]);
      if (atc.status === 'fulfilled' && atc.value) {
        setStreamATC(atc.value.data.result?.count);
      } else {
        const errorMessage = getAnalyticsServerMessage(atc, 'Error fetching ATC data: fetchAnalytics');
        setAtcError(errorMessage);
      }

      if (views.status === 'fulfilled' && views.value) {
        setStreamViews(views.value.data.result?.peakCount);
      } else {
        const errorMessage = getAnalyticsServerMessage(views, 'Error fetching Reach data: fetchAnalytics');
        setReachrror(errorMessage);
      }

      if (sales.status === 'fulfilled' && sales.value) {
        setStreamTotalSales(sales.value.data.result?.totalSalesValue.toString());
        setOrderCount(sales.value.data.result?.totalOrderCount);
        setAvgOrderValue(sales.value.data?.result?.averageOrderValue);
      } else {
        const errorMessage = getAnalyticsServerMessage(sales, 'Error fetching Sales data: fetchAnalytics');
        setSalesError(errorMessage);
      }
    } catch (error) {
      const errorMessage = geteErrorMessage(error, 'fetchStreamInfo');
      if (errorMessage === COMMON_ERROR_MESSAGE.ERROR.NETWORK_ERROR) {
        setIsNetworkError(true);
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (_.isEmpty(authToken) && _.isEmpty(streamId)) {
        setLoading(false);
        handleApiErrorWithoutNetwork(
          '',
          COMMON_ERROR_MESSAGE.ERROR.RETRY_ERROR,
          dispatch,
          'fetchData - authToken OR streamId',
        );
        return;
      }

      await fetchStreamInfo().then(streamInfo => {
        const startTimeDate = new Date(streamInfo?.actual_start_time);
        const referenceDate = new Date('2024-08-22T23:59:59Z'); // August 22nd, 2024

        if (startTimeDate <= referenceDate) {
          setShowWarning(true);
          setStreamATC('NA');
          setStreamViews('NA');
          setStreamTotalSales('NA');
          setOrderCount('NA');
          setAvgOrderValue('NA');
          setCommentsCount('NA');
        } else {
          getComments();
          if (!appId || _.isEmpty(appId)) {
            setLoading(false);
            handleApiErrorWithoutNetwork(
              'App id is missing',
              COMMON_ERROR_MESSAGE.ERROR.RETRY_ERROR,
              dispatch,
              'fetchData - appId',
            );
            const diffTime = new Date().getTime() - new Date(streamInfo?.actual_end_time).getTime();
            setIsRecentStream(diffTime < 4 * 60 * 60 * 1000);
            return;
          }
          fetchAnalytics().then(() => {
            setLoading(false);
          });
        }
        const diffTime = new Date().getTime() - new Date(streamInfo?.actual_end_time).getTime();
        setIsRecentStream(diffTime < 4 * 60 * 60 * 1000);
      });
    };
    if (authToken) {
      setLoading(true);
      fetchData();
    }
  }, [streamId, authToken, appId]);

  useEffect(() => {
    const fetchShopifyCreds = async () => {
      setLoading(true);
      LivelyApi.getShopifyCreds(authToken)
        .then(response => {
          const newQueryRunner = apolloQueryRunner();
          newQueryRunner
            .initClient(`https://${response?.data?.data?.store_name}/api/2024-07/graphql.json`, (_, {headers}) => {
              return {
                headers: {
                  ...headers,
                  'X-Shopify-Storefront-Access-Token': response?.data?.data?.access_token,
                },
              };
            })
            .then(async () => {
              await newQueryRunner.runQuery('query', GET_SHOP, {}, {}).then(res => {
                const moneyFormatter = formatDisplayPrice(res.data.shop);
                setCurrencyCode(() => moneyFormatter);
                // setLoading(false);
              });
            });
        })
        .catch(error => {
          if (error.code === 'ERR_NETWORK') {
            setIsNetworkError(true);
          }
          setLoading(false);
          handleApiError(error, COMMON_ERROR_MESSAGE.ERROR.SHOPIFY_ERROR, dispatch, 'getShopifyCreds', false);
        });
    };

    if (authToken) {
      fetchShopifyCreds();
    }
  }, [authToken]);

  useEffect(() => {
    const setClock = () => {
      if (!streamInfo?.actual_end_time) {
        logger.error('End time is not available.');
        return;
      }
      const endTimeDate = new Date(streamInfo.actual_end_time);
      const endTimeInSeconds = endTimeDate.getTime() / 1000;
      const currentTimeDate = new Date();
      const currentTimeInSeconds = currentTimeDate.getTime() / 1000;
      const twelveHoursInSeconds = 12 * 3600;

      const endTimePlusTwelveHoursInSeconds = endTimeInSeconds + twelveHoursInSeconds;
      if (currentTimeInSeconds < endTimePlusTwelveHoursInSeconds) {
        setShowNote(true);
        setTimeRemaining('Please check back for analytics 12 hours after the stream ends.');
      } else {
        setShowNote(false);
        setTimeRemaining('');
      }
    };
    setClock();
  }, [streamInfo]);

  const handleToast = (message: string) => {
    dispatch(
      makeToast({
        content: message,
        appearances: 'error',
        duration: 3000,
      }),
    );
  };

  return (
    <>
      <LiveStreamHeader showActionButtons={false} />
      {isNetworkError ? (
        <NetworkErrorPage />
      ) : !loading ? (
        <View style={styles.container}>
          <ScrollView style={{width: '100%'}} contentContainerStyle={styles.wrapper}>
            <View style={styles.contentContainer}>
              <Pressable onPress={() => navigate(-1)} style={styles.BackButton}>
                <Icon
                  name="chevron-left"
                  iconType="MaterialCommunityIcons"
                  size={24}
                  color={'rgba(136, 136, 136, 1)'}
                  style={{margin: 0}}
                />
                <Text
                  style={[
                    commonStyles.baseText,
                    {
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'rgba(136, 136, 136, 1)',
                    },
                  ]}>
                  BACK
                </Text>
              </Pressable>

              <TextElement
                style={{textAlign: 'left', width: '100%', marginTop: 15}}
                color="SECONDARY"
                fontSize="2xl"
                lineHeight="xl"
                fontWeight="600">
                {streamInfo?.streaming_name}
              </TextElement>
              {!hasTimeData && (
                <View style={styles.note}>
                  <Icon name="information-outline" iconType="MaterialCommunityIcons" size={16} />
                  <Text style={[commonStyles.baseText, {fontSize: 12}]}>
                    The time-seek feature is unavailable in this stream because the live app was running an outdated
                    version.
                  </Text>
                </View>
              )}
              {isRecentStream && (
                <View style={styles.note}>
                  <Icon name="information-outline" iconType="MaterialCommunityIcons" size={16} />
                  <Text style={[commonStyles.baseText, {fontSize: 12}]}>
                    It can take up to 4 hours for a past stream recording to process and become available in the mobile
                    app.
                  </Text>
                </View>
              )}
              {showNote && (
                <View style={styles.note}>
                  <Icon name="information-outline" iconType="MaterialCommunityIcons" size={16} />
                  <Text style={[commonStyles.baseText, {fontSize: 12}]}>{timeRemaining}</Text>
                </View>
              )}

              {showWarning && (
                <View style={styles.note}>
                  <Icon name="information-outline" iconType="MaterialCommunityIcons" size={16} />
                  <Text style={[commonStyles.baseText, {fontSize: 13}]}>
                    Live Stream Analytics will be available for Live Streams happened after 22nd August 2024
                  </Text>
                </View>
              )}

              <View style={styles.StreamConatiner}>
                <StreamDetails streamInfo={streamInfo} streamInfoError={streamInfoError} handleToast={handleToast} />
                <StreamAnalytics
                  streamATC={streamATC}
                  streamViews={streamViews}
                  streamtotalSales={streamtotalSales}
                  orderCount={orderCount}
                  commentsCount={commentsCount}
                  currencyCode={currencyCode}
                  avgOrderValue={avgOrderValue}
                  showWarning={showWarning}
                  commentError={commentError}
                  salesError={salesError}
                  reachError={reachError}
                  handleToast={handleToast}
                  atcError={atcError}
                />
              </View>
            </View>
          </ScrollView>
        </View>
      ) : (
        <View style={styles.rootContainer}>
          <Image source={require('@/root/web/assets/images/preloader.svg')} style={{width: 100, height: 100}} />
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  customerProductCardNew: {
    width: '-webkit-fill-available',
    marginRight: 12,
    marginLeft: 22,
    height: 70,
    borderRadius: 12,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    top: 70,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#FFFFFF',
    position: 'absolute',
  },
  customerProductCard: {
    position: 'absolute',
    bottom: 30,
    width: 240,
    height: 60,
    borderRadius: 10,
    backgroundColor: '#00000040',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#A5A5A5',
  },
  customerAddButtonNew: {
    height: 30,
    borderRadius: 27,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#000',
    marginRight: 10,
    marginBottom: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  customerAddButton: {
    height: 30,
    borderRadius: 27,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: '#A5A5A5',
    marginRight: 10,
    marginBottom: 4,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000',
  },
  customerAddButtonV2: {
    height: 35,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 5,
    backgroundColor: '#000',
  },
  customerLabelText: {
    fontSize: 12,
    color: '#FFF',
    overflow: 'hidden',
    fontWeight: '600',
    marginBottom: 5,
  },
  customerProductCardV2: {
    width: 150,
    borderRadius: 10,
    backgroundColor: '#0005',
    justifyContent: 'center',
    marginTop: 10,
    padding: 10,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ffffff99',
  },
  rootContainer: {
    width: '100%',
    height: '90%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
    alignItems: 'center',
    flex: 1,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  contentContainer: {
    width: '95%',
    marginBottom: 30,
    paddingTop: 20,
  },
  BackButton: {
    backgroundColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: 5,
  },
  StreamConatiner: {
    marginTop: 20,
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
  },
  note: {
    backgroundColor: '#FBFBFB',
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 6,
    gap: 10,
    alignSelf: 'baseline',
    marginTop: 10,
    width: '100%',
    padding: 10,
  },
});

export default AnalyticsDashboard;
