import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import QRCode from 'react-qr-code';

import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface IContentDisplay {
  text: string;
  boldText?: string;
  subText: string | null;
  qrLink: string;
}

export const ContentDisplay: React.FunctionComponent<IContentDisplay> = ({text, subText, qrLink, boldText}) => {
  return (
    <View style={styles.wrapper}>
      <View style={styles.qrWrapper}>
        <QRCode size={256} style={QRCodeStyle} value={qrLink} viewBox={`0 0 256 256`} />
      </View>
      <View style={styles.textWrapper}>
        <Text style={[commonStyles.baseText, styles.text]}>
          {text}{' '}
          <Text style={[commonStyles.baseText, {fontWeight: '500', fontSize: 14, color: '#000000'}]}>{boldText}</Text>
        </Text>
        <Text style={[commonStyles.baseText, styles.subText]}>{subText}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  text: {
    fontSize: 14,
    lineHeight: 20,
  },
  subText: {
    marginTop: 20,
    fontSize: 14,
    lineHeight: 20,
  },
  textWrapper: {
    width: 222,
  },
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    width: '100%',
  },
  qrWrapper: {
    borderWidth: 1,
    borderColor: '#000000',
    padding: 10,
    borderRadius: 12,
  },
});

const QRCodeStyle = {
  width: 153,
  height: 153,
  background: '#FFFFFF',
};
