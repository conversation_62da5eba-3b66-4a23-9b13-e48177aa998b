import React, {useEffect, useLayoutEffect, useState} from 'react';
import {StyleSheet, View, Text, Image, Pressable} from 'react-native';

import {Route, Routes, useNavigate, useParams} from 'react-router';

import _ from 'lodash';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import {ScrollView} from 'react-native';
import {callWebflowApis, resetMandtoryFields} from '../../actions/onboardingActions';
import OnboardingLoader from '../onboarding/components/onboardingLoader';
import Analytics from '@/root/web/lib/segment';
import Button from '../../components-v2/base/Button';
import {useIntercom} from 'react-use-intercom';
import {MaterialCommunityIcons} from 'apptile-core';
import {checkApptileEmailSelector, currentPlanFeaturesSelector} from '../../selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {fetchMyAddOns, fetchIntegrations} from '../../actions/editorActions';
import {getMyAddOns, selectCurrentPlanWithDetails} from '../../selectors/BillingSelector';
import TextElement from '../../components-v2/base/TextElement';
import {AddOnOrderSummery} from '../subscription/components/subscription/AddOnOrderSummery';
import ModalComponent from '../../components-v2/base/Modal';
import {ConformationModal} from './components/ConformationModal';
import imagePlanMapping from '../../common/featureGatingConstants';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const isEven = (value: number) => {
  if (value % 2 === 0) return true;
  else return false;
};

const MyTheme: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {orgId, id: appId, branchName, forkId} = useParams();
  const currentPlan = useSelector(selectCurrentPlanWithDetails);
  const {showNewMessage} = useIntercom();
  const [purchase, setPurchase] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState({});
  const [currentTheme, setCurrentTheme] = useState();
  const {blueprints} = useSelector((state: EditorRootState) => state.blueprint);
  const onPurchaseModalVisibilityChange = (value: boolean) => {
    if (!value) {
      setPurchase(false);
    }
  };
  const onConfirmModalVisibilityChange = (value: boolean) => {
    if (!value) {
      setCurrentTheme(undefined);
    }
  };

  useEffect(() => {
    dispatch(fetchMyAddOns(appId as string));
    dispatch(fetchIntegrations());
  }, []);

  const allIntegrationsById = useSelector((state: EditorRootState) => state.integration.allIntegrationsById);

  const requiredIntegrations = Object.values(allIntegrationsById);

  const addOns = useSelector(getMyAddOns);

  useLayoutEffect(() => {
    dispatch(resetMandtoryFields());
  }, [dispatch, blueprints]);
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isPremiumDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);
  const getMessage = (theme: any) => {
    return `Please activate theme ${theme?.name} (${theme?.slug}) for my store`;
  };

  const isApptileUser = useSelector(checkApptileEmailSelector);
  let isPublished = useSelector((state: EditorRootState) => state.apptile.isPublished);

  return (
    <View style={styles.rootContainer}>
      {!isApptileUser && isPublished && (
        <View style={styles.disableThemeWrapper}>
          <View style={styles.disableThemeSubWrapper}>
            <Text
              style={[
                commonStyles.baseText,
                {
                  color: '#fff',
                  fontSize: 20,
                  fontWeight: '700',
                  marginBottom: 20,
                },
              ]}>
              Your app is currently live.
            </Text>
            <Text
              style={[
                commonStyles.baseText,
                {
                  color: '#fff',
                  fontSize: 16,
                  marginTop: 10,
                  marginBottom: 20,
                },
              ]}>
              Please talk to us for a theme switch.
            </Text>
            <Button color="SECONDARY" size="LARGE" onPress={() => showNewMessage()}>
              Talk to us
            </Button>
          </View>
        </View>
      )}
      <ScrollView>
        <View style={styles.bodyContainer}>
          <View style={styles.bodyTitleContainer}>
            <Text style={styles.bodyTitleText}>Select a theme for your app.</Text>
          </View>

          <View style={styles.themesRootContainer}>
            {/* <Text style={styles.themesRootTitleText}>
              Popular Themes <MaterialCommunityIcons name={'star'} size={20} color={'#EDDF5E'} />
            </Text> */}

              <View style={styles.themeContainer}>
                {blueprints
                  ?.filter((theme: any) => theme?.slug != 'custom-theme')
                  ?.map((theme, index) => (
                    <View style={styles.themeSubContainer} key={index}>
                      <Pressable
                        onPress={() => {
                          // window.open('https://apptile.com/theme/');
                        }}
                        style={styles.themeImageContainer}>
                        <Image
                          //onMouseEnter={() => handleMouseEnter(index)}
                          //onMouseLeave={() => handleMouseLeave(index)}
                          style={styles.themeImage}
                          source={{uri: theme?.['thumbnailUrl']}}
                        />
                        {theme?.premium && (
                          <>
                            <View style={styles.premiumBadge}>
                              <Image
                                source={imagePlanMapping['SMALL']}
                                resizeMode="contain"
                                style={{width: 30, height: 30, position: 'absolute'}}
                              />
                            </View>
                            {_.find(addOns, {integrationCode: theme?.slug}) && (
                              <View style={styles.purchasedBadge}>
                                <TextElement fontSize="xs" color="SECONDARY">
                                  PURCHASED
                                </TextElement>
                              </View>
                            )}
                          </>
                        )}
                      </Pressable>

                      {theme?.['slug'] !== 'custom-theme' && (
                        <>
                          <View style={styles.hoverButtonsContainer}>
                            {theme?.['selfServe'] ? (
                                theme?.['themePreviewAppId'] && 
                              <Button
                                onPress={() => {
                                  Analytics.track('dashboard:themeListing_previewClicked', {
                                    theme: theme?.slug,
                                  });
                                  navigate(
                                    `/dashboard/${orgId}/app/${appId}/theme-preview/${theme?.slug}?fromDashboard=true`,
                                  );
                                }}
                                size="LARGE"
                                variant="PILL"
                                color="PRIMARY"
                                textStyles={styles.hoverButtonText}
                                containerStyles={styles.previewButton}>
                                Live preview
                              </Button>
                            ) : (
                              <Button
                                onPress={() => {
                                  Analytics.track('dashboard:themeListing_themeDetailsClicked', {
                                    theme: theme?.slug,
                                  });
                                  window.open(`https://apptile.com/theme/${theme?.slug}`);
                                }}
                                size="LARGE"
                                variant="PILL"
                                color="PRIMARY"
                                textStyles={styles.hoverButtonText}
                                icon="open-in-new"
                                iconPosition="RIGHT"
                                containerStyles={styles.previewButton}>
                                Theme details
                              </Button>
                            )}
                            {theme?.premium && isPremiumDisabled && !_.find(addOns, {integrationCode: theme?.slug}) ? (
                              _.find(requiredIntegrations, {integrationCode: theme?.slug})?.monthlyPrice ? (
                                <Button
                                  textStyles={styles.hoverButtonText}
                                  onPress={() => {
                                    Analytics.track(`purchase:theme_getButton`, {
                                      theme: theme?.slug,
                                    });
                                    setPurchase(true);
                                    setSelectedTheme(
                                      _.find(requiredIntegrations, {integrationCode: theme?.slug}) ?? {},
                                    );
                                  }}
                                  size="LARGE"
                                  color="DEFAULT"
                                  containerStyles={styles.UseTemplateButton}>
                                  Get for ${_.find(requiredIntegrations, {integrationCode: theme?.slug})?.monthlyPrice}
                                  /month
                                </Button>
                              ) : (
                                <Button
                                  textStyles={styles.hoverButtonText}
                                  onPress={() => showNewMessage(getMessage(theme))}
                                  size="MEDIUM"
                                  color="CTA"
                                  containerStyles={styles.UseTemplateButton}>
                                  Talk to us
                                </Button>
                              )
                            ) : theme?.['selfServe'] ? (
                              <Button
                                textStyles={styles.hoverButtonText}
                                onPress={() => {
                                  Analytics.track('dashboard:themeListing_useThemeClicked', {
                                    theme: theme?.slug,
                                  });
                                  setCurrentTheme(theme);
                                  onConfirmModalVisibilityChange(true);
                                }}
                                size="LARGE"
                                color="CTA"
                                containerStyles={{...styles.UseTemplateButton, width: theme?.['themePreviewAppId'] ? 200 : '100%'}}>
                                Use theme
                              </Button>
                            ) : (
                              <Button
                                textStyles={styles.hoverButtonText}
                                onPress={() => showNewMessage(getMessage(theme))}
                                size="LARGE"
                                color="CTA"
                                containerStyles={styles.UseTemplateButton}>
                                Talk to us
                              </Button>
                            )}
                          </View>
                          <Text style={styles.themeTitle}>{theme?.name}</Text>
                        </>
                      )}
                    </View>
                  ))}
                {!isEven(blueprints?.length || 0) && <View style={styles.themeSubContainer} />}

                {/* all modals */}
                <View style={{position: 'absolute'}}>
                  {purchase && selectedTheme && (
                    <ModalComponent
                      visible={purchase}
                      modalBackgroundStyles={{backgroundColor: 'rgba(0, 0, 0, 0.1)'}}
                      onVisibleChange={onPurchaseModalVisibilityChange}
                      content={
                        <View style={{width: '100%', alignItems: 'flex-start', padding: 30}}>
                          <Pressable onPress={() => setPurchase(false)}>
                            <MaterialCommunityIcons name="arrow-left" size={25} color={'#000000'} />
                          </Pressable>
                          <AddOnOrderSummery
                            currentIntegration={selectedTheme ?? {}}
                            plan={currentPlan}
                            currentAddOnPrice={Number(selectedTheme?.monthlyPrice)}
                          />
                        </View>
                      }
                    />
                  )}
                  {!!currentTheme && (
                    <ModalComponent
                      visible={!!currentTheme}
                      modalBackgroundStyles={{backgroundColor: 'rgba(0, 0, 0, 0.1)'}}
                      onVisibleChange={onConfirmModalVisibilityChange}
                      content={
                        <>
                          <ConformationModal
                            themeSlug={currentTheme?.name}
                            onConfirmPress={() => {
                              navigate(
                                `/dashboard/${orgId}/app/${appId}/f/${forkId}/b/${branchName}/overlay/themes/shift-theme/${currentTheme?.slug}`,
                              );
                            }}
                            setCurrentTheme={setCurrentTheme}
                          />
                          <Pressable
                            onPress={() => {
                              setCurrentTheme(undefined);
                            }}
                            style={styles.modalCloseIcon}>
                            <MaterialCommunityIcons name="close" size={20} color="#000000" />
                          </Pressable>
                        </>
                      }
                    />
                  )}
                </View>
              </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  disableThemeWrapper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  disableThemeSubWrapper: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 5,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
    paddingHorizontal: 100,
  },
  rootContainer: {
    height: '100vh',
    backgroundColor: '#ECE9E1',
    fontFamily: theme.FONT_FAMILY,
    flex: 1,
  },
  themeSubContainer: {marginBottom: 40, width: 412},
  headerContainer: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 72,
  },
  purchasedBadge: {
    position: 'absolute',
    backgroundColor: '#F4E4FA',
    padding: 5,
    right: 55,
    width: 90,
    top: 15,
    borderRadius: 7,
    alignItems: 'center',
  },
  bodyContainer: {
    marginLeft: '5%',
    marginRight: '5%',
    alignItems: 'center',
  },
  bodyTitleContainer: {
    alignItems: 'center',
    width: '100%',
  },
  themesRootContainer: {
    marginTop: 16,
    marginBottom: 65,
    justifyContent: 'center',
    flex: 1,
  },
  hoverSubContainer: {
    position: 'relative',
    width: '100%',
    flexDirection: 'row',
  },
  hoverButtonsContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  hoverButtonText: {fontSize: 14, fontFamily: theme.FONT_FAMILY, alignSelf: 'center'},
  themeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    columnGap: 32,
    marginTop: 30,
  },
  pillContainer: {
    backgroundColor: '#E0D6C6',
    height: 30,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 11,
    paddingRight: 11,
    marginRight: 6,
    marginBottom: 6,
  },
  pillText: {
    color: '#858585',
    fontSize: 14,
    fontFamily: theme.FONT_FAMILY,
  },
  UseTemplateButton: {
    backgroundColor: '#1060E0',
    borderWidth: 0,
    width: 200,
    height: 42,
  },
  previewButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 0,
    width: 182,
    height: 42,
  },
  pillsContainer: {
    flexDirection: 'row',
    marginTop: 13,
    width: '100%',
    flexWrap: 'wrap',
  },

  themeImageContainer: {
    position: 'relative',
    width: '100%',
    height: 'auto',
    aspectRatio: '459/392',
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#BEBEBE',
    overflow: 'hidden',
  },

  modalCloseIcon: {
    position: 'absolute',
    right: 20,
    top: 20,
    zIndex: 9,
  },

  themeImage: {
    width: '100%',
    height: '100%',
  },

  hoverBackgroundImage: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },

  themeHoverContainer: {
    height: 140,
    position: 'absolute',
    width: '100%',
    bottom: 0,
    left: 0,
    right: 0,
  },

  themeTitle: {
    fontSize: 20,
    fontWeight: '500',
    marginTop: 20,
    fontFamily: theme.FONT_FAMILY,
    textAlign: 'center',
  },
  themeDescription: {
    fontSize: 14,
    fontWeight: '400',
    color: '#858585',
    marginTop: 4,
    fontFamily: theme.FONT_FAMILY,
  },
  themesRootTitleText: {
    fontSize: 24,
    fontWeight: '500',
    marginBottom: 30,
  },
  bodyTitleText: {
    marginTop: 48,
    fontSize: 24,
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '500',
  },
  bodyTitleSubText: {
    marginTop: 30,
    fontSize: 18,
    color: '#848484',
  },
  backButton: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    left: 70,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: theme.ONBOARDING_FONT_WEIGHT,
    fontFamily: theme.FONT_FAMILY,
    marginLeft: 10,
    color: '#535353',
  },
  premiumBadge: {
    position: 'absolute',
    right: 45,
    top: 15,
  },
});

export const ThemeRouter = () => {
  return (
    <Routes>
      <Route path="themes" element={<MyTheme />} />
    </Routes>
  );
};
