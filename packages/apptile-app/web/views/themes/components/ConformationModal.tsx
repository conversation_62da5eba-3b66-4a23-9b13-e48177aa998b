import TextElement from '@/root/web/components-v2/base/TextElement';
import TextInput from '@/root/web/components-v2/base/TextInput';
import React, {useEffect, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import Button from '@/root/web/components-v2/base/Button';

export const ConformationModal = ({
  onConfirmPress,
  themeSlug,
  setCurrentTheme,
}: {
  onConfirmPress: (prePopulatedContent?: string | undefined) => void;
  themeSlug: string;
  setCurrentTheme: any;
}) => {
  const [input, setInput] = useState('');
  const [disableUseTheme, setDisableUseTheme] = useState(true);

  useEffect(() => {
    if (input?.toLowerCase() === 'confirm') {
      setDisableUseTheme(false);
    } else {
      setDisableUseTheme(true);
    }
  }, [input]);

  return (
    <View style={styles.wrapper}>
      <TextElement color="SECONDARY" fontSize="xl">
        CHANGE YOUR APP THEME
      </TextElement>
      <View style={styles.contentWrapper}>
        <TextElement color="SECONDARY" style={styles.activateText}>
          Activate{' '}
          <TextElement color="SECONDARY" fontWeight="500">
            {themeSlug}
          </TextElement>{' '}
          theme for your store
        </TextElement>
        <View style={styles.noteWrapper}>
          <TextElement style={[{color: '#E84646'}, styles.font14]}>Note:</TextElement>
          <TextElement color="SECONDARY" style={[styles.font14, {textAlign: 'center'}]}>
            Your design changes within the current theme will be lost!
          </TextElement>
        </View>
        <View style={styles.inputWrapper}>
          <TextInput
            containerStyle={styles.inputStyles}
            value={input}
            placeholder="Type “Confirm” to change"
            onChange={(e: any) => {
              setInput(e.target.value);
            }}
          />
          <Button
            containerStyles={styles.buttonStyles}
            onPress={() => {
              onConfirmPress();
              setCurrentTheme(undefined);
            }}
            disabled={disableUseTheme}
            color="CTA">
            Use this Theme
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  inputStyles: {
    width: 281,
    height: 47,
    borderRadius: 8,
  },
  contentWrapper: {
    alignItems: 'center',
  },
  wrapper: {
    paddingVertical: 40,
    paddingHorizontal: 56,
    alignItems: 'center',
  },
  noteWrapper: {
    flexDirection: 'row',
    marginTop: 15,
    width: 315,
    gap: 10,
  },
  activateText: {
    marginTop: 41,
    textAlign: 'center',
    fontSize: 14,
  },
  inputWrapper: {
    flexDirection: 'row',
    gap: 15,
    marginTop: 40,
  },
  buttonStyles: {
    width: 143,
    height: 47,
  },
  font14: {
    fontSize: 14,
  },
});
