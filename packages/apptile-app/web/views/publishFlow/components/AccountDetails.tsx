import TextElement from '@/root/web/components-v2/base/TextElement';
import RadioGroupControl from '@/root/web/components/controls/RadioGroupControl';
import React, {useEffect, useState} from 'react';
import {View, StyleSheet, Text, Pressable} from 'react-native';
import {IFormData} from '..';
import Tooltip from '@/root/web/components-v2/base/Tooltip/Index';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const AccountDetails: React.FC<{
  onSelection: (value: string) => any;
  onFormChange: (data: boolean, field: string) => void;
  formData: IFormData;
}> = ({onSelection, onFormChange, formData}) => {
  const [value, setValue] = useState(formData.hasDeveloperAccount ? 'Yes' : 'No');

  useEffect(() => {
    onSelection(formData.hasDeveloperAccount ? 'Yes' : 'No');
  }, [value, formData.hasDeveloperAccount]);

  return (
    <View style={styles.wrapper}>
      <TextElement color="SECONDARY" fontSize="sm" style={styles.marginBottom20}>
        A developer account is needed to launch apps on the Apple App Store and Google Play Store.
      </TextElement>
      <View style={styles.contentWrapper}>
        <TextElement color="SECONDARY" fontSize="sm" style={styles.marginBottom20}>
          Do you have a developer account?
        </TextElement>
        <RadioGroupControl
          label=""
          value={value}
          onChange={(newValue: string) => {
            setValue(newValue);
            onFormChange(newValue === 'Yes' ? true : false, 'hasDeveloperAccount');
          }}
          defaultValue={value}
          options={['Yes', 'No']}
          disableBinding={true}
        />
      </View>
      {value === 'No' && (
        <TextElement color="SECONDARY" fontSize="xs" lineHeight="md">
          Note: Don’t worry, we can launch the apps on our developer account. ‘Apptile’ will appear as the developer on
          the app store listing, and transferring the app to your own developer account later is easy and takes 1-2
          days.
        </TextElement>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    paddingVertical: 30,
  },
  marginBottom20: {
    marginBottom: 20,
  },
  titleInput: {width: 191, padding: 8, fontSize: 13},
  marginBottom5: {
    marginBottom: 5,
  },
  contentWrapper: {
    width: 300,
    marginBottom: 20,
  },
  descriptionWrapper: {},
  inputWrapper: {},
});

export default AccountDetails;
