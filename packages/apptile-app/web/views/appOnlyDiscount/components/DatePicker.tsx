import React, {useEffect} from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';
import Modal from '@/root/web/components-v2/base/Modal';
import {TimePickerModal, DatePickerModalContent} from 'rn-dates';
import moment from 'moment';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import Button from '@/root/web/components-v2/base/Button';
import TextElement from '@/root/web/components-v2/base/TextElement';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface DateAndTimeControlProps {
  value: string;
  defaultValue?: string;
  placeholder?: string;
  onChange: (value: string) => void;
  showDate?: boolean;
  disabled?: boolean;
}

const styles = StyleSheet.create({
  container: {
    flexBasis: 'auto',
    marginVertical: theme.PRIMARY_MARGIN,
  },
  dateTimePickerContainer: {
    width: 328,
    borderRadius: 28,
  },
  DateInputStyle: {
    padding: 10,
  },
  TimeInputStyle: {
    marginTop: 10,
    padding: 10,
  },
  icon: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  modalTop: {
    padding: 14,
    borderBottomColor: theme.INPUT_BORDER,
    borderBottomWidth: 1,
  },
  modalMiddle: {
    paddingHorizontal: 10,
  },
  modalBottom: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopColor: theme.INPUT_BORDER,
    borderTopWidth: 1,
  },
});

const DatePicker: React.FC<DateAndTimeControlProps> = ({onChange, value, disabled}) => {
  const [isDatePickerVisible, setDatePickerVisible] = React.useState(false);
  const [isTimePickerVisible, setTimePickerVisible] = React.useState(false);
  const currentDate = new Date(new Date().getTime() + 360000);
  const [selectedDate, setSelectedDate] = React.useState(
    new Date(value).toString() === 'Invalid Date' ? currentDate : new Date(value),
  );

  useEffect(() => {
    const bindCheck = typeof value === 'string' && value.startsWith('{{') ? true : false;
    const dateValue = new Date(value).toString() === 'Invalid Date' ? currentDate : new Date(value);
    if (!bindCheck && selectedDate.getTime() != dateValue.getTime()) setSelectedDate(dateValue);
  }, [selectedDate.getTime(), value]);

  return (
    <>
      <View style={styles.container}>
        <View style={[disabled && {opacity: '50%'}]}>
          <Pressable
            style={[styles.DateInputStyle, commonStyles.input]}
            disabled={disabled}
            onPress={() => {
              setDatePickerVisible(true);
            }}>
            {!disabled ? (
              <Text style={commonStyles.inputText}>
                {('00' + selectedDate.getDate()).slice(-2) +
                  '/' +
                  ('00' + (selectedDate.getMonth() + 1)).slice(-2) +
                  '/' +
                  selectedDate.getFullYear()}
              </Text>
            ) : (
              <Text style={commonStyles.inputText}>null</Text>
            )}

            <View style={styles.icon}>
              <ApptileWebIcon name={'calender'} size={18} />
            </View>
          </Pressable>
        </View>
      </View>

      <Modal
        onVisibleChange={setDatePickerVisible}
        visible={isDatePickerVisible}
        content={
          <DateTimePicker
            pickerType="DATE"
            date={selectedDate}
            onChange={onChange}
            setPickerVisible={setDatePickerVisible}
          />
        }
      />
      <Modal
        onVisibleChange={setTimePickerVisible}
        visible={isTimePickerVisible}
        content={
          <DateTimePicker
            pickerType="TIME"
            date={selectedDate}
            onChange={onChange}
            setPickerVisible={setTimePickerVisible}
          />
        }
      />
    </>
  );
};

type DateTimePickerProps = {
  onChange: (value: string) => void;
  date: Date;
  pickerType: 'DATE' | 'TIME';
  setPickerVisible: (value: boolean) => void;
};
const DateTimePicker: React.FC<DateTimePickerProps> = props => {
  const [currentComp, setCurrentComp] = React.useState<'DATE' | 'TIME'>(props.pickerType || 'DATE');
  const {onChange, date, setPickerVisible} = props;
  const [currentDate, setCurrentDate] = React.useState<Date>(date);

  const themeValue = {
    secondaryColor: '#fff',
    primaryColor: theme.CONTROL_ACTIVE_COLOR,
    accentColor: theme.CONTROL_INPUT_COLOR,
    backgroundColor: theme.INPUT_BACKGROUND,
    fontFamily: theme.FONT_FAMILY,
  };

  const onDateChange = React.useCallback(
    params => {
      let pickedDate = new Date(params.date);
      let updatedHours = new Date(pickedDate.setHours(currentDate.getHours()));
      let updatedMinutes = new Date(updatedHours.setMinutes(currentDate.getMinutes()));
      let updatedSeconds = new Date(updatedMinutes.setSeconds(0));
      setCurrentDate(new Date(updatedSeconds));
    },
    [currentDate],
  );

  const onTimeChange = React.useCallback(
    ({hours, minutes}) => {
      let pickedDate = currentDate;
      let updatedHours = new Date(pickedDate.setHours(hours));
      let updatedMinutes = new Date(updatedHours.setMinutes(minutes));
      let updatedSeconds = new Date(updatedMinutes.setSeconds(0));
      setCurrentDate(new Date(updatedSeconds));
    },
    [currentDate],
  );
  const mCurrentDate = moment(currentDate);
  const mToday = moment().startOf('day');

  const isDateDisabled = !mCurrentDate.isSameOrAfter(mToday);

  return (
    <View style={styles.dateTimePickerContainer}>
      <View style={styles.modalTop}>
        <Text style={commonStyles.baseText}>Select {currentComp == 'DATE' ? 'Date' : 'Time'}</Text>
        {currentComp === 'DATE' && (
          <View style={{marginTop: 25, flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={[commonStyles.baseText, {fontSize: 30, lineHeight: 32}]}>
              {currentDate.toDateString()?.slice(0, -4)}
            </Text>
            <ApptileWebIcon name="edit-icon" size={30} />
          </View>
        )}
      </View>
      <View style={styles.modalMiddle}>
        {currentComp === 'DATE' && (
          <DatePickerModalContent
            locale="en"
            mode="single"
            date={currentDate}
            onChange={onDateChange}
            validRange={{
              startDate: new Date(),
              endDate: moment().add(30, 'days').toDate(),
            }}
            themeValue={themeValue}
          />
        )}
        {currentComp === 'TIME' && (
          <TimePickerModal
            modal={false}
            visible={true}
            hours={currentDate.getHours()}
            minutes={currentDate.getMinutes()}
            uppercase={true}
            onChange={onTimeChange}
            themeValue={themeValue}
          />
        )}
      </View>
      {isDateDisabled && (
        <View>
          <Text
            style={[commonStyles.baseText, {color: theme.ERROR_BACKGROUND, paddingBottom: 30, textAlign: 'center'}]}>
            Date &amp; Time can not be in the past.
          </Text>
        </View>
      )}
      <View style={styles.modalBottom}>
        <Button
          variant="TEXT"
          color="SECONDARY"
          onPress={() => {
            setPickerVisible(false);
          }}>
          Cancel
        </Button>
        <Button
          variant="TEXT"
          color="SECONDARY"
          disabled={isDateDisabled}
          onPress={() => {
            setPickerVisible(false);
            onChange(new Date(currentDate).toISOString());
          }}>
          Ok
        </Button>
      </View>
    </View>
  );
};

export default DatePicker;
