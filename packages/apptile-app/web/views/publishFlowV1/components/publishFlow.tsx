import React from 'react';
import {View, StyleSheet} from 'react-native';
import Header from './header';
import Stepper from './stepper';
import Footer from './footer';
import AppDetails from './appDetails';
import FirebaseDetails from './firebasedetails';
import {usePublishContext} from '../context';
import BuildDetails from './buildDetails';
import BuildList from './buildsList';

const PublishFlow: React.FC = () => {
  const {step} = usePublishContext();

  const renderStep = () => {
    switch (step) {
      case 1:
        return <AppDetails />;
      case 2:
        return <FirebaseDetails />;
      case 3:
        return <BuildDetails />;
      case 4:
        return <BuildList />;
      default:
        return null;
    }
  };

  return (
    <div style={styles.container}>
      <Header />
      <div style={styles.wrapper}>
        <Stepper />
        <div style={styles.content}>{renderStep()}</div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
    overflow: 'hidden',
  },
  wrapper: {
    flex: 1,
    padding: 20,
    backgroundColor: '#ECE9E1',
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  content: {
    flex: 1,
    overflow: 'auto',
    height: 0, // This forces the content to respect parent height
  },
};

export default PublishFlow;
