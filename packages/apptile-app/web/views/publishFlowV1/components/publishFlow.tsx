import React from 'react';
import {View, StyleSheet, ScrollView} from 'react-native';
import Header from './header';
import Stepper from './stepper';
import Footer from './footer';
import AppDetails from './appDetails';
import FirebaseDetails from './firebasedetails';
import {usePublishContext} from '../context';
import BuildDetails from './buildDetails';
import BuildList from './buildsList';

const PublishFlow: React.FC = () => {
  const {step} = usePublishContext();

  const renderStep = () => {
    switch (step) {
      case 1:
        return <AppDetails />;
      case 2:
        return <FirebaseDetails />;
      case 3:
        return <BuildDetails />;
      case 4:
        return <BuildList />;
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <Header />
      <View style={styles.wrapper}>
        <Stepper />
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          nestedScrollEnabled={true}>
          {renderStep()}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    flex: 1,
    height: '100vh',
    overflow: 'hidden',
  },
  wrapper: {
    flex: 1,
    padding: 20,
    backgroundColor: '#ECE9E1',
    display: 'flex',
    flexDirection: 'column',
  },
  content: {
    flex: 1,
    minHeight: 0, // This forces the ScrollView to respect its parent's height
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
});

export default PublishFlow;
