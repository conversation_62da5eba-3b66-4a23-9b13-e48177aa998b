import {debounce} from 'lodash';
import {connect} from 'react-redux';
import {bindActionCreators} from 'redux';
import {
  pluginConfigUpdate,
  pluginConfigDeleteEvent,
  pluginConfigEventUpdate,
  pluginLayoutUpdate,
  pluginStyleUpdate,
  navConfigUpdate,
  navUpdateName,
  navComponentDelete,
  updatePageId,
  updatePageConfig,
  deletePageConfig,
} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {AppDispatch} from '../../../../app/store';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {
  pluginUpdateId,
  pluginDelete,
  createModuleFromWidget,
  reorderEventUpward,
  reorderEventDownward,
} from '../../../actions/editorActions';
import {
  selectNavComponentSelector,
  selectSelectedNavComponent,
  selectSelectedPageConfig,
  selectSelectedPageId,
  selectSelectedPluginConfig,
  selectSelectedPluginPageId,
} from '../../../selectors/EditorSelectors';
import PropertyInspector from '../components/PropertyInspectorV2';

const mapDispatchToProps = (dispatch: AppDispatch) => {
  const debouncedPluginConfigUpdate = debounce(
    (widgetId, pageId, update) => dispatch(pluginConfigUpdate(widgetId, pageId, update)),
    450,
  );
  const debouncedNavConfigUpdate = debounce(
    (navSelector, propertyName, value) => dispatch(navConfigUpdate(navSelector, propertyName, value)),
    450,
  );
  const debouncedUpdatePageConfig = debounce((pageId, update) => dispatch(updatePageConfig(pageId, update)), 450);

  return {
    ...bindActionCreators(
      {
        pluginConfigUpdate,
        pluginConfigDeleteEvent,
        pluginLayoutUpdate,
        pluginStyleUpdate,
        pluginConfigEventUpdate,
        pluginUpdateId,
        pluginDelete,
        navConfigUpdate,
        navUpdateName,
        navComponentDelete,
        updatePageId,
        updatePageConfig,
        deletePageConfig,
        createModuleFromWidget,
        reorderEventUpward,
        reorderEventDownward,
      },
      dispatch,
    ),
    debouncedPluginConfigUpdate,
    debouncedNavConfigUpdate,
    debouncedUpdatePageConfig,
  };
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    appConfig: selectAppConfig(state),
    selectedPluginConfig: selectSelectedPluginConfig(state),
    selectedNavComponent: selectSelectedNavComponent(state),
    pageId: selectSelectedPluginPageId(state),
    pluginSelector: state.editor.selectedPluginConfigSel,
    target: state.editor.selectedPluginTarget,
    navComponentSelector: selectNavComponentSelector(state),
    selectedPageId: selectSelectedPageId(state),
    selectedPageConfig: selectSelectedPageConfig(state),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(PropertyInspector);
