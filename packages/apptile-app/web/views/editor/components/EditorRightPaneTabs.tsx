import React, {useCallback, useEffect, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {EditorState, SettingsConfig} from 'apptile-core';
import {AppDispatch} from '../../../../app/store';
import {
  fetchAppIntegrations,
  fetchIntegrations,
  openPluginListing,
  openPropertyInspector,
  openThemeEditor,
  openTilesBrowser,
} from '../../../actions/editorActions';
import PluginListingControlContainer from '../../pluginListing/containers/PluginListingControlContainer';
import PropertyInspectorContainer from '../containers/PropertyInspectorContainer';
import {ThemeEditor} from '@/root/web/components/controls/themeEditor';
import TilesExplorer from '@/root/web/components-v2/TilesExplorer';
import Tabs from '@/root/web/components-v2/composite/Tabs';
import Button from '@/root/web/components-v2/base/Button';
import {getMappedTileTagsForScreen} from '@/root/web/common/tileConstants';
import _, {pick} from 'lodash';
import {selectActiveScreen} from '../../../selectors/EditorSelectors';
import {selectAppSettingsForKey, BrandSettingsTypes} from 'apptile-core';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import BlueprintsApi from '@/root/web/api/BlueprintsApi';
const BRAND_EXPOSED_TILES = BrandSettingsTypes.BRAND_EXPOSED_TILES,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY,
  BRAND_TILE_TAG = BrandSettingsTypes.BRAND_TILE_TAG,
  BRAND_TILE_TAG_DISPLAY = BrandSettingsTypes.BRAND_TILE_TAG_DISPLAY;

interface EditorRightPaneTabsProps {
  editor: EditorState;
  appIntegrationIds: any;
  integrationsById: any;
  appId: string;
}

const TagNav = {
  all: 'All',
  products: 'Products',
  collections: 'Collections',
  'image-banner': 'Image',
  'video-banner': 'Video',
  blogs: 'Blog',
  // countdown: 'Countdown',
  reviews: 'Reviews',
  // faqs: 'FAQs',
};

const EditorRightPaneTabs: React.FC<EditorRightPaneTabsProps> = ({
  editor,
  integrationsById,
  appIntegrationIds,
  appId,
}) => {
  const dispatch: AppDispatch = useDispatch();
  const onOpenPI = useCallback(() => {
    dispatch(openPropertyInspector());
  }, [dispatch]);
  const onOpenListing = useCallback(() => {
    dispatch(openPluginListing());
  }, [dispatch]);
  const onOpenThemeEditor = useCallback(() => {
    dispatch(openThemeEditor());
  }, [dispatch]);
  const onOpenTilesBrowser = useCallback(() => {
    dispatch(openTilesBrowser());
    dispatch(fetchAppIntegrations(appId));
    dispatch(fetchIntegrations());
  }, [dispatch, appId]);
  const appIntegrations = Object.values(pick(integrationsById, appIntegrationIds)).map(item => item.integrationCode);
  const activeScreen = useSelector(selectActiveScreen);
  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const brandSpecificTag = brandSettings.getSettingValue(BRAND_TILE_TAG) ?? '';
  const brandSpecificTagDisplay = brandSettings.getSettingValue(BRAND_TILE_TAG_DISPLAY) ?? '';
  const brandExposedTiles = brandSettings.getSettingValue(BRAND_EXPOSED_TILES) ?? '';
  const [exposedModules, setExposedModules] = useState<string[]>(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  const [blueprintDetails, setBlueprintDetails] = useState(null);
  const activeBlueprintUUID = useSelector((state: EditorRootState) => state.appConfig.current?.blueprintUUID);
  const {blueprints} = useSelector((state: EditorRootState) => state.blueprint);

  useEffect(() => {
    setExposedModules(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  }, [brandExposedTiles]);

  let [tileTags, setTileTags] = useState({});
  useEffect(() => {
    let mappedTiles = getMappedTileTagsForScreen(activeScreen?.[0]?.name);
    if (brandSpecificTag && brandSpecificTagDisplay && brandSpecificTag.trim() && brandSpecificTagDisplay.trim()) {
      mappedTiles = {...{[brandSpecificTag.trim()]: brandSpecificTagDisplay.trim()}, ...mappedTiles};
    }
    if (exposedModules.length > 0) {
      mappedTiles = {...{exposedTiles: 'My Tiles'}, ...mappedTiles};
    }
    if (blueprintDetails?.slug) {
      mappedTiles = {...{themeTiles: `${blueprintDetails?.slug}`}, ...mappedTiles};
    }
    setTileTags(mappedTiles);
  }, [activeScreen, setTileTags, blueprintDetails?.slug, exposedModules]);

  useEffect(() => {
    if (activeBlueprintUUID && typeof activeBlueprintUUID === 'string') {
      const blueprint = blueprints.find(blueprint => blueprint.id === activeBlueprintUUID);
      setBlueprintDetails(blueprint);
    }
  }, [activeBlueprintUUID, blueprints]);

  const [hasTileAnimationEverHappened, setTileAnimationEverHappend] = useState<boolean>(false);
  const [isTileAnimationHappening, setTileAnimationStatus] = useState<boolean>(false);
  return (
    <>
      <View style={[styles.editorRightPaneLayout, {overflow: isTileAnimationHappening ? 'visible' : 'hidden'}]}>
        <View style={[styles.editorRightPaneTabs]}>
          <View style={[styles.tabButtonsContainer]}>
            <Button
              variant={editor.isPropertyInspectorOpen ? 'FILLED-PILL' : 'TEXT'}
              color="SECONDARY"
              onPress={onOpenPI}>
              Properties
            </Button>
            <Button
              variant={editor.isPluginListingOpen ? 'FILLED-PILL' : 'TEXT'}
              color="SECONDARY"
              onPress={onOpenListing}>
              Components
            </Button>
            <Button
              variant={editor.isTilesBrowserOpen ? 'FILLED-PILL' : 'TEXT'}
              color="SECONDARY"
              onPress={onOpenTilesBrowser}>
              Tiles
            </Button>
            <Button
              variant={editor.isThemeEditorOpen ? 'FILLED-PILL' : 'TEXT'}
              color="SECONDARY"
              onPress={onOpenThemeEditor}>
              Theme
            </Button>
          </View>
        </View>
        <View
          style={[
            {
              display: editor.isPropertyInspectorOpen ? 'flex' : 'none',
              flex: 1,
            },
          ]}>
          <PropertyInspectorContainer editor={editor} />
        </View>
        <View
          style={[
            {
              display: editor.isThemeEditorOpen ? 'flex' : 'none',
              flex: 1,
              overflow: isTileAnimationHappening ? 'visible' : 'hidden',
            },
          ]}>
          <ThemeEditor />
        </View>
        <View
          style={[
            {
              display: editor.isPluginListingOpen ? 'flex' : 'none',
              flex: 1,
            },
          ]}>
          {editor.isPluginListingOpen && <PluginListingControlContainer />}
        </View>
        <View
          style={[
            {
              display: editor.isTilesBrowserOpen ? 'flex' : 'none',
              flex: 1,
              // overflow:'visible'
            },
          ]}>
          {editor.isTilesBrowserOpen && (
            <Tabs
              rootStyles={{flex: 1, flexBasis: 'auto'}}
              tabs={Object.keys(tileTags).map(tag => ({
                title: tileTags[tag],
                disableScroll: true,
                component: (
                  <TilesExplorer
                    tags={[tag]}
                    appId={appId}
                    themeSlug={blueprintDetails?.slug}
                    appIntegrations={appIntegrations}
                    appIntegrationObj={integrationsById}
                    screenBasedTiles={tileTags}
                    tileAnimation={{
                      isTileAnimationHappening,
                      hasTileAnimationEverHappened,
                      setTileAnimationStatus,
                      setTileAnimationEverHappend,
                    }}
                    legacyMode={true}
                  />
                ),
              }))}
              noOfLines={2}
              activeVariant="FILLED"
              inactiveVariant="OUTLINED"
              activeColor="QUATERNARY"
              inactiveColor="SECONDARY"
              size="SMALL"
            />
          )}
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  editorRightPaneLayout: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'stretch',
    overflow: 'hidden',
  },
  editorRightPaneTabs: {
    flex: 1,
    height: 50,
    flexBasis: 50,
    flexShrink: 0,
    flexGrow: 0,
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderBottomColor: '#303030',
    borderBottomWidth: 1,
  },
  tabButtonsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignContent: 'center',
    justifyContent: 'space-around',
  },
});

export default EditorRightPaneTabs;
