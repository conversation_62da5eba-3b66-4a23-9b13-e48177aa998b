import React from 'react';
import {ScrollView, View} from 'react-native';
import {EditorProps} from 'apptile-core';
import PIControlsFormContainer from '../../propertyInspector/containers/PIControlsFormContainerV2';
import TileHeader from '@/root/web/components-v2/TileHeader';

type PropertyInspectorProps = EditorProps & {
  editor: EditorState;
  onChat: () => void;
  onEditCode: () => void;
};

interface PropertyInspectorState {}

class PropertyInspector extends React.Component<PropertyInspectorProps, PropertyInspectorState> {
  constructor(props: PropertyInspectorProps) {
    super(props);
  }

  onDelete = () => {
    const {pluginDelete, selectedPluginConfig, pageId} = this.props;
    const pluginId = selectedPluginConfig?.id;

    if (pluginId && pageId) {
      pluginDelete(pluginId, pageId);
    }
  };

  render() {
    return (
      <View style={{flexDirection: 'column'}}>
        <TileHeader 
          onEditCode={this.props.onEditCode} 
          isDeletable={true} 
          onDelete={this.onDelete} 
          onChat={this.props.onChat} 
        />
        <ScrollView style={{flex: 1, maxHeight: window.innerHeight - 54 - 60}}>
          <PIControlsFormContainer {...this.props} />
        </ScrollView>
      </View>
    );
  }
}

export default PropertyInspector;
