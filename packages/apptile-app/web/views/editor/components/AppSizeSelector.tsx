import React, {useRef, useState} from 'react';
import {Pressable, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import {AppSizeDefinition, appSizeDefinitions} from '@/root/web/common/appSizeTypes';
import Button from '@/root/web/components-v2/base/Button';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import {MaterialCommunityIcons} from 'apptile-core';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

type AppSizeSelectorProps = {
  label: string;
  onChange: (appSize: AppSizeDefinition) => void;
};
const AppSizeSelector: React.FC<AppSizeSelectorProps> = ({label, onChange}) => {
  const buttonRef = useRef<Text>(null);
  const [showPopover, setShowPopover] = useState(false);

  return (
    <>
      <PopoverComponent
        trigger={
          <Button
            ref={buttonRef}
            variant="TEXT"
            color="SECONDARY"
            size="LARGE"
            onPress={() => setShowPopover(!showPopover)}>
            <View style={styles.previewButtonInternalWrapper}>
              <View style={{flexDirection: 'row'}}>
                <Text style={[commonStyles.baseText, {fontWeight: '500', fontSize: 14, color: theme.SECONDARY_COLOR}]}>
                  Device:{' '}
                </Text>
                <Text style={[commonStyles.baseText]}>{label}</Text>
              </View>
              <MaterialCommunityIcons
                name={showPopover ? 'chevron-down' : 'chevron-up'}
                size={16}
                color={theme.SECONDARY_COLOR}
              />
            </View>
          </Button>
        }
        onVisibleChange={setShowPopover}
        visible={showPopover}>
        <View style={styles.dialogStyle}>
          <TouchableOpacity style={[styles.dialogHeader]} onPress={() => setShowPopover(false)}>
            <Text style={{fontSize: 10}}>Close</Text>
          </TouchableOpacity>
          <View style={styles.dialogBody}>
            {appSizeDefinitions.map(appSize => (
              <Pressable
                onPress={() => {
                  onChange?.(appSize);
                  setShowPopover(false);
                }}
                key={appSize.label}
                style={styles.rowContainer}>
                <Text>{appSize.label}</Text>
                <Text style={styles.date}>
                  {appSize.size.width} X {appSize.size.height}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      </PopoverComponent>
    </>
  );
};

const styles = StyleSheet.create({
  flex1: {flex: 1},
  dialogStyle: {
    flex: 1,
    alignSelf: 'center',
    flexDirection: 'column',
    width: 240,
    minHeight: 300,
    marginBottom: 15,
    backgroundColor: '#fff',
    boxShadow: '#00000012 0px 0px 20px 14px',
    borderRadius: 8,
    overflow: 'hidden',
  },
  previewButtonInternalWrapper: {
    flexDirection: 'row',
    gap: 5,
    backgroundColor: '#FFF',
    padding: 10,
    borderRadius: 4,
    minWidth: 250,
    justifyContent: 'space-between',
  },
  dialogHeader: {
    padding: 8,
    marginBottom: 12,
    alignItems: 'center',
    backgroundColor: '#eeeeee',
  },
  dialogBody: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'column',
    paddingHorizontal: 8,
  },
  rowContainer: {
    marginBottom: 8,
  },
  date: {
    marginTop: 4,
    fontSize: 11,
    color: '#999999',
  },
  note: {
    fontSize: 12,
    color: '#999999',
    textAlign: 'right',
    marginTop: 4,
  },
  liveIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
    color: 'green',
    fontWeight: 'bold',
    backgroundColor: '#e3ffe3',
  },
});

export default AppSizeSelector;
