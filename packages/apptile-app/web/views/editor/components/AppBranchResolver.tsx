import React, {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {Navigate, useLocation, useParams} from 'react-router-dom';
import {fetchAppBranches} from '../../../../web/actions/editorActions';
import {selectAppBranchByName} from '@/root/web/selectors/AppSelectors';
import {ActivityIndicator} from 'react-native';
import _ from 'lodash';
import {DEFAULT_BRANCH_NAME} from 'apptile-core';

export const AppBranchResolver = ({...rest}) => {
  const location = useLocation();
  const {id: appId, forkId} = useParams();

  const urlParams = new URLSearchParams(window.location.search);
  let redirectDir = "/dashboard/store"
  if (urlParams.get('tiledev') === 'true') {
    redirectDir = "/studio"
  }

  const branchByNames = useSelector(selectAppBranchByName);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchAppBranches(appId, forkId));
  }, [appId, forkId, dispatch]);

  //Filtering isDefault branch in branchByNames
  const defaultBranch = _.find(branchByNames, branch => branch.isDefault) ?? {branchName: DEFAULT_BRANCH_NAME};

  // If the last character of the url is '/'
  if (!_.isEmpty(defaultBranch)) {
    return (
      <Navigate
        replace
        {...rest}
        to={{
          pathname: location.pathname + `/b/${defaultBranch?.branchName ?? DEFAULT_BRANCH_NAME}${redirectDir}`,
          search: location.search,
        }}
      />
    );
  } else {
    return <ActivityIndicator size={'large'} />;
  }
};
