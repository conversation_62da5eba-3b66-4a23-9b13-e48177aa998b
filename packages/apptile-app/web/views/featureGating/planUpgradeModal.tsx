import React, {useEffect} from 'react';
import {View, StyleSheet, Pressable, Text, Image} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import {fetchPaymentUrl} from '../../actions/editorActions';
import {useNavigate} from '../../routing.web';
import Button from '../../components-v2/base/Button';
import {EditorRootState} from '../../store/EditorRootState';
import _ from 'lodash';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const PlanUpgradeModal = ({setShowPlanUpgradeModal}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const currentPlans = useSelector((state: EditorRootState) => state.billing.plansById);
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const requiredPlan = _.find(Object.values(currentPlans), {name: 'CORE'});
  const {confirmationUrl, paymentUrlFetched, paymentUrlFetching} = useSelector(
    (state: EditorRootState) => state.billing.subscription,
  );
  useEffect(() => {
    if (paymentUrlFetched && confirmationUrl) {
      if (window.top === window.self) {
        window.location = confirmationUrl as any;
      } else {
        window.parent.location = confirmationUrl as any;
        // redirectRemote(confirmationUrl);
      }
    }
  }, [paymentUrlFetched, confirmationUrl]);

  const onPaymentClick = () => {
    if (requiredPlan) dispatch(fetchPaymentUrl(requiredPlan.id, appId));
  };

  return (
    <View style={styles.mainWrapper}>
      <Text style={[commonStyles.baseText, styles.title]}>Activate your Core Plan</Text>
      <View style={styles.leftContainer}>
        <Text style={[commonStyles.baseText, styles.subDescription, styles.lineHeight]}>
          Hey, we noticed that your 'Core’ pricing plan isn't yet activated!
        </Text>
        <Text style={[commonStyles.baseText, styles.subDescription, styles.lineHeight, {marginTop: 20}]}>
          As a reminder - the Core Plan includes:
        </Text>
        <ul style={{marginTop: 0}}>
          <li>
            <Text style={[commonStyles.baseText, styles.subDescription, styles.lineHeight]}>No fixed monthly cost</Text>
          </li>
          <li>
            <Text style={[commonStyles.baseText, styles.subDescription, styles.lineHeight]}>
              5% commission on sales made through the mobile app
            </Text>
          </li>
        </ul>
      </View>

      <Button color="CTA" size="MEDIUM" onPress={onPaymentClick} containerStyles={{width: 130, marginTop: 14}}>
        Confirm plan
      </Button>
      <View style={styles.footerWrapper}>
        <Text
          style={[
            commonStyles.baseText,
            styles.subDescription,
            styles.lineHeight,
            {textAlign: 'center', marginTop: 0},
          ]}>
          Need more info? Feel free to contact us on chat or check out our pricing plans.{' '}
          <Pressable
            onPress={() => {
              // navigate('../dashboard/settings/billing');
              window.open('https://apptile.com/pricing');
              // setShowPlanUpgradeModal(false);
            }}
            style={{flexDirection: 'row', gap: 5, marginTop: 8}}>
            <Text style={[commonStyles.baseText, {color: '#1060E0', fontSize: 15, fontWeight: '500'}]}>
              View Pricing
            </Text>{' '}
            <MaterialCommunityIcons name="arrow-right" size={16} color="#1060E0" />
          </Pressable>
        </Text>
      </View>
      {/* <Pressable
        onPress={() => {
          setShowPlanUpgradeModal(false);
        }}
        style={styles.modalCloseIcon}>
        <MaterialCommunityIcons name="close" size={20} color="#000000" />
      </Pressable> */}
    </View>
  );
};

const styles = StyleSheet.create({
  leftContainer: {
    flex: 1,
    width: 450,
    backgroundColor: 'white',
    alignItems: 'flex-start',
  },
  footerWrapper: {
    width: 450,
    marginBottom: 40,
    marginTop: 30,
  },
  mainWrapper: {width: 600, alignItems: 'center'},
  upgradeCrown: {backgroundColor: '#F4E4FA', padding: 4, borderRadius: 3},
  modalCloseIcon: {
    position: 'absolute',
    right: 20,
    top: 20,
    zIndex: 9,
  },
  lineHeight: {
    lineHeight: 20,
  },
  rightContainer: {},
  upgradeButton: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
  },
  upgradeText: {
    color: '#2C2B2B',
    fontSize: 13,
  },
  title: {
    fontSize: 25,
    fontWeight: '600',
    marginTop: 40,
    color: '#000000',
    lineHeight: 50,
  },
  description: {
    marginTop: 24,
    fontSize: 17,
    fontWeight: '500',
    color: '#161616',
  },
  subDescription: {
    marginTop: 24,
    fontSize: 15,
    fontWeight: '500',
    textAlign: 'left',
    color: '#555',
  },
  featureList: {
    marginTop: 20,
  },
});

export default PlanUpgradeModal;
