import React, {useState} from 'react';
import {View, StyleSheet, Pressable, Text, Image} from 'react-native';
import {Icon, MaterialCommunityIcons} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import {setOpenPremiumModal} from '../../actions/editorActions';
import {useNavigate} from '../../routing.web';
import {EditorRootState} from '../../store/EditorRootState';
import {selectSubscriptionPlans} from '../../selectors/BillingSelector';
import _ from 'lodash';
import Box from '../../components-v2/base/Box';
import TextElement from '../../components-v2/base/TextElement';
import PaymentButton from '../subscription/components/subscription/PaymentButton';
import {BillingIntervalEnum} from '../subscription/PublishPricing';
import {getSubscriptionCurrencySymbol, getSubscriptionStrikeoutPrice} from '../../common/utils';
import imagePlanMapping from '../../common/featureGatingConstants';
import Button from '../../components-v2/base/Button';
import {useIntercom} from 'react-use-intercom';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const PremiumUpgradeModal = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const plans = useSelector(selectSubscriptionPlans);
  const [billingInterval, setBillingInterval] = useState(BillingIntervalEnum.MONTHLY);

  const premiumValue = useSelector((state: EditorRootState) => state.platform.featureGating.premiumModalPlan);
  const upsellingFeature = useSelector((state: EditorRootState) => state.platform.featureGating.upsellingFeature);

  const plan = _.find(plans, {name: premiumValue});
  const {showNewMessage} = useIntercom();

  return (
    <View style={{flexDirection: 'row', width: 811}}>
      <View style={styles.leftContainer}>
        <View style={{paddingLeft: 32}}>
          <View style={styles.upgradeButton}>
            <Image source={imagePlanMapping.SMALL} resizeMode="contain" style={{width: 20, height: 20}} />
            <Text style={styles.upgradeText}>Upgrade to {_.lowerCase(premiumValue)} plan</Text>
          </View>

          {!plan && (
            <>
              <Text
                style={[
                  commonStyles.baseText,
                  styles.description,
                  {padding: 10, marginBottom: 10, textAlign: 'center'},
                ]}>
                We enable {!upsellingFeature ? 'this feature' : upsellingFeature} on a particular plan. Please connect
                with us to know more
              </Text>
            </>
          )}
          {plan && (
            <>
              <Text style={[commonStyles.baseText, styles.subText]}>
                To unlock {!upsellingFeature ? 'this feature' : upsellingFeature}.
              </Text>
              <Text style={[commonStyles.baseText, styles.description]}>
                Other features included in the {_.lowerCase(premiumValue)} plan:
              </Text>
            </>
          )}

          {plan && (
            <View style={styles.featureList}>
              {!!upsellingFeature && (
                <View style={styles.featureItem}>
                  <Icon name="check" style={styles.fontSize13} color="TERTIARY" />
                  <Box style={styles.featureText}>
                    <TextElement style={styles.fontSize13} color="EDITOR_LIGHT_BLACK">
                      {upsellingFeature}
                    </TextElement>
                  </Box>
                </View>
              )}
              {plan.features &&
                plan.features.map((feature, index) => {
                  return (
                    <View style={styles.featureItem} key={index}>
                      <Icon name="check" style={styles.fontSize13} color="TERTIARY" />
                      <Box style={styles.featureText}>
                        <TextElement style={styles.fontSize13} color="EDITOR_LIGHT_BLACK" key={index}>
                          {feature.name}
                        </TextElement>
                      </Box>
                    </View>
                  );
                })}
              <View style={styles.featureItem}>
                <Icon name="check" style={styles.fontSize13} color="TERTIARY" />
                <Box style={styles.featureText}>
                  <TextElement style={styles.fontSize13} color="EDITOR_LIGHT_BLACK">
                    and many more...
                  </TextElement>
                </Box>
              </View>
            </View>
          )}
          {plan && (
            <>
              <View style={styles.billingPriceWrapper}>
                <BillingPriceCard
                  billingInterval={BillingIntervalEnum.MONTHLY}
                  setBillingInterval={setBillingInterval}
                  plan={plan}
                  selectedBillingInterval={billingInterval}
                />
                <BillingPriceCard
                  billingInterval={BillingIntervalEnum.ANNUAL}
                  setBillingInterval={setBillingInterval}
                  plan={plan}
                  selectedBillingInterval={billingInterval}
                />
              </View>
              <View style={{width: 150}}>
                <PaymentButton
                  billingInterval={billingInterval}
                  title={`Upgrade to ${_.lowerCase(plan?.name)}`}
                  color={'CTA'}
                  planId={plan?.id}
                />
              </View>
            </>
          )}
          {!plan && (
            <View style={{paddingRight: 8}}>
              <Button onPress={() => showNewMessage('Please help me enable this feature -')} size="LARGE" color="CTA">
                Talk to us
              </Button>
            </View>
          )}
        </View>
      </View>
      <View style={styles.rightContainer}>
        <Pressable
          onPress={() => {
            dispatch(setOpenPremiumModal(false, ''));
          }}
          style={styles.modalCloseIcon}>
          <MaterialCommunityIcons name="close" size={20} color="#FFFFFF" />
        </Pressable>
        <img
          style={{width: '100%', height: '100%', objectFit: 'fill'}}
          src={require('@/root/web/assets/images/upgradeModalImage.png')}
        />
      </View>
    </View>
  );
};

const BillingPriceCard = ({setBillingInterval, billingInterval, plan, selectedBillingInterval}) => {
  let requiredPrice;
  let requiredPriceDiscount;
  let requiredPercentageOff;
  let currencySymbols;
  if (plan) {
    if (billingInterval === BillingIntervalEnum.MONTHLY) {
      requiredPrice = plan.monthlyPrice;
      requiredPriceDiscount = plan.monthlyPriceDiscount;
      requiredPercentageOff = Math.round((Number(plan.monthlyPriceDiscount) / Number(plan.monthlyPrice)) * 100);
    } else if (billingInterval === BillingIntervalEnum.ANNUAL) {
      requiredPrice = Math.round(Number(plan.annualPrice) / 12);
      requiredPriceDiscount = Math.round(Number(plan.annualPriceDiscount) / 12);
      requiredPercentageOff = Math.round((Number(plan.annualPriceDiscount) / Number(plan.annualPrice)) * 100);
    }
    currencySymbols = getSubscriptionCurrencySymbol(plan.currencyCode);
  }

  const {strikeoutPrice, displayPrice} = getSubscriptionStrikeoutPrice(
    Number(requiredPrice),
    Number(requiredPriceDiscount),
  );

  const isSelected = billingInterval === selectedBillingInterval;

  return (
    <Pressable onPress={() => setBillingInterval(billingInterval)}>
      <View style={[styles.monthlyPriceWrapper, isSelected && styles.selectedPriceWrapper]}>
        <TextElement color="SECONDARY" fontSize="sm">
          {billingInterval === BillingIntervalEnum.MONTHLY ? 'Monthly plan' : 'Annual plan'}
        </TextElement>
        <Box space="md" style={styles.priceBox}>
          <TextElement style={{fontSize: 20}} color={isSelected ? 'PRIMARY' : 'SECONDARY'} fontWeight="500">
            {displayPrice > 0 ? `${currencySymbols}${displayPrice}` : `FREE`}
          </TextElement>
          <TextElement fontSize="xs" color={isSelected ? 'PRIMARY' : 'EDITOR_LIGHT_BLACK'}>
            /mo
          </TextElement>
        </Box>
        {billingInterval === BillingIntervalEnum.ANNUAL && (
          <View style={[styles.annualDiscountWrapper, isSelected && styles.selectedDiscountWrapper]}>
            <TextElement style={{fontSize: 9, lineHeight: 9}} color="DEFAULT">
              Save {requiredPercentageOff}%
            </TextElement>
          </View>
        )}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  leftContainer: {
    flex: 1,
    marginVertical: 32,
    backgroundColor: 'white',
    justifyContent: 'flex-start',
  },
  selectedPriceWrapper: {
    backgroundColor: 'rgba(0, 91, 228, .05)',
    borderColor: '#1060E0',
  },
  priceBox: {flexDirection: 'row', alignItems: 'flex-end'},
  billingPriceWrapper: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 22,
    marginBottom: 22,
  },
  fontSize13: {
    fontSize: 13,
  },
  subText: {
    fontSize: 13,
    marginTop: 8,
    fontWeight: '400',
  },
  selectedDiscountWrapper: {
    backgroundColor: '#1060E0',
  },
  monthlyPriceWrapper: {
    paddingVertical: 24,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#000',
    overflow: 'hidden',
  },
  featureItem: {flexDirection: 'row', alignItems: 'center'},
  featureText: {padding: 2, flex: 1},
  annualDiscountWrapper: {
    position: 'absolute',
    top: 0,
    right: -5,
    backgroundColor: '#333',
    borderBottomLeftRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  upgradeCrown: {backgroundColor: '#F4E4FA', padding: 4, borderRadius: 3},
  modalCloseIcon: {
    position: 'absolute',
    right: 20,
    top: 20,
    zIndex: 9,
  },
  rightContainer: {
    flex: 1.2,
  },
  upgradeButton: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
  },
  upgradeText: {
    color: '#2C2B2B',
    fontSize: 20,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 20,
  },
  description: {
    marginTop: 16,
    fontSize: 13,
    fontWeight: '400',
    color: '#161616',
  },
  featureList: {
    marginTop: 8,
  },
});

export default PremiumUpgradeModal;
