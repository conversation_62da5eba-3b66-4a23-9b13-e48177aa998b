import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {ActivityIndicator, Button, StyleSheet, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import Popover, {PopoverMode} from 'react-native-popover-view';
import {MaterialCommunityIcons} from 'apptile-core';
import {useDispatch, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {exportBlueprint, updateBlueprintInfo, updateBlueprint} from '../../actions/editorActions';
import BlueprintsApi from '../../api/BlueprintsApi';
import RadioGroupControl from '../../components/controls/RadioGroupControl';
import { ISourcePlatformType } from '../../../../apptile-core/common/datatypes/types';
import { EditorRootState } from '../../store/EditorRootState';

interface BlueprintsExportDialogProps {
  blueprintUUID: string | null;
  onClose: () => void;
  isOpen: boolean;
}

const BlueprintsExportDialog: React.FC<BlueprintsExportDialogProps> = props => {
  const {onClose, blueprintUUID, isOpen} = props;
  const dispatch = useDispatch();
  const {
    exportBlueprint: exportBlueprintToServer,
    updateBlueprintInfo: updateBlueprintDetails,
    updateBlueprint: updateBlueprintVersion,
  } = useMemo(() => bindActionCreators({exportBlueprint, updateBlueprintInfo, updateBlueprint}, dispatch), [dispatch]);
  const platformType = useSelector((state: EditorRootState) => state.platform.platformType);
  const [blueprintName, setBlueprintName] = useState('');
  const [coverImage, setCoverImage] = useState('');
  const [blueprintDetails, setBlueprintDetails] = useState(null);
  const [tags, setTags] = useState<string[]>([]);
  const [tagsStr, setTagsString] = useState<string>('');
  const [isLoading, setLoading] = useState(false);
  const [isNew, setNew] = useState(true);
  const [sourcePlatformType, setSourcePlatformType] = useState<ISourcePlatformType>(platformType as ISourcePlatformType);

  useEffect(() => {
    const tags = tagsStr.split(',').map(str => str.trim());
    setTags(tags);
  }, [tagsStr]);

  useEffect(() => {
    setBlueprintName(blueprintDetails?.name ?? '');
    setTagsString(blueprintDetails?.tags?.join(',') ?? '');
    setCoverImage(blueprintDetails?.coverImage ?? '');
  }, [blueprintDetails]);

  useEffect(() => {
    if (blueprintUUID) {
      BlueprintsApi.getBlueprint(blueprintUUID)
        .then(resp => {
          if (resp.data) {
            setBlueprintDetails(resp.data);
            setNew(false);
          }
        })
        .catch(() => {
          setLoading(false);
          setNew(true);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [blueprintUUID]);

  const onExportBlueprint = useCallback(() => {
    if (!blueprintUUID) {
      exportBlueprintToServer({
        name: blueprintName,
        coverImage,
        tags,
        sourcePlatformType: sourcePlatformType
      });
    }
  }, [coverImage, exportBlueprintToServer, blueprintName, blueprintUUID, tags, sourcePlatformType]);

  const saveBlueprintDetails = useCallback(() => {
    if (blueprintUUID) {
      updateBlueprintDetails({
        id: blueprintUUID,
        name: blueprintName,
        coverImage,
        tags,
        sourcePlatformType: sourcePlatformType
      });
    }
  }, [coverImage, blueprintName, blueprintUUID, tags, updateBlueprintDetails, sourcePlatformType]);

  const exportBlueprintVersion = useCallback(() => {
    if (blueprintUUID) {
      updateBlueprintVersion({
        id: blueprintUUID,
        name: blueprintName,
        coverImage,
        tags,
        sourcePlatformType: sourcePlatformType
      });
    }
  }, [coverImage, updateBlueprintVersion, blueprintName, blueprintUUID, tags, sourcePlatformType]);

  return (
    <Popover
      isVisible={isOpen}
      mode={PopoverMode.RN_MODAL}
      onRequestClose={() => {
        setLoading(true);
        onClose();
      }}
      popoverStyle={{flexBasis: 'auto', width: 600}}
      backgroundStyle={{backgroundColor: 'rgba(0,0,0,0.3)'}}>
      <View style={styles.dialogStyle}>
        <View style={styles.dialogHeader}>
          <Text>Export Dialog</Text>
          <TouchableOpacity onPress={onClose} style={[styles.closeButton]}>
            <MaterialCommunityIcons name="close-circle-outline" size={24} />
          </TouchableOpacity>
        </View>
        {isLoading ? (
          <ActivityIndicator />
        ) : (
          <ScrollView style={{flex: 1}} contentContainerStyle={styles.dialogBody}>
            <View style={styles.rowContainer}>
              <Text style={styles.textHeading}>Name</Text>
              <TextInput style={styles.textInput} value={blueprintName ?? ''} onChangeText={setBlueprintName} />
            </View>
            <View style={styles.rowContainer}>
              <Text style={styles.textHeading}>Cover Image</Text>
              <TextInput style={styles.textInput} value={coverImage ?? ''} onChangeText={setCoverImage} />
            </View>
            <View style={styles.rowContainer}>
              <Text style={styles.textHeading}>Tags</Text>
              <TextInput style={styles.textInput} value={tagsStr ?? ''} onChangeText={setTagsString} />
            </View>

            <View style={styles.rowContainer}>
              <Text style={styles.textHeading}>Source Platform</Text>
              <RadioGroupControl
              options={[
                {text: 'Shoplazza', value: ISourcePlatformType.SHOPLAZZA},
                {text: 'Shopify', value: ISourcePlatformType.SHOPIFY},
              ]}
              disableBinding
              allowDeselect
              value={sourcePlatformType}
              label={''}
              onChange={(value)=>{
                setSourcePlatformType(value as ISourcePlatformType);
              }}
            />
            </View>
          </ScrollView>
        )}
        <View style={styles.dialogFooter}>
          {isNew ? (
            <Button disabled={blueprintName === ''} title="Export" onPress={onExportBlueprint} />
          ) : (
            <>
              <Button disabled={blueprintName === ''} title="Save Changes" onPress={saveBlueprintDetails} />
              <View style={styles.bottomActionButtons}>
                <Button disabled={blueprintName === ''} title="Update Blueprint" onPress={exportBlueprintVersion} />
              </View>
            </>
          )}
        </View>
      </View>
    </Popover>
  );
};

const styles = StyleSheet.create({
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    minHeight: 400,
    flexBasis: 'auto',
    backgroundColor: '#fff',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ccc',
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  dialogHeader: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    padding: 10,
    backgroundColor: '#eee',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: '#fff',
    padding: 10,
    alignItems: 'stretch',
  },
  textHeading: {
    flex: 1,
    fontSize: 16,
    margin: 10,
  },
  textSubtitle: {
    fontSize: 10,
    fontColor: '#888',
  },
  textInput: {flex: 1, padding: 4, borderColor: '#ccc', borderWidth: 1},
  rowContainer: {
    flex: 0,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    height: 'auto',
    flexBasis: 'auto',
    marginVertical: 16,
  },
  inputRow: {
    flex: 0,
    flexBasis: 'auto',
    flexDirection: 'row',
    height: 40,
    width: '100%',
    borderRadius: 4,
    padding: 5,
    alignItems: 'center',
    alignContent: 'center',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  inputCell: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
  },
  selectorRow: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    height: 'auto',
    marginBottom: 5,
    flexGrow: 0,
    flexShrink: 0,
  },
  selectorCell: {
    flex: 1,
    flexBasis: '30%',
    width: '30%',
    height: 'auto',
    margin: 4,
  },
  selectorText: {
    fontSize: 10,
  },
  dialogFooter: {
    flex: 1,
    flexBasis: '60',
    flexDirection: 'row',
    height: 40,
    minHeight: 40,
    maxHeight: 40,
    width: 'auto',
    padding: 4,
    paddingLeft: 10,
    paddingRight: 10,
    flexGrow: 0,
    flexShrink: 0,
    borderTopColor: '#ccc',
    borderTopWidth: 1,
    justifyContent: 'flex-end',
  },
  bottomActionButtons: {
    marginLeft: 8,
  },
});

export default React.memo(BlueprintsExportDialog);
