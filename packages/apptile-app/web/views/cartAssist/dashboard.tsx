import {
  CART_LINES_ADD,
  CART_LINES_REMOVE,
  CART_LINES_UPDATE,
  SHOPPING_CART_CREATE,
} from 'apptile-shopify';
import React, { useState } from 'react';
import { Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import Icon from '../../../../apptile-core/icons';
import { datasourceTypeModelSel } from '../../../../apptile-core/selectors/AppModelSelector';
import { CART_ACTIONS_ENUM, clearLineItems, setCustomer } from '../../actions/cartAssistActions';
import { makeToast } from '../../actions/toastActions';
import { CartAssistApi } from '../../api/CartAssistApi';
import Button from '../../components-v2/base/Button';
import { EditorRootState } from '../../store/EditorRootState';
import ProductPicker from './productPicker';
import { I_CART_ASSIST_CART } from './types';
import { TranformCustomerWithMetafields, TransformToAdd, TransformToDelete, TransformToUpdate } from './utils';
import { DeleteConfirmationModal } from './deleteConfirmationModel';
import {toggleAppIntegration} from '../../actions/editorActions';
import { currentIntegrationSelector } from '../integrations/components/deleteIntegration/DeleteIntegration';
import _ from 'lodash';
import ModalComponent from '../../components-v2/base/Modal';
import { DisclaimerModal } from './disclaimerModal';
import { getAppConstants } from '../../../../apptile-core/constants/api';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();


const apptileCheckoutCustomAtt = getAppConstants().APPTILE_CHECKOUT_CUSTOM_ATTRIBUTE as string;

const Dashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const currentPage = location.pathname.split('/').pop();
  const isHomePage = currentPage == 'configure';
  const isCartPage = currentPage == 'cart';
  const isModifyPage = currentPage == 'modify';
  const isCreatePage = currentPage == 'create';

  const [isCopied, setIsCopied] = useState(false);
  const [deleteModal, setDeleteModal] = useState(false)
  const [openProductModal, setOpenProductModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showDisclaimer, setShowDisclaimer] = useState(false)

  const integrationState = useSelector(currentIntegrationSelector);
  const {integration} = integrationState;
  const id = _.get(integration, ['appIntegrations', 0, 'id'], null);
  const platformType = _.get(integration, ['appIntegrations', 0, 'platformType'], null);
  const customer = useSelector((state: EditorRootState) => state.cartAssist.customer);
  const appId = useSelector((state: EditorRootState) => state.apptile.appId) as string;
  const newLineItems = useSelector((state: EditorRootState) => state.cartAssist.newLineItems);
  const changesExist = useSelector((state: EditorRootState) => state.cartAssist.changesExists);
  const cart = useSelector((state: EditorRootState) => state.cartAssist.cart) as I_CART_ASSIST_CART;
  const existingLineItems = useSelector((state: EditorRootState) => state.cartAssist.existingLineItems);
  const shopifyModelSel = useSelector((state: EditorRootState) => datasourceTypeModelSel(state, 'shopifyV_22_10'));
  const removedFromExistingLineItem = useSelector(
    (state: EditorRootState) => state.cartAssist.removedFromExistingLineItem,
  );

  let queryRunner: any = undefined;
  queryRunner = shopifyModelSel && shopifyModelSel?.toJS()?.queryRunner;
  if (!queryRunner || typeof queryRunner.runQuery !== 'function') {
    console.error('queryRunner is not properly initialized:', queryRunner);
    return;
  }
  const handleCTAClick = (type: CART_ACTIONS_ENUM) => {
    switch (type) {
      case CART_ACTIONS_ENUM.CREATE: {
        setShowDisclaimer(true)
        break;
      }
      case CART_ACTIONS_ENUM.MODIFY: {
        navigate('../cart/modify');
        break;
      }
      default:
        break;
    }
  };

  const handleCheckoutCopy = () => {
    setIsCopied(true);
    window.navigator.clipboard.writeText(cart.checkoutUrl).then(() =>
      dispatch(
        makeToast({
          content: 'Copied',
          appearances: 'info',
        }),
      ),
    );
    setTimeout(() => {
      setIsCopied(false);
    }, 5000);
  };

  const handleDiscardPress = () => {
    dispatch(clearLineItems());
    navigate(-1);
  };

  async function saveCart() {
    const updateLines = TransformToUpdate(existingLineItems);
    const addLines = TransformToAdd(newLineItems);
    const deleteLines = TransformToDelete(removedFromExistingLineItem);
    try {
      setLoading(true);
      if (updateLines.length) {
        await queryRunner.runQuery('mutation', CART_LINES_UPDATE, {
          cartId: cart.id,
          lines: updateLines,
        });
      }
      if (addLines.length) {
        await queryRunner.runQuery('mutation', CART_LINES_ADD, {
          cartId: cart.id,
          lines: addLines,
        });
      }
      if (deleteLines.length) {
        await queryRunner.runQuery('mutation', CART_LINES_REMOVE, {
          cartId: cart.id,
          lineIds: deleteLines,
        });
      }
      dispatch(
        makeToast({
          content: 'Cart Saved',
          appearances: 'success',
        }),
      );
    } catch (error) {
      dispatch(
        makeToast({
          content: 'Please refresh the page to make any further actions!',
          appearances: 'error',
        }),
      );
      console.log('ERROR IN SAVING CART: ', error);
    } finally {
      setLoading(false);
      dispatch(clearLineItems());
    }
  }
  const handleSavePress = async () => {
    await saveCart();
    navigate(-1);
  };

  const createCart = async () => {
    const addLines = TransformToAdd(newLineItems);
    try {
      setLoading(true);
      const response = await queryRunner.runQuery('mutation', SHOPPING_CART_CREATE, {
        input: {
          buyerIdentity: {
            email: customer.email,
            phone: customer.phone,
          },
          lines: addLines,
        },
      });
      if(response?.data?.cartCreate?.userErrors.length > 0) {
        dispatch(makeToast({
          content: response?.data?.cartCreate?.userErrors[0]?.message,
          appearances: 'error'
        }))
        navigate(-1)
      }
      const cartRawData = response.data.cartCreate.cart;
      const custId = customer.id.split('/').pop();
      await CartAssistApi.updateMetafield(appId, custId!, cartRawData?.id?.split('/').pop()).catch(error => console.log("METAFIELD NOT UPDATED", error))
      CartAssistApi.getCustomer(appId, customer?.id, 'Apptile-CartAssist').then(response => {
        const customer = TranformCustomerWithMetafields(response.data);
        dispatch(setCustomer(customer));
        dispatch(
          makeToast({
            content: 'Cart Created',
            appearances: 'success',
          }),
        );
        navigate(-1);
      }).catch(error => console.log("COULDN'T GET CUSTOMER", error))
    } catch (error) {
      console.log('ERROR IN CREATING CART: ', error);
      dispatch(
        makeToast({
          content: 'Please refresh the page to make any further actions!',
          appearances: 'error',
        }),
      );
    } finally {
      setLoading(false);
      dispatch(clearLineItems());
    }
  };
  const handleCreatePress = async () => {
    await createCart();
  };
  const handleBacktoCustomers = () => navigate(-1);

  const deleteCartAssist = () => {
    dispatch(toggleAppIntegration(appId, id, false, platformType, []));
    const currentPath = window.location.pathname;
    const targetSegment = '/integrations';
    const targetIndex = currentPath.indexOf(targetSegment);
    if (targetIndex !== -1) {
      const newPath = currentPath.substring(0, targetIndex + targetSegment.length);
      navigate(newPath);
    }
  };
  
  let headerBody;
  let footerBody;
  if (isHomePage) {
    headerBody = (
      <>
        <Text style={[styles.text, {fontWeight: theme.FONT_WEIGHT_BOLD, fontSize: 18}]}>Customer's Carts</Text>
        <Text style={styles.text}>Search for a customer to view or modify their cart</Text>
      </>
    );
  }
  if (isCartPage) {
    headerBody = (
      <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 4}}>
        <View style={{flexDirection: 'column', gap: 2}}>
          <Pressable style={{gap: 4, flexDirection: 'row', alignItems: 'center'}} onPress={handleBacktoCustomers}>
            <Icon color="#767676" iconType="AntDesign" name="arrowleft" size={16} />
            <Text style={[styles.text, {fontSize: 13, color: '#767676', fontWeight: '500'}]}>Go back</Text>
          </Pressable>
          <Text style={[styles.text, {fontSize: 20}]}>{customer?.displayName}&apos;s Cart</Text>
        </View>
        <View style={{flexDirection: 'row', alignItems: 'center', gap: 10}}>
          <Pressable
            style={[!cart.checkoutUrl && styles.webCursorNotAllowed, {
              paddingVertical: 10, 
              paddingHorizontal: 15, 
              flexDirection: 'row',
              alignItems: 'center',
              gap: 6,
              borderWidth: 1,
              borderStyle: 'solid',
              borderRadius: 30,
              backgroundColor: 'white'
            }]}
            onPress={handleCheckoutCopy}
            disabled={!cart.checkoutUrl}
          >
            <Icon name='content-copy' iconType='MaterialCommunityIcons' size={13} color={'black'} />
            <Text style={[styles.text]}>
              {isCopied ? 'Copied' : 'Checkout Link'}
            </Text>
          </Pressable>
          <Button
            containerStyles={{backgroundColor: theme.PRIMARY_COLOR}}
            onPress={() => handleCTAClick(!cart?.id ? CART_ACTIONS_ENUM.CREATE : CART_ACTIONS_ENUM.MODIFY)}>
            <Text>{!cart?.id ? 'Create Cart' : 'Modify Cart'}</Text>
          </Button>
        </View>
      </View>
    );
  }
  if (isModifyPage) {
    headerBody = (
      <>
        <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 4}}>
          <Text style={[styles.text, {fontSize: 18}]}>Modifying {customer?.displayName}&apos;s Cart</Text>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 10, position: 'relative'}}>
            <Button icon="plus" containerStyles={{}} onPress={() => setOpenProductModal(!openProductModal)}>
              Add items
            </Button>
            {openProductModal && (
              <View style={styles.addContainer}>
                <ProductPicker setOpenProductModal={setOpenProductModal} />
              </View>
            )}
          </View>
        </View>
      </>
    );
    footerBody = (
      <View style={{flexDirection: 'row', alignSelf: 'flex-end', alignItems: 'center', paddingVertical: 4, gap: 10}}>
        <Button
          containerStyles={[
            changesExist
              ? {backgroundColor: 'white'}
              : [styles.webCursorNotAllowed, {backgroundColor: 'transparent', borderColor: theme.INPUT_BORDER}],
          ]}
          textStyles={changesExist ? {color: 'black'} : {color: theme.INPUT_BORDER}}
          disabled={!changesExist}
          onPress={handleDiscardPress}>
          Discard Changes
        </Button>
        <Button
          containerStyles={
            changesExist
              ? {backgroundColor: theme.PRIMARY_COLOR}
              : [styles.webCursorNotAllowed, {backgroundColor: theme.INPUT_BORDER}]
          }
          onPress={handleSavePress}
          disabled={!changesExist}>
          {loading ? 'Saving...' : 'Save Cart'}
        </Button>
      </View>
    );
  }
  if (isCreatePage) {
    headerBody = (
      <>
        <View style={{flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 4}}>
          <Text style={[styles.text, {fontSize: 18}]}>Creating {customer.displayName}&apos;s Cart</Text>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 10, position: 'relative'}}>
            <Button
              icon='plus'
              onPress={() => setOpenProductModal(!openProductModal)}
            >
              Add items
            </Button>
            {openProductModal && (
              <View style={styles.addContainer}>
                <ProductPicker setOpenProductModal={setOpenProductModal} />
              </View>
            )}
          </View>
        </View>
      </>
    );
    footerBody = (
      <View style={{flexDirection: 'row', alignSelf: 'flex-end', alignItems: 'center', paddingVertical: 4, gap: 10}}>
        <Button
          containerStyles={[
            changesExist
              ? {backgroundColor: 'white'}
              : [styles.webCursorNotAllowed, {backgroundColor: 'transparent', borderColor: theme.INPUT_BORDER}],
          ]}
          textStyles={changesExist ? {color: 'black'} : {color: theme.INPUT_BORDER}}
          disabled={!changesExist}
          onPress={handleDiscardPress}>
          Discard Changes
        </Button>
        <Button
          containerStyles={
            changesExist
              ? {backgroundColor: theme.PRIMARY_COLOR}
              : [styles.webCursorNotAllowed, {backgroundColor: theme.INPUT_BORDER}]
          }
          onPress={handleCreatePress}
          disabled={!changesExist}>
          {loading ? 'Creating...' : 'Create Cart'}
        </Button>
      </View>
    );
  }

  return (
    <>
    <View style={styles.superWrapper}>
      <View style={styles.wrapper}>
        <View style={styles.container}>
          <View style={[styles.titleContainer, { zIndex: 2}]}>{headerBody}</View>
          <View style={{paddingHorizontal: 16, flexGrow: 1, flexShrink: 1, flexBasis: 'auto'}}>
            <Outlet />
          </View>
          {footerBody && <View style={[styles.footerContainer, { zIndex: 1 }]}>{footerBody}</View>}
        </View>
      </View>

      {isHomePage && (
        <Pressable
          style={{ marginTop: 16, alignSelf: 'flex-end' }}
          onPress={() => {
            setDeleteModal(true);
          }}>
          <Text style={[commonStyles.baseText, {color: '#E93B3B'}]}>DISCONNECT</Text>
        </Pressable>
      )}
    </View>
    <ModalComponent
      onVisibleChange={setDeleteModal}
      visible={deleteModal}
      content={<DeleteConfirmationModal onConfirmPress={deleteCartAssist} />}
    />
    <ModalComponent
      onVisibleChange={setShowDisclaimer}
      visible={showDisclaimer}
      content={<DisclaimerModal onConfirmPress={() => {
        setShowDisclaimer(false)
        navigate('../cart/create')} 
      }/>}
    />
    </>
  );
};

export default Dashboard;

const styles = StyleSheet.create({
  superWrapper: {
    flex: 1,
    width: '100%',
    minHeight: '75vh',
    maxHeight: '75vh',
  },
  wrapper: {
    marginTop: 20,
    flex: 1,
    gap: 8,
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
    borderRadius: 8,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  titleContainer: {
    backgroundColor: theme.INPUT_BACKGROUND,
    paddingHorizontal: 24,
    paddingVertical: 16,
    gap: 6,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  footerContainer: {
    backgroundColor: theme.INPUT_BACKGROUND,
    paddingHorizontal: 24,
    paddingVertical: 16,
    gap: 6,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  text: {
    fontFamily: theme.FONT_FAMILY,
    fontSize: 14,
  },
  addContainer: {
    position: 'absolute',
    top: 50,
    left: -250,
    width: 350,
    flex: 1,
    ...Platform.select({
      web: {
        zIndex: 9999,
      },
    }),
  },
  webCursorNotAllowed: {
    ...Platform.select({
      web: {
        cursor: 'not-allowed',
      },
    }),
  },
});
