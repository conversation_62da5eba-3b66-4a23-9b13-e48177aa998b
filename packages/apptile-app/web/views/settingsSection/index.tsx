import React from 'react';
import {Route, Routes} from 'react-router';
import PaymentSummary from '../subscription/PaymentSummary';
import Pricing from '../subscription/Pricing';
import {ListingView} from './listing';
import {Settings} from './settings';
import {YourAccountSection} from './yourAccount';

export const SettingsRouter: React.FC = () => {

  let SettingsPage;
  let AccountSettingsPage;
  let BillingSectionPage;

  if (window.apptileWebSDK.status === 'loading' || window.apptileWebSDK.status === 'notstarted') {
    return <div>loading...</div>;
  } else if (window.apptileWebSDK.status === 'error') {
    return <div>Failed to load</div>
  } else {
    // if (window.apptileWebSDK?.moduleExports?.components?.SettingsPageList) {
    //   SettingsPage = window.apptileWebSDK?.moduleExports?.components?.SettingsPageList;
    // } else {
    //   SettingsPage = () => <Settings />;
    // }

    if (window.apptileWebSDK?.moduleExports?.components?.AccountSettings) {
      AccountSettingsPage = window.apptileWebSDK?.moduleExports?.components?.AccountSettings;
    } else {  
      AccountSettingsPage = () => <YourAccountSection />;
    }

    if (window.apptileWebSDK?.moduleExports?.components?.BillingSettings) {
      BillingSectionPage = window.apptileWebSDK?.moduleExports?.components?.BillingSettings;
    } else {
      BillingSectionPage = () => <Pricing />;
    } 
  }

  return (
    <Routes>
      <Route path="/settings" element={<Settings />} />
      <Route path="/settings/your-account" element={<AccountSettingsPage />} />
      <Route path="/settings/billing" element={<BillingSectionPage />} />
      <Route path="/settings/billing/plans/:planId" element={<PaymentSummary />} />
      <Route path="/settings/app-listing" element={<ListingView />} />
    </Routes>
  );
};
