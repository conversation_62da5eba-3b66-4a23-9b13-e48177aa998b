import React from 'react';
import {StyleSheet, View} from 'react-native';

import theme from '@/root/web/styles-v2/theme';
import {IntegrationRouter} from '@/root/web/views/integrations';
import {useDispatch, useSelector} from 'react-redux';
import {Route, Routes} from 'react-router';
import {changeAppContextData, fetchAppForks, setOrgId} from '../../actions/editorActions';
import LeftSidebar from '../../layout-v2/LeftSidebar';
import MainBlock from '../../layout-v2/MainBlock';
import RightSidebar from '../../layout-v2/RightSidebar';
import {BuildManagerRouter} from '../buildManager';
import {NotificationRouter} from '../notificationAdmin';
import {ThemeContainer} from 'apptile-core';

import {StoreRouter} from '@/root/web/views/myStore';
import {ThemeRouter} from '@/root/web/views/themes';
import {initApptileIsEditable} from 'apptile-core';
import {SettingsRouter} from '../settingsSection';
import {CustomDragLayer} from '../../components/CustomDragLayer';
import {AnalyticsRouter} from '../analytics';
import {EditorRootState} from '../../store/EditorRootState';
import {LiveRouter} from '../live';

import EditorContext from '@/root/web/context/editorContext';
import ShiftThemeScreen from '../themes/ShiftTheme';
import {TopBar} from '../../layout-v2/TopBar';
import {PublishPricing} from '../subscription/PublishPricing';
import BetaBuildTag from '../../components/BetaBuildTag';
import {PublishFlowRouter} from '../publishFlowV1';
import WebSDKBetaBuildTag from '../../components/WebSDKBetaBuildTag';
import useWebSDKUpdate from '../../common/useWebSDKUpdate';

const styles = StyleSheet.create({
  root: {
    flex: 1,
    height: '100vh',
    flexBasis: 'auto',
    backgroundColor: theme.PRIMARY_BACKGROUND,
  },
  fullscreenHeight: {
    height: 'calc(100vh - 45px)',
  },
  row: {
    flexDirection: 'row',
  },
});

function EditorContainerV2() {
  const {isFullscreen} = useSelector((state: EditorRootState) => state.shopify);
  const appId = useSelector(state => state.apptile?.appId);
  const orgId = useSelector(state => state.apptile?.orgId);
  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(initApptileIsEditable(true));
    if (appId) {
      dispatch(changeAppContextData(appId));
      dispatch(fetchAppForks(appId));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appId]);

  //Set Platform state org id
  React.useEffect(() => {
    orgId && dispatch(setOrgId(orgId));
  }, [orgId, dispatch]);

  return (
    <EditorContext.Provider value={{layout: 'editorV2'}}>
      <View style={[styles.root, isFullscreen && styles.fullscreenHeight]}>
        <EditorRouter />
        <DashboardRouter />
        <PricingRouter />
        <BetaBuildTag />
        <WebSDKBetaBuildTag />
      </View>
    </EditorContext.Provider>
  );
}

const EditorRouter = () => {
  return (
    <Routes>
      <Route path=":panelName/*" element={<AppEditor />} />
      <Route path="*" element={<AppEditor />} />
    </Routes>
  );
};

const AppEditor = () => {
  return (
    <>
      <TopBar />
      <ThemeContainer>
        <View style={{flexDirection: 'row', flex: 1, flexBasis: 'auto'}}>
          <LeftSidebar mainBar="APP_EDITOR" />
          <MainBlock />
          <RightSidebar />
          <CustomDragLayer />
        </View>
      </ThemeContainer>
    </>
  );
};
const DashboardRouter = () => {
  return (
    <Routes>
      <Route path="dashboard/*" element={<Dashboards />} />
      <Route path="overlay/*" element={<Overlays />} />
    </Routes>
  );
};

const PricingRouter = () => {
  return (
    <Routes>
      <Route path="pricing/*" element={<PublishPricing />} />
    </Routes>
  );
};

const Dashboards = () => {
  const {isFullscreen} = useSelector((state: EditorRootState) => state.shopify);
  const externalDashboardRoutes = window.apptileWebSDK?.moduleExports?.dashboardRoutes;
  //Hook to hot reload when the websdk updates
  useWebSDKUpdate();

  return (
    <View style={[styles.root, styles.row, isFullscreen && styles.fullscreenHeight, StyleSheet.absoluteFill]}>
      <LeftSidebar mainBar="DASHBOARD" />
      <StoreRouter />
      <ThemeRouter />
      <AnalyticsRouter />
      <LiveRouter />
      <NotificationRouter />
      <BuildManagerRouter />
      <IntegrationRouter />
      <SettingsRouter />
      <PublishFlowRouter />
      {externalDashboardRoutes &&
        Object.keys(externalDashboardRoutes).map((key: string) => {
          const {component: Component} = externalDashboardRoutes[key];
          return <Component key={key} LeftSidebar={LeftSidebar} />;
        })}
    </View>
  );
};

const Overlays = () => {
  const {isFullscreen} = useSelector((state: EditorRootState) => state.shopify);
  return (
    <View style={[styles.root, styles.row, isFullscreen && styles.fullscreenHeight, StyleSheet.absoluteFill]}>
      <Routes>
        <Route path="themes/shift-theme/:slug" element={<ShiftThemeScreen />} />
      </Routes>
    </View>
  );
};

export default EditorContainerV2;
