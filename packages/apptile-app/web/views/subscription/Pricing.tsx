import {AppDispatch} from 'apptile-core';
import React, { useState } from 'react';
import {ActivityIndicator, Pressable, StyleSheet, View} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import {connect, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {fetchMySubscription, fetchPlanList} from '../../actions/editorActions';
import {IPlanListing} from '../../api/ApiTypes';
import Box from '../../components-v2/base/Box';
import {
  getMyAddOns,
  selectCurrentPlanWithDetails,
  selectCurrentSubscription,
  selectPaymentStatus,
  selectSubscriptionPlans,
} from '../../selectors/BillingSelector';
import {SubscriptionState} from '../../store/BillingReducer';
import {EditorRootState} from '../../store/EditorRootState';
import {PricingCard} from './components/pricing/PricingCard';
import TextElement from '@/root/web/components-v2/base/TextElement';
import Button from '@/root/web/components-v2/base/Button';
import LeftSideBar from '@/root/web/layout-v2/LeftSidebar';
import {getSubscriptionStrikeoutPrice} from '../../common/utils';
import Icon from '@/root/web/components-v2/base/Icon';
import {useIntercom} from 'react-use-intercom';
import _ from 'lodash';
import RadioGroupControlV2 from '../../components/controls-v2/RadioGroupControl';
import {BillingIntervalEnum} from './PublishPricing';
import {Text} from 'react-native';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface PricingProps {
  fetchPlanList: () => void;
  fetchMySubscription: (appId: string) => void;
  plans: IPlanListing[];
  currentPlan: IPlanListing;
  currentSubscription: SubscriptionState;
}

const Pricing: React.FC<PricingProps> = ({plans, currentPlan, currentSubscription}) => {
  const {fetchPlansLoading} = currentSubscription;
  const {show, hide, isOpen} = useIntercom();
  const currentActiveSubscription = useSelector(selectCurrentSubscription);
  const [togglePlanShow, setTogglePlanShow] = useState(false);
  const monthlyPriceDiscount = currentActiveSubscription?.monthlyPriceDiscount;
  const billingInterval = currentSubscription?.currentSubscription?.billingInterval;

  const addOns = useSelector(getMyAddOns);

  const toggleIntercom = () => {
    if (isOpen) {
      hide();
    } else {
      show();
    }
  };

  let planDisplayPrice = 0;
  if (currentPlan) {
    planDisplayPrice = getSubscriptionStrikeoutPrice(
      parseInt(currentPlan.monthlyPrice),
      currentPlan.monthlyPriceDiscount,
    ).displayPrice;
  }

  const isPaidCustomer = useSelector(selectPaymentStatus);
  const [pricingDuration, setPricingDuration] = useState(BillingIntervalEnum.ANNUAL);

  const showPlanChoose = !isPaidCustomer || togglePlanShow;

  const planPrice =
    billingInterval === 'ANNUAL'
      ? currentActiveSubscription?.planAnnualPrice
      : currentActiveSubscription?.planMonthlyPrice;

  const planDiscount =
    billingInterval === 'ANNUAL'
      ? currentActiveSubscription?.annualPriceDiscount
      : currentActiveSubscription?.monthlyPriceDiscount;

  const billingINtervalName = billingInterval === 'ANNUAL' ? 'Year' : 'Month';

  return (
    <View style={[styles.rootContainer, StyleSheet.absoluteFill]}>
      <LeftSideBar mainBar="APP_SETTINGS" />

      <View style={styles.container}>
        <ScrollView contentContainerStyle={styles.wrapper}>
          <View style={styles.contentContainer}>
            <View>
              {togglePlanShow && (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: 10,
                    marginBottom: 20,
                  }}>
                  <Icon name="keyboard-backspace" size="lg" color="SECONDARY" />
                  <Pressable onPress={() => setTogglePlanShow(false)}>
                    <TextElement color="SECONDARY">Back</TextElement>
                  </Pressable>
                </View>
              )}
              <TextElement color="SECONDARY" fontSize="3xl" lineHeight="3xl" fontWeight="600">
                Billing Information
              </TextElement>
              <Box space="xl">
                <TextElement color="EDITOR_LIGHT_BLACK" fontSize="xs" lineHeight="xs">
                  Manage your billing details!
                </TextElement>
              </Box>
            </View>
            <View style={styles.mainMidContainer}>
              <View>
                <TextElement color="SECONDARY" fontSize="xl" lineHeight="2xl" fontWeight="500">
                  Your Plan
                </TextElement>
                <TextElement color="EDITOR_LIGHT_BLACK" fontSize="xs" lineHeight="md">
                  {currentActiveSubscription ? currentPlan?.name : 'No plan selected'}
                </TextElement>
              </View>
              <View>
                {planDiscount && Number(planDiscount) !== 0 && (
                  <TextElement
                    color="EDITOR_LIGHT_BLACK"
                    fontSize="xs"
                    lineHeight="md"
                    style={{textDecorationLine: 'line-through'}}>
                    ${planPrice}/month
                  </TextElement>
                )}
                <TextElement color="SECONDARY" fontSize="xl" lineHeight="xl" fontWeight="500">
                  {planDisplayPrice === 0 && 'Free Trial'}
                  {planDisplayPrice !== 0 &&
                    '$' + String(Number(planPrice) - Number(planDiscount)) + '/' + billingINtervalName}
                </TextElement>
              </View>
            </View>
            {addOns && !_.isEmpty(addOns) && (
              <View style={styles.mainAddOnMidContainer}>
                <View style={styles.addOnHeader}>
                  <TextElement color="SECONDARY" fontSize="lg" lineHeight="2xl" fontWeight="500">
                    Your Add-ons
                  </TextElement>
                  <TextElement color="SECONDARY" fontSize="lg" lineHeight="2xl" fontWeight="500">
                    Subscription amount
                  </TextElement>
                </View>
                <View style={styles.addOnContentWrapper}>
                  {addOns.map((a: any) => (
                    <View style={styles.addOnContent}>
                      <TextElement color="EDITOR_LIGHT_BLACK" fontSize="sm" lineHeight="2xl" fontWeight="500">
                        {a.title}
                      </TextElement>
                      <TextElement color="EDITOR_LIGHT_BLACK" fontSize="sm" lineHeight="2xl" fontWeight="500">
                        ${a.monthlyPrice ?? 0}/{billingINtervalName}
                      </TextElement>
                    </View>
                  ))}
                </View>
              </View>
            )}
            {showPlanChoose && (
              <View style={styles.pricePlanWrapper}>
                <View style={styles.radioWrapper}>
                  <Text style={[commonStyles.baseText, styles.planSelectText]}>Select a plan</Text>
                  <View style={{width: 500}}>
                    <RadioGroupControlV2
                      label=" "
                      options={[
                        {
                          text: 'Monthly',
                          value: BillingIntervalEnum.MONTHLY,
                        },
                        {
                          text: 'Annual(Save 10%)',
                          value: BillingIntervalEnum.ANNUAL,
                        },
                      ]}
                      disableBinding={true}
                      value={pricingDuration}
                      onChange={(value: string) => setPricingDuration(value)}
                    />
                  </View>
                </View>
                <View style={styles.pricingContainer}>
                  {fetchPlansLoading && !plans && <ActivityIndicator size={34} />}

                  {!fetchPlansLoading &&
                    plans &&
                    plans.map((plan, index) => {
                      return (
                        <PricingCard
                          enablePopularBorderRadius
                          cardWrapperStyles={styles.pricingCardWrapper}
                          billingInterval={pricingDuration}
                          key={index}
                          {...{plan}}
                        />
                      );
                    })}
                </View>
              </View>
            )}
            {!showPlanChoose && (
              <View style={styles.planDescriptionContainer}>
                <View>
                  <TextElement color="SECONDARY" fontSize="xl" lineHeight="2xl" fontWeight="500">
                    Features
                  </TextElement>
                  <View>
                    {currentPlan &&
                      currentPlan.features &&
                      currentPlan.features.map((feature, index) => {
                        return (
                          <Box style={styles.featureItem} space="xs">
                            <Icon name="check-circle-outline" size="lg" color="PRIMARY" />
                            <Box style={styles.featureText}>
                              <TextElement fontSize="xs" color="EDITOR_LIGHT_BLACK" key={index}>
                                {feature.name}
                              </TextElement>
                            </Box>
                          </Box>
                        );
                      })}
                  </View>
                </View>
                <View>
                  <Button size="MEDIUM" variant="TEXT" color="PRIMARY" onPress={toggleIntercom}>
                    Learn more
                  </Button>
                </View>
              </View>
            )}
            {!isPaidCustomer ? (
              <View style={styles.mainBottomContainer}>
                <View>
                  <TextElement color="SECONDARY" fontSize="xl" lineHeight="2xl" fontWeight="500">
                    Need any help?
                  </TextElement>
                  <TextElement color="EDITOR_LIGHT_BLACK" fontSize="xs" lineHeight="sm" fontWeight="400">
                    Get help and support instantly
                  </TextElement>
                </View>
                <View>
                  <Button size="MEDIUM" variant="TEXT" color="PRIMARY" onPress={toggleIntercom}>
                    Contact us
                  </Button>
                </View>
              </View>
            ) : (
              <View style={styles.mainBottomContainer}>
                <View>
                  <TextElement color="SECONDARY" fontSize="xl" lineHeight="2xl" fontWeight="500">
                    Update your plan
                  </TextElement>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      width: '100%',
                      gap: 10,
                    }}>
                    <TextElement color="EDITOR_LIGHT_BLACK" fontSize="xs" lineHeight="sm" fontWeight="400">
                      Get help and support instantly
                    </TextElement>
                    <Pressable onPress={toggleIntercom}>
                      <Text
                        style={{
                          color: theme.CTA_BACKGROUND,
                          fontFamily: theme.FONT_FAMILY,
                          fontSize: 14,
                        }}>
                        Contact us
                      </Text>
                    </Pressable>
                  </View>
                </View>
                <View>
                  <Button
                    size="MEDIUM"
                    variant="TEXT"
                    color="PRIMARY"
                    onPress={() => setTogglePlanShow(!togglePlanShow)}>
                    Update here
                  </Button>
                </View>
              </View>
            )}
          </View>
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    flexDirection: 'row',
  },
  container: {
    flex: 1,
  },
  selectPlanWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 30,
  },
  radioWrapper: {
    marginTop: '2%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
  },
  pricePlanWrapper: {backgroundColor: '#FFF', borderRadius: 20, padding: 5, marginTop: 20},
  planSelectText: {fontSize: 20, fontWeight: '600', color: '#000', alignContent: 'center'},
  pricingCardWrapper: {borderRadius: 24, marginTop: 10, width: 270, borderWidth: 1, borderColor: '#E5E5E5'},
  addOnHeader: {flexDirection: 'row', justifyContent: 'space-between'},
  addOnContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  addOnContentWrapper: {
    marginTop: 20,
  },
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  contentContainer: {width: '95%', marginBottom: 50, paddingVertical: 40},
  mainAddOnMidContainer: {
    marginTop: '2%',
    backgroundColor: '#FFFFFF',
    border: '1.2px solid #E4E4E4',
    borderRadius: 20,
    padding: '2%',
  },
  mainMidContainer: {
    marginTop: '2%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    border: '1.2px solid #E4E4E4',
    borderRadius: 20,
    padding: '2%',
  },
  planDescriptionContainer: {
    marginTop: '2%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    backgroundColor: '#FFFFFF',
    border: '1.2px solid #E4E4E4',
    borderRadius: 20,
    padding: '2%',
    marginBottom: '2%',
  },
  mainBottomContainer: {
    marginTop: '2%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    border: '1.2px solid #E4E4E4',
    borderRadius: 20,
    padding: '2%',
    height: '80px',
    marginBottom: '2%',
  },
  pricingContainer: {
    flexDirection: 'row',
    // padding: 12,
    flexWrap: 'nowrap',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    overflow: 'scroll',
    gap: 10,
    flex: 1,
  },
  rounderContainer: {},
  featuresBox: {justifyContent: 'flex-start', flex: 1},
  featureItem: {flexDirection: 'row', alignItems: 'center'},
  featureText: {padding: 8, flex: 1},
});

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {
    ...bindActionCreators(
      {
        fetchPlanList,
        fetchMySubscription,
      },
      dispatch,
    ),
  };
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    plans: selectSubscriptionPlans(state),
    currentPlan: selectCurrentPlanWithDetails(state),
    currentSubscription: state.billing.subscription,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(Pricing);
