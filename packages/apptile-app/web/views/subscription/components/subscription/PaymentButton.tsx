import {selectSubscription} from '@/root/web/selectors/BillingSelector';
import React, {useEffect} from 'react';
import {View} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';
import {fetchPaymentUrl} from '../../../../actions/editorActions';
import {EditorRootState} from '../../../../store/EditorRootState';
import BillingButton from '../BillingButton';
import {COLORS} from '@/root/web/styles-v2/types';
import Analytics from '@/root/web/lib/segment';
import {BillingIntervalEnum} from '../../PublishPricing';

interface PaymentButtonProps {
  planId: string;
  color?: COLORS;
  title?: string;
  billingInterval?: BillingIntervalEnum;
}

const PaymentButton: React.FC<PaymentButtonProps> = params => {
  const {planId, color, title = 'Select plan', billingInterval} = params;
  const subscription = useSelector(selectSubscription);
  const dispatch = useDispatch();
  const appId = useSelector((state: EditorRootState) => state.apptile.appId);
  const {confirmationUrl, paymentUrlFetched, paymentUrlFetching} = subscription;
  // const appBridge = useAppBridge();

  // const redirectRemote = useCallback(
  //   (url: string) => {
  //     if (appBridge) {
  //       const redirect = Redirect.create(appBridge);
  //       redirect.dispatch(Redirect.Action.REMOTE, url);
  //     }
  //   },
  //   [appBridge],
  // );

  useEffect(() => {
    if (paymentUrlFetched && confirmationUrl) {
      if (window.top === window.self) {
        window.location = confirmationUrl as any;
      } else {
        window.parent.location = confirmationUrl as any;
        // redirectRemote(confirmationUrl);
      }
    }
  }, [paymentUrlFetched, confirmationUrl]);

  const onPaymentClick = () => {
    Analytics.track('editor:paymentButton_selectPlanClicked');
    dispatch(fetchPaymentUrl(planId, appId, billingInterval));
  };

  return (
    <View>
      <BillingButton color={color} title={title} onPress={onPaymentClick} loading={paymentUrlFetching} />
    </View>
  );
};

export default PaymentButton;
