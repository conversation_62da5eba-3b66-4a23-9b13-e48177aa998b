import {selectCurrentPlanWithDetails, selectCurrentSubscription} from '@/root/web/selectors/BillingSelector';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import React, {useEffect} from 'react';
import {LogRocket} from 'apptile-core';
import {useSelector} from 'react-redux';
import {checkApptileEmailSelector} from '@/root/web/selectors/FeatureGatingSelector';
import {ISourcePlatformType} from '../../../../../apptile-core/common/datatypes/types';

const getBrandSelector = (state: EditorRootState) => {
  return state.appModel?.getModelValue(['shopify', 'shop']);
};

export const LogRocketIdentify = ({orgId}: {orgId: string}) => {
  const {name: planName} = useSelector(selectCurrentPlanWithDetails) ?? {};
  const currentActiveSubscription = useSelector(selectCurrentSubscription);
  const {userLoggedIn, userFetched, user} = useSelector((state: EditorRootState) => state.user);
  const {orgsById} = useSelector((state: EditorRootState) => state.orgs);
  const shopDomain = useSelector(getBrandSelector)?.primaryDomain?.host;
  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;
  const partner = localStorage.getItem('apptile-partner-name');

  //Initialize segment
  const myStoreUrl = orgsById[orgId]?.sourcePlatformIdentity;
  // to make sure that the logrocket is enabled only when shopify is opened in the iframe
  const isRoot = window.self == window.top;

  //Initialize
  useEffect(() => {
    try {
      const logRocketKey = window.apptileWebSDK?.moduleExports?.logRocketKey || '97heiy/apptile-web';
      if (
        !isApptileEmail &&
        // commented this for temporary, untill finding the root cause for the shoplazza logrocket issue
        // process.env.ENABLE_LOGROCKET &&
        (!isRoot || partner == ISourcePlatformType.SHOPLAZZA)
      ) {
        logger.info('Initializing logrocket', myStoreUrl);
        LogRocket.init(logRocketKey as string);
      } else {
        logger.info('Not initializing logrocket');
      }
    } catch (err) {
      console.error('Initialization of logrocket failed');
    }
  }, [isApptileEmail, isRoot, myStoreUrl]);

  //Identify
  useEffect(() => {
    if (
      user &&
      userFetched &&
      userLoggedIn &&
      orgId &&
      // commented this for temporary, untill finding the root cause for the shoplazza logrocket issue
      // process.env.ENABLE_LOGROCKET &&
      (!isRoot || partner == ISourcePlatformType.SHOPLAZZA) &&
      !isApptileEmail
    ) {
      logger.info('Identifying logrocket');
      //@ts-ignore
      LogRocket.identify(user.id, {
        name: `${user.firstname} ${user.lastname}`,
        email: user.email,
        storeUrl: myStoreUrl ?? null,
        plan: currentActiveSubscription ? planName : null,
        shopDomain: shopDomain ?? null,
        buildnumber: (window as any).BN_FOR_LOGROCKET,
      });
      logger.info('LogRocket initialized');
    }
  }, [
    user,
    userLoggedIn,
    userFetched,
    orgId,
    planName,
    currentActiveSubscription,
    myStoreUrl,
    shopDomain,
    isRoot,
    isApptileEmail,
  ]);

  return <></>;
};
