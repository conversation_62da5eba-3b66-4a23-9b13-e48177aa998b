import {Provider} from '@shopify/app-bridge-react';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {connect, useDispatch, useSelector} from 'react-redux';
import {Route, Routes, useLocation, useMatch} from 'react-router';
import {
  fetchAppBranches,
  fetchAppIntegrations,
  fetchIntegrationCategories,
  fetchIntegrations,
  fetchMyAddOns,
  fetchMySubscription,
  fetchPlanList,
  fetchVideoAssets,
  platformInit,
  setActiveNotificationProvider,
  setEditorFeatures,
  setHasOneSignal,
} from '../../actions/editorActions';
import {EditorRootState} from '../../store/EditorRootState';
import PlatformRouter from './PlatformRouter';
import {useAppBridge} from '@shopify/app-bridge-react';
import {Fullscreen} from '@shopify/app-bridge/actions';
import {View} from 'react-native';
import Button from '../../components-v2/base/Button';
import {FullscreenBar} from './components/FullscreenBar';
import {setShopifyState} from '../../actions/shopifyActions';
import _ from 'lodash';
import {planFeaturesList} from 'apptile-core';
import {selectCurrentPlanWithDetails} from '../../selectors/BillingSelector';
import {fetchOnboardingMetadata} from '../../actions/onboardingActions';
import {checkApptileEmailSelector} from '../../selectors/FeatureGatingSelector';
import {whitelistedEmails} from '../../common/featureGatingConstants';
import {ISourcePlatformType} from '../../../../apptile-core/common/datatypes/types';
import {loadWebSDKBundle} from '../../index.web';
import TilePlatformRouter from '../prompt-to-app/TilePlatformRouter';
import {APPTILE_VERTICAL_TYPE} from '../../../.env.json';

const apiKey = process.env.REACT_APP_SHOPIFY_API_KEY;
const isTileDev = APPTILE_VERTICAL_TYPE === 'tile';

const PlatformInit: React.FC = ({}) => {
  const location = useLocation();
  const dispatch = useDispatch();

  const platformState = useSelector((state: EditorRootState) => state.platform);
  const {platformInitialized, platformHost, isEmbeddedInShopify} = platformState;

  const getHost = () => {
    const queryParams: URLSearchParams = new URLSearchParams(location.search);
    return queryParams.get('host') ? queryParams.get('host') : platformHost;
  };

  const host = getHost();
  const orgId = useMatch('/dashboard/:orgId/*')?.params?.orgId ?? '';
  const {appId, forkId, appSaveId} = useSelector((state: EditorRootState) => state.apptile);
  const {appsById} = useSelector((state: EditorRootState) => state.orgs);
  const platformType = appsById?.[appId as string]?.platformType || ISourcePlatformType.SHOPIFY;

  const callWebSDKBundle = (platformType: ISourcePlatformType) => {
    const checkPlatformType = localStorage.getItem('apptile-partner-name');
    if (!checkPlatformType) {
      localStorage.setItem('apptile-partner-name', platformType);
      loadWebSDKBundle();
    } else {
      // If the platformtype changes, then replacing the platformType value in the localStorage and calling the function
      if (platformType != checkPlatformType) {
        localStorage.setItem('apptile-partner-name', platformType);
        loadWebSDKBundle();
      }
    }
  };

  useEffect(() => {
    if (host && platformType) {
      callWebSDKBundle(platformType);
      dispatch(platformInit(host, platformType, true));
      window.appHost = host;
    }
  }, [dispatch, host, platformType]);

  useEffect(() => {
    if (orgId && !host && platformType) {
      callWebSDKBundle(platformType);
      dispatch(platformInit('', platformType, false, orgId));
    }
  }, [dispatch, orgId, platformType]);

  const preloader = document.querySelector('.preloader');
  const hidePreloader = useCallback(
    () => () => {
      if (preloader) preloader.remove();
    },
    [preloader],
  );

  useEffect(() => {
    if (platformInitialized) hidePreloader();
  }, [platformInitialized, hidePreloader]);

  useEffect(() => {
    dispatch(fetchPlanList());
    dispatch(fetchMySubscription(appId as string, orgId as string));
  }, [dispatch, platformType]);

  useEffect(() => {
    if (appId) {
      // dispatch(fetchMySubscription(appId as string, orgId as string));
      dispatch(fetchMyAddOns(appId as string));
      dispatch(fetchAppIntegrations(appId as string));
      dispatch(fetchIntegrations());
      dispatch(fetchIntegrationCategories());
      dispatch(fetchOnboardingMetadata(appId as string));
      dispatch(fetchAppBranches(appId as string, forkId));
      dispatch(fetchVideoAssets());
    }
  }, [appId, forkId, dispatch]);

  //Check if appintegrations has onesignal
  const appIntegrationsById = useSelector((state: EditorRootState) => state.integration.appIntegrationsById) ?? {};
  //getting all the integrations whose category is 'notification'
  const notificationIntegrations = Object.values(appIntegrationsById).filter(
    integration => integration.category === 'Notifications',
  );
  //getting the active notification integrations
  const activeNotificationIntegrations = notificationIntegrations.filter(
    integration => integration.appIntegrations[0].active,
  );
  //If the active list doesnot contain oneSignal then redirect to NotificationUnavailable page
  const activeNotificationIntegrationCode = activeNotificationIntegrations[0]?.integrationCode;
  useEffect(() => {
    if (activeNotificationIntegrations.length === 0) {
      dispatch(setActiveNotificationProvider('APPTILE'));
    }
    if (activeNotificationIntegrations.length > 0) {
      dispatch(setActiveNotificationProvider(activeNotificationIntegrationCode));
      if (activeNotificationIntegrationCode === 'oneSignal') {
        dispatch(setHasOneSignal(appId as string));
      }
    }
  }, [dispatch, activeNotificationIntegrationCode]);

  const currentPlan = useSelector(selectCurrentPlanWithDetails);

  const basePlan = currentPlan?.basePlan ?? currentPlan;

  let editorFeatures =
    _.find(planFeaturesList, (e: any) => e.serverCode === basePlan?.name)?.allowedFeatures ??
    planFeaturesList.CORE.allowedFeatures;

  const {userFetched, user} = useSelector((state: EditorRootState) => state.user);
  const isApptileUser = useSelector(checkApptileEmailSelector);

  if (userFetched && user?.email && typeof user?.email === 'string') {
    if (isApptileUser || whitelistedEmails.includes(user?.email))
      editorFeatures = planFeaturesList.ENTERPRISE.allowedFeatures;
  }
  useEffect(() => {
    dispatch(setEditorFeatures(editorFeatures));
  }, [dispatch, editorFeatures]);

  const routes = (
    <Routes>
      <Route path="*" element={isTileDev ? <TilePlatformRouter /> : <PlatformRouter />} />
    </Routes>
  );

  return (
    <>
      {isEmbeddedInShopify && (
        <Provider
          config={{
            apiKey: apiKey,
            host: host,
            forceRedirect: true,
          }}>
          {platformInitialized && host ? <ShopifyFullScreenControl host={host} /> : <></>}
          {platformInitialized && routes}
        </Provider>
      )}
      {!isEmbeddedInShopify && <>{routes}</>}
    </>
  );
};

const ShopifyFullScreenControl = ({host}) => {
  const app = useRef<any>(useAppBridge());
  const shopifyFullScreen = useRef<any>();
  const dispatch = useDispatch();
  const [isFullScreen, SetIsFullScreen] = useState<boolean>(false);

  // const fullscreen = Fullscreen.create(app);
  useEffect(() => {
    // setShopifyBridge(app);
    // setShopifyFullScreen(fullscreen);
    shopifyFullScreen.current = Fullscreen.create(app.current);
    const unsubscribeEnter = shopifyFullScreen.current?.subscribe(Fullscreen.Action.ENTER, () => {
      SetIsFullScreen(true);
    });
    const unsubscribeExit = shopifyFullScreen.current?.subscribe(Fullscreen.Action.EXIT, () => {
      SetIsFullScreen(false);
    });
    return () => {
      unsubscribeEnter();
      unsubscribeExit();
    };
  }, [app]);

  const setFullScreen = () => {
    shopifyFullScreen.current?.dispatch(isFullScreen ? Fullscreen.Action.EXIT : Fullscreen.Action.ENTER);
  };

  useEffect(() => {
    dispatch(setShopifyState({isFullscreen: isFullScreen}));
  }, [dispatch, isFullScreen]);

  useEffect(() => {
    if (window.platformConfigs?.requestedFullScreen) {
      SetIsFullScreen(true);
      shopifyFullScreen.current?.dispatch(Fullscreen.Action.ENTER);
    }
  }, []);

  return app ? (
    <>
      {isFullScreen ? (
        <FullscreenBar setFullScreen={setFullScreen} isFullScreen={isFullScreen} />
      ) : (
        <View style={[{position: 'absolute', bottom: 50, left: 20, zIndex: 1}]}>
          <Button
            containerStyles={{backgroundColor: '#FFF', borderColor: 'transparent'}}
            variant="PILL"
            color="SECONDARY"
            size="LARGE"
            icon={isFullScreen ? 'fullscreen-exit' : 'fullscreen'}
            onPress={() => setFullScreen()}>
            {/* {isFullScreen ? 'Exit Fullscreen' : 'Enter Fullscreen} */}
          </Button>
          {/* <FullscreenModal isFullScreen={isFullScreen} setFullScreen={setFullScreen} /> */}
        </View>
      )}
    </>
  ) : null;
};

export default connect()(PlatformInit);
