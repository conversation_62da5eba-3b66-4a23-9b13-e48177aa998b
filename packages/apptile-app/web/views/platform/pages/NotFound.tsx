import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import AuthTopBanner from '../../auth/components/AuthTopBanner';

const NotFound: React.FC = ({}) => {
  return (
    <View style={styles.container}>
      <AuthTopBanner />
      <View style={styles.wrapperStyle}>
        <View>
          <Text style={styles.header}>Page Not found!</Text>
          <Text style={styles.descText}>The page you are looking for might have been removed</Text>
          <Text style={styles.descText}>had its name changed or is temporarily unavailable.</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // alignItems: 'center',
    // justifyContent: 'flex-start',
    // overflow: 'scroll',
    backgroundColor: '#ECE9E1',
    minHeight: '100vh',
  },
  header: {
    fontSize: 40,
    fontWeight: '600',
    margin: 20,
    textAlign: 'center',
    color: '#000000',
  },
  descText: {
    fontWeight: '500',
    textAlign: 'center',
  },
  wrapperStyle: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    borderRadius: 10,
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
  },
});

export default NotFound;
