import React, {useEffect} from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import Button from '@/root/web/components-v2/base/Button';
import Analytics from '@/root/web/lib/segment';
import AuthTopBanner from '../../auth/components/AuthTopBanner';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();


const NetworkUnavailable: React.FC = ({}) => {
  // const location = useLocation();
  const onReload = () => {
    window.location.reload();
  };

  useEffect(() => {
    Analytics.track('editor:networkErrorScreen_networkRequestFailed');
  }, []);

  return (
    <View style={styles.container}>
      <View style={{position: 'absolute', top: 0, right: 0, left: 0}}>
        <AuthTopBanner />
      </View>
      <View style={styles.wrapperStyle}>
        <Image
          source={require('@/root/web/assets/images/something_went_wrong.png')}
          style={{width: 162, height: 148}}
        />
        <Text style={[commonStyles.baseText, styles.header]}>Something went wrong</Text>
        <Text style={[commonStyles.baseText, styles.headerSub]}>Please reload the page</Text>
        <Button size="LARGE" onPress={onReload} containerStyles={{paddingHorizontal: 24}} color="CTA">
          Reload
        </Button>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'scroll',
    height: '100vh',
    backgroundColor: '#ece9e1',
  },
  header: {
    fontSize: 32,
    fontWeight: '600',
    textAlign: 'center',
  },
  headerSub: {
    fontSize: 16,
    marginTop: 20,
    marginBottom: 16,
  },
  descText: {
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 20,
  },
  wrapperStyle: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 140,
    paddingVertical: 32,
    borderRadius: 10,
    flexBasis: 'auto',
    flexGrow: 0,
    flexShrink: 0,
  },
});

export default NetworkUnavailable;
