import DropDownControl from '@/root/web/components/controls/DropDownControl';
import React, {useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, TextInput, View} from 'react-native';
import {useDispatch} from 'react-redux';
import {MaterialCommunityIcons} from 'apptile-core';
import {createOrgApp, getBlueprints} from '../../../actions/editorActions';
import {useParams} from '../../../routing.web';
import {useSelector} from 'react-redux';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import CollapsiblePanel from '../../../components/CollapsiblePanel';
import Button from '../../../components-v2/base/Button';

interface CreateAppModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const CreateAppModal: React.FC<CreateAppModalProps> = ({isVisible, onClose}) => {
  const [formVisible, setFormVisible] = useState(isVisible);
  useEffect(() => {
    setFormVisible(isVisible);
  }, [isVisible]);
  const [name, setName] = useState<string>();
  const [baseBlueprintId, setBlueprintName] = useState<string>('');
  const dispatch = useDispatch();
  let params = useParams();
  const orgId = params.orgId;
  const platformType = useSelector((state: EditorRootState) => state.platform.platformType);
  useEffect(() => {
    dispatch(getBlueprints(platformType as string));
  }, []);

  const blueprintState = useSelector((state: EditorRootState) => state.blueprint);
  const onCreateNew = () => {
    dispatch(createOrgApp(orgId, name, baseBlueprintId));
    setFormVisible(false);
    if (onClose) onClose();
  };

  const onCloseHandler = useCallback(() => {
    setFormVisible(!formVisible);
    if (onClose) onClose();
  }, [formVisible, onClose]);

  return (
    <CollapsiblePanel
      title="Create New App"
      isHeaderHidden
      isOpen={formVisible}
      overflowStyle={{width: 450, backgroundColor: 'white'}}
      backgroundStyle={{flex: 1, marginTop: 40, backgroundColor: 'transparent', alignItems: 'center'}}>
      <View style={styles.modalTitlebar}>
        <Text style={styles.modalText}>Create an app</Text>
        <Pressable style={styles.buttonClose} onPress={onCloseHandler}>
          <MaterialCommunityIcons size={24} name="close" />
        </Pressable>
      </View>

      <View style={styles.formStyle}>
        <View style={styles.textInputWrapper}>
          <Text style={styles.textInputLabel}>App name</Text>
          <TextInput
            defaultValue={name}
            onChangeText={value => setName(value)}
            placeholder={'App name'}
            style={styles.textInput}
          />
        </View>

        <View style={styles.textInputWrapper}>
          <Text style={styles.textInputLabel}>Base blueprint</Text>
          <DropDownControl
            defaultValue={baseBlueprintId}
            onChange={value => setBlueprintName(value)}
            label={'Blueprint'}
            options={blueprintState.blueprints}
            value={baseBlueprintId}
            disableBinding={true}
            nameKey="name"
            valueKey="id"
          />
        </View>
        <View style={styles.buttonWrapper}>
          <Button iconPosition="LEFT" variant="FILLED" color="CTA" onPress={onCreateNew}>
            Create App
          </Button>
        </View>
      </View>
    </CollapsiblePanel>
  );
};

const styles = StyleSheet.create({
  formStyle: {
    flex: 1,
    width: 450,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonClose: {flex: 1, textAlign: 'right'},
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: {
    flex: 1,
    fontSize: 20,
    fontWeight: '600',
  },
  appHeaderAction: {
    flex: 1,
  },
  textInputLabel: {
    padding: 8,
    paddingLeft: 0,
    fontSize: 16,
  },
  textInputWrapper: {
    width: '100%',
    borderRadius: 5,
    padding: 20,
    flex: 1,
  },
  buttonWrapper: {
    width: '100%',
    borderRadius: 5,
    padding: 20,
    flex: 1,
  },
  textInput: {
    backgroundColor: '#fff',
    paddingHorizontal: 10,
    paddingVertical: 10,
    borderColor: '#ccc',
    borderRadius: 5,
    borderWidth: 1,
  },
  modalTitlebar: {
    flexDirection: 'row',
    width: 450,
    padding: 20,
  },
});

export default CreateAppModal;
