import React, {useState, useEffect, useCallback} from 'react';
import {StyleSheet, View, Text, Pressable} from 'react-native';
import Modal from '@/root/web/components-v2/base/Modal';
import {DatePickerModalContent} from 'rn-dates';
import moment from 'moment';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import Button from '@/root/web/components-v2/base/Button';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface DateAndTimeControlProps {
  value: string | null;
  placeholder?: string;
  onChange: (value: string | null) => void;
  disabled?: boolean;
  start: any;
  end: any;
}

const styles = StyleSheet.create({
  container: {
    flexBasis: 'auto',
    marginVertical: theme.PRIMARY_MARGIN,
  },
  dateTimePickerContainer: {
    width: 328,
    borderRadius: 28,
  },
  DateInputStyle: {
    padding: 10,
  },
  icon: {
    position: 'absolute',
    right: 8,
    top: 8,
  },
  modalTop: {
    padding: 14,
    borderBottomColor: theme.INPUT_BORDER,
    borderBottomWidth: 1,
  },
  modalMiddle: {
    paddingHorizontal: 10,
  },
  modalBottom: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopColor: theme.INPUT_BORDER,
    borderTopWidth: 1,
  },
});

const DatePicker: React.FC<DateAndTimeControlProps> = ({onChange, value, disabled, start, end}) => {
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState<string | null>(value);

  useEffect(() => {
    if (value) {
      setSelectedDate(value);
    }
  }, [value]);

  const handleDateChange = useCallback(
    (dateString: string) => {
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) {
        const formattedDate = date.toISOString().split('T')[0];
        setSelectedDate(formattedDate);
        onChange(formattedDate);
      } else {
        setSelectedDate(null);
        onChange(null);
      }
    },
    [onChange],
  );

  return (
    <>
      <View style={styles.container}>
        <View style={[disabled && {opacity: '50%'}]}>
          <Pressable
            style={[styles.DateInputStyle, commonStyles.input]}
            disabled={disabled}
            onPress={() => setDatePickerVisible(true)}>
            <Text style={commonStyles.inputText}>{selectedDate ? selectedDate : 'Select a date'}</Text>
            <View style={styles.icon}>
              <ApptileWebIcon name={'calender'} size={18} />
            </View>
          </Pressable>
        </View>
      </View>

      <Modal
        onVisibleChange={setDatePickerVisible}
        visible={isDatePickerVisible}
        content={
          <DateTimePicker
            date={selectedDate ? new Date(selectedDate) : new Date()}
            onChange={handleDateChange}
            setPickerVisible={setDatePickerVisible}
            start={start}
            end={end}
          />
        }
      />
    </>
  );
};

type DateTimePickerProps = {
  onChange: (value: string) => void;
  date: Date;
  setPickerVisible: (value: boolean) => void;
  start: any;
  end: any;
};

const DateTimePicker: React.FC<DateTimePickerProps> = ({onChange, date, setPickerVisible, start, end}) => {
  const [currentDate, setCurrentDate] = useState(date.toISOString().split('T')[0]);

  const themeValue = {
    secondaryColor: '#fff',
    primaryColor: theme.CONTROL_ACTIVE_COLOR,
    accentColor: theme.CONTROL_INPUT_COLOR,
    backgroundColor: theme.INPUT_BACKGROUND,
    fontFamily: theme.FONT_FAMILY,
  };

  const onDateChange = useCallback(params => {
    const newDate = new Date(params.date);
    if (!isNaN(newDate.getTime())) {
      setCurrentDate(newDate.toISOString().split('T')[0]);
    }
  }, []);
  console.log('currentdate', currentDate);
  return (
    <View style={styles.dateTimePickerContainer}>
      <View style={styles.modalTop}>
        <Text style={commonStyles.baseText}>Select Date</Text>
        <View style={{marginTop: 25, flexDirection: 'row', justifyContent: 'space-between'}}>
          <Text style={[commonStyles.baseText, {fontSize: 30, lineHeight: 32}]}>{currentDate}</Text>
          <ApptileWebIcon name="edit-icon" size={30} />
        </View>
      </View>
      <View style={styles.modalMiddle}>
        <DatePickerModalContent
          locale="en"
          mode="single"
          date={new Date(currentDate)}
          onChange={onDateChange}
          validRange={{
            startDate: new Date(),
            endDate: moment().add(30, 'days').toDate(),
          }}
          themeValue={themeValue}
        />
      </View>
      {end && start >= currentDate && (
        <View>
          <Text
            style={[commonStyles.baseText, {color: theme.ERROR_BACKGROUND, paddingBottom: 30, textAlign: 'center'}]}>
            Date cannot be in the past.
          </Text>
        </View>
      )}
      <View style={styles.modalBottom}>
        <Button
          variant="TEXT"
          color="SECONDARY"
          onPress={() => {
            setPickerVisible(false);
          }}>
          Cancel
        </Button>
        <Button
          variant="TEXT"
          color="SECONDARY"
          disabled={end ? (start >= currentDate ? true : false) : false}
          onPress={() => {
            setPickerVisible(false);
            onChange(currentDate); 
          }}>
          Ok
        </Button>
      </View>
    </View>
  );
};

export default DatePicker;
