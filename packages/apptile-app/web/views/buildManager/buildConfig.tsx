import React, {useEffect, useRef} from 'react';
import {copyToClipboard} from '../../common/utils';
import {ActivityIndicator} from 'react-native';
import {View, Text, StyleSheet, TextInput, Pressable, ScrollView, Switch, TouchableOpacity} from 'react-native';
import {BackButton, Title, Button} from './shared';
import {useParams} from '@/root/web/routing.web';
import {BuildManagerApi} from '@/root/web/api/BuildApi';
import AppConfigApi from '@/root/web/api/AppConfigApi';
import {isEmpty} from 'lodash';
import {makeToast} from '@/root/web/actions/toastActions';
import {useDispatch} from 'react-redux';
import {useNavigate} from 'react-router';
import {currentPlanFeaturesSelector} from '@/root/web/selectors/FeatureGatingSelector';
import packageJson from '@/root/package.json';
import {MaterialCommunityIcons} from 'apptile-core';
import {allAvailablePlans, Plan} from 'apptile-core';
import validator from 'validator';
import DropDownControl from '../../components/controls/DropDownControl';
import {useBuildPlatformContext} from './context';
import {useSelector} from 'react-redux';
import {selectCurrentPlanWithDetails} from '@/root/web/selectors/BillingSelector';
import _ from 'lodash';
import Icon from 'react-native-vector-icons/AntDesign';

type IBuildConfig = {
  setScreen: (v: 'SETTINGS' | 'ASSETS' | 'SECRETS' | 'BUILD_CONFIG') => void;
};

type LastBuildVersion_ApiResponse = {
  semVersion: string;
  version: string;
};

type headNames = string[];
type headOptions = {
  name: string;
  value: string;
};
type IConfig = {
  version: string;
  semVersion: string;
};

export const BuildConfig: React.FC<IBuildConfig> = props => {
  const param = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {record, featureFlags} = useBuildPlatformContext();
  const storeDistributionConfigRef = useRef(null);
  const {user} = useSelector((state: EditorRootState) => state.user);

  const platform = record.platform;

  const currentPlan = useSelector(selectCurrentPlanWithDetails)?.name;

  const isEnterpriseOrPlus = currentPlan === 'PLUS' || currentPlan === 'ENTERPRISE';

  // const [buildAndroid, setBuildAndroid] = React.useState(true);
  // const [buildIos, setBuildIos] = React.useState(true);
  const [buildSourceGitHeadNames, setbuildSourceGitHeadNames] = React.useState<headOptions[] | []>([]);
  const [tagNames, setTagNames] = React.useState<headOptions[] | []>([]);
  const [selectedtag, setSelectedTag] = React.useState('v0.12.0');
  const [selectedBranch, setSelectedBranch] = React.useState('');
  const [isValid, setValid] = React.useState<boolean>(true);
  const [isBuildDisabled, setBuildDisabled] = React.useState<boolean>(false);
  const [developerMode, setDeveloperMode] = React.useState<boolean>(false);
  const [devConfig, setDevConfig] = React.useState<any>('');
  const [isLoading, setLoading] = React.useState<boolean>(true);

  const [config, setConfig] = React.useState({
    version: '',
    semVersion: '',
    publishOnApptile: true,
    sentry_sample_rate: '0.05',
    enableSourceMap: true,
    enableOneSignal: true,

    enableMoEngage: false,
    enableCleverTap: false,
    enableApptileAnalytics: isEnterpriseOrPlus,
    enableLively: false,
    enableAppTrackingTransparency: false,
    enableIpadSupport: false,
    forceAppGroupCreation: false,
    isIndividualAccount: false,
    firebaseConfig: {
      creationType: 'USE_MANUALLY_UPLOADED_FIREBASE',
      existingProjectId: '',
    },
    enableNativeSplash: false,

    enableLivelyPIP: false,
  });

  const updateFirebaseConfigType = value => {
    setConfig(prev => ({...prev, firebaseConfig: {...prev.firebaseConfig, creationType: value}}));
  };

  const setExistingProjectId = value => {
    setConfig(prev => ({...prev, firebaseConfig: {...prev.firebaseConfig, existingProjectId: value}}));
  };

  const updateConfig = (key: keyof typeof config) => {
    return (value: string | boolean | string[]) => {
      setConfig(prev => {
        let updatedConfig = {...prev, [key]: value};

        // Handle enableLivelyPIP depending on enableLively and plan type
        if (key === 'enableLively') {
          if (value) {
            if (isEnterpriseOrPlus) {
              updatedConfig.enableLivelyPIP = true; // Automatically enable PIP for enterprise or plus plans
            }
          } else {
            updatedConfig.enableLivelyPIP = false; // Automatically deselect PIP when enableLively is false
          }
        }

        return updatedConfig;
      });
    };
  };

  React.useEffect(() => {
    validateBuildConfig(config, setValid);
  }, [config]);

  React.useEffect(() => {
    if (featureFlags?.builds_disabled !== undefined) {
      // Convert the string "true"/"false" to a boolean as its a jsonb type in db. added jsonb for flexibility in db
      setBuildDisabled(featureFlags.builds_disabled === 'true');
      console.log(featureFlags.builds_disabled === 'true', 'Set Builds Disabled');
    }
  }, [featureFlags]);
  console.log(featureFlags, 'featureFlags from Build Config....');

  React.useEffect(() => {
    const fetchBuildData = async () => {
      try {
        const autoFetchVersion = async () => {
          try {
            const appID = param.id as string;
            if (isEmpty(appID)) return;

            // Call the API and retrieve the response
            const response = await BuildManagerApi.getLatestBuildVersion<LastBuildVersion_ApiResponse>(appID, platform);
            const {semVersion: apiSemVersion, version, ...messages} = response.data ?? {}; // assuming `messages` contains warnings, info, success, errors

            let semVersion = apiSemVersion;

            // Handle versioning logic
            if (semVersion) {
              const semVersionArray = semVersion.split('.');
              semVersionArray[2] = String(parseInt(semVersionArray[2]) + 1); // Increment the patch version
              semVersion = semVersionArray.join('.');
            } else {
              semVersion = '1.0.0';
            }

            updateConfig('semVersion')(semVersion);
            updateConfig('version')((Number.parseInt(version ?? 0) + 1).toString());

            console.log(messages);
            // Dispatch toast notifications based on messages from the API response
            messages?.success?.forEach((message: string) => {
              dispatch(
                makeToast({
                  content: message,
                  appearances: 'success',
                  duration: 5000,
                }),
              );
            });

            messages?.info?.forEach((message: string) => {
              dispatch(
                makeToast({
                  content: message,
                  appearances: 'info',
                  duration: 15000,
                }),
              );
            });

            messages?.warnings?.forEach((message: string) => {
              dispatch(
                makeToast({
                  content: message,
                  appearances: 'warning',
                  duration: 30000,
                }),
              );
            });

            messages?.errors?.forEach((message: string) => {
              dispatch(
                makeToast({
                  content: message,
                  appearances: 'error',
                  duration: 30000,
                }),
              );
            });
          } catch (err) {
            console.log(err, 'error here');
            dispatch(
              makeToast({
                content: 'An error occurred while fetching the build version.',
                appearances: 'error',
                duration: 3000,
              }),
            );
          }
        };

        const fetchLatestBuildConfig = async () => {
          const appID = param.id as string;
          if (isEmpty(appID)) return;

          const response = await BuildManagerApi.getLatestBuildConfig(appID, platform);
          const latestbuildConfig = response.data ?? {};
          storeDistributionConfigRef.current = response.data;

          let filteredConfig = _.omit(config, ['version', 'semVersion']);
          let pickedValues = _.pick(latestbuildConfig?.[platform.toLowerCase()], _.keys(filteredConfig));

          // Handle condition for `buildSourceGitHeadType === 'latestRelease'`

          pickedValues = _.omit(pickedValues, ['buildSourceGitHeadType', 'buildSourceGitHeadName']);

          // Handle `enableLively` and `enableLivelyPIP` logic
          if (latestbuildConfig?.[platform.toLowerCase()]?.enableLivelyPIP === undefined && pickedValues.enableLively) {
            pickedValues.enableLivelyPIP = isEnterpriseOrPlus ? true : false;
          }

          // Set the config with the updated values
          setConfig(prevConfig => {
            return {...prevConfig, ...pickedValues};
          });

          console.log(storeDistributionConfigRef.current);
        };

        await autoFetchVersion();
        await fetchLatestBuildConfig();
        setLoading(false);
      } catch (err) {
        console.log(err, 'error here');
      }
    };

    fetchBuildData();
  }, [param.id]);

  const handleBuild = async (platform: 'android' | 'ios') => {
    try {
      const appID = param.id as string;
      if (isEmpty(appID) || !isValid) return;

      const appManifest = await AppConfigApi.fetchAppManifest(appID);
      if (!appManifest?.forks?.[0]?.publishedCommitId) {
        throw new Error('Please Publish The App and Try Again');
      }

      const {
        firebaseConfig: {existingProjectId, ...restFirebaseConfig},
        ...restConfig
      } = config;

      const buildPayload = {
        ...restConfig,
        firebaseConfig: restFirebaseConfig,
        frameworkVersion: packageJson.version,
        platforms: [platform],
        triggeredBy: `${user.firstname} ${user.lastname}`,
        isSDKBuild: true, //THIS IS HARDCODED FOR TILES.DEV STAGING. NEED TO FIGURE OUT LOGIC FOR BUILDING SDK
      };

      if (buildPayload?.buildSourceGitHeadName.trim() === '') {
        delete buildPayload.buildSourceGitHeadName;
        delete buildPayload.buildSourceGitHeadType;
      }

      // Validate and assign devConfig if provided
      if (!isEmpty(devConfig)) {
        try {
          JSON.parse(devConfig);
          buildPayload.devConfig = devConfig;
        } catch {
          throw new Error('Dev Config must be a valid JSON');
        }
      }

      // Set Firebase existing project ID if using an existing Firebase setup
      if (buildPayload.firebaseConfig.creationType === 'USE_EXISTING_FIREBASE') {
        buildPayload.firebaseConfig.existingProjectId = existingProjectId;
      }

      const response = await BuildManagerApi.createBuild(appID, buildPayload);
      let responseData;

      // Parse response data, if it's a string
      if (typeof response.data === 'string') {
        try {
          responseData = JSON.parse(response.data);
        } catch (error) {
          console.error('Failed to parse response data as JSON:', error);
          return;
        }
      } else {
        responseData = response.data;
      }

      const {errors, warnings, info, success} = responseData;

      let hasPendingBuildMessage = false;

      // Handle success messages
      success?.forEach((message: string) => {
        dispatch(
          makeToast({
            content: message,
            appearances: 'success',
            duration: 5000,
          }),
        );
      });

      // Handle info messages and check for pending build
      info?.forEach((message: string) => {
        dispatch(
          makeToast({
            content: message,
            appearances: 'info',
            duration: 30000,
          }),
        );

        if (message.includes('is already in progress')) {
          hasPendingBuildMessage = true;
        }
      });

      // Handle warning messages
      warnings?.forEach((message: string) => {
        dispatch(
          makeToast({
            content: message,
            appearances: 'warning',
            duration: 30000,
          }),
        );
      });

      // Handle error messages
      errors?.forEach((message: string) => {
        dispatch(
          makeToast({
            content: message,
            appearances: 'error',
            duration: 30000,
          }),
        );
      });

      // Navigate if no errors and no pending build
      if ((!errors || errors.length === 0) && !hasPendingBuildMessage) {
        navigate(
          `/dashboard/${param?.orgId}/app/${param?.id}/f/${param.forkId}/b/${param.branchName}/builds/list`,
        );
      }
    } catch (err) {
      // Handle HTTP errors with specific messages
      if (err.response && err.response.status === 400) {
        const errorMessage = err.response.data.message || 'Bad Request';
        dispatch(
          makeToast({
            content: errorMessage,
            appearances: 'error',
            duration: 30000,
          }),
        );
      } else {
        console.log(err);
        dispatch(
          makeToast({
            content: err.message,
            appearances: 'error',
          }),
        );
      }
    }
  };

  return (
    <ScrollView style={styles.card}>
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        {isLoading && <ActivityIndicator size="large" />}
        {storeDistributionConfigRef.current && (
          <TouchableOpacity
            style={{
              marginBottom: 20,
              width: '100%',
              alignItems: 'flex-end',
            }}
            onPress={() => {
              try {
                copyToClipboard(JSON.stringify(storeDistributionConfigRef.current ?? {}));
                dispatch(
                  makeToast({
                    content: 'Distribution config has been copied succesfully',
                    appearances: 'success',
                  }),
                );
              } catch (err) {
                dispatch(
                  makeToast({
                    content: 'Distribution config  copy failed',
                    appearances: 'error',
                  }),
                );
                console.log(err);
              }
            }}>
            <Icon name="copy1" color="#0075F2" size={18} />
          </TouchableOpacity>
        )}
      </View>
      <BackButton onPress={() => props.setScreen('SECRETS')} />
      <Title content="Build Config" />
      <InputBox name="Version" value={config.version} onChange={updateConfig('version')} />
      <InputBox name="Semver Version" value={config.semVersion} onChange={updateConfig('semVersion')} />

      <InputBox
        name="Enter Your Branch"
        value={selectedBranch}
        onChange={value => {
          setSelectedBranch(value);
          updateConfig('buildSourceGitHeadName')(value);
          updateConfig('buildSourceGitHeadType')('branch');
        }}
      />

      {/* <DropDownControl
        label={'Firebase Configuration For Build'}
        value={config.firebaseConfig.creationType}
        defaultValue={'USE_MANUALLY_UPLOADED_FIREBASE'}
        options={[
          {name: 'From Uploaded Files', value: 'USE_MANUALLY_UPLOADED_FIREBASE'},
          {name: "Create Firebase on Apptile's Account", value: 'CREATE_FIREBASE_ON_APPTILE'},
          {
            name: "Create Firebase on Existing/Customer's Firebase with Ownership Access",
            value: 'USE_EXISTING_FIREBASE',
          },
        ]}
        onChange={updateFirebaseConfigType}
        nameKey={'name'}
        valueKey={'value'}
        disableBinding={true}
      /> */}

      {config.firebaseConfig.creationType === 'USE_EXISTING_FIREBASE' && (
        <InputBox
          name="Existing Firebase Project Id"
          value={config.firebaseConfig.existingProjectId}
          onChange={setExistingProjectId}
        />
      )}

      {/* Removing Flexibility :(     */}

      {/* <View>
        <Text>Developer Mode</Text>
        <Switch onValueChange={() => setDeveloperMode(!developerMode)} value={developerMode} />
      </View>

      {developerMode && <InputBox name="Developer Config" value={devConfig} onChange={setDevConfig} />} */}

      <Text style={{fontSize: 14, fontWeight: '700', marginTop: 50}}>Select Integrations</Text>
      <View style={{margin: 15, display: 'flex', flexDirection: 'row', flexWrap: 'wrap'}}>
        <CheckBoxWidget label="Sentry" checked={config.enableSourceMap} onChange={updateConfig('enableSourceMap')} />

        <CheckBoxWidget
          label="App Tracking Permission"
          checked={config.enableAppTrackingTransparency}
          onChange={updateConfig('enableAppTrackingTransparency')}
        />

        <CheckBoxWidget
          // isDisabled={!isPlusCustomer}
          label="Apptile Analytics"
          checked={config.enableApptileAnalytics}
          onChange={updateConfig('enableApptileAnalytics')}
        />

        <CheckBoxWidget label="MoEngage" checked={config.enableMoEngage} onChange={updateConfig('enableMoEngage')} />
        <CheckBoxWidget
          label="Clever Tap"
          checked={config.enableCleverTap}
          onChange={updateConfig('enableCleverTap')}
        />
        <CheckBoxWidget
          label="Force AppGroup Creation"
          checked={config.forceAppGroupCreation}
          onChange={updateConfig('forceAppGroupCreation')}
        />
        <CheckBoxWidget
          label="Ipad Support"
          checked={config.enableIpadSupport}
          onChange={updateConfig('enableIpadSupport')}
        />
        <CheckBoxWidget
          label="Native Splash"
          checked={config.enableNativeSplash}
          onChange={updateConfig('enableNativeSplash')}
        />

        <CheckBoxWidget
          label="Lively PIP"
          checked={config.enableLivelyPIP}
          onChange={updateConfig('enableLivelyPIP')}
          isDisabled={!config.enableLively} // Disable PIP if Lively is not enabled
        />

        <CheckBoxWidget
          label="Individual Account"
          checked={config.isIndividualAccount}
          onChange={updateConfig('isIndividualAccount')}
        />
      </View>
      <Button onPress={() => handleBuild(platform)} text="Build" isDisabled={!isValid || isBuildDisabled} />
    </ScrollView>
  );
};

type IInputBox = {
  name: string;
  value: string;
  onChange?: (value: string) => void;
};

const InputBox: React.FC<IInputBox> = props => {
  return (
    <View style={styles.inputBoxContainer}>
      <Text style={styles.labelText}>{props.name}</Text>
      <TextInput style={styles.inputField} value={props.value} onChangeText={props.onChange} />
    </View>
  );
};

const validateBuildConfig = (record: IConfig, valCB: (val: boolean) => void) => {
  let isValid = true;

  for (const key in validationMap) {
    const validatorFunc = validationMap[key];
    if (!validatorFunc(record[key], record)) {
      isValid = false;
    }
  }

  valCB(isValid);
};

export const validationMap: Partial<Record<keyof IConfig, (...args: any[]) => boolean>> = {
  version: (val: string) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isLength(val, {min: 1});
  },

  semVersion: (val: string) => {
    if (isEmpty(val)) {
      return false;
    }

    return validator.isSemVer(val);
  },
};

export const CheckBoxWidget = props => {
  const {label, checked, onChange, isDisabled} = props;
  const [isChecked, setChecked] = React.useState(checked);

  useEffect(() => {
    if (isDisabled) {
      setChecked(false);
    } else {
      setChecked(checked);
    }
  }, [checked, isDisabled]);

  return (
    <View style={[styles.checkboxContainer]}>
      <Pressable
        style={[
          styles.checkbox,
          {
            backgroundColor: 'transparent',
            borderColor: '#D1D5DB',
          },
        ]}
        onPress={() => {
          if (!isDisabled) {
            onChange(!isChecked);
            setChecked(!isChecked);
          }
        }}>
        {isChecked && <MaterialCommunityIcons name="check" size={18} />}
      </Pressable>

      <Text style={{marginLeft: 5}}>{label}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  inputBoxContainer: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    margin: 15,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    margin: 15,
    // justifyContent: 'center',
  },
  checkbox: {
    height: 20,
    // minWidth: 22,
    width: 22,
    borderWidth: 1,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  inputField: {
    outlineStyle: 'none',
    fontSize: 15,
    backgroundColor: '#f1f5f9',
    padding: 10,
    color: '#475569',
    width: '100%',
  },
  card: {
    position: 'relative',
    backgroundColor: '#f8fafc',
    width: 500,
    padding: 24,
    marginBottom: 10,
    paddingVertical: 30,
    borderRadius: 10,
    minHeighteight: 550,
  },
  labelText: {
    color: '#64748b',
    marginBottom: 10,
  },
});
