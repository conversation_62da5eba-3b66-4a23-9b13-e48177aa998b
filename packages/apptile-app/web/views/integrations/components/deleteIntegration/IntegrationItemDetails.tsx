import React from 'react';
import {clearTempPageData, fetchPage} from '@/root/web/actions/editorActions';
import {cloneDeep, get} from 'lodash';
import {Text, View, StyleSheet, ScrollView} from 'react-native';
import {useDispatch} from 'react-redux';
import {ApptileWebIcon} from '@/root/web/icons/ApptileWebIcon.web';
import TilesApi from '@/root/web/api/TilesApi';
import CircularProgressBar from './CircularLoader';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const IntegrationItemDetails = (props: any) => {
  const {tempPages, platformType, isAddIntegartion, isApplyTimer, setIsApplyTimer} = props;
  const [tiles, setTiles] = React.useState([]);
  const [progressBarState, setBarState]: any[] = React.useState([]);
  const [replaceScreens, setReplaceScreens] = React.useState(null);
  const [counter, setCounter] = React.useState(0);
  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(fetchPage([platformType], true, true, !isAddIntegartion));
    TilesApi.getTiles([platformType], 0, 50).then(resp => {
      setTiles(resp.data?.items);
    });
    return () => {
      dispatch(clearTempPageData());
    };
  }, []);

  React.useEffect(() => {
    let updatedProgressBarState = cloneDeep(progressBarState);
    if (tempPages.length && !replaceScreens) {
      const replaceScreens = tempPages.filter((page: any) => page.mappedScreen);
      if (replaceScreens.length) {
        setReplaceScreens(replaceScreens);
        updatedProgressBarState.push(0);
      }
      updatedProgressBarState.push(0);
      setBarState(updatedProgressBarState);
    }
  }, [tempPages.length]);

  React.useEffect(() => {
    let updatedProgressBarState = cloneDeep(progressBarState);
    if (tiles.length) {
      updatedProgressBarState.push(0);
      setBarState(updatedProgressBarState);
    }
  }, [tiles.length]);

  React.useEffect(() => {
    if (isApplyTimer) {
      const index = progressBarState.findIndex(value => value === 0 || value !== 100);
      if (index === -1 && isApplyTimer) {
        return setIsApplyTimer(null);
      }
      progressBarState.forEach((state, idx) => {
        if (index === idx) {
          progressBarState[idx] = progressBarState[idx] + 1;
        }
      });
      const timeout = setTimeout(() => {
        setBarState(progressBarState);
        setCounter(counter + 1);
      }, 15);
      return () => {
        clearTimeout(timeout);
      };
    }
  }, [isApplyTimer, counter, progressBarState]);

  const numberColor1 = progressBarState[0] > 0 && progressBarState[0] < 100;
  const numberColor2 = progressBarState[1] > 0 && progressBarState[1] < 100;
  const numberColor3 = progressBarState[2] > 0 && progressBarState[2] < 100;

  const getPagesProgressState = () => {
    if (progressBarState.length === 3 || (progressBarState.length === 2 && replaceScreens)) {
      return 1;
    }
    return 0;
  };

  const getTilesProgressState = () => {
    if (progressBarState.length === 3) {
      return 2;
    }
    if (progressBarState.length === 2) {
      return 1;
    }
    return 0;
  };

  const replaceTitleText = progressBarState[0] > 0 ? 'Replacing Pages' : 'Replace Pages';
  const addOrRemovePageText = isAddIntegartion
    ? progressBarState[getPagesProgressState()] > 0
      ? 'Adding Pages'
      : 'Add Pages'
    : progressBarState[getPagesProgressState()] > 0
    ? 'Removing Pages'
    : 'Remove Pages';
  const addOrRemoveTilesText = isAddIntegartion
    ? progressBarState[getTilesProgressState()] > 0
      ? 'Enabling Tiles'
      : 'Enable Tiles'
    : progressBarState[getTilesProgressState()] > 0
    ? 'Disabling Tiles'
    : 'Disable Tiles';

  return (
    <View style={styles.screenContainer}>
      <Text style={styles.infoText}>
        {isAddIntegartion ? 'Connecting' : 'Disconnecting'} will make the following changes:
      </Text>
      <ScrollView contentContainerStyle={styles.wrapper} style={styles.tilesSection} horizontal>
        {replaceScreens && (
          <View style={styles.column}>
            <View style={styles.progressBar}>
              {progressBarState[0] === 100 ? (
                <MaterialCommunityIcons name="checkbox-marked-circle" size={24} />
              ) : (
                <CircularProgressBar strokeWidth={3} sqSize={25} percentage={progressBarState[0]} number={1} />
              )}
              <Text style={[styles.title, numberColor1 ? {color: '#005BE4'} : {}]}>{replaceTitleText}</Text>
            </View>
            {replaceScreens.map(page => {
              const mappedScreen = get(page, 'mappedScreen', '');
              return (
                <View key={mappedScreen} style={styles.pageContainer}>
                  <ApptileWebIcon name={'pages'} size={16} style={styles.tileImg} />
                  <Text style={styles.pageName}>{mappedScreen}</Text>
                </View>
              );
            })}
          </View>
        )}
        {!!tempPages.length && (
          <View style={styles.column}>
            <View style={styles.progressBar}>
              {progressBarState[getPagesProgressState()] === 100 ? (
                <MaterialCommunityIcons name="checkbox-marked-circle" size={24} />
              ) : (
                <CircularProgressBar
                  strokeWidth={3}
                  sqSize={25}
                  percentage={progressBarState[getPagesProgressState()]}
                  number={getPagesProgressState() + 1}
                />
              )}
              <Text style={[styles.title, numberColor2 ? {color: '#005BE4'} : {}]}>{addOrRemovePageText}</Text>
            </View>
            {tempPages.map((page: any) => {
              return (
                <View key={page.name} style={styles.pageContainer}>
                  <ApptileWebIcon name={'pages'} size={16} style={styles.tileImg} />
                  <Text style={styles.pageName}>{page.name}</Text>
                </View>
              );
            })}
          </View>
        )}
        {!!tiles.length && (
          <View style={styles.column}>
            <View style={styles.progressBar}>
              {progressBarState[getTilesProgressState()] === 100 ? (
                <MaterialCommunityIcons name="checkbox-marked-circle" size={24} />
              ) : (
                <CircularProgressBar
                  strokeWidth={3}
                  sqSize={25}
                  percentage={progressBarState[getTilesProgressState()]}
                  number={getTilesProgressState() + 1}
                />
              )}
              <Text style={[styles.title, numberColor3 ? {color: '#005BE4'} : {}]}>{addOrRemoveTilesText}</Text>
            </View>
            {tiles.map((tile: any) => {
              return (
                <View key={tile.name} style={styles.pageContainer}>
                  <ApptileWebIcon name={'tiles'} size={16} style={styles.tileImg} />
                  <Text style={styles.pageName}>{tile.name}</Text>
                </View>
              );
            })}
          </View>
        )}
        {!tiles.length && !tempPages.length && <Text>There will be no impact on any screen or tile</Text>}
      </ScrollView>
    </View>
  );
};

export default IntegrationItemDetails;

const styles = StyleSheet.create({
  screenMapping: {
    flex: 1,
    flexDirection: 'row',
  },
  warning: {
    fontSize: 10,
    fontStyle: 'italic',
    marginTop: 8,
  },
  screenContainer: {
    marginVertical: 8,
    width: '100%',
  },
  note: {
    fontSize: 12,
    marginVertical: 16,
  },
  infoText: {
    fontSize: 13,
  },
  wrapper: {
    flex: 1,
    justifyContent: 'center',
  },
  tilesSection: {
    borderWidth: 1,
    borderColor: '#BFBFBF',
    borderRadius: 8,
    paddingHorizontal: 56,
    paddingVertical: 32,
    marginTop: 16,
  },
  title: {
    fontWeight: '600',
    marginBottom: 6,
    marginLeft: 8,
  },
  pageContainer: {
    display: 'flex',
    flexDirection: 'row',
    marginLeft: 32,
    marginVertical: 4,
  },
  tileImg: {
    marginRight: 6,
  },
  pageName: {
    fontSize: 13,
    lineHeight: 15,
  },
  column: {
    marginHorizontal: 16,
    display: 'flex',
  },
  progressBar: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
});
