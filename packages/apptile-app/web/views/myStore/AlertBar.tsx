import {default as React} from 'react';
import {Text, View} from 'react-native';
import Icon, {COLORS, SIZES} from '../../components-v2/base/Icon';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();


export const AlertBar = ({
  alertMessage,
  alertIcon,
  alertColor,
  alertIconSize,
}: {
  alertMessage: string;
  alertIcon: string;
  alertColor: COLORS;
  alertIconSize: SIZES;
}) => {
  return (
    <View
      style={[
        {
          backgroundColor: '#FFF',
          borderRadius: 10,
          width: '100%',
          padding: 10,
          margin: 16,
          minHeight: 30,
          justifyContent: 'center',
          flexDirection: 'row',
          alignItems: 'center',
        },
      ]}>
      <View style={{padding: 12}}>
        <Icon name={alertIcon} size={alertIconSize || '6xl'} color={alertColor} />
      </View>
      <Text style={[commonStyles.baseText, {color: '#535353'}]}>{alertMessage}</Text>
    </View>
  );
};
