import React, {useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {useLocation, useNavigate, useParams} from 'react-router-dom';
import Button from '@/root/web/components-v2/base/Button';
import {MaterialCommunityIcons} from 'apptile-core';
import apptileTheme from '../../styles-v2/theme';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import _ from 'lodash';
import OnboardingLoader from './components/onboardingLoader';
import {fetchAddOnPaymentUrl, fetchIntegrations, fetchMyAddOns} from '../../actions/editorActions';
import Analytics from '@/root/web/lib/segment';
import {getMyAddOns, selectSubscription} from '../../selectors/BillingSelector';
import {currentPlanFeaturesSelector} from '../../selectors/FeatureGatingSelector';
import {allAvailablePlans} from 'apptile-core';
import {useIntercom} from 'react-use-intercom';

const iframeStyles = {
  height: '100%',
  width: '100%',
  border: 'none',
};

const ThemePreviewScreen = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get a specific query parameter
  const fromDashboard = new URLSearchParams(location.search).get('fromDashboard');
  const { webflowData } = useSelector((state: EditorRootState) => state.onboarding);
  let { orgId, appId, forkId, slug } = useParams();
  const { themes, preview, loading } = webflowData;
  const dispatch = useDispatch();
  const { showNewMessage } = useIntercom();

  useEffect(() => {
    dispatch(fetchMyAddOns(appId as string));
    dispatch(fetchIntegrations());
  }, []);

  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isPremiumDisabled = !currentPlanFeatures.includes(allAvailablePlans.PRO);

  const allIntegrationsById = useSelector((state: EditorRootState) => state.integration.allIntegrationsById);

  const requiredIntegrations = Object.values(allIntegrationsById);

  const addOns = useSelector(getMyAddOns);

  const onPaymentClick = (slug: string) => {
    dispatch(fetchAddOnPaymentUrl('THEME', slug, appId));
  };

  const {confirmationUrl, paymentUrlFetched} = useSelector(selectSubscription);
  useEffect(() => {
    if (paymentUrlFetched && confirmationUrl) {
      if (window.top === window.self) {
        window.location = confirmationUrl as any;
      } else {
        window.parent.location = confirmationUrl as any;
        // redirectRemote(confirmationUrl);
      }
    }
  }, [paymentUrlFetched, confirmationUrl]);

  const [collection, setCollection] = useState({});

  useEffect(() => {
    setCollection(_.find(themes?.items, t => t.slug === slug));
  }, [slug, themes]);

  const getMessage = (theme: any) => {
    return `Please activate theme ${theme?.name} (${theme?.slug}) for my store`;
  };

  return (
    <View style={styles.rootContainer}>
      <View style={styles.headerButtonsContainer}>
        <Text style={styles.livePreviewText}>Live Preview</Text>
        <Pressable onPress={() => navigate(-1)} style={styles.backButton}>
          <MaterialCommunityIcons name={'keyboard-backspace'} size={20} color={'black'} />
          <Text style={styles.backTextButton}>{collection?.name}</Text>
        </Pressable>
        {!fromDashboard && (
          <>
            {collection?.premium && isPremiumDisabled && !_.find(addOns, {code: collection?.slug}) ? (
              _.find(requiredIntegrations, {integrationCode: collection?.slug})?.monthlyPrice ? (
                <Button
                  onPress={() => {
                    Analytics.track(`onboarding:themePreview_getButton`, {
                      theme: collection?.slug,
                    });
                    onPaymentClick(collection?.slug);
                  }}
                  size="MEDIUM"
                  color="DEFAULT"
                  containerStyles={styles.PurchaseTemplateButton}>
                  Get for ${_.find(requiredIntegrations, {integrationCode: collection?.slug})?.monthlyPrice}
                  /month
                </Button>
              ) : (
                <Button
                  onPress={() => showNewMessage(getMessage(collection))}
                  size="MEDIUM"
                  color="CTA"
                  containerStyles={styles.UseTemplateButton}>
                  Talk to us
                </Button>
              )
            ) : (
              <Button
                onPress={() => {
                  navigate(`../getting-ready/${collection?.slug}`);
                  Analytics.track('onboarding:themePreview_useThemeClicked', {
                    theme: collection?.slug,
                  });
                }}
                size="MEDIUM"
                color="DEFAULT"
                containerStyles={styles.UseTemplateButton}>
                Use theme
              </Button>
            )}
          </>
        )}
      </View>
      <View style={styles.iframeContainer}>
        {!loading ? (
          <iframe
            style={iframeStyles}
            src={`https://app.apptile.io/preview.html?appId=${collection?.['themePreviewAppId']}}`}></iframe>
        ) : (
          <OnboardingLoader customContainerStyles={{marginTop: 50}} />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    height: '100vh',
    fontFamily: apptileTheme.FONT_FAMILY,
    backgroundColor: '#FFFFFF',
  },

  iframeContainer: {
    flex: 1,
    alignItems: 'center',
  },
  backTextButton: {marginLeft: 20, fontSize: 14, fontFamily: apptileTheme.FONT_FAMILY, fontWeight: '500'},
  headerButtonsContainer: {
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    height: 72,
    paddingLeft: 60,
    paddingRight: 60,
    position: 'relative',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    shadowColor: '#00000040',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 7,
  },
  livePreviewText: {
    color: '#535353',
    fontSize: 16,
    textAlign: 'center',
    position: 'absolute',
    left: 0,
    right: 0,
    fontFamily: apptileTheme.FONT_FAMILY,
    fontWeight: '400',
  },
  UseTemplateButton: {
    backgroundColor: '#1060E0',
    borderWidth: 0,
    marginLeft: 10,
    height: 40,
    width: 140,
  },
  PurchaseTemplateButton: {
    backgroundColor: '#1060E0',
    borderWidth: 0,
    marginLeft: 10,
    height: 40,
    width: 240,
  },
  previewButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 0,
    margin: 5,
    width: 214,
    height: 42,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonTextStyle: {
    fontSize: 14,
    fontWeight: apptileTheme.ONBOARDING_FONT_WEIGHT,
  },
});

export default ThemePreviewScreen;
