import React, {useEffect, useLayoutEffect, useState} from 'react';
import {Image, Pressable, ScrollView, StyleSheet, Text, View, Animated} from 'react-native';
import apptileTheme from '../../styles-v2/theme';
import NavigationStepper from './components/NavigationStepper';
import {MaterialCommunityIcons} from 'apptile-core';
import _ from 'lodash';
import {useParams, useNavigate} from 'react-router';
import OnboardingLoader from './components/onboardingLoader';
import {useDispatch, useSelector} from 'react-redux';
import {EditorRootState} from '../../store/EditorRootState';
import Button from '../../components-v2/base/Button';
import Analytics from '@/root/web/lib/segment';
import {fetchAddOnPaymentUrl, fetchIntegrations, fetchMyAddOns} from '../../actions/editorActions';
import {getMyAddOns, selectSubscription} from '../../selectors/BillingSelector';
import TextElement from '../../components-v2/base/TextElement';
import {useIntercom} from 'react-use-intercom';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();


const ThemeScreen = () => {
  const navigate = useNavigate();
  const {webflowData} = useSelector((state: EditorRootState) => state.onboarding);
  const {themes, tags, loading, preview} = webflowData;
  const {showNewMessage} = useIntercom();

  const dispatch = useDispatch();

  const {orgId, appId} = useParams();
  // const [isHoveredIndex, setIsHoveredIndex] = useState(0);
  // const opacity = useState(new Animated.Value(0))[0];

  useEffect(() => {
    dispatch(fetchMyAddOns(appId as string));
    dispatch(fetchIntegrations());
  }, []);

  const allIntegrationsById = useSelector((state: EditorRootState) => state.integration.allIntegrationsById);
  const paymentUrlFetching = useSelector((state: EditorRootState) => state.billing.subscription.paymentUrlFetching);

  const requiredIntegrations = Object.values(allIntegrationsById);

  const addOns = useSelector(getMyAddOns);

  const onPaymentClick = (slug: string) => {
    dispatch(fetchAddOnPaymentUrl('THEME', slug, appId));
  };

  const {confirmationUrl, paymentUrlFetched} = useSelector(selectSubscription);
  useEffect(() => {
    if (paymentUrlFetched && confirmationUrl) {
      if (window.top === window.self) {
        window.location = confirmationUrl as any;
      } else {
        window.parent.location = confirmationUrl as any;
        // redirectRemote(confirmationUrl);
      }
    }
  }, [paymentUrlFetched, confirmationUrl]);

  // const handleMouseEnter = index => {
  //   setIsHoveredIndex(index);
  //   Animated.timing(opacity, {
  //     toValue: 1,
  //     duration: 200,
  //     useNativeDriver: true,
  //   }).start();
  // };

  // const handleMouseLeave = index => {
  //   setIsHoveredIndex(index);
  //   Animated.timing(opacity, {
  //     toValue: 0,
  //     duration: 200,
  //     useNativeDriver: true,
  //   }).start();
  // };

  const isEven = (value: number) => {
    if (value % 2 === 0) return true;
    else return false;
  };

  const getMessage = (theme: any) => {
    return `Please activate theme ${theme?.name} (${theme?.slug}) for my store`;
  };

  return (
    <View style={styles.rootContainer}>
      <ScrollView>
        <View style={styles.headerContainer}>
          <Pressable onPress={() => navigate(-1)} style={styles.backButton}>
            <MaterialCommunityIcons name={'keyboard-backspace'} size={20} color={'#535353'} />
            <Text style={styles.backButtonText}>BACK</Text>
          </Pressable>
          <NavigationStepper location={2} />
        </View>

        <View style={styles.bodyContainer}>
          <View style={styles.bodyTitleContainer}>
            <Text style={styles.bodyTitleText}>Select a theme for your app.</Text>
          </View>

          <View style={styles.themesRootContainer}>
            {!loading ? (
              <View style={styles.themeContainer}>
                {themes?.items?.map((theme, index) => (
                  <View style={styles.themeSubContainer} key={index}>
                    {theme?.['slug'] === 'custom-theme' ? (
                      <Pressable
                        onPress={() => {
                          navigate(`../getting-ready/${theme?.slug}`);
                          Analytics.track('onboarding:themeListing_useThemeClicked', {
                            theme: theme?.slug,
                          });
                        }}>
                        <View style={styles.blankThemeImageContainer}>
                          <Image
                            style={[styles.blankThemeImage, {borderRadius: 30}]}
                            source={{uri: theme?.['thumbnailUrl']}}
                          />
                        </View>
                      </Pressable>
                    ) : (
                      <View style={{backgroundColor: '#FFF', height: 466}}>
                        <View style={styles.themeImageContainer}>
                          <Image style={styles.themeImage} source={{uri: theme?.['thumbnailUrl']}} />
                          {theme?.premium && (
                            <>
                              {_.find(addOns, {code: theme?.slug}) && (
                                <View style={styles.purchasedBadge}>
                                  <TextElement fontSize="xs" color="SECONDARY">
                                    PURCHASED
                                  </TextElement>
                                </View>
                              )}
                            </>
                          )}
                        </View>
                        <View style={{marginHorizontal: 16, marginVertical: 16}}>
                          <View style={styles.pillsContainer}>
                            {/* {theme?.premium && (
                              <View style={[styles.pillContainer, {backgroundColor: '#FFC107', flexDirection: 'row'}]}>
                                <MaterialCommunityIcons name={'lightning-bolt'} size={17} color={'#FFFFFF'} />
                                <Text style={[styles.pillText, {color: '#1E1E1E'}]}>LIGHTNING FAST</Text>
                              </View>
                            )} */}
                            {theme?.tags?.map((tag: string, tagIndex: number) => (
                              <View key={tagIndex} style={styles.pillContainer}>
                                <Text style={styles.pillText}>{tag}</Text>
                              </View>
                            ))}
                          </View>

                          <View style={styles.titleWrapper}>
                            <Text style={styles.themeTitle}>{theme?.name}</Text>
                            {theme?.premium ? (
                              <View>
                                <View style={styles.upgradeButton}>
                                  {/* <View style={styles.upgradeCrown}>
                                    <MaterialCommunityIcons name="crown-outline" size={16} color="#BD00FF" />
                                  </View> */}

                                  <Text style={styles.upgradeText}>
                                    {_.find(requiredIntegrations, {integrationCode: theme?.slug})?.monthlyPrice
                                      ? '$' +
                                        `${
                                          _.find(requiredIntegrations, {integrationCode: theme?.slug})?.monthlyPrice
                                        } /m`
                                      : 'Premium'}
                                  </Text>
                                </View>
                              </View>
                            ) : (
                              <Text style={styles.upgradeText}>Free</Text>
                            )}
                          </View>
                          {theme?.author && (
                            <Text style={[commonStyles.baseText, styles.authorText]}>Designed by {theme?.author}</Text>
                          )}
                          <View style={styles.hoverButtonsContainer}>
                            {theme?.['selfServe'] ? (
                              theme?.['themePreviewAppId'] && 
                              <Button
                                onPress={() => {
                                  Analytics.track('onboarding:themeListing_previewClicked', {
                                    theme: theme?.slug,
                                  });
                                  navigate(`../theme-preview/${theme?.slug}`);
                                }}
                                size="MEDIUM"
                                variant="PILL"
                                color="SECONDARY"
                                // textStyles={styles.hoverButtonText}
                                containerStyles={styles.previewButton}>
                                Live preview
                              </Button>
                            ) : (
                              <Button
                                onPress={() => {
                                  Analytics.track('onboarding:themeListing_themeDetailsClicked', {
                                    theme: theme?.slug,
                                  });
                                  window.open(`https://apptile.com/theme/${theme?.slug}`);
                                }}
                                size="SMALL"
                                variant="PILL"
                                color="SECONDARY"
                                // textStyles={styles.hoverButtonText}
                                icon="open-in-new"
                                iconPosition="RIGHT"
                                containerStyles={styles.previewButton}>
                                Theme details
                              </Button>
                            )}

                            {theme?.premium && !_.find(addOns, {code: theme?.slug}) ? (
                              _.find(requiredIntegrations, {integrationCode: theme?.slug})?.monthlyPrice ? (
                                <Button
                                  textStyles={styles.hoverButtonText}
                                  onPress={() => {
                                    Analytics.track('onboarding:theme_getButton', {
                                      theme: theme?.slug,
                                    });
                                    onPaymentClick(theme?.slug);
                                  }}
                                  disabled={paymentUrlFetching}
                                  loading={paymentUrlFetching}
                                  size="MEDIUM"
                                  color="CTA"
                                  containerStyles={styles.useTemplateButton}>
                                  Buy theme
                                </Button>
                              ) : (
                                <Button
                                  textStyles={styles.hoverButtonText}
                                  onPress={() => showNewMessage(getMessage(theme))}
                                  size="MEDIUM"
                                  color="CTA"
                                  containerStyles={styles.useTemplateButton}>
                                  Talk to us
                                </Button>
                              )
                            ) : theme?.['selfServe'] ? (
                              <Button
                                textStyles={styles.hoverButtonText}
                                onPress={() => {
                                  navigate(`../getting-ready/${theme?.slug}`);
                                  Analytics.track('onboarding:themeListing_useThemeClicked', {
                                    theme: theme?.slug,
                                  });
                                }}
                                size="MEDIUM"
                                color="CTA"
                                containerStyles={{...styles.useTemplateButton, width:theme?.['themePreviewAppId'] ? '48%': '100%'}}>
                                Use theme
                              </Button>
                            ) : (
                              <Button
                                textStyles={styles.hoverButtonText}
                                onPress={() => showNewMessage(getMessage(theme))}
                                size="MEDIUM"
                                color="CTA"
                                containerStyles={styles.useTemplateButton}>
                                Talk to us
                              </Button>
                            )}
                          </View>
                        </View>
                      </View>
                    )}
                  </View>
                ))}
                {!isEven(themes?.items?.length || 0) && (
                  <>
                    <View style={styles.themeSubContainer} />
                    <View style={styles.themeSubContainer} />
                  </>
                )}
              </View>
            ) : (
              <OnboardingLoader customContainerStyles={{marginTop: 50}} />
            )}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    height: '100vh',
    backgroundColor: '#ECE9E1',
    fontFamily: apptileTheme.FONT_FAMILY,
  },
  titleWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  authorText: {
    color: '#8B8B8B',
    fontSize: 12,
    marginTop: 4,
  },
  premiumBadge: {
    position: 'absolute',
    backgroundColor: '#fff',
    padding: 5,
    right: 15,
    top: 15,
    borderRadius: 7,
    borderWidth: 3,
    borderColor: '#0001',
  },
  upgradeButton: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
  },
  upgradeText: {
    color: '#2C2B2B',
    fontSize: 16,
    lineHeight: 18,
    fontWeight: '500',
  },
  upgradeCrown: {backgroundColor: '#F4E4FA', padding: 4, borderRadius: 3},
  purchasedBadge: {
    position: 'absolute',
    backgroundColor: '#F4E4FA',
    padding: 5,
    right: 55,
    width: 90,
    top: 15,
    borderRadius: 7,
    alignItems: 'center',
  },
  themeSubContainer: {
    marginBottom: 40,
    minWidth: 300,
    width: '28%',
    borderRadius: 30,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 72,
  },
  bodyContainer: {
    marginLeft: '5%',
    marginRight: '5%',
    alignItems: 'center',
  },
  bodyTitleContainer: {
    alignItems: 'center',
    width: '100%',
  },
  themesRootContainer: {
    marginTop: 16,
    marginBottom: 65,
    maxWidth: 1240,
    justifyContent: 'center',
  },
  hoverSubContainer: {
    position: 'relative',
    width: '100%',
    flexDirection: 'row',
  },
  hoverButtonsContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    marginTop: 17,
  },
  hoverButtonText: {},
  themeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 48,
    marginTop: 30,
  },
  pillContainer: {
    backgroundColor: '#F9F4E9',
    height: 24,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 11,
    paddingRight: 11,
    marginRight: 6,
    marginBottom: 6,
  },
  pillText: {
    color: '#858585',
    fontSize: 12,
    fontFamily: apptileTheme.FONT_FAMILY,
  },
  useTemplateButton: {
    borderWidth: 0,
    width: '48%',
    height: 42,
  },
  previewButton: {
    width: '48%',
    height: 42,
  },
  pillsContainer: {
    flexDirection: 'row',
    width: '100%',
    flexWrap: 'wrap',
  },

  themeImageContainer: {
    position: 'relative',
    width: '100%',
    height: 'auto',
    aspectRatio: '459/392',
    overflow: 'hidden',
  },

  blankThemeImageContainer: {
    position: 'relative',
    width: '100%',
    height: 592,
    overflow: 'hidden',
  },

  themeImage: {
    width: '100%',
    height: '100%',
  },

  blankThemeImage: {
    width: '100%',
    height: '100%',
  },

  hoverBackgroundImage: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },

  themeTitle: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: apptileTheme.FONT_FAMILY,
  },
  themeDescription: {
    fontSize: 14,
    fontWeight: '400',
    color: '#858585',
    marginTop: 4,
    fontFamily: apptileTheme.FONT_FAMILY,
  },
  themesRootTitleText: {
    fontSize: 24,
    fontWeight: '500',
    marginBottom: 30,
  },
  bodyTitleText: {
    marginTop: 30,
    fontSize: 24,
    fontFamily: apptileTheme.FONT_FAMILY,
    fontWeight: '500',
  },
  bodyTitleSubText: {
    marginTop: 30,
    fontSize: 18,
    color: '#848484',
  },
  backButton: {
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    left: 70,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: apptileTheme.ONBOARDING_FONT_WEIGHT,
    fontFamily: apptileTheme.FONT_FAMILY,
    marginLeft: 10,
    color: '#535353',
  },
});

export default ThemeScreen;
