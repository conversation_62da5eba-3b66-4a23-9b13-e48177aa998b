import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';

import theme from '@/root/web/styles-v2/theme';

const stepMapping: any = {
  1: 'Sign Up',
  //2: 'Sector',
  2: 'Select Theme',
};

const NavigationStepper = ({location}) => {
  const isStepSelected = stepNumber => location === stepNumber;
  const isStepCompleted = stepNumber => location > stepNumber;

  const renderStepNumber = stepNumber => {
    let activeComponent;

    if (isStepCompleted(stepNumber)) {
      activeComponent = (
        <View style={[styles.numberBackground, styles.iconBackgroundColor]}>
          {' '}
          <MaterialCommunityIcons name="check" size={17} color={'#FFFFFF'} />
        </View>
      );
    } else {
      activeComponent = (
        <View
          style={[
            styles.numberBackground,
            isStepSelected(stepNumber) ? styles.selectedNumberBackgroundColor : styles.numberBackgroundColor,
          ]}>
          <Text
            style={[styles.numberText, isStepSelected(stepNumber) ? styles.selectedNumberColor : styles.numberColor]}>
            {stepNumber}
          </Text>
        </View>
      );
    }
    return (
      <>
        {activeComponent}
        <Text style={[isStepSelected(stepNumber) ? styles.stepText : styles.selectedStepText]}>
          {stepMapping[stepNumber]}
        </Text>
      </>
    );
  };

  return (
    <View style={styles.rootContainer}>
      <View style={styles.stepContainer}>{renderStepNumber(1)}</View>

      <View style={styles.dashLine} />

      {/* <View style={styles.stepContainer}>{renderStepNumber(2)}</View>

      <View style={styles.dashLine} /> */}

      <View style={styles.stepContainer}>{renderStepNumber(2)}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  rootContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepText: {
    marginLeft: 5,
    color: '#535353',
    fontSize: 14,
    fontWeight: '500',
    fontFamily: theme.FONT_FAMILY,
  },
  selectedStepText: {
    marginLeft: 5,
    color: '#535353',
    fontSize: 13,
    fontWeight: '400',
    opacity: 0.6,
    fontFamily: theme.FONT_FAMILY,
  },
  dashLine: {
    width: 31,
    marginRight: 10,
    marginLeft: 10,
    borderBottomWidth: 2,
    borderColor: '#A3A3A3',
  },
  numberBackground: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 100,
    height: 32,
    width: 32,
    marginRight: 7,
  },
  iconBackgroundColor: {
    backgroundColor: '#1D1D1C',
  },
  selectedNumberBackgroundColor: {
    backgroundColor: '#1060E0',
  },
  numberBackgroundColor: {
    borderWidth: 1,
    borderColor: '#D9D9D9',
  },
  numberText: {
    color: '#535353',
    fontSize: 17,
    fontWeight: '400',
    fontFamily: theme.FONT_FAMILY,
  },
  selectedNumberColor: {
    color: 'white',
  },
  numberColor: {
    color: '#1D1D1C',
  },
});

export default NavigationStepper;
