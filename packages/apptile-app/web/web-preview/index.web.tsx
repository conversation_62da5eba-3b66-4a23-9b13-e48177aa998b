import {shouldInitWebLogrocket} from '../globalvariables';
import 'react-native-gesture-handler';
import {AppRegistry} from 'react-native';
import {name as appName} from '../../app.json';
import {
  rootSaga,
  sagaMiddleware,
  setAppConstants,
  logger,
  store,
  ThemeContainer,
  ApptileAnimationsContextProvider,
} from 'apptile-core';
import queryString from 'query-string';
import livelyScript from '../lively.txt';
import shoppableFeedScript from '../lively-shoppableFeed.txt';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Provider} from 'react-redux';
import {WebFontLoader} from '@/root/web/views/settings/brand/fonts';
import AppPreview from './AppPreview.web';
import {WEB_API_SERVER_ENDPOINT} from '../../.env.json';
import {getLinkingPrefixesInDevAndWeb} from '../../app/common/utils/getLinkingPrefixes';
import {GetRegisteredNativePage} from '../../app/views/prebuilt';
import {getLinkingPrefixesInDistributedApp} from '../../app/common/utils/mobile-only';
import appBranchesSaga from '../sagas/AppBranchSaga';
import appForksSaga from '../sagas/AppForkSaga';
import editorAppConfigSagas from '../sagas/editorAppConfigSaga';
import {useEffect, useState} from 'react';
import apptileSaga from '../../../apptile-core/sagas/ApptileSaga';
import appModelSagas, {
  appEventsHandlerSagas,
  appModelPageSagas,
  appModelTriggerSagas,
} from '../../../apptile-core/sagas/AppModelSaga';
import editorActionSagas from '../sagas/editorActionsSaga';
import appSagas from '../sagas/AppSaga';
import assetSagas from '../sagas/AssetSaga';
import billingSagas from '../sagas/BillingSaga';
import editorModuleActionsSagas from '../sagas/editorModuleActionsSaga';
import orgSagas from '../sagas/OrgSaga';
import platformSagas from '../sagas/PlatformSaga';
import themeSagas from '../sagas/ThemeSaga';
import userSagas from '../sagas/UserSaga';
import settingsSagas from '../sagas/SettingsSaga';
import integrationSagas from '../sagas/IntegrationSaga';
import TilesActionsSaga from '../sagas/TilesActionsSaga';
import PagesActionSaga from '../sagas/PagesActionSaga';
import BlueprintActionSaga from '../sagas/blueprintActionSaga';
import onboardingSagas from '../sagas/OnboardingSaga';
import appConfigSagas from '../../../apptile-core/sagas/AppConfigSaga';
import eventHandlersSaga from '../../../apptile-core/sagas/EventHandlersSaga';
import apptileThemeSagas from '../../../apptile-core/sagas/ApptileThemeSaga';

global.GetRegisteredNativePage = GetRegisteredNativePage;
global.WEB_API_SERVER_ENDPOINT = WEB_API_SERVER_ENDPOINT;
global.getLinkingPrefixesInDevAndWeb = getLinkingPrefixesInDevAndWeb;
global.getLinkingPrefixesInDistributedApp = getLinkingPrefixesInDistributedApp;

logger.info('Logger initialized for preview app');

const WebPreviewV2 = () => {
  const [booted, setBooted] = useState(false);

  useEffect(() => {
    sagaMiddleware.run(rootSaga, [
      apptileSaga,
      appModelSagas,
      editorActionSagas,
      editorModuleActionsSagas,
      userSagas,
      orgSagas,
      appSagas,
      assetSagas,
      themeSagas,
      billingSagas,
      platformSagas,
      settingsSagas,
      integrationSagas,
      TilesActionsSaga,
      editorAppConfigSagas,
      PagesActionSaga,
      BlueprintActionSaga,
      onboardingSagas,
      appBranchesSaga,
      appForksSaga,
      appConfigSagas,
      appModelPageSagas,
      appModelTriggerSagas,
      appEventsHandlerSagas,
      eventHandlersSaga,
      apptileThemeSagas,
    ]);
    setBooted(true);
  }, []);

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      {booted ? (
        <Provider store={store}>
          <WebFontLoader />
          <ApptileAnimationsContextProvider>
            <ThemeContainer>
              <AppPreview appId={appId} />
            </ThemeContainer>
          </ApptileAnimationsContextProvider>
        </Provider>
      ) : (
        <></>
      )}
    </GestureHandlerRootView>
  );
};

console.log('glvarcheck', shouldInitWebLogrocket);
window.WEB_PREVIEW_MODE = true;

const parsedURL = queryString.parseUrl(window.location.href);
const appId = parsedURL?.query?.id as string;
setAppConstants({APPTILE_APP_ID: appId as string});
AppRegistry.registerComponent(appName, () => WebPreviewV2);

AppRegistry.runApplication(appName, {
  initialProps: {},
  rootTag: document.getElementById('pre-root'),
});

window.reloadLively = new Function(livelyScript) as () => void;
window.reloadShoppableFeeds = new Function(shoppableFeedScript) as () => void;
