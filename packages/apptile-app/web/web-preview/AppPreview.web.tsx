import ApptileApp from './App.web';
import {ApptileCanvasScaleContext, getAppConstants} from 'apptile-core';
import React from 'react';
import phoneBG from '../assets/images/phone-frame.png';
import {StyleSheet} from 'react-native';
import theme from '../views/prompt-to-app/styles-prompt-to-app/theme';
import Animated from 'react-native-reanimated';

const PREVIEW_HEIGHT = 784;
const PREVIEW_WIDTH = 393;

const AppPreview: React.FC<any> = () => {
  const appId = getAppConstants().APPTILE_APP_ID;
  const isMobile = window.innerWidth < 768;

  return (
    <div style={styles.previewContainer}>
      <div style={styles.appContainer}>
        <ApptileCanvasScaleContext.Provider value={1}>
          <Animated.View
            style={isMobile ? styles.appCanvasMobile : styles.appCanvas}
            className="app-container-parent"
            id="app-canvas">
            <ApptileApp appId={appId!} />
          </Animated.View>
          {!isMobile ? (
            <img
              src={phoneBG}
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(calc(-50% + 2.5px), -50%)', // To perfectly align the app with the phone frame png, as the png has a little extra space in the right side
                borderRadius: 45,
                width: PREVIEW_WIDTH + 50,
                height: PREVIEW_HEIGHT + 40,
                transformOrigin: 'center',
              }}
            />
          ) : null}
        </ApptileCanvasScaleContext.Provider>
      </div>
    </div>
  );
};

const styles = StyleSheet.create({
  previewContainer: {
    height: '100dvh',
    maxHeight: '100dvh',
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    backgroundColor: theme.DEFAULT_BACKGROUND,
  },
  appContainer: {
    display: 'flex',
    flexGrow: 0,
    flexShrink: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: theme.DEFAULT_BACKGROUND,
    height: '100dvh',
  },
  appCanvas: {
    width: PREVIEW_WIDTH,
    height: PREVIEW_HEIGHT,
    borderRadius: 45,
    overflow: 'hidden',
    zIndex: 1,
    transform: [{scale: 1}],
  },
  appCanvasMobile: {
    height: '100dvh',
    width: '100dvw',
    borderRadius: 0,
    // overflow: 'hidden',
    zIndex: 1,
    transform: [{scale: 1}],
  },
  copyModal: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 2,
  },
});

export default AppPreview;

document.head.insertAdjacentHTML(
  'beforeend',
  `<style>
    .app-container-parent > :first-child {
      height: 100%;
    }
  </style>`,
);
