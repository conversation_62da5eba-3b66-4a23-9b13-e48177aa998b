import {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {configureDatasources, REGISTER_PLUGINS} from '@/root/web/actions/editorActions';
import {initApptileConfig} from '@/root/app/common/apptile/ApptileConfigManager';
import {initPlugins} from '@/root/app/plugins/initPlugins';
import {loadDatasourcePlugins as loadShopifyPlugin} from 'apptile-shopify';
import {loadDatasourcePlugins} from 'apptile-datasource';
import {ApptileAnimationsContextProvider, LocalStorage, RecordSerializer, RegisteredPlugins} from 'apptile-core';
import {LocalStorage as localStorage} from 'apptile-core';
import {AppContainer} from 'apptile-core';
import {DispatchActions} from 'apptile-core';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {initNavigators} from 'apptile-core';
import {APPCONFIG_CDN} from '../../.env.json';
import {Api} from '../api/Api';
import {configProcessor} from '../common/configProcessor';
import loaderSvg from '../assets/images/preloader.svg';

type PushLogs = {
  logs: Array<{
    id: number;
    appId: number;
    iosBundleId: number;
    androidBundleId: number;
    publishedCommitId: number;
    comment: string;
  }>;
  artefacts: Array<{
    id: number;
    type: 'ios-jsbundle' | 'android-jsbundle' | 'plugins' | 'navigators';
    tag: string;
    cdnlink: string;
  }>;
};

const App = ({appId}: {appId: string}) => {
  const dispatch = useDispatch();
  const [apptileInit, setApptileInit] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const appConfigFetchedReduxStateSelector = (state: EditorRootState) => state.appConfig.isFetched;
  const appConfigFetchedReduxState = useSelector(appConfigFetchedReduxStateSelector);

  const loadPreview = async () => {
    try {
      const pushLogResponse = await Api.get<PushLogs>(Api.API_SERVER + `/api/v2/app/${appId}/pushLogs?limit=5`);
      const log = pushLogResponse.data.logs[0];

      if (!log) {
        setError('Please save your app first');
        return;
      }

      const configResponse = await Api.get(`${APPCONFIG_CDN}/${appId}/main/main/${log.publishedCommitId}.json`, {
        withCredentials: false,
      });

      const config = configResponse.data as string;

      dispatch({
        type: DispatchActions.LOAD_WEB_PREVIEW,
        payload: {
          appId: appId,
          appConfig: configProcessor(RecordSerializer.parse(config)).value,
        },
      });

      await Promise.all([
        initPlugins({codeArtefacts: pushLogResponse.data.artefacts, uuid: appId}),
        initNavigators({codeArtefacts: pushLogResponse.data.artefacts, uuid: appId}),
        loadShopifyPlugin(),
        loadDatasourcePlugins(),
      ]);

      const pluginsLoaded = RegisteredPlugins;
      dispatch({
        type: REGISTER_PLUGINS,
        payload: pluginsLoaded,
      });

      await initApptileConfig();

      setApptileInit(true);
    } catch (error) {
      console.error('Failed to load preview: ', error);
      setError('Failed to load preview');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (appId && !apptileInit) {
      loadPreview();
    }

    if (appId) {
      LocalStorage.setNamespace(appId);
    }

    return () => {
      dispatch({
        type: DispatchActions.CLEAN_UP_APP,
      });
    };
  }, [appId, dispatch]);

  useEffect(() => {
    if (appConfigFetchedReduxState && appId) {
      localStorage.setNamespace(appId as string);
      dispatch(configureDatasources(appId as string));
    }
  }, [appId, appConfigFetchedReduxState, dispatch]);

  if (error) {
    return (
      <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
        <p>{error}</p>
      </div>
    );
  }

  if (loading || !apptileInit) {
    return (
      <div className="preloader">
        <div className="preview-container">
          <img src={loaderSvg} alt="App Loading" width="180" height="auto" />
        </div>
      </div>
    );
  }

  return (
    <ApptileAnimationsContextProvider>
      <AppContainer apptileInit={apptileInit} />
    </ApptileAnimationsContextProvider>
  );
};

export default App;
