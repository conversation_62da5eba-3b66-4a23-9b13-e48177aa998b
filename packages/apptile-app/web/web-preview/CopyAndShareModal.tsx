import {useState} from 'react';
import Button from '../components-v2/base/Button';

const CopyAndShareModal = () => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(location.href)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 1500);
      })
      .catch(err => {
        console.error('Failed to copy: ', err);
      });
  };

  return (
    <div
      style={{
        boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)',
        borderRadius: 10,
        padding: 20,
        maxWidth: 400,
        backgroundColor: 'white',
      }}>
      <div
        style={{
          backgroundColor: '#f1f5ff',
          borderRadius: '12px',
          padding: '16px',
          display: 'flex',
          alignItems: 'center',
          marginBottom: '24px',
        }}>
        <span
          style={{
            fontSize: '24px',
            marginRight: '12px',
          }}>
          🚀
        </span>
        <p
          style={{
            margin: 0,
            fontSize: '16px',
            color: 'black',
          }}>
          Share your application with a preview link
        </p>
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#f8fafc',
          borderRadius: '12px',
          padding: '16px',
        }}>
        <span
          style={{
            fontSize: '16px',
            color: '#334155',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            flex: 1,
            marginRight: '12px',
          }}>
          {location.href}
        </span>
        <Button
          color={copied ? 'SUCCESS' : 'DEFAULT'}
          variant="FILLED"
          onPress={copyToClipboard}
          containerStyles={{pointerEvents: copied ? 'none' : 'auto'}}>
          {copied ? 'Copied' : 'Copy Link'}
        </Button>
      </div>
    </div>
  );
};

export default CopyAndShareModal;
