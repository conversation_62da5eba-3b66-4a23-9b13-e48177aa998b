import {useState} from 'react';
import {VSCodeTheme} from '../lib/theme';
import {FileNode} from '../types';
import FileTreeItem from './file-tree-item';
import ResizeHandle from './resive-handle';
import {MaterialCommunityIcons} from 'apptile-core';

const FileTree: React.FC<{
  files: FileNode[];
  onFileSelect: (file: FileNode) => void;
  onCreateFile: (parentPath: string) => void;
  onCreateFolder: (parentPath: string) => void;
  onDelete: (path: string) => void;
  onRename: (oldPath: string, newPath: string) => void;
  width: number;
  onWidthChange: (width: number) => void;
  isLoading?: boolean;
}> = ({
  files,
  onFileSelect,
  onCreateFile,
  onCreateFolder,
  onDelete,
  onRename,
  width,
  onWidthChange,
  isLoading = false,
}) => {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['src', 'src/components']));

  const handleToggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  return (
    <div className="flex h-full">
      <div
        className="h-full overflow-auto"
        style={{
          width,
          backgroundColor: VSCodeTheme.background.sidebar,
        }}>
        <div
          className="p-3 text-xs font-medium uppercase tracking-wide border-b flex items-center justify-between"
          style={{
            color: VSCodeTheme.text.secondary,
            borderBottomColor: VSCodeTheme.background.border,
          }}>
          <span>Explorer</span>
          <div className="flex gap-1">
            <button
              className="w-5 h-5 flex items-center justify-center rounded transition-colors"
              style={{color: VSCodeTheme.text.secondary, cursor: 'pointer'}}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = VSCodeTheme.ui.buttonHover;
                e.currentTarget.style.color = VSCodeTheme.text.primary;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = VSCodeTheme.text.secondary;
              }}
              onClick={() => onCreateFile('')}
              title="New File">
              <MaterialCommunityIcons name="file-document-outline" size={16} color={VSCodeTheme.text.secondary} />
            </button>
            <button
              className="w-5 h-5 flex items-center justify-center rounded transition-colors"
              style={{color: VSCodeTheme.text.secondary, cursor: 'pointer'}}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = VSCodeTheme.ui.buttonHover;
                e.currentTarget.style.color = VSCodeTheme.text.primary;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = VSCodeTheme.text.secondary;
              }}
              onClick={() => onCreateFolder('')}
              title="New Folder">
              <MaterialCommunityIcons name="folder-plus-outline" size={16} color={VSCodeTheme.text.secondary} />
            </button>
          </div>
        </div>
        {isLoading && (
          <div>
            <div className="w-full h-1 relative overflow-hidden bg-gray-700 rounded-full">
              <div
                className="absolute inset-0 animate-indeterminate-progress bg-blue-500 rounded-full"
                style={{
                  animation: 'indeterminate-progress 1.5s infinite linear',
                  transformOrigin: '0% 50%',
                }}
              />
            </div>
          </div>
        )}
        <div className="py-1">
          {files.map(file => (
            <FileTreeItem
              key={file.path}
              node={file}
              level={0}
              onFileSelect={onFileSelect}
              expandedFolders={expandedFolders}
              onToggleFolder={handleToggleFolder}
              onCreateFile={onCreateFile}
              onCreateFolder={onCreateFolder}
              onDelete={onDelete}
              onRename={onRename}
            />
          ))}
        </div>
      </div>
      <ResizeHandle onResize={onWidthChange} initialWidth={width} />
    </div>
  );
};

export default FileTree;
