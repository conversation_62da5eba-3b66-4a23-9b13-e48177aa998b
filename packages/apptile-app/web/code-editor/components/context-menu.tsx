import {useEffect} from 'react';
import {VSCodeTheme} from '../lib/theme';
import {MaterialCommunityIcons} from 'apptile-core';

const ContextMenu: React.FC<{
  x: number;
  y: number;
  onClose: () => void;
  onCreateFile: () => void;
  onCreateFolder: () => void;
  onDelete?: () => void;
  onRename?: () => void;
  isFolder?: boolean;
}> = ({x, y, onClose, onCreateFile, onCreateFolder, onDelete, onRename, isFolder}) => {
  useEffect(() => {
    const handleClickOutside = () => onClose();
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [onClose]);

  return (
    <div
      className="fixed z-50 min-w-[180px] rounded-md shadow-lg border"
      style={{
        left: x,
        top: y,
        backgroundColor: VSCodeTheme.background.secondary,
        borderColor: VSCodeTheme.background.border,
      }}
      onClick={e => e.stopPropagation()}>
      <div className="py-1">
        <button
          className="w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors"
          style={{
            color: VSCodeTheme.text.primary,
            // @ts-ignore
            ':hover': {backgroundColor: VSCodeTheme.background.hover},
          }}
          onMouseEnter={e => (e.currentTarget.style.backgroundColor = VSCodeTheme.background.hover)}
          onMouseLeave={e => (e.currentTarget.style.backgroundColor = 'transparent')}
          onClick={() => {
            onCreateFile();
            onClose();
          }}>
          <MaterialCommunityIcons name="file-document-outline" size={16} color={VSCodeTheme.text.secondary} />
          New File
        </button>
        <button
          className="w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors"
          style={{
            color: VSCodeTheme.text.primary,
          }}
          onMouseEnter={e => (e.currentTarget.style.backgroundColor = VSCodeTheme.background.hover)}
          onMouseLeave={e => (e.currentTarget.style.backgroundColor = 'transparent')}
          onClick={() => {
            onCreateFolder();
            onClose();
          }}>
          <MaterialCommunityIcons name="folder-plus-outline" size={16} color={VSCodeTheme.text.secondary} />
          New Folder
        </button>
        {/* {onRename && (
          <>
            <div className="h-px my-1" style={{backgroundColor: VSCodeTheme.background.border}} />
            <button
              className="w-full px-3 py-2 text-left text-sm transition-colors"
              style={{color: VSCodeTheme.text.primary}}
              onMouseEnter={e => (e.currentTarget.style.backgroundColor = VSCodeTheme.background.hover)}
              onMouseLeave={e => (e.currentTarget.style.backgroundColor = 'transparent')}
              onClick={() => {
                onRename();
                onClose();
              }}>
              Rename
            </button>
          </>
        )} */}
        {onDelete && (
          <button
            className="w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors"
            style={{color: VSCodeTheme.text.error}}
            onMouseEnter={e => (e.currentTarget.style.backgroundColor = VSCodeTheme.background.hover)}
            onMouseLeave={e => (e.currentTarget.style.backgroundColor = 'transparent')}
            onClick={() => {
              onDelete();
              onClose();
            }}>
            <MaterialCommunityIcons name="delete-outline" size={16} color={VSCodeTheme.text.error} />
            Delete
          </button>
        )}
      </div>
    </div>
  );
};

export default ContextMenu;
