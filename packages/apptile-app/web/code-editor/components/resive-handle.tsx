import {useEffect, useState} from 'react';
import {VSCodeTheme} from '../lib/theme';

const ResizeHandle: React.FC<{
  onResize: (width: number) => void;
  initialWidth: number;
}> = ({onResize, initialWidth}) => {
  const [isResizing, setIsResizing] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(initialWidth);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const deltaX = e.clientX - startX;
      const newWidth = Math.max(200, Math.min(600, startWidth + deltaX));
      onResize(newWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, startX, startWidth, onResize]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    setStartX(e.clientX);
    setStartWidth(initialWidth);
  };

  return (
    <div
      className="w-0.5 cursor-col-resize flex items-center justify-center group transition-colors"
      style={{
        backgroundColor: isResizing ? VSCodeTheme.text.accent : VSCodeTheme.background.border,
      }}
      onMouseDown={handleMouseDown}
      onMouseEnter={e => {
        if (!isResizing) {
          e.currentTarget.style.backgroundColor = VSCodeTheme.background.border;
        }
      }}
      onMouseLeave={e => {
        if (!isResizing) {
          e.currentTarget.style.backgroundColor = VSCodeTheme.background.border;
        }
      }}
    />
  );
};

export default ResizeHandle;
