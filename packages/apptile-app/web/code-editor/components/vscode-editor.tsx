import {useCallback, useEffect, useRef, useState} from 'react';
import Editor from '@monaco-editor/react';
import type * as monaco from 'monaco-editor';
import {VSCodeTheme} from '../lib/theme';
import '../styles.css';
import {VSCodeEditorProps} from '../types';
import FileTree from './file-tree.tsx';
import TabBar from './tab-bar.tsx';
import loaderSvg from '../../assets/images/preloader.svg';

// Monaco Editor configuration
const configureMonaco = (monacoInstance: typeof monaco) => {
  // Configure TypeScript compiler options
  monacoInstance.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monacoInstance.languages.typescript.ScriptTarget.ES2020,
    allowNonTsExtensions: true,
    moduleResolution: monacoInstance.languages.typescript.ModuleResolutionKind.NodeJs,
    module: monacoInstance.languages.typescript.ModuleKind.ESNext,
    noEmit: true,
    esModuleInterop: true,
    jsx: monacoInstance.languages.typescript.JsxEmit.ReactJSX,
    allowJs: true,
    checkJs: false,
    strict: false,
    allowSyntheticDefaultImports: true,
    skipLibCheck: true,
    forceConsistentCasingInFileNames: true,
    resolveJsonModule: true,
    isolatedModules: true,
  });

  // Configure JavaScript compiler options to prevent TypeScript errors
  monacoInstance.languages.typescript.javascriptDefaults.setCompilerOptions({
    target: monacoInstance.languages.typescript.ScriptTarget.ES2020,
    allowNonTsExtensions: true,
    allowJs: true,
    checkJs: false,
    jsx: monacoInstance.languages.typescript.JsxEmit.ReactJSX,
    noEmit: true,
    esModuleInterop: true,
    allowSyntheticDefaultImports: true,
  });

  // Disable TypeScript diagnostics for JavaScript files
  monacoInstance.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: true,
    noSyntaxValidation: false,
    noSuggestionDiagnostics: true,
  });

  // Configure TypeScript diagnostics
  monacoInstance.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
    noSemanticValidation: false,
    noSyntaxValidation: false,
    noSuggestionDiagnostics: false,
  });

  // Add React type definitions
  const reactTypes = `
declare module "react" {
  export interface Component<P = {}, S = {}> {}
  export class Component<P, S> {
    constructor(props: P);
    setState<K extends keyof S>(state: ((prevState: Readonly<S>, props: Readonly<P>) => (Pick<S, K> | S | null)) | (Pick<S, K> | S | null), callback?: () => void): void;
    render(): React.ReactNode;
    props: Readonly<P>;
    state: Readonly<S>;
  }
  export function useState<S>(initialState: S | (() => S)): [S, (value: S | ((prev: S) => S)) => void];
  export function useEffect(effect: () => void | (() => void), deps?: any[]): void;
  export function useCallback<T extends (...args: any[]) => any>(callback: T, deps: any[]): T;
  export function useMemo<T>(factory: () => T, deps: any[]): T;
  export function useRef<T>(initialValue: T): { current: T };
  export function useRef<T = undefined>(): { current: T | undefined };
  export type ReactNode = string | number | boolean | null | undefined | ReactElement | ReactNode[];
  export interface ReactElement<P = any> {
    type: any;
    props: P;
    key: string | number | null;
  }
  export interface FC<P = {}> {
    (props: P): ReactElement | null;
  }
  export const Fragment: FC<{ children?: ReactNode }>;
}
`;

  monacoInstance.languages.typescript.typescriptDefaults.addExtraLib(
    reactTypes,
    'file:///node_modules/@types/react/index.d.ts',
  );

  // Register Prettier formatter
  monacoInstance.languages.registerDocumentFormattingEditProvider('typescript', {
    async provideDocumentFormattingEdits(model) {
      try {
        const prettier = await import('prettier/standalone');
        const typescriptParser = await import('prettier/parser-typescript');

        const formatted = prettier.format(model.getValue(), {
          parser: 'typescript',
          plugins: [typescriptParser],
          semi: true,
          singleQuote: true,
          tabWidth: 2,
          trailingComma: 'es5',
          printWidth: 100,
        });

        return [
          {
            range: model.getFullModelRange(),
            text: formatted,
          },
        ];
      } catch (error) {
        console.error('Prettier formatting error:', error);
        return [];
      }
    },
  });

  monacoInstance.languages.registerDocumentFormattingEditProvider('javascript', {
    async provideDocumentFormattingEdits(model) {
      try {
        const prettier = await import('prettier/standalone');
        const babelParser = await import('prettier/parser-babel');

        const formatted = prettier.format(model.getValue(), {
          parser: 'babel',
          plugins: [babelParser],
          semi: true,
          singleQuote: true,
          tabWidth: 2,
          trailingComma: 'es5',
          printWidth: 100,
        });

        return [
          {
            range: model.getFullModelRange(),
            text: formatted,
          },
        ];
      } catch (error) {
        console.error('Prettier formatting error:', error);
        return [];
      }
    },
  });
};

const VSCodeEditor: React.FC<VSCodeEditorProps> = ({
  initialFiles = [],
  openFiles,
  setOpenFiles,
  activeFile,
  fileContents,
  setFileContents,
  onFileSelect,
  onTabSelect,
  onTabClose,
  onFileChange,
  onFileSave,
  onFileCreate,
  onCreateFolder,
  onFileDelete,
  onFileRename,
  theme = 'vs-dark',
  fontSize = 12,
  tabSize = 2,
  wordWrap = 'on',
  minimap = true,
  lineNumbers = 'on',
  folding = true,
  autoSave = false,
  autoSaveDelay = 1000,
  showFileTree = true,
  initialFileTreeWidth = 280,
  className = '',
  height = '100vh',
  isFileTreeLoading = false,
  isFileContentLoading = false,
}) => {
  const [fileTreeWidth, setFileTreeWidth] = useState(initialFileTreeWidth);
  const editorRef = useRef<any>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  const handleEditorChange = useCallback(
    (value: string | undefined) => {
      if (!activeFile || value === undefined) return;

      setFileContents(prev => new Map(prev).set(activeFile, value));
      setOpenFiles(prev =>
        prev.map(file =>
          file.path === activeFile ? {...file, content: value, isDirty: value !== file.savedContent} : file,
        ),
      );

      onFileChange?.(activeFile, value);

      if (autoSave) {
        if (autoSaveTimeoutRef.current) {
          clearTimeout(autoSaveTimeoutRef.current);
        }
        autoSaveTimeoutRef.current = setTimeout(() => {
          onFileSave?.(activeFile, value);
        }, autoSaveDelay);
      }
    },
    [activeFile, autoSave, autoSaveDelay, onFileSave, onFileChange, setFileContents, setOpenFiles],
  );

  const getLanguage = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    const fullName = fileName.toLowerCase();

    if (fullName.includes('.native.') || fullName.includes('.ios.') || fullName.includes('.android.')) {
      if (ext === 'tsx' || ext === 'ts') return 'typescript';
      if (ext === 'jsx' || ext === 'js') return 'javascript';
    }

    switch (ext) {
      case 'tsx':
      case 'ts':
        return 'typescript';
      case 'jsx':
      case 'js':
        return 'javascript';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'css':
        return 'css';
      case 'html':
        return 'html';
      case 'yml':
      case 'yaml':
        return 'yaml';
      default:
        return 'plaintext';
    }
  };

  const handleEditorDidMount = (editor: any, monacoInstance: typeof monaco) => {
    editorRef.current = editor;
    configureMonaco(monacoInstance);

    // Add keyboard shortcuts
    editor.addAction({
      id: 'save-file',
      label: 'Save File',
      keybindings: [monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.KeyS],
      run: () => {
        if (activeFile && onFileSave) {
          const content = fileContents.get(activeFile);
          if (content !== undefined) {
            onFileSave(activeFile, content);
          }
        }
      },
    });

    editor.addAction({
      id: 'save-all-files',
      label: 'Save All Files',
      keybindings: [monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyMod.Shift | monacoInstance.KeyCode.KeyS],
      run: () => {
        if (openFiles.length > 0 && onFileSave) {
          openFiles.forEach(file => {
            const content = fileContents.get(file.path);
            if (content !== undefined) {
              onFileSave(file.path, content);
            }
          });
        }
      },
    });

    editor.addAction({
      id: 'format-document',
      label: 'Format Document',
      keybindings: [monacoInstance.KeyMod.Shift | monacoInstance.KeyMod.Alt | monacoInstance.KeyCode.KeyF],
      run: () => {
        editor.getAction('editor.action.formatDocument').run();
      },
    });

    editor.addAction({
      id: 'find-replace',
      label: 'Find and Replace',
      keybindings: [monacoInstance.KeyMod.CtrlCmd | monacoInstance.KeyCode.KeyH],
      run: () => {
        editor.getAction('editor.action.startFindReplaceAction').run();
      },
    });
  };

  const handleCreateFile = useCallback(
    (parentPath: string) => {
      const name = prompt('Enter file name:');
      if (name && onFileCreate) {
        onFileCreate(parentPath, name);
      }
    },
    [onFileCreate],
  );

  const handleCreateFolder = useCallback(
    (parentPath: string) => {
      const name = prompt('Enter folder name:');
      if (name && onCreateFolder) {
        onCreateFolder(parentPath, name);
      }
    },
    [onCreateFolder],
  );

  // Function to get the current content from the editor
  const getCurrentEditorContent = useCallback(() => {
    if (editorRef.current && activeFile) {
      const currentValue = editorRef.current.getValue();
      // Update fileContents with current editor value to ensure it's up-to-date
      setFileContents(prev => new Map(prev).set(activeFile, currentValue));
      console.log(`Got current editor content for ${activeFile}: ${currentValue.length} chars`);
      return currentValue;
    }
    return null;
  }, [activeFile, setFileContents]);

  // Global keyboard shortcuts for saving files
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Save current file with Cmd+S or Ctrl+S
      if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 's' && !e.shiftKey) {
        e.preventDefault(); // Prevent browser's default save behavior

        if (activeFile && onFileSave) {
          const activeFileObj = openFiles.find(f => f.path === activeFile);

          if (activeFileObj) {
            // Get the latest content directly from the editor
            const latestContent = getCurrentEditorContent();

            if (latestContent !== null) {
              console.log(`Saving via shortcut: ${activeFile} (${activeFileObj.name})`);
              onFileSave(activeFile, latestContent);
            } else {
              // Fall back to content from state if editor ref isn't available
              const stateContent = fileContents.get(activeFile);
              if (stateContent !== undefined) {
                console.log(`Saving via shortcut (fallback): ${activeFile} (${activeFileObj.name})`);
                onFileSave(activeFile, stateContent);
              } else {
                console.error(`Cannot save file ${activeFile}: content undefined`);
              }
            }
          } else {
            console.error(`Cannot save file ${activeFile}: file not found in open files`);
          }
        }
      }

      // Save all changed files with Cmd+Shift+S or Ctrl+Shift+S
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key.toLowerCase() === 's') {
        e.preventDefault(); // Prevent browser's default save behavior

        if (onFileSave) {
          // First, ensure current file content is up-to-date
          if (activeFile) {
            getCurrentEditorContent();
          }

          // Only get files that are actually dirty (have unsaved changes)
          const dirtyFiles = openFiles.filter(file => file.isDirty);

          if (dirtyFiles.length === 0) {
            console.log('No files with unsaved changes to save');
            return;
          }

          console.log(`Found ${dirtyFiles.length} files with unsaved changes`);
          let savedCount = 0;

          dirtyFiles.forEach(file => {
            // For the active file, we'll use the latest content from the editor
            const content =
              file.path === activeFile && editorRef.current
                ? editorRef.current.getValue()
                : fileContents.get(file.path);

            if (content !== undefined) {
              console.log(`Saving (batch): ${file.path} (${file.name})`);
              onFileSave(file.path, content);
              savedCount++;
            } else {
              console.error(`Cannot save file ${file.path} (${file.name}): content undefined`);
            }
          });

          if (savedCount > 0) {
            console.log(`Saved ${savedCount} modified files`);
          } else if (dirtyFiles.length > 0) {
            console.error(`Failed to save ${dirtyFiles.length} files: content undefined`);
          }
        }
      }
    };

    // Add event listener to window
    window.addEventListener('keydown', handleKeyDown);

    // Cleanup function
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [activeFile, fileContents, onFileSave, openFiles, getCurrentEditorContent]);

  // Get active file content and name with better error handling
  const activeFileContent = activeFile ? fileContents.get(activeFile) || '' : '';
  const activeFileObj = activeFile ? openFiles.find(f => f.path === activeFile) : null;
  const activeFileName = activeFileObj?.name || '';

  return (
    <div
      className={`flex ${className}`}
      style={{
        height,
        backgroundColor: VSCodeTheme.background.primary,
      }}>
      {/* File Tree Sidebar */}
      {showFileTree && (
        <FileTree
          files={initialFiles}
          onFileSelect={onFileSelect}
          onCreateFile={handleCreateFile}
          onCreateFolder={handleCreateFolder}
          onDelete={(path: string) => onFileDelete && onFileDelete(path)}
          onRename={(oldPath: string, newPath: string) => onFileRename && onFileRename(oldPath, newPath)}
          width={fileTreeWidth}
          onWidthChange={setFileTreeWidth}
          isLoading={isFileTreeLoading}
        />
      )}

      {/* Editor Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Tab Bar */}
        {openFiles.length > 0 && (
          <TabBar
            openFiles={openFiles}
            activeFile={activeFile}
            onTabSelect={onTabSelect}
            onTabClose={onTabClose}
            onSave={(path: string) => {
              if (path === activeFile && editorRef.current) {
                // Get the most recent content directly from the editor
                const latestContent = editorRef.current.getValue();
                console.log(`Tab save button: Using latest editor content for ${path}`);
                onFileSave && onFileSave(path, latestContent);
              } else {
                // Fall back to content from state for non-active files
                const content = fileContents.get(path) || '';
                console.log(`Tab save button: Using state content for ${path}`);
                onFileSave && onFileSave(path, content);
              }
            }}
            onSaveAll={() => {
              // First, ensure current file content is up-to-date
              if (activeFile) {
                getCurrentEditorContent();
              }

              // Only get files that are actually dirty (have unsaved changes)
              const dirtyFiles = openFiles.filter(file => file.isDirty);

              if (dirtyFiles.length === 0) {
                console.log('No files with unsaved changes to save');
                return;
              }

              console.log(`Found ${dirtyFiles.length} files with unsaved changes`);
              let savedCount = 0;

              dirtyFiles.forEach(file => {
                // For the active file, we'll use the latest content from the editor
                const content =
                  file.path === activeFile && editorRef.current
                    ? editorRef.current.getValue()
                    : fileContents.get(file.path);

                if (content !== undefined) {
                  console.log(`Saving (batch): ${file.path} (${file.name})`);
                  onFileSave && onFileSave(file.path, content);
                  savedCount++;
                } else {
                  console.error(`Cannot save file ${file.path} (${file.name}): content undefined`);
                }
              });

              if (savedCount > 0) {
                console.log(`Saved ${savedCount} modified files`);
              } else if (dirtyFiles.length > 0) {
                console.error(`Failed to save ${dirtyFiles.length} files: content undefined`);
              }
            }}
          />
        )}

        {/* Editor */}
        <div className="flex-1 min-h-0 relative">
          {activeFile && isFileContentLoading ? (
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                backgroundColor: VSCodeTheme.background.editor,
              }}>
              <img src={loaderSvg} alt="Loading" className="w-16 h-16" />
            </div>
          ) : activeFile ? (
            <Editor
              height="100%"
              language={getLanguage(activeFileName)}
              value={activeFileContent}
              theme={theme}
              onChange={handleEditorChange}
              onMount={handleEditorDidMount}
              options={{
                fontSize,
                fontFamily: "'Fira Code', 'Cascadia Code', 'JetBrains Mono', 'SF Mono', monospace",
                fontLigatures: true,
                minimap: {enabled: minimap},
                scrollBeyondLastLine: false,
                wordWrap,
                automaticLayout: true,
                tabSize,
                insertSpaces: true,
                renderWhitespace: 'selection',
                bracketPairColorization: {enabled: true},
                formatOnPaste: true,
                formatOnType: true,
                autoIndent: 'full',
                suggestOnTriggerCharacters: true,
                acceptSuggestionOnEnter: 'on',
                quickSuggestions: {
                  other: true,
                  comments: true,
                  strings: true,
                },
                parameterHints: {enabled: true},
                hover: {enabled: true},
                lineNumbers,
                folding,
                find: {
                  addExtraSpaceOnTop: false,
                  autoFindInSelection: 'never',
                  seedSearchStringFromSelection: 'always',
                },
                smoothScrolling: true,
                cursorBlinking: 'smooth',
                cursorSmoothCaretAnimation: 'on',
                mouseWheelZoom: true,
                padding: {top: 16, bottom: 16},
              }}
            />
          ) : (
            <div
              className="h-full flex items-center justify-center text-lg"
              style={{
                color: VSCodeTheme.text.secondary,
                backgroundColor: VSCodeTheme.background.editor,
              }}>
              <div className="text-center">
                <p>Select a file to start editing</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VSCodeEditor;
