import {VSCodeTheme} from '../lib/theme';
import {OpenFile} from '../types';
import {MaterialCommunityIcons} from 'apptile-core';
import {getFileIconBasedOnFileType} from '../lib/utils';
import Button from '@/root/web/components-v2/base/Button';

const TabBar: React.FC<{
  openFiles: OpenFile[];
  activeFile: string | null;
  onTabSelect: (path: string) => void;
  onTabClose: (path: string) => void;
  onSave: (path: string) => void;
  onSaveAll?: () => void;
}> = ({openFiles, activeFile, onTabSelect, onTabClose, onSave, onSaveAll}) => {
  // Check if we have at least 2 dirty files for Save All button
  const dirtyFilesCount = openFiles.filter(file => file.isDirty).length;
  return (
    <div
      className="border-b flex overflow-x-auto"
      style={{
        backgroundColor: VSCodeTheme.background.tab,
        borderBottomColor: VSCodeTheme.background.border,
      }}>
      <div className="flex min-w-0 flex-1">
        {openFiles.map(file => (
          <div
            key={file.path}
            className={`flex items-center h-9 px-4 cursor-pointer border-r min-w-0 max-w-[200px] group transition-colors relative`}
            style={{
              backgroundColor: activeFile === file.path ? VSCodeTheme.background.tabActive : VSCodeTheme.background.tab,
              color: activeFile === file.path ? VSCodeTheme.text.primary : VSCodeTheme.text.secondary,
              borderRightColor: VSCodeTheme.background.border,
              borderTop: activeFile === file.path ? `2px solid ${VSCodeTheme.text.accent}` : '2px solid transparent',
            }}
            onMouseEnter={e => {
              if (activeFile !== file.path) {
                e.currentTarget.style.backgroundColor = VSCodeTheme.background.tabHover;
              }
            }}
            onMouseLeave={e => {
              if (activeFile !== file.path) {
                e.currentTarget.style.backgroundColor = VSCodeTheme.background.tab;
              }
            }}
            onClick={() => onTabSelect(file.path)}>
            <MaterialCommunityIcons
              name={getFileIconBasedOnFileType(file.name.split('.').pop() || '')}
              size={16}
              color={VSCodeTheme.fileTree.file}
              style={{marginRight: 5}}
            />
            <span className="text-sm truncate mr-2 min-w-0">{file.name}</span>

            {/* Enhanced unsaved changes indicator */}
            {file.isDirty && (
              <div
                className="w-2 h-2 rounded-full mr-2 flex-shrink-0 animate-pulse"
                style={{backgroundColor: VSCodeTheme.text.warning}}
                title="Unsaved changes"
              />
            )}

            <div className="flex items-center gap-1 flex-shrink-0">
              <button
                className="w-5 h-5 flex items-center justify-center rounded transition-colors cursor-pointer"
                style={{color: VSCodeTheme.text.secondary}}
                onMouseEnter={e => {
                  e.currentTarget.style.backgroundColor = VSCodeTheme.ui.buttonHover;
                  e.currentTarget.style.color = VSCodeTheme.text.primary;
                }}
                onMouseLeave={e => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = VSCodeTheme.text.secondary;
                }}
                onClick={e => {
                  e.stopPropagation();
                  onTabClose(file.path);
                }}
                title="Close">
                <MaterialCommunityIcons name="close" size={16} color={VSCodeTheme.text.secondary} />
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {/* Global Save Buttons */}
      <div className="flex items-center mr-2 gap-2">
        {openFiles.length > 0 && (
          <Button
            variant="FILLED"
            color="DEFAULT"
            containerStyles={{
              borderRadius: 4,
              backgroundColor: '#4A4A4A',
              borderColor: '#4A4A4A',
              zIndex: 100,
              height: 28,
              paddingHorizontal: 12,
            }}
            textStyles={{
              fontSize: 12,
            }}
            onPress={() => {
              if (activeFile) {
                console.log(`Global save button clicked for active file: ${activeFile}`);
                onSave(activeFile);
              }
            }}>
            Save
          </Button>
        )}
        
        {dirtyFilesCount >= 2 && onSaveAll && (
          <Button
            variant="FILLED"
            color="DEFAULT"
            containerStyles={{
              borderRadius: 4,
              backgroundColor: '#4A4A4A',
              borderColor: '#4A4A4A',
              zIndex: 100,
              height: 28,
              paddingHorizontal: 12,
            }}
            textStyles={{
              fontSize: 12,
            }}
            onPress={() => {
              console.log(`Save All button clicked for ${dirtyFilesCount} dirty files`);
              onSaveAll();
            }}>
            Save All
          </Button>
        )}
      </div>
    </div>
  );
};

export default TabBar;
