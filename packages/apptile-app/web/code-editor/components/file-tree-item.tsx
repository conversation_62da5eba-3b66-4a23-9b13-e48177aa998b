import {useState} from 'react';
import {FileNode} from '../types';
import {VSCodeTheme} from '../lib/theme';
import ContextMenu from './context-menu';
import {MaterialCommunityIcons} from 'apptile-core';
import {getFileIconBasedOnFileType} from '../lib/utils';

const FileTreeItem: React.FC<{
  node: FileNode;
  level: number;
  onFileSelect: (file: FileNode) => void;
  expandedFolders: Set<string>;
  onToggleFolder: (path: string) => void;
  onCreateFile: (parentPath: string) => void;
  onCreateFolder: (parentPath: string) => void;
  onDelete: (path: string) => void;
  onRename: (path: string, newName: string) => void;
}> = ({
  node,
  level,
  onFileSelect,
  expandedFolders,
  onToggleFolder,
  onCreateFile,
  onCreateFolder,
  onDelete,
  onRename,
}) => {
  const [contextMenu, setContextMenu] = useState<{x: number; y: number} | null>(null);
  const [isRenaming, setIsRenaming] = useState(false);
  const [newName, setNewName] = useState(node.name);
  const [isHovered, setIsHovered] = useState(false);
  const isExpanded = expandedFolders.has(node.path);
  const paddingLeft = level * 16 + 8;

  const handleClick = () => {
    if (node.type === 'folder') {
      onToggleFolder(node.path);
    } else {
      onFileSelect(node);
    }
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setContextMenu({x: e.clientX, y: e.clientY});
  };

  const handleRename = () => {
    if (newName.trim() && newName !== node.name) {
      const newPath = node.path.replace(node.name, newName.trim());
      onRename(node.path, newPath);
    }
    setIsRenaming(false);
    setNewName(node.name);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setIsRenaming(false);
      setNewName(node.name);
    }
  };

  return (
    <div>
      <div
        className="flex items-center h-7 cursor-pointer relative group transition-colors"
        style={{
          paddingLeft,
          color: VSCodeTheme.text.primary,
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onMouseOver={e => {
          e.currentTarget.style.backgroundColor = VSCodeTheme.background.hover;
        }}
        onMouseOut={e => {
          e.currentTarget.style.backgroundColor = 'transparent';
        }}
        onClick={handleClick}
        onContextMenu={handleContextMenu}>
        <div className="w-4 h-4 flex items-center justify-center mr-2">
          {node.type === 'folder' ? (
            isExpanded ? (
              <MaterialCommunityIcons name="folder-open" size={16} color={VSCodeTheme.fileTree.folder} />
            ) : (
              <MaterialCommunityIcons name="folder" size={16} color={VSCodeTheme.fileTree.folder} />
            )
          ) : (
            <MaterialCommunityIcons name={getFileIconBasedOnFileType(node.name.split('.').pop() || '')} size={16} color={VSCodeTheme.fileTree.file} />
          )}
        </div>

        {isRenaming ? (
          <input
            type="text"
            value={newName}
            onChange={e => setNewName(e.target.value)}
            onBlur={handleRename}
            onKeyDown={handleKeyDown}
            className="text-sm px-1 rounded flex-1 outline-none"
            style={{
              backgroundColor: VSCodeTheme.background.input,
              color: VSCodeTheme.text.primary,
              border: `1px solid ${VSCodeTheme.text.accent}`,
            }}
            autoFocus
            onClick={e => e.stopPropagation()}
          />
        ) : (
          <span className="truncate text-sm flex-1">{node.name}</span>
        )}

        {/* Hover buttons for folders */}
        {node.type === 'folder' && isHovered && !isRenaming && (
          <div className="flex items-center gap-1 mr-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              className="w-5 h-5 flex items-center justify-center rounded transition-colors"
              style={{
                color: VSCodeTheme.text.secondary,
                cursor: 'pointer',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = VSCodeTheme.ui.buttonHover;
                e.currentTarget.style.color = VSCodeTheme.text.primary;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = VSCodeTheme.text.secondary;
              }}
              onClick={e => {
                e.stopPropagation();
                onCreateFile(node.path);
              }}
              title="New File">
              <MaterialCommunityIcons name="file-document-outline" size={12} color={VSCodeTheme.text.secondary} />
            </button>
            <button
              className="w-5 h-5 flex items-center justify-center rounded transition-colors"
              style={{
                color: VSCodeTheme.text.secondary,
                cursor: 'pointer',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = VSCodeTheme.ui.buttonHover;
                e.currentTarget.style.color = VSCodeTheme.text.primary;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = VSCodeTheme.text.secondary;
              }}
              onClick={e => {
                e.stopPropagation();
                onCreateFolder(node.path);
              }}
              title="New Folder">
              <MaterialCommunityIcons name="folder-plus-outline" size={12} color={VSCodeTheme.text.secondary} />
            </button>
          </div>
        )}

        {isHovered && !isRenaming && (
          <div className="flex items-center gap-1 mr-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              className="w-5 h-5 flex items-center justify-center rounded transition-colors"
              style={{
                color: VSCodeTheme.text.secondary,
                cursor: 'pointer',
              }}
              onMouseEnter={e => {
                e.currentTarget.style.backgroundColor = VSCodeTheme.ui.buttonHover;
                e.currentTarget.style.color = VSCodeTheme.text.primary;
              }}
              onMouseLeave={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = VSCodeTheme.text.secondary;
              }}
              onClick={e => {
                e.stopPropagation();
                onDelete(node.path);
              }}
              title="Delete">
              <MaterialCommunityIcons name="delete-outline" size={12} color={VSCodeTheme.text.secondary} />
            </button>
          </div>
        )}
      </div>

      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          onClose={() => setContextMenu(null)}
          onCreateFile={() =>
            onCreateFile(node.type === 'folder' ? node.path : node.path.split('/').slice(0, -1).join('/'))
          }
          onCreateFolder={() =>
            onCreateFolder(node.type === 'folder' ? node.path : node.path.split('/').slice(0, -1).join('/'))
          }
          onDelete={() => onDelete(node.path)}
          onRename={() => setIsRenaming(true)}
          isFolder={node.type === 'folder'}
        />
      )}

      {node.type === 'folder' && isExpanded && node.children && (
        <div>
          {node.children.map(child => (
            <FileTreeItem
              key={child.path}
              node={child}
              level={level + 1}
              onFileSelect={onFileSelect}
              expandedFolders={expandedFolders}
              onToggleFolder={onToggleFolder}
              onCreateFile={onCreateFile}
              onCreateFolder={onCreateFolder}
              onDelete={onDelete}
              onRename={onRename}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default FileTreeItem;
