import axios from 'axios';
import {FileNode} from '../types';
import {PLUGIN_SERVER_URL} from '../../../.env.json';

export const getFileTree = async (appId: string, type: 'navigators' | 'plugins', artefactName: string) => {
  const response = await axios.get<FileNode[]>(`${PLUGIN_SERVER_URL}/cli/${appId}/file-tree/${type}/${artefactName}`);
  return response.data;
};

export const getFileContent = async (
  appId: string,
  type: 'navigators' | 'plugins',
  artefactName: string,
  filePath: string,
) => {
  const response = await axios.get<string>(
    `${PLUGIN_SERVER_URL}/cli/${appId}/file-content/${type}/${artefactName}/${encodeURIComponent(filePath)}`,
  );
  return response.data;
};

export const saveFileContent = async (
  appId: string,
  type: 'navigators' | 'plugins',
  artefactName: string,
  filePath: string,
  content: string,
) => {
  const response = await axios.post<{message: string}>(`${PLUGIN_SERVER_URL}/cli/${appId}/update-source`, {
    itemType: type,
    itemName: artefactName,
    relativePath: filePath,
    content,
  });
  return response.data;
};

export const createFolder = async (
  appId: string,
  type: 'navigators' | 'plugins',
  artefactName: string,
  folderPath: string,
) => {
  const response = await axios.post<{success: boolean; message: string}>(
    `${PLUGIN_SERVER_URL}/cli/${appId}/manage-source`,
    {
      itemType: type,
      itemName: artefactName,
      relativePath: folderPath,
      isFolder: true,
      action: 'create',
    },
  );
  return response.data;
};

export const createFile = async (
  appId: string,
  type: 'navigators' | 'plugins',
  artefactName: string,
  filePath: string,
) => {
  const response = await axios.post<{success: boolean; message: string}>(
    `${PLUGIN_SERVER_URL}/cli/${appId}/manage-source`,
    {
      itemType: type,
      itemName: artefactName,
      relativePath: filePath,
      isFolder: false,
      action: 'create',
    },
  );
  return response.data;
};

export const deleteFolder = async (
  appId: string,
  type: 'navigators' | 'plugins',
  artefactName: string,
  folderPath: string,
) => {
  const response = await axios.post<{success: boolean; message: string}>(
    `${PLUGIN_SERVER_URL}/cli/${appId}/manage-source`,
    {
      itemType: type,
      itemName: artefactName,
      relativePath: folderPath,
      isFolder: true,
      action: 'delete',
    },
  );
  return response.data;
};

export const deleteFile = async (
  appId: string,
  type: 'navigators' | 'plugins',
  artefactName: string,
  filePath: string,
) => {
  const response = await axios.post<{success: boolean; message: string}>(
    `${PLUGIN_SERVER_URL}/cli/${appId}/manage-source`,
    {
      itemType: type,
      itemName: artefactName,
      relativePath: filePath,
      isFolder: false,
      action: 'delete',
    },
  );
  return response.data;
};
