export const VSCodeTheme = {
  // Background colors
  background: {
    primary: '#000000', // Total black
    secondary: '#1f1f1f', // Slightly lighter black
    tertiary: '#161b22', // Dark gray
    sidebar: '#1f1f1f', // Sidebar background
    editor: '#1f1f1f', // Editor background
    tab: '#1f1f1f', // Tab background
    tabActive: '#4a4a4a', // Active tab
    tabHover: '#4a4a4a', // Tab hover
    hover: '#4a4a4a', // General hover
    border: '#30363d', // Border color
    input: '#1f1f1f', // Input background
  },

  // Text colors
  text: {
    primary: '#f0f6fc', // Primary text (white)
    secondary: '#8b949e', // Secondary text (gray)
    muted: '#6e7681', // Muted text
    accent: '#58a6ff', // Accent color (blue)
    success: '#3fb950', // Success color (green)
    warning: '#d29922', // Warning color (yellow)
    error: '#f85149', // Error color (red)
  },

  // File tree colors
  fileTree: {
    folder: '#8b949e', // Folder icon
    file: '#ffffff', // File icon
    folderOpen: '#8b949e', // Open folder icon
  },

  // Editor colors
  editor: {
    selection: '#264f78', // Selection background
    lineHighlight: '#161b22', // Current line highlight
    cursor: '#f0f6fc', // Cursor color
  },

  // UI elements
  ui: {
    button: '#21262d', // Button background
    buttonHover: '#30363d', // Button hover
    scrollbar: '#6e7681', // Scrollbar
    scrollbarHover: '#8b949e', // Scrollbar hover
  },
} as const;

export type VSCodeThemeType = typeof VSCodeTheme;
