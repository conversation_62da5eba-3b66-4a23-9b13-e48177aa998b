export interface FileNode {
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: FileNode[];
  content?: string;
}

export interface OpenFile {
  path: string;
  name: string;
  content: string;
  isDirty: boolean;
  savedContent: string;
}

export interface VSCodeEditorProps {
  // File tree state
  initialFiles?: FileNode[];

  // Open files state
  openFiles: OpenFile[];
  setOpenFiles: React.Dispatch<React.SetStateAction<OpenFile[]>>;
  activeFile: string | null;
  fileContents: Map<string, string>;
  setFileContents: React.Dispatch<React.SetStateAction<Map<string, string>>>;

  // File operations
  onFileSelect: (file: FileNode) => void;
  onTabSelect: (path: string) => void;
  onTabClose: (path: string) => void;
  onFileChange?: (path: string, content: string) => void;
  onFileSave?: (path: string, content: string) => void;
  onFileCreate?: (parentPath: string, name: string) => Promise<string | null> | string | null;
  onCreateFolder?: (parentPath: string, name: string) => Promise<string | null> | string | null;
  onFileDelete?: (path: string) => void;
  onFileRename?: (oldPath: string, newPath: string) => void;

  // Loading states
  isFileTreeLoading?: boolean;
  isFileContentLoading?: boolean;

  // Editor configuration
  theme?: 'vs-dark' | 'light' | 'vs';
  fontSize?: number;
  tabSize?: number;
  wordWrap?: 'on' | 'off' | 'wordWrapColumn' | 'bounded';
  minimap?: boolean;
  lineNumbers?: 'on' | 'off' | 'relative' | 'interval';
  folding?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
  showFileTree?: boolean;
  initialFileTreeWidth?: number;
  className?: string;
  height?: string | number;
}
