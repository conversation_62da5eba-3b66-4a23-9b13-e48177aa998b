import {useCallback, useEffect, useState, useRef} from 'react';
import VSCodeEditor from './components/vscode-editor';
import {AppRegistry} from 'react-native';
import {FileNode, OpenFile} from './types';
import {
  getFileTree,
  getFileContent,
  saveFileContent,
  createFolder,
  createFile,
  deleteFolder,
  deleteFile,
} from './lib/api';
import {PLUGIN_SERVER_URL} from '../../.env.json';

export default function CodeEditor() {
  const [appContext, setAppContext] = useState<{
    appId: string;
    type: 'navigators' | 'plugins';
    artefactName: string;
  } | null>(null);

  const [fileTree, setFileTree] = useState<FileNode[]>([]);
  const [isFileTreeLoading, setIsFileTreeLoading] = useState(false);
  const [isFileContentLoading, setIsFileContentLoading] = useState(false);
  const [openFiles, setOpenFiles] = useState<OpenFile[]>([]);
  const [activeFile, setActiveFile] = useState<string | null>(null);
  const [fileContents, setFileContents] = useState<Map<string, string>>(new Map());
  const pageInitializedRef = useRef(false);

  const handleFileTreeLoad = useCallback(async () => {
    if (!appContext) return;

    setIsFileTreeLoading(true);
    try {
      const fileTreeResponse = await getFileTree(appContext.appId, appContext.type, appContext.artefactName);
      setFileTree(fileTreeResponse);
      console.log('File tree loaded');
    } catch (error) {
      console.error('Error loading file tree:', error);
    } finally {
      setIsFileTreeLoading(false);
    }
  }, [appContext]);

  // Refresh the file tree from the server
  const refreshFileTree = useCallback(async () => {
    console.log('Refreshing file tree...');
    await handleFileTreeLoad();
  }, [handleFileTreeLoad]);

  const findFileByPath = useCallback((path: string, nodes: FileNode[]): FileNode | null => {
    for (const node of nodes) {
      if (node.path === path) {
        return node;
      }
      if (node.children) {
        const found = findFileByPath(path, node.children);
        if (found) return found;
      }
    }
    return null;
  }, []);

  const handleFileSelect = useCallback(
    async (file: FileNode) => {
      if (file.type !== 'file') return;

      if (!appContext) return;

      // Check if file is already open
      const isOpen = openFiles.some(f => f.path === file.path);

      if (!isOpen) {
        try {
          setIsFileContentLoading(true);
          const content = await getFileContent(appContext.appId, appContext.type, appContext.artefactName, file.path);

          const newFile: OpenFile = {
            path: file.path,
            name: file.name,
            content,
            isDirty: false,
            savedContent: content,
          };

          setOpenFiles(prev => [...prev, newFile]);
          setFileContents(prev => new Map(prev).set(file.path, content));
        } catch (error) {
          console.error('Error fetching file content:', error);
        } finally {
          setIsFileContentLoading(false);
        }
      } else {
        // If file is already open, still make it active without loading
        setActiveFile(file.path);
      }

      setActiveFile(file.path);
    },
    [appContext, openFiles],
  );

  const handleTabSelect = useCallback((path: string) => {
    setActiveFile(path);
  }, []);

  const handleTabClose = useCallback(
    (path: string) => {
      setOpenFiles(prev => prev.filter(f => f.path !== path));
      setFileContents(prev => {
        const newMap = new Map(prev);
        newMap.delete(path);
        return newMap;
      });

      if (activeFile === path) {
        const remainingFiles = openFiles.filter(f => f.path !== path);
        setActiveFile(remainingFiles.length > 0 ? remainingFiles[remainingFiles.length - 1].path : null);
      }
    },
    [openFiles, activeFile],
  );

  const handleSave = useCallback(
    async (path: string, contentOverride?: string) => {
      // Guard against invalid path
      if (!path) {
        console.error('Cannot save file: Invalid file path');
        return;
      }

      const file = openFiles.find(f => f.path === path);
      // Use content override if provided, otherwise get from fileContents map
      const content = contentOverride !== undefined ? contentOverride : fileContents.get(path);

      // Verify we have the file, its content, and app context before saving
      if (!file) {
        console.error(`Cannot save file: File with path ${path} not found in open files`);
        return;
      }

      if (content === undefined) {
        console.error(`Cannot save file: Content for file ${path} is undefined`);
        return;
      }

      if (!appContext) {
        console.error('Cannot save file: Application context is not available');
        return;
      }

      console.log(`Attempting to save file: ${path} (${file.name})`);

      try {
        // Make API call to save file content
        await saveFileContent(appContext.appId, appContext.type, appContext.artefactName, path, content);

        // Update open files to mark file as clean and update savedContent
        setOpenFiles(prev => prev.map(f => (f.path === path ? {...f, isDirty: false, savedContent: content} : f)));

        // Update file tree with new content
        setFileTree(prevFiles => {
          const updateInNode = (nodes: FileNode[]): FileNode[] => {
            return nodes.map(node => {
              if (node.path === path) {
                return {...node, content};
              }
              if (node.children) {
                return {...node, children: updateInNode(node.children)};
              }
              return node;
            });
          };
          return updateInNode(prevFiles);
        });

        console.log(`File saved successfully: ${path} (${file.name})`);

        // Call the compileall API and notify parent window after successful save
        try {
          let urlPrefix = `${PLUGIN_SERVER_URL}/${appContext.type}`;

          console.log(`Calling compileall API for ${appContext.appId}`);

          // Call the compileall API
          await fetch(`${urlPrefix}/${appContext.appId}/compileall`, {
            method: 'POST',
          });

          // Send a message to the parent window to trigger reload
          if (window.parent && window.parent !== window) {
            console.log('Sending message to parent window to reload plugins/navigators');
            window.parent.postMessage(
              {
                type: 'CODE_EDITOR_SAVE_COMPLETE',
                appId: appContext.appId,
                artefactType: appContext.type,
              },
              '*',
            );
          }
        } catch (compileError) {
          console.error('Error during compile or reload:', compileError);
        }
      } catch (error) {
        console.error(`Error saving file ${path} (${file.name}):`, error);
      }
    },
    [openFiles, fileContents, appContext],
  );

  const handleFileChange = useCallback((path: string, content: string) => {
    setFileContents(prev => new Map(prev).set(path, content));
    setOpenFiles(prev =>
      prev.map(file => (file.path === path ? {...file, content, isDirty: content !== file.savedContent} : file)),
    );

    console.log(`File changed: ${path}`);
  }, []);

  const handleCreateFile = useCallback(
    async (parentPath: string, name: string): Promise<string | null> => {
      if (!appContext) return null;

      const newPath = parentPath ? `${parentPath}/${name}` : name;
      try {
        // Create file on the server
        await createFile(appContext.appId, appContext.type, appContext.artefactName, newPath);
        console.log(`File created on server: ${newPath}`);

        // Refresh the file tree to ensure UI matches server state
        await refreshFileTree();

        // Prepare the new file to be opened in the editor
        const newFile: OpenFile = {
          path: newPath,
          name,
          content: '',
          isDirty: false,
          savedContent: '',
        };

        // Update local state to open the file
        setOpenFiles(prev => {
          // Check if file is already in open files to avoid duplicates
          const fileExists = prev.some(f => f.path === newPath);
          if (fileExists) {
            console.log(`File ${newPath} already open, not adding to open files`);
            return prev;
          }
          console.log(`Adding ${newPath} to open files`);
          return [...prev, newFile];
        });

        // Set file content in the content map
        setFileContents(prev => {
          const newMap = new Map(prev);
          newMap.set(newPath, '');
          return newMap;
        });

        // Set as active file
        setActiveFile(newPath);
        console.log(`File created and opened: ${newPath}`);

        return newPath;
      } catch (error) {
        console.error('Error creating file:', error);
        return null;
      }
    },
    [appContext, refreshFileTree],
  );

  const handleCreateFolder = useCallback(
    async (parentPath: string, name: string): Promise<string | null> => {
      if (!appContext) return null;

      const newPath = parentPath ? `${parentPath}/${name}` : name;
      try {
        // Create folder on the server
        await createFolder(appContext.appId, appContext.type, appContext.artefactName, newPath);
        console.log(`Folder created on server: ${newPath}`);

        // Refresh the file tree to ensure UI matches server state
        await refreshFileTree();

        console.log(`Folder created: ${newPath}`);
        return newPath;
      } catch (error) {
        console.error('Error creating folder:', error);
        return null;
      }
    },
    [appContext, refreshFileTree],
  );

  const handleFileDelete = useCallback(
    async (path: string) => {
      if (!appContext) return;

      const node = findFileByPath(path, fileTree);
      if (!node) return;

      try {
        // Delete file/folder on the server
        if (node.type === 'file') {
          await deleteFile(appContext.appId, appContext.type, appContext.artefactName, path);
        } else {
          await deleteFolder(appContext.appId, appContext.type, appContext.artefactName, path);
        }

        // Update local state to remove the file
        setOpenFiles(prev => prev.filter(f => f.path !== path));

        // If the deleted file was active, set a new active file
        if (activeFile === path) {
          const remainingFiles = openFiles.filter(f => f.path !== path);
          setActiveFile(remainingFiles.length > 0 ? remainingFiles[0].path : null);
        }

        // Remove file content from the content map
        setFileContents(prev => {
          const newMap = new Map(prev);
          newMap.delete(path);
          return newMap;
        });

        // Refresh the file tree to ensure UI matches server state
        await refreshFileTree();

        console.log(`${node.type} deleted: ${path}`);
      } catch (error) {
        console.error('Error deleting item:', error);
      }
    },
    [appContext, fileTree, activeFile, openFiles, findFileByPath, refreshFileTree],
  );

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const appId = urlParams.get('appId');
    const itemType = urlParams.get('type') as 'navigators' | 'plugins';
    const artefactName = urlParams.get('artefactName');

    if (appId && itemType && artefactName) {
      setAppContext({
        appId,
        type: itemType,
        artefactName,
      });

      setIsFileTreeLoading(true);
      getFileTree(appId, itemType, artefactName)
        .then(data => {
          setFileTree(data);
        })
        .catch(error => {
          console.error('Error fetching file tree:', error);
        })
        .finally(() => {
          setIsFileTreeLoading(false);
        });
    }
  }, []);

  useEffect(() => {
    if (appContext) {
      handleFileTreeLoad();
    }
  }, [handleFileTreeLoad]);

  useEffect(() => {
    if (pageInitializedRef.current) return;

    if (fileTree.length > 0) {
      const componentFile = findFileByPath('component.jsx', fileTree);
      if (componentFile) {
        handleFileSelect(componentFile);
        pageInitializedRef.current = true;
      }
    }
  }, [fileTree]);

  return (
    <div className="w-full h-screen">
      {appContext ? (
        <VSCodeEditor
          initialFiles={fileTree}
          openFiles={openFiles}
          setOpenFiles={setOpenFiles}
          activeFile={activeFile}
          fileContents={fileContents}
          setFileContents={setFileContents}
          onFileSelect={handleFileSelect}
          onTabSelect={handleTabSelect}
          onTabClose={handleTabClose}
          onFileChange={handleFileChange}
          onFileSave={handleSave}
          onFileCreate={handleCreateFile}
          onCreateFolder={handleCreateFolder}
          onFileDelete={handleFileDelete}
          isFileTreeLoading={isFileTreeLoading}
          isFileContentLoading={isFileContentLoading}
          fontSize={12}
          tabSize={2}
          wordWrap="off"
          minimap={true}
          lineNumbers="on"
          folding={true}
          autoSave={false}
          autoSaveDelay={1000}
          showFileTree={true}
          initialFileTreeWidth={250}
          height="100vh"
        />
      ) : (
        <div className="w-full h-screen flex items-center justify-center">
          <p className="text-2xl text-white">Failed to load the editor</p>
        </div>
      )}
    </div>
  );
}

const appName = 'ApptileCodeEditor';

AppRegistry.registerComponent(appName, () => CodeEditor);
AppRegistry.runApplication(appName, {
  initialProps: {},
  rootTag: document.getElementById('app-root'),
});
