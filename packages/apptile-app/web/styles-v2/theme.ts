import {Platform} from 'react-native';

const theme = {
  DEFAULT_COLOR: '#FFFFFF',
  PRIMARY_COLOR: '#FFFFFF',
  SECONDARY_COLOR: '#000000',
  TERTIARY_COLOR: '#070707',
  QUATERNARY_COLOR: '#000000',
  NEW_TAB_COLOR: '#000000',
  DISABLED_COLOR: '#858585',
  SUCCESS_COLOR: '#FFFFFF',
  WARNING_COLOR: '#FFFFFF',
  ERROR_COLOR: '#FFFFFF',
  TAB_COLOR: '#005BE4',
  TILE_COLOR: '#000000',
  CTA_COLOR: '#FFFFFF',
  PREMIUM_COLOR: '#BD00FF',

  DEFAULT: '#FFFFFF',
  PRIMARY: '#005BE4',
  SECONDARY: '#000000',
  TERTIARY: '#070707',
  QUATERNARY: '#E8E8E8',
  DISABLED: '#858585',
  SUCCESS: '#14B8A6',
  WARNING: '#EAB308',
  ERROR: '#FF4C28',
  TAB: '#F9F9F9',
  TILE: '#000000',
  CTA: '#005BE4',
  PREMIUM: '#BD00FF',

  DEFAULT_BACKGROUND: '#000000',
  PRIMARY_BACKGROUND: '#ECE9E1',
  SECONDARY_BACKGROUND: '#F9F9F9',
  TERTIARY_BACKGROUND: '#F3F3F3',
  QUATERNARY_BACKGROUND: '#E8E8E8',
  NEW_TAB_BACKGROUND: '#0000000D',
  DISABLED_BACKGROUND: '#D9D9D9',
  SUCCESS_BACKGROUND: '#14B8A6',
  WARNING_BACKGROUND: '#EAB308',
  ERROR_BACKGROUND: '#FF4C28',
  TAB_BACKGROUND: '#FFFFFF',
  TILE_BACKGROUND: '#FFFFFF',
  TILE_LABEL_BACKGROUND: '#F9F9F9',
  CTA_BACKGROUND: '#005BE4',
  PREMIUM_BACKGROUND: '#FAEAFF',

  PRIMARY_OPAQUE_BACKGROUND: '#1060E00D',

  EDITOR_GREY_COLOR: '#C8C8C8',
  EDITOR_LIGHT_BLACK_COLOR: '#535353',

  TEXT_COLOR: '#070707',

  FONT_FAMILY: "'Work Sans', sans-serif",
  FONT_FAMILY_BODY: 'Work Sans',

  PRIMARY_BORDER: '#000000',
  SECONDARY_BORDER: '#D7D7D7',
  TERTIARY_BORDER: '#C8C8C8',
  QUATERNARY_BORDER: '#000000',
  NEW_TAB_BORDER: '#0000000D',
  PREMIUM_BORDER: '#BD00FF1A',
  CTA_BORDER: '#005BE4',
  TILE_BORDER: '#DADADA',
  TILE_BORDER_COLOR: '#DADADA',
  CONTROL_BORDER: '#E5E5E5',
  grabbable: {
    ...Platform.select({
      web: {
        cursor: 'grab',
      },
    }),
  },
  hoverable: {
    ...Platform.select({
      web: {
        cursor: 'pointer',
      },
    }),
  },
  modal: {
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    border: 'none',
    padding: 10,
  },
  smallModal: {
    borderRadius: 10,
    backgroundColor: '#FFFFFF',
    border: 'none',
    padding: 7,
  },

  INPUT_BACKGROUND: '#F3F3F3',
  INPUT_BORDER_RADIUS: 8,
  INPUT_BORDER: '#DADADA',

  CONTROL_ACTIVE_COLOR: '#005BE4',
  CONTROL_INPUT_COLOR: '#535353',
  CONTROL_PLACEHOLDER_COLOR: '#A3A3A3',

  FONT_SIZE: 13,
  FONT_SIZE_HEADING: 11,
  FONT_COLOR: '#1D1D1C',
  FONT_WEIGHT: '400',
  FONT_WEIGHT_BOLD: '500',
  FONT_WEIGHT_BOLDER: '600',
  LINE_HEIGHT: 15,
  LINE_HEIGHT_HEADING: 17,
  PRIMARY_HEIGHT: 30,
  PRIMARY_MARGIN: 4,
  FONT_SIZE_ALERT: 16,
  LINE_HEIGHT_ALERT: 19,

  ONBOARDING_FONT_SIZE: 40,
  ONBOARDING_FONT_WEIGHT: '500',

  RIGHT_SIDE_BAR_WIDTH: 360,
} as const;

export default theme;
