import {DispatchAction} from 'apptile-core';
import {SagaIterator} from '@redux-saga/types';
import {normalize, schema} from 'normalizr';
import {all, call, put, takeLatest} from 'redux-saga/effects';
import {
  ConfirmChargePayload,
  CONFIRM_CHARGE,
  CO<PERSON><PERSON><PERSON>_CHARGE_FAILED,
  CONF<PERSON><PERSON>_CHARGE_SUCCESS,
  FetchMyScubscriptionPayload,
  FetchPaymentUrl,
  FetchPlanPayload,
  FETCH_MY_SUBSCRIPTION,
  FETCH_MY_SUBSCRIPTION_FAILED,
  FETCH_MY_SUBSCRIPTION_SUCCESS,
  FETCH_PAYMENT_URL,
  FETCH_PAYMENT_URL_FAILED,
  FETCH_PAYMENT_URL_SUCCESS,
  FETCH_PLAN,
  FETCH_PLANS_LIST,
  FETCH_PLANS_LIST_FAILED,
  FETCH_PLANS_LIST_SUCCESS,
  <PERSON>ETCH_PLAN_FAILED,
  <PERSON>ETCH_PLAN_SUCCESS,
  <PERSON>ET<PERSON>_ADD_ON_PAYMENT_URL,
  <PERSON><PERSON><PERSON>_MY_ADD_ONS_SUCCESS,
  FET<PERSON>_MY_ADD_ONS_FAILED,
  <PERSON>ET<PERSON>_MY_ADD_ONS,
} from '../actions/editorActions';
import {makeToast} from '../actions/toastActions';
import {FetchNormalizedResponse, ISubscriptionPlan} from '../api/ApiTypes';
import BillingApi from '../api/BillingApi';

const itemSchema = new schema.Entity('items', {variants: []}, {idAttribute: 'id'});
const itemListSchema = new schema.Array(itemSchema);

function _normalizeFetchPlans(response: any): FetchNormalizedResponse<ISubscriptionPlan> {
  return normalize(response, itemListSchema);
}

function _normalizeFetchPlan(response: any): FetchNormalizedResponse<ISubscriptionPlan> {
  return normalize(response, itemSchema);
}

export function* fetchPaymentUrl(action: DispatchAction<FetchPaymentUrl>): SagaIterator {
  try {
    const {planId, appId, billingInterval} = action.payload;
    const result = yield call(BillingApi.fetchPaymentUrl, planId, appId, billingInterval);
    const {paymentConfirmationUrl} = result.data;
    yield put({
      type: FETCH_PAYMENT_URL_SUCCESS,
      payload: paymentConfirmationUrl,
    });
  } catch (e) {
    yield put({
      type: FETCH_PAYMENT_URL_FAILED,
      payload: {
        e,
      },
    });
    yield put(
      makeToast({
        content: 'We can not process your payment! Please contact our support Team!',
        appearances: 'error',
      }),
    );
  }
}

export function* fetchAddOnPaymentUrl(action: DispatchAction<FetchPaymentUrl>): SagaIterator {
  try {
    const {addOnType, addOnCode, appId} = action.payload;
    const result = yield call(BillingApi.fetchAddOnPaymentUrl, addOnType, addOnCode, appId);
    const {paymentConfirmationUrl} = result.data;
    yield put({
      type: FETCH_PAYMENT_URL_SUCCESS,
      payload: paymentConfirmationUrl,
    });
  } catch (e) {
    yield put({
      type: FETCH_PAYMENT_URL_FAILED,
      payload: {
        e,
      },
    });
    yield put(
      makeToast({
        content: 'We can not process your payment! Please contact our support Team!',
        appearances: 'error',
      }),
    );
  }
}

export function* fetchPlanList(): SagaIterator {
  try {
    const result = yield call(BillingApi.fetchPlanList);
    yield put({
      type: FETCH_PLANS_LIST_SUCCESS,
      payload: _normalizeFetchPlans(result.data),
    });
  } catch (e) {
    yield put({
      type: FETCH_PLANS_LIST_FAILED,
      payload: {
        e,
      },
    });
  }
}

export function* fetchMySubscription(action: DispatchAction<FetchMyScubscriptionPayload>): SagaIterator {
  try {
    const result = yield call(BillingApi.fetchMyPlan, action.payload.appId, action.payload.organizationId);
    const {plan, ...subscription} = result.data;
    yield put({
      type: FETCH_MY_SUBSCRIPTION_SUCCESS,
      payload: subscription,
    });

    if (plan) {
      yield put({
        type: FETCH_PLAN_SUCCESS,
        payload: _normalizeFetchPlan(plan),
      });
    }
  } catch (e) {
    yield put({
      type: FETCH_MY_SUBSCRIPTION_FAILED,
      payload: {
        e,
      },
    });
  }
}

export function* fetchMyAddOns(action: DispatchAction<FetchMyScubscriptionPayload>): SagaIterator {
  try {
    const result = yield call(BillingApi.fetchMyAddOns, action.payload.appId);
    const addOns = result.data;

    if (addOns) {
      yield put({
        type: FETCH_MY_ADD_ONS_SUCCESS,
        payload: addOns,
      });
    }
  } catch (e) {
    yield put({
      type: FETCH_MY_ADD_ONS_FAILED,
      payload: {
        e,
      },
    });
  }
}

export function* fetchPlan(action: DispatchAction<FetchPlanPayload>): SagaIterator {
  try {
    const {planId} = action.payload;
    const result = yield call(BillingApi.fetchPlan, planId);
    yield put({
      type: FETCH_PLAN_SUCCESS,
      payload: _normalizeFetchPlan(result.data),
    });
  } catch (e) {
    yield put({
      type: FETCH_PLAN_FAILED,
      payload: {
        e,
      },
    });
  }
}

export function* confirmCharge(action: DispatchAction<ConfirmChargePayload>): SagaIterator {
  try {
    const {chargeId, orgAddOnId} = action.payload;
    const result = yield call(BillingApi.confirmCharge, chargeId, orgAddOnId);
    yield put({
      type: CONFIRM_CHARGE_SUCCESS,
      payload: result.data,
    });
  } catch (e) {
    yield put({
      type: CONFIRM_CHARGE_FAILED,
      payload: {
        e,
      },
    });
  }
}

export default function* billingSagas(): SagaIterator {
  yield all([
    takeLatest(FETCH_PAYMENT_URL, fetchPaymentUrl),
    takeLatest(FETCH_ADD_ON_PAYMENT_URL, fetchAddOnPaymentUrl),
    takeLatest(FETCH_PLANS_LIST, fetchPlanList),
    takeLatest(FETCH_PLAN, fetchPlan),
    takeLatest(FETCH_MY_SUBSCRIPTION, fetchMySubscription),
    takeLatest(CONFIRM_CHARGE, confirmCharge),
    takeLatest(FETCH_MY_ADD_ONS, fetchMyAddOns),
  ]);
}
