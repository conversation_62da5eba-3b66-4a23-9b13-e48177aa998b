// This file is always an copy of the file frontend-tools.ts in apptile-cli
// TODO(gaurav): Find a better way than copying this file. (perhaps compile and 
// serve it as a javascript file)

// If you are in the apptile-cli. This file must be updated in ReactNativeTSProjeect
// because tool calls will be made over there.

import {all, call, delay, put, select, StrictEffect, take, takeLatest} from 'redux-saga/effects';
import {DispatchAction, DispatchActions, store, getNavigationContext} from 'apptile-core';
import { reloadExternalPlugins } from '@/root/app/plugins/initPlugins';
import { softRestartConfig } from '../actions/editorActions';
import { SagaIterator } from 'redux-saga';
import { RUN_CHAT_COMPLETION_REQUEST } from '../actions/aiActions';

async function getImagesFromPlannerAgent(appId: string) {
  try {
    return await fetch(`${window.PLUGIN_SERVER_URL}/home/<USER>/plannerimages`)
      .then(res => res.json());
  } catch (err) {
    console.error("Could not fetch images from planner");
    return [];
  }
}

async function createNewPlugin(appId: string, name: string) {
  await fetch(`${window.PLUGIN_SERVER_URL}/plugins/${appId}/create`, {
    method: 'POST',
    headers: {
      'content-type': 'application/json',
    },
    body: JSON.stringify({
      labelPrefix: name,
      listingName: name,
      displayDescription: 'Basic plugin created from template',
      listingIcon: 'badge',
      pluginRegistryName: name,
    }),
  });
  
  return new Promise((resolve, reject) => {
    setTimeout(async () => {
      try {
        await fetch(`${window.PLUGIN_SERVER_URL}/plugins/${appId}/compileall`, {
          method: 'POST',
        });
        await reloadExternalPlugins({uuid: appId});
      } catch (err) {
        console.error('[AGENT] reloading external plugins failed!', err)
      }
      store.dispatch(softRestartConfig());
      resolve({});
    }, 3000)
  })
}

export const toolsMap: Record<string, {description: string; handler: (input: any) => Generator<StrictEffect<any, any>, string, any>}> = {
  nocodelayer_create_global_plugin: {
    "description": "Creating a global state container __name__",
    handler: function *createGlobalPlugin(input: {name: string; value: string; pluginType: "StatePlugin"|"LocalStoragePlugin"}) {
      yield put({
        type: DispatchActions.CREATE_GLOBAL_STATE_REQUEST,
        payload: {
          stateName: input.name,
          pluginType: input.pluginType,
          stateValue: input.value,
        },
      });

      yield take(DispatchActions.CREATE_GLOBAL_STATE_DONE);
      yield put({
        type: DispatchActions.SELECT_PLUGIN,
        payload: [input.name]
      });
      yield put({
        type: 'EDITOR_OPEN_PROPERTY_INSPECTOR'
      });

      yield delay(2000);

      return JSON.stringify({
        isGlobalStateCreated: true,
        name: input.name,
      });
    }
  },
  nocodelayer_create_screen: {
    "description": "Creating a new screen __name__",
    handler: function *createScreen(input: {name: string;}) {
      let screenName = input.name;
      let pageName = screenName;

      yield put({
        type: 'ADD_NEW_PAGE',
        payload: {
          pageId: pageName
        }
      });
      const status = yield take(['ADD_NEW_PAGE_DONE', 'ADD_NEW_PAGE_ERROR']);
      if (status.type === 'ADD_NEW_PAGE_DONE') {
        yield put({
          type: 'UPDATE_PAGE_CONFIG_PATH',
          payload: {
            pageId: pageName,
            selector: [],
            update: {
              containerType: 'View'
            }
          }
        });
        yield put({
          type: 'ADD_NAVIGATION_PAGE',
          payload: {
            navSelector: ['/'],
            screenConfig: {},
            screenName
          }
        });
        yield delay(1000);
        yield put({
          type: 'EDITOR_SELECT_NAV_COMPONENT',
          payload: ['/', screenName]
        });
        yield put({
          type: 'EDITOR_OPEN_PROPERTY_INSPECTOR'
        });
        yield put({
          type: 'EDITOR_SELECTED_PAGE_TYPE',
          payload: 'screen'
        });
        yield put({
          type: 'UPDATE_NAVIGATION_CONFIG_PATH',
          payload: {
            navSelector: ['/', screenName],
            selector: [],
            update: {screen: pageName} 
          }
        });
        return 'done';
      } else {
        return 'failed to perform action!';
      }
    }
  },
  nocodelayer_create_plugin: {
    "description": "Creating a component __name__",
    handler: function *createPlugin(input: {name: string; apptile_context: { appId: string;}}) {
      yield call(createNewPlugin, input.apptile_context.appId, input.name);
      yield put({
        type: 'EDITOR_OPEN_PROPERTY_INSPECTOR'
      });
      yield delay(100);
      yield put({
        type: 'EDITOR_OPEN_PLUGIN_LISTING'
      });
      return 'done';
    }
  },
  nocodelayer_generate_code_for_plugin: {
    "description": "Generating code for the component __name__ which is in the screen __screen__",
    handler: function *promptPluginAgent(input: {
      name: string; 
      prompt: string; 
      screen: string; 
      inlcude_image: boolean;
      apptile_context: {
        appId: string; 
        liveMessageSubscriber: (message: string) => void;
        model: string;
        provider: "claude"|"openai";
      }
    }) {
      let retryCount = 0;
      let userPromptResponseFromFrontend = "You stopped in the middle of generation because of a network issue. Please continue.";
      let responseForPlanner = "done";

      let shouldRetry;
      do {
        let isFinished = false;
        shouldRetry = false;
        debugger
        if (input.inlcude_image) {
          const imageContents = yield call(getImagesFromPlannerAgent, input.apptile_context.appId);
          if (imageContents.length && imageContents[0].content) {
            input.prompt += `
            <br/>
            <img src="data:${imageContents[0].content_type};base64,${imageContents[0].content}"/>`;
          }
        }

        yield put({
          type: RUN_CHAT_COMPLETION_REQUEST,
          payload: {
            prompt: shouldRetry ? userPromptResponseFromFrontend : input.prompt,
            model: input.apptile_context.model,
            provider: input.apptile_context.provider,
            completionUrl: `${window.PLUGIN_SERVER_URL}/plugins/prompt/${input.apptile_context.appId}/${input.name}`,
            appId: input.apptile_context.appId,
            usePlanner: false,
            liveMessageSubscriber: input.apptile_context.liveMessageSubscriber,
            onCompleted: (err: any) => {
              if (err && err.type === "network error") {
                // prompt user and loop around
                const response = window.prompt(
                  `A network error happened that stopped code generation for ${input.name}. Ask the model to continue with this message?`, 
                  userPromptResponseFromFrontend
                );
                userPromptResponseFromFrontend = response || userPromptResponseFromFrontend;
                shouldRetry = true;
                retryCount++;
                if (retryCount >= 2) {
                  isFinished = true;
                  shouldRetry = false;
                  responseForPlanner = "There were repeated connectivity issues, tool has been aborted! Instruct user to check their internet connection.";
                }
              } else {
                console.log("[AGENT] Finished with code generation. Reloading plugins")
                reloadExternalPlugins({ uuid: input.apptile_context.appId })
                  .then(() => {
                    return store.dispatch(softRestartConfig());
                  })
                  .then(() => {
                    const context = getNavigationContext();
                    context.navigate('Home');
                    setTimeout(() => {
                      if (input.screen) {
                        const context = getNavigationContext();
                        context.navigate(input.screen)
                      }
                    }, 300);
                    isFinished = true;
                  })
                  .catch(err => {
                    console.error("[AGENT] Failed in code generation tool", err);
                  });
              }
            }
          },
        });
        
        while (!isFinished && !shouldRetry) {
          console.log("Waiting 5 seconds");
          yield delay(5000);
        }
        console.log("End of should retry loop: ", shouldRetry);
      } while (shouldRetry);
      return responseForPlanner;
    }
  },
  nocodelayer_add_plugin_to_screen: {
    "description": "Adding component __pluginName__ to screen __screenName__",
    handler: function *dropPluginInPage(input: {screenName: string; pluginName: string; insertAfter: string|null;}) {
      // const lastPlugin = store.getState().appConfig.current.getIn(['pages', input.screenName, 'plugins']).last()?.get('id');
      if (input.insertAfter) {
        yield put({
          type: 'ADD_PLUGIN',
          payload: {
            afterRefWidget: true,
            configType: "widget",
            container: "",
            pageId: input.screenName,
            pluginType: input.pluginName,
            refWidget: input.insertAfter,
          }
        });
      } else {
        yield put({
          type: 'ADD_PLUGIN',
          payload: {
            afterRefWidget: false,
            configType: "widget",
            container: "",
            pageId: input.screenName,
            pluginType: input.pluginName,
            refWidget: "",
          }
        });
      }
      // Why this soft refersh is needed is not clear
      yield delay(2000);
      yield put(softRestartConfig());
      // yield take('FINALIZE_COMMIT_APP_MODEL');
      yield delay(2000);
      const navContext = getNavigationContext();
      navContext.navigate(input.screenName);
      yield delay(2000);
      return 'done';
    }
  },
  nocodelayer_read_current_screens: {
    "description": "Checking what screens already exist",
    handler: function *readCurrentScreens() {
      function constructPluginForest(plugins) {
        const idMap = new Map();
        for (let i = 0; i < plugins.length; ++i) {
            const plugin = plugins[i];
            idMap.set(plugin.id, {name: plugin.name, id: plugin.id, children: []});
        }
    
        let forest = [];
        for (let i = 0; i < plugins.length; ++i) {
            const plugin = plugins[i];
            if (plugin.parentId) {
                const parent = idMap.get(plugin.parentId);
                const self = idMap.get(plugin.id);
                parent.children.push(self)
            } else {
                forest.push(idMap.get(plugin.id))
            }
        }
        return forest;
      }

      function makeTree(appConfig, navConfigRoot, collected) {
        if (navConfigRoot.type === "navigator") {
          collected.type = navConfigRoot.navigatorType + " navigator";
          collected.children = [];
          const screenNames = Object.keys(navConfigRoot.screens);
          for (let i = 0; i < screenNames.length; ++i) {
            const screenName = screenNames[i];
            const obj = {};
            collected.children.push(obj)
            makeTree(appConfig, navConfigRoot.screens[screenName], obj);
          }
        } else {
          collected.type = 'screen';
          collected.name = navConfigRoot.name;
          const plugins = appConfig
            .getIn(['pages', collected.name, 'plugins'])
            .toList()
            .map(it => ({
              name: it.subtype, 
              id: it.id, 
              parentId: it.layout.container
            }))
            .toJS();
          collected.plugins = constructPluginForest(plugins);
        }
      }

      try {
        const appConfig = yield select(state => state.appConfig.current);
        const navConfig = appConfig.getIn(['navigation', 'rootNavigator']);
        const root = navConfig.toJS();
        const simplifiedNavTree = {};
        makeTree(appConfig, root, simplifiedNavTree);

        return JSON.stringify(simplifiedNavTree, null, 2);
      } catch (err) {
        console.error("Failed to list current screens: ", err);
        return "Operation failed!";
      }
    }
  },
  nocodelayer_read_global_plugin_value: {
    "description": "Checking the value stored in the global state container __plugin_name__",
    handler: function *readGlobalPluginValue(input: {plugin_name: string;}) {   
      console.log("[AGENT] running tool nocodelayer_read_global_plugin_value", input);
      const pluginValue = yield select(state => state.appModel.values.getIn([input.plugin_name, 'value']));
      const currentValue = JSON.stringify(pluginValue);
      const pluginConfig = yield select(state => state.appConfig.current.getIn(['plugins', input.plugin_name]));
      const answer = `
<global-plugin-info>
  <plugin-type description="the type of the global plugin">
    ${pluginConfig && pluginConfig.getIn(['subtype'])}
  </plugin-type>
  <intial-value description="this is the initial configuration which is a javascript expression interpolated within double braces">
    ${pluginConfig && pluginConfig.getIn(['config', 'value'])}
  </initial-value>
  <current-value description="the value that is currently stored in the plugin">
    ${currentValue}
  </current-value>
</global-plugin-info>
`;
      console.log("Tool result: ", answer)
      return answer;
    }
  },
  nocodelayer_list_global_plugins: {
    "description": "Checking what global state containers already exist",
    handler: function *listGlobalPlugins() {
      const currentAppConfig = yield select(state => state.appConfig.current);
      const pluginNames = Object.keys(currentAppConfig.get('plugins').toJS());
      return JSON.stringify(pluginNames);
    }
  },
  nocodelayer_update_global_plugin_defaultvalue: {
    description: "Updating the initial value for the global plugin __plugin_name__",
    handler: function *updateGlobalPluginConfig(input: {plugin_name: string, new_value: string}) {
      yield put({
        type: "PLUGIN_UPDATE_CONFIG_PATH",
        payload: {
          pluginId: input.plugin_name, // name is the same as id for AI
          pageId: null,
          selector: [
              "config"
          ],
          update: {
            value: input.new_value
          }
        }
      });
      yield delay(3000);
      return "Done";
    }
  }
};