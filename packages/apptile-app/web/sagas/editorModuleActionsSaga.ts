import {SagaIterator} from '@redux-saga/types';
import * as Immutable from 'immutable';
import {isEqual, keyBy} from 'lodash';
import {all, call, put, select, takeEvery, takeLatest, delay} from 'redux-saga/effects';
import {
  DispatchActions, 
  DispatchAction, 
  EVENT_PARAM_IDENTIFIER, 
  NAMESPACE_SPERATOR, 
  bulkPluginUpdates, 
  UNSAFE_renameVariables, 
  getPluginModelSelectorFromSelector, 
  objsel,
} from 'apptile-core';
import TemplatesApi from '../api/TemplatesApi';
import {
  AddModulePluginPropertyPayload,
  ADD_MODULE_PLUGIN_PROPERTY_EDITOR,
  CreateModuleFromWidget,
  PluginPropsAndEditors,
  DeleteModulePluginPropertyPayload,
  DELETE_MODULE_PLUGIN_PROPERTY_EDITOR,
  DELETE_MODULE_RECORD,
  DUPLICATE_MODULE_RECORD,
  ForwardModulePluginPropertyPayload,
  FORWARD_MODULE_PLUGIN_PROPERTY,
  ReorderModulePluginPropertiesPayload,
  REORDER_MODULE_PLUGIN_PROPERTIES,
  UpdateModuleDefinitionPayload,
  UpdateModulePluginPropertyPayload,
  UpdateModulePropertyPayload,
  UPDATE_MODULE_DEF_RECORD,
  UPDATE_MODULE_PLUGIN_PROPERTY,
  UPDATE_MODULE_PROPERTY,
  SAVE_MODULE_VARIANT,
  SaveModuleVariantPayload,
  FILL_MODULE_MANDATORY_FIELDS,
  moduleMandtoryFieldsFailed,
  moduleMandtoryFieldsFinished,
} from '../actions/editorActions';
import {
  AppConfig,
  AppModelType,
  EventHandlerConfig,
  ModuleEditorRecord,
  ModuleEventHandlerConfig,
  ModuleRecord,
  PageConfig,
  PluginConfig,
  PluginNamespaceImpl,
  RecordSerializer,
  LayoutRecord,
} from 'apptile-core';
import type {
  BatchCreateModulePluginsAction,
  CreateModule,
  CreateModuleEvent,
  CreateModuleOutput,
  CreateModuleProperty,
  DeleteModuleEvent,
  DeleteModuleOutput,
  DeleteModuleProperty,
  PersistModuleInputs,
  // TODO(gaurav): import this type
  // RemapModuleInstance,
  RenameModuleEvent,
  RenameModuleOutput,
  RenameModuleProperty,
  UpdateModuleOutput,
  PluginConfigType,
  ModuleEditorConfig,
} from 'apptile-core';
import {
  selectPlugin,
  getPluginSelector,
  batchInitModelForPlugins,
  triggerOnPluginUpdate,
  commitStageModel,
  GetRegisteredOnPluginUpdate,
  PluginIdTypePage,
  JSModel,
} from 'apptile-core';
import {strsel, strsel_index, updateProperty, Selector} from 'apptile-core';
import {getJSBindingVariables, evaluateJSBindingString} from 'apptile-core';
import {isJSBinding} from 'apptile-core';
import {SHARED_GLOBAL_MODEL_LIBRARIES, SHARED_GLOBAL_MODEL_PROPERTIES} from 'apptile-core';
import {getPluginsContained} from 'apptile-core';
import {selectAppConfig, selectPluginConfig} from 'apptile-core';
import {selectAppModel, selectStageModel} from 'apptile-core';
import {selectPageConfigForPage} from 'apptile-core';
import {apptileState} from '../../app/store/ApptileReducer';
import {
  closeModuleCreationDialog,
  CREATE_MODULE_FROM_WIDGET,
  openModuleCreationDialog,
  SAVE_MODULE_RECORD,
  setModuleCreationParams,
} from '../actions/editorActions';
import {EditorRootState} from '../store/EditorRootState';
import {selectParentModulePlugin} from '../selectors/EditorSelectors';
import {selectModuleByUUID} from 'apptile-core';
import _ from 'lodash';
import {makeToast} from '../actions/toastActions';
import {selectModulePluginsInApp} from '../selectors/EditorModuleSelectors';
import {v4 as uuidv4} from 'uuid';
import TilesApi from '../api/TilesApi';
import {initModulePlugins} from '../common/module/initModulePlugins';
import {pluginConfigUpdatePath} from 'apptile-core';
import {getShopifyObjectCache} from '../integrations/shopify/ShopifyObjectCache';

function* makeSinglePluginTile(action: DispatchAction<PluginPropsAndEditors>): SagaIterator {
  const tileName = action.payload.tileConfig.name;
  const appConfig: AppConfig = yield select(selectAppConfig);
  const config = Immutable.OrderedMap({
    "root": new PluginConfig({
      id: "root",
      type: "widget",
      subtype: "ContainerWidget",
      config: Immutable.Map({
        detectVisibility: false,
        enableHaptics: false,
        hapticMethod: "",
        isTappable: false,
        makeKeyboardAware: false,
        style: Immutable.Map({})
      }),
      layout: new LayoutRecord({container: ""})
    }),
    "component": new PluginConfig({
      id: "component",
      type: "widget",
      subtype: action.payload.subtype,
      layout: new LayoutRecord({container: "root"}),
      config: Immutable.Map(
        Object.entries(action.payload.tileConfig.defaultProps)
          .map(([k, v]) => {
            if (typeof v.defaultValue === "object") {
              return [k, `{{(${JSON.stringify(v.defaultValue)})}}`];
            } else if (typeof v.defaultValue === "number") {
              return [k, `{{${v.defaultValue}}}`]
            } else {
              return [k, v.defaultValue];
            }
          })
      ),
    })
  })
  const editors = Immutable.OrderedMap(
    Object.entries(action.payload.tileConfig.defaultProps)
      .map(([k, v]) => {
        let editorRecord: ModuleEditorRecord;
        if (v.type === "codeInput") {
          editorRecord = new ModuleEditorRecord({
            advanceProperty: false,
            basePlan: "CORE",
            label: v.label,
            mandatory: false,
            selector: ["component", "config", k],
            value: undefined,
            editorType: {
              type: "codeInput",
              defaultValue: undefined,
              label: "",
              name: k,
              props: {
                label: k,
                singleLine: true
              },
            }
          });
        } else if (v.type === "colorInput") {
          editorRecord = new ModuleEditorRecord({
            advanceProperty: false,
            basePlan: "CORE",
            label: v.label,
            mandatory: false,
            selector: ["component", "config", k],
            value: undefined,
            editorType: {
              type: "colorInput",
              defaultValue: undefined,
              label: "",
              name: k,
              props: {
                assetProperty: "assetId",
                disableBinding: true,
                label: k,
                sourceTypeProperty: "sourceType",
                urlProperty: "value"
              },
            }
          });
        } else {
          throw new Error(`Unsupported editor type encountered: ${k} ${v}`);
        }
        return [`component.config.${k}`, editorRecord];
      })
  );
  let moduleRecord = new ModuleRecord({moduleName: tileName, moduleUUID: uuidv4()});
  moduleRecord = moduleRecord.set('events', []);
  moduleRecord = moduleRecord.set('moduleConfig', config);
  moduleRecord = moduleRecord.set('defaultEventHandlers', Immutable.List());
  moduleRecord = moduleRecord.set('basicEditors', editors);
  let newAppConfig = appConfig.setIn(['modules', moduleRecord.moduleUUID], moduleRecord);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });

}

/*
function* wrapPluginIntoTile(action: DispatchAction<{tileName: string;}>): SagaIterator {
  const tileName = action.payload.tileName;
  const appConfig: AppConfig = yield select(selectAppConfig);
  const config = Immutable.OrderedMap({
    "root": new PluginConfig({
      id: "root",
      type: "widget",
      subtype: "ContainerWidget",
      config: Immutable.Map({
        detectVisibility: false,
        enableHaptics: false,
        hapticMethod: "",
        isTappable: false,
        makeKeyboardAware: false,
        style: Immutable.Map({})
      }),
      layout: new LayoutRecord({container: ""})
    }),
    "component": new PluginConfig({
      id: "component",
      type: "widget",
      subtype: "drawingdemo",
      layout: new LayoutRecord({container: "root"}),
      config: Immutable.Map({
        title: "My Ratings",
        starFillColor: "#0000ff",
        totalReviews: "3221"
      }),
    })
  })
  const editors = Immutable.OrderedMap({
    "component.config.title": {
      advanceProperty: false,
      basePlan: "CORE",
      label: "Card title",
      mandatory: false,
      selector: ["component", "config", "title"],
      value: undefined,
      editorType: {
        type: "codeInput",
        defaultValue: undefined,
        label: "",
        name: "title",
        props: {
          label: "title",
          singleLine: true
        },
      }
    },
    "component.config.starFillColor": new ModuleEditorRecord({
      advanceProperty: false,
      basePlan: "CORE",
      label: "Fill color",
      mandatory: false,
      selector: ["component", "config", "starFillColor"],
      value: undefined,
      editorType: {
        type: "colorInput",
        defaultValue: undefined,
        label: "",
        name: "starFillColor",
        props: {
          assetProperty: "assetId",
          disableBinding: true,
          label: "starFillColor",
          sourceTypeProperty: "sourceType",
          urlProperty: "value"
        },
      }
    }),
    "component.config.totalReviews": {
      advanceProperty: false,
      basePlan: "CORE",
      label: "Total Reviews",
      mandatory: false,
      selector: ["component", "config", "totalReviews"],
      value: undefined,
      editorType: {
        type: "codeInput",
        defaultValue: undefined,
        label: "",
        name: "totalReviews",
        props: {
          label: "title",
          singleLine: true
        },
      }
    }
  });
  let moduleRecord = new ModuleRecord({moduleName: tileName, moduleUUID: uuidv4()});
  moduleRecord = moduleRecord.set('events', []);
  moduleRecord = moduleRecord.set('moduleConfig', config);
  moduleRecord = moduleRecord.set('defaultEventHandlers', Immutable.List());
  moduleRecord = moduleRecord.set('basicEditors', editors);
  let newAppConfig = appConfig.setIn(['modules', moduleRecord.moduleUUID], moduleRecord);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}
*/

function* handleCreateModuleFromWidget(action: DispatchAction<CreateModuleFromWidget>): SagaIterator {
  try {
    const {widgetId, pageId} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    const appModel: AppModelType = yield select(selectAppModel);
    const pageConfig = yield select(selectPageConfigForPage, pageId);
    const pluginConfig = yield select(selectPluginConfig, pageId, widgetId);

    const depGraph = appModel.dependencyGraph;
    const pageKeys = appModel.getPageKeysForId(pageId);
    const pageKey = pageKeys.first();
    if (!pageKey) {
      throw `Could not find Instance of pageId: ${pageId}`;
    }

    let containedPlugins: Immutable.Set<PluginConfig> = Immutable.Set();
    containedPlugins = containedPlugins.add(pluginConfig);
    containedPlugins = containedPlugins.concat(getPluginsContained(widgetId, pageConfig));

    const modulePlugins = containedPlugins.filter(config => {
      // remove plugins that are not from same namespace as container, like plugins from a sub-moudule etc.
      // return !!depGraph.hasNode(getPluginSelector(pageKey, config.id, config.namespace));
      return pluginConfig.namespace == config.namespace;
    });
    const modulePluginIds: Immutable.Set<string> = modulePlugins.map(config => config.id);
    const moduleParentContainerId = pluginConfig.layout?.container;
    const modulePluginsMap = modulePlugins.toOrderedMap().mapKeys((_, config) => config.id);
    const eventHandlersMap = yield call(getEventsToForward, modulePluginsMap);

    let selectors = Immutable.Set<Selector>();
    modulePlugins.toArray().forEach(config => {
      selectors = selectors.concat(
        depGraph.getObjectSelectors(getPluginSelector(pageKey, config.id, config.namespace)),
      );
    });

    logger.info(modulePlugins.toArray().map(config => strsel(getPluginSelector(pageKey, config.id, config.namespace))));

    const validSelectors = selectors
      .filter(selector => {
        return !!depGraph.lookupDynamicString(selector);
      })
      .filter(selector => {
        const selectorNamespace = depGraph.lookupNamespace(selector);
        // Filter out selectors which are in child namespace like module output properties and sub-module selectors.
        if (isEqual(pluginConfig.namespace?.getNamespace(), selectorNamespace)) {
          const dynamicString = depGraph.lookupDynamicString(selector);
          let jsBindingCheck = false;
          if (typeof dynamicString === 'string') {
            jsBindingCheck = isJSBinding(dynamicString);
          }
          return jsBindingCheck;
        } else {
          return false;
        }
      });

    const selVariablesMapping = validSelectors
      .toArray()
      .map(selector => {
        return {
          bindingVars: getJSBindingVariables(depGraph.lookupDynamicString(selector)),
          bindingString: depGraph.lookupDynamicString(selector),
          selector,
        };
      })
      .map(selectorBindings => {
        selectorBindings.bindingVars = selectorBindings.bindingVars?.filter(bindingSel => {
          if (
            SHARED_GLOBAL_MODEL_PROPERTIES.includes(bindingSel[0]) ||
            SHARED_GLOBAL_MODEL_LIBRARIES.includes(bindingSel[0]) ||
            modulePluginIds.contains(bindingSel[0])
          ) {
            return false;
          } else {
            return true;
          }
        });
        return selectorBindings;
      });
    let inputSelectorStrings = selVariablesMapping.reduce((acc, selectorBindings) => {
      selectorBindings.bindingVars.forEach(varSelector => {
        for (let i = 1; i <= varSelector.length; i++) {
          let varPathSel = varSelector.slice(0, i);
          let pageVarPathSel = getPluginSelector(pageKey, varSelector[0], pluginConfig.namespace).concat(
            varSelector.slice(1, i),
          );

          if (
            depGraph.hasNode(varPathSel) ||
            depGraph.hasNode(pageVarPathSel) ||
            varPathSel[varPathSel.length - 1] === 'i'
          ) {
            if (i !== varSelector.length) continue;
          } else {
            varPathSel = varSelector.slice(0, i - 1);
            pageVarPathSel = getPluginSelector(pageKey, varSelector[0], pluginConfig.namespace).concat(
              varSelector.slice(1, i - 1),
            );
          }
          const jsString = strsel_index(varPathSel.slice(0));
          if (acc.includes(jsString)) {
            break;
          } else {
            if (jsString) acc.push(jsString);
            break;
          }
        }
      });
      return acc;
    }, []);
    let variablesBySelector = keyBy(selVariablesMapping, selectorBindings => {
      return strsel(selectorBindings.selector);
    });

    logger.info(inputSelectorStrings, variablesBySelector, modulePluginIds.toArray(), moduleParentContainerId);
    yield put(
      setModuleCreationParams({
        pageId,
        pageKey,
        pluginId: widgetId,
        variablesBySelector,
        inputSelectorStrings,
        eventHandlersMap,
        modulePluginIds: modulePluginIds.toArray(),
        moduleParentContainerId,
      }),
    );
    yield put(openModuleCreationDialog());
  } catch (e) {
    logger.error(e);
  }
}

function* getEventsToForward(
  modulePlugins: Immutable.Map<string, PluginConfig>,
): Record<string, Record<string, Array<{index: number; event: EventHandlerConfig}>>> {
  let eventHandlersMap: Record<string, Record<string, Array<{index: number; event: EventHandlerConfig}>>> = {};
  for (const [pluginId, pluginConfig] of modulePlugins) {
    if (pluginConfig?.config && pluginConfig.config.get('events')) {
      const pluginEvents: Immutable.List<EventHandlerConfig> = pluginConfig.config.get('events');
      for (const [eventIndex, eventHandler] of pluginEvents.toKeyedSeq()) {
        // Skip event handlers that operate on plugins within module.
        const isPageEvent = eventHandler.type === 'page';
        if (!isPageEvent && modulePlugins.has(eventHandler.pluginId)) continue;

        if (!eventHandlersMap[pluginId]) eventHandlersMap[pluginId] = {};
        if (!eventHandlersMap[pluginId][eventHandler.label]) eventHandlersMap[pluginId][eventHandler.label] = [];
        eventHandlersMap[pluginId][eventHandler.label].push({
          index: eventIndex,
          event: eventHandler,
        });
      }
    }
  }
  return eventHandlersMap;
}

function* handleSaveModule(action: DispatchAction<string>): SagaIterator {
  try {
    const moduleUUID = action.payload;
    const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);

    const moduleData = RecordSerializer.stringify(moduleRecord);
    const moduleResponse = yield call(TemplatesApi.getTemplate, moduleRecord.moduleUUID);
    logger.info('module Resposnse', moduleResponse);
    if (moduleResponse?.data?.uuid !== moduleRecord.moduleUUID)
      yield call(TemplatesApi.createModuleTemplate, apptile.orgId, moduleRecord);
    yield call(TemplatesApi.saveModuleTemplate, moduleUUID, moduleData);
  } catch (e) {
    logger.error(e);
  }
}

function* handleDeleteModule(action: DispatchAction<string>): SagaIterator {
  try {
    const moduleUUID = action.payload;
    const apptile: apptileState = yield select((state: EditorRootState) => state.apptile);
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
    const modulePlugins: Immutable.List<PluginConfig> = yield select(selectModulePluginsInApp);
    const pluginsUsingModule = modulePlugins.filter(
      pluginConfig => pluginConfig?.config?.get('moduleUUID') === moduleUUID,
    );
    if (pluginsUsingModule.count() > 0) {
      yield put(
        makeToast({
          content: `Tile "${moduleRecord.moduleName}" cannot be removed!.
      It's being referenced by ${pluginsUsingModule.count()} instances.`,
          appearances: 'warning',
        }),
      );
    } else {
      let newAppConfig = appConfig;
      newAppConfig = appConfig.deleteIn(['modules', moduleUUID]);
      yield put({
        type: DispatchActions.UPDATE_APP_CONFIG,
        payload: newAppConfig,
      });
    }
    yield put(makeToast({content: `Tile "${moduleRecord.moduleName}" removed.`, appearances: 'warning'}));
  } catch (e) {
    logger.error(e);
  }
}

function* handleDuplicateModule(action: DispatchAction<string>): SagaIterator {
  try {
    const moduleUUID = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    const oldModuleRecord = appConfig.modules.get(moduleUUID).toJSON();
    oldModuleRecord.moduleUUID = uuidv4();
    oldModuleRecord.moduleName = `${oldModuleRecord.moduleName}-duplicate`;
    const moduleRecord = new ModuleRecord(oldModuleRecord);
    newAppConfig = newAppConfig.setIn(['modules', moduleRecord.moduleUUID], moduleRecord);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put(makeToast({content: `Tile "${moduleRecord.moduleName}" Duplicated.`, appearances: 'info'}));
  } catch (e) {
    logger.error(e);
  }
}

function* handleUpdateModuleDefinition(action: DispatchAction<UpdateModuleDefinitionPayload>): SagaIterator {
  try {
    const {moduleInstanceId, pageId, moduleUUID} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
    const moduleInstanceConfig: PluginConfig = appConfig.getPagePlugins(pageId).get(moduleInstanceId);
    if (!moduleInstanceConfig && !moduleRecord) {
      throw `Could not find Record of tile ${moduleInstanceId}`;
    }
    const parentNamespace = moduleInstanceConfig.get('namespace') || new PluginNamespaceImpl([]);
    const moduleNamespace = new PluginNamespaceImpl(
      parentNamespace.getNamespace().concat([moduleInstanceConfig.config.get('childNamespace')]),
      moduleInstanceId,
    );
    const pageConfig: PageConfig = appConfig.getPage(pageId);
    const modulePlugins = pageConfig.plugins.filter((pluginConfig, pluginId) => {
      return _.isEqual(moduleNamespace.getNamespace(), pluginConfig.namespace?.getNamespace());
    });

    let newModuleRecord = moduleRecord;
    newModuleRecord = newModuleRecord.set('moduleConfig', newModuleRecord.moduleConfig.clear());
    logger.info('MODULE: PRE-UPDATE - ', moduleRecord);
    newModuleRecord = newModuleRecord.set(
      'defaultEventHandlers',
      moduleInstanceConfig.getIn(['config', 'events']).map((e: EventHandlerConfig, i: number) => {
        return new ModuleEventHandlerConfig({
          eventHandler: e,
          isExposed: moduleRecord.defaultEventHandlers?.get(i)?.get('isExposed') || false,
          name: moduleRecord.defaultEventHandlers?.get(i)?.get('name') || '',
          type: moduleRecord.defaultEventHandlers?.get(i)?.get('type') || 'both',
        });
      }),
    );
    const pluginIds: string[] = [];
    modulePlugins.forEach((pluginConfig, namespacedPluginId) => {
      const pluginId = pluginConfig.namespace?.getPluginId();
      const nsContainerId = pluginConfig.layout?.container;
      const nsContainerConfig = modulePlugins.get(nsContainerId);
      const containerId = nsContainerConfig ? nsContainerConfig.namespace?.getPluginId() : '';
      let modulePluginConfig: PluginConfig = moduleRecord.moduleConfig.get(pluginId);
      if (!modulePluginConfig) {
        modulePluginConfig = pluginConfig
          .set('id', pluginId)
          .set('namespace', undefined)
          .setIn(['layout', 'container'], containerId);
      } else {
        modulePluginConfig = modulePluginConfig
          .set('layout', pluginConfig.layout.set('container', containerId))
          .set('config', pluginConfig.config)
          .set('analytics', pluginConfig.analytics)
          .set('animations', pluginConfig.animations);
      }
      newModuleRecord = newModuleRecord.setIn(['moduleConfig', pluginId], modulePluginConfig);
      pluginIds.push(pluginId as string);
    });
    newModuleRecord = newModuleRecord.set(
      'editors',
      newModuleRecord
        .get('editors')
        .filter(e => pluginIds.includes(e?.selector[0]) || e?.editorType?.type == 'editorSectionHeader'),
    );
    newModuleRecord = newModuleRecord.set(
      'styleEditors',
      newModuleRecord
        .get('styleEditors')
        .filter(e => pluginIds.includes(e?.selector[0]) || e?.editorType?.type == 'editorSectionHeader'),
    );
    newModuleRecord = newModuleRecord.set(
      'basicEditors',
      newModuleRecord
        .get('basicEditors')
        .filter(e => pluginIds.includes(e?.selector[0]) || e?.editorType?.type == 'editorSectionHeader'),
    );
    logger.info('MODULE: POST-UPDATE - ', newModuleRecord);
    let newAppConfig = appConfig.setIn(['modules', moduleUUID], newModuleRecord);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put(makeToast({content: `Tile "${moduleRecord.moduleName}"  definition updated.`, appearances: 'success'}));
  } catch (e) {
    logger.error(e);
    yield put(makeToast({content: `Tile definition update failed!`, appearances: 'error'}));
  }
}

function* handleSaveModuleVariant(action: DispatchAction<SaveModuleVariantPayload>): SagaIterator {
  try {
    const {moduleInstanceId, pageId, moduleUUID} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);
    const moduleInstanceConfig: PluginConfig = appConfig.getPagePlugins(pageId).get(moduleInstanceId);
    if (!moduleInstanceConfig && !moduleRecord) {
      throw `Could not find Record of tile ${moduleInstanceId}`;
    }
    const parentNamespace = moduleInstanceConfig.get('namespace') || new PluginNamespaceImpl([]);
    const moduleNamespace = new PluginNamespaceImpl(
      parentNamespace.getNamespace().concat([moduleInstanceConfig.config.get('childNamespace')]),
      moduleInstanceId,
    );
    const pageConfig: PageConfig = appConfig.getPage(pageId);
    const modulePlugins = pageConfig.plugins.filter((pluginConfig, pluginId) => {
      return _.isEqual(moduleNamespace.getNamespace(), pluginConfig.namespace?.getNamespace());
    });

    let newModuleRecord = moduleRecord;
    newModuleRecord = newModuleRecord.set('moduleConfig', newModuleRecord.moduleConfig.clear());
    logger.info('MODULE: PRE-UPDATE - ', moduleRecord);
    newModuleRecord = newModuleRecord.set(
      'defaultEventHandlers',
      moduleInstanceConfig.getIn(['config', 'events']).map((e: EventHandlerConfig, i: number) => {
        return new ModuleEventHandlerConfig({
          eventHandler: e,
          isExposed: moduleRecord.defaultEventHandlers?.get(i)?.get('isExposed') || false,
          name: moduleRecord.defaultEventHandlers?.get(i)?.get('name') || '',
          type: moduleRecord.defaultEventHandlers?.get(i)?.get('type') || 'both',
        });
      }),
    );
    const pluginIds: string[] = [];
    modulePlugins.forEach((pluginConfig, namespacedPluginId) => {
      const pluginId = pluginConfig.namespace?.getPluginId();
      const nsContainerId = pluginConfig.layout?.container;
      const nsContainerConfig = modulePlugins.get(nsContainerId);
      const containerId = nsContainerConfig ? nsContainerConfig.namespace?.getPluginId() : '';
      let modulePluginConfig: PluginConfig = moduleRecord.moduleConfig.get(pluginId);
      if (!modulePluginConfig) {
        modulePluginConfig = pluginConfig
          .set('id', pluginId)
          .set('namespace', undefined)
          .setIn(['layout', 'container'], containerId);
      } else {
        modulePluginConfig = modulePluginConfig
          .set('layout', pluginConfig.layout.set('container', containerId))
          .set('config', pluginConfig.config)
          .set('analytics', pluginConfig.analytics)
          .set('animations', pluginConfig.animations);
      }
      newModuleRecord = newModuleRecord.setIn(['moduleConfig', pluginId], modulePluginConfig);
      pluginIds.push(pluginId as string);
    });
    newModuleRecord = newModuleRecord.set(
      'editors',
      newModuleRecord
        .get('editors')
        .filter(e => pluginIds.includes(e?.selector[0]) || e?.editorType?.type == 'editorSectionHeader'),
    );
    newModuleRecord = newModuleRecord.set(
      'styleEditors',
      newModuleRecord
        .get('styleEditors')
        .filter(e => pluginIds.includes(e?.selector[0]) || e?.editorType?.type == 'editorSectionHeader'),
    );
    newModuleRecord = newModuleRecord.set(
      'basicEditors',
      newModuleRecord
        .get('basicEditors')
        .filter(e => pluginIds.includes(e?.selector[0]) || e?.editorType?.type == 'editorSectionHeader'),
    );
    logger.info('MODULE: POST-UPDATE - ', newModuleRecord);
    const moduleData = RecordSerializer.stringify(newModuleRecord);
    const variantDetails = {
      name: 'My Style',
      coverImage:
        'https://cdn-demo.apptile.io/b55a2d75-47b5-468e-8de3-fe9700fa303e/6e9886ad-651b-42e4-ae68-8c0b41ac1af9/original.png',
      tileId: moduleUUID,
    };
    try {
      const moduleResponse = yield call(TilesApi.createOrgTileVariantTemplate, {...variantDetails, data: moduleData});
      yield put(
        makeToast({
          content: `Your style is saved for this tile.`,
          appearances: 'success',
        }),
      );
    } catch (e) {
      yield put(
        makeToast({
          content: `Error exporting Variant "${variantDetails.name}" for "${moduleRecord.moduleName}" !`,
          appearances: 'error',
        }),
      );
    }
  } catch (e) {
    logger.error(e);
    yield put(makeToast({content: `Tile definition update failed!`, appearances: 'error'}));
  }
}

function* handleForwardModulePluginProperty(action: DispatchAction<ForwardModulePluginPropertyPayload>): SagaIterator {
  try {
    const {pageId, pluginId, isForwarded, selector, name, location} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    const parentModuleConfig = yield select(selectParentModulePlugin, pageId, pluginId);
    if (!parentModuleConfig) throw 'Cannot Find Parent Module';
    const moduleUUID = parentModuleConfig.config.get('moduleUUID');
    let moduleRecord: ModuleRecord = new ModuleRecord(appConfig.modules.get(moduleUUID));

    const editorType = location === 'basic' ? 'basicEditors' : location === 'style' ? 'styleEditors' : 'editors';
    if (isForwarded)
      moduleRecord = moduleRecord.setIn(
        [editorType, strsel(selector)],
        new ModuleEditorRecord({
          label: name,
          selector,
        }),
      );
    else moduleRecord = moduleRecord.deleteIn([editorType, strsel(selector)]);

    let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
  } catch (e) {
    logger.error(e);
  }
}

function* handleAddModulePluginPropertyEditor(action: DispatchAction<AddModulePluginPropertyPayload>): SagaIterator {
  try {
    const {moduleUUID, editorRecord, location} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    let moduleRecord: ModuleRecord = new ModuleRecord(appConfig.modules.get(moduleUUID));
    if (!moduleRecord) throw 'Cannot Find Parent Module';
    const editorType = location === 'basic' ? 'basicEditors' : location === 'style' ? 'styleEditors' : 'editors';

    moduleRecord = moduleRecord.setIn([editorType, strsel(editorRecord.selector)], editorRecord);

    let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
  } catch (e) {
    logger.error(e);
  }
}

function* handleDeleteModulePluginPropertyEditor(
  action: DispatchAction<DeleteModulePluginPropertyPayload>,
): SagaIterator {
  try {
    const {moduleUUID, editorSelector, location} = action.payload;
    const appConfig: AppConfig = yield select(state => state.appConfig.current);
    let moduleRecord: ModuleRecord = new ModuleRecord(appConfig.modules.get(moduleUUID));
    if (!moduleRecord) throw 'Cannot Find Parent Module';
    const editorType = location === 'basic' ? 'basicEditors' : location === 'style' ? 'styleEditors' : 'editors';

    moduleRecord = moduleRecord.deleteIn([editorType, strsel(editorSelector)]);

    let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
  } catch (e) {
    logger.error(e);
  }
}

function* handleUpdateModulePluginProperty(action: DispatchAction<UpdateModulePluginPropertyPayload>): SagaIterator {
  const {moduleUUID, editorSelKey, location, update} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  const parentModuleConfig = yield select(selectModuleByUUID, moduleUUID);
  if (!parentModuleConfig) throw 'Cannot Find Parent Module';
  let moduleRecord: ModuleRecord = new ModuleRecord(appConfig.modules.get(moduleUUID));
  const editorType = location === 'basic' ? 'basicEditors' : location === 'style' ? 'styleEditors' : 'editors';

  moduleRecord = moduleRecord?.mergeIn([editorType, editorSelKey], update);
  let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}

function* handleUpdateModuleProperty(action: DispatchAction<UpdateModulePropertyPayload>): SagaIterator {
  const {moduleUUID, property, value} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  const parentModuleConfig = yield select(selectModuleByUUID, moduleUUID);
  if (!parentModuleConfig) throw 'Cannot Find Parent Module';
  let moduleRecord: ModuleRecord = appConfig.modules.get(moduleUUID);

  moduleRecord = moduleRecord?.setIn([property], value);
  let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord);
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}

function* handleReorderModuleProperties(action: DispatchAction<ReorderModulePluginPropertiesPayload>): SagaIterator {
  const {moduleUUID, location, keys} = action.payload;
  const appConfig: AppConfig = yield select(state => state.appConfig.current);
  let moduleRecord: ModuleRecord = new ModuleRecord(appConfig.modules.get(moduleUUID));
  if (!moduleRecord) throw 'Cannot Find Parent Module';
  const editorType = location === 'basic' ? 'basicEditors' : location === 'style' ? 'styleEditors' : 'editors';

  let editors = moduleRecord.get(editorType);
  let newEditors = Immutable.OrderedMap<string, ModuleEditorRecord>();
  keys?.map(key => {
    newEditors = newEditors.set(key, editors.get(key));
    editors = editors.remove(key);
  });
  newEditors = newEditors.merge(editors);
  let newAppConfig = appConfig.setIn(['modules', moduleUUID], moduleRecord.set(editorType, newEditors));
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
}

export function batchCreateModulePluginsImpl(
  appConfig: AppConfig,
  pageId: string,
  moduleId: string,
  plugins: Immutable.OrderedMap<string, PluginConfig>,
): AppConfig {
  const pageConfig = appConfig.pages.get(pageId);

  const pluginsToAdd = plugins.filter(plugin => !pageConfig?.plugins.has(plugin.id));
  if (!pluginsToAdd?.count()) return appConfig;

  let pagePlugins = pageConfig?.plugins.takeUntil((_, pluginId) => pluginId == moduleId);
  pagePlugins = pagePlugins?.set(moduleId, pageConfig?.plugins.get(moduleId));
  pagePlugins = pagePlugins?.concat(pluginsToAdd);
  pagePlugins = pagePlugins?.concat(pageConfig?.plugins.skipUntil((c, pluginId) => pluginId == moduleId).skip(1));

  appConfig = appConfig.setIn(['pages', pageId, 'plugins'], pagePlugins);
  return appConfig;
}

export function* handleBatchCreateModulePlugins(action: DispatchAction<BatchCreateModulePluginsAction>) {
  try {
    const {pageId, moduleId, plugins} = action.payload;
    let appConfig: AppConfig = yield select(selectAppConfig);
    appConfig = batchCreateModulePluginsImpl(appConfig, pageId, moduleId, plugins);
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: appConfig,
    });
  } catch (e) {
    logger.error(e);
  }
}

export function* handleRenameModuleProperty(action: DispatchAction<RenameModuleProperty>): SagaIterator {
  try {
    const {moduleUUID, propertyType, oldName, newName} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    const configKey = propertyType === 'query' ? 'queries' : 'inputs';
    const keysChanged = {};
    moduleRecord = moduleRecord.set(configKey, _.without(moduleRecord.get(configKey), oldName).concat([newName]));
    moduleRecord = moduleRecord.set(
      'moduleConfig',
      moduleRecord.moduleConfig.mapEntries(([id, pluginConfig]) => {
        pluginConfig = pluginConfig.set(
          'config',
          pluginConfig.config.map((jsString: any, key: any) => {
            const {newValue, hasNewValue} = updateProperty(jsString, oldName, newName);
            if (hasNewValue) {
              keysChanged[jsString] = newValue;
              return newValue;
            }
            return jsString;
          }),
        );
        if (id === oldName) {
          return [newName, pluginConfig.set('id', newName)];
        } else {
          return [id, pluginConfig];
        }
      }),
    );
    console.log(moduleRecord);
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    // Rename all ModuleInstance plugin config variables to the new name.
    newAppConfig.pages.forEach((pageConfig, pageId) => {
      const childNamespaces = [];
      pageConfig.plugins.forEach((pluginConfig, pluginId) => {
        if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
          childNamespaces.push(pluginConfig.config.get('childNamespace'));
          newAppConfig = newAppConfig.setIn(
            ['pages', pageId, 'plugins', pluginId, 'config'],
            newAppConfig
              .getIn(['pages', pageId, 'plugins', pluginId, 'config'])
              ?.mapKeys(k => (k === oldName ? newName : k)),
          );
        }
        if (childNamespaces.length > 0) {
          if (
            pluginConfig?.namespace?.namespace?.[0] &&
            childNamespaces.includes(pluginConfig?.namespace?.namespace?.[0])
          ) {
            newAppConfig = newAppConfig.setIn(
              ['pages', pageId, 'plugins', pluginId, 'config'],
              newAppConfig.getIn(['pages', pageId, 'plugins', pluginId, 'config'])?.map((jsString: string) => {
                if (typeof jsString === 'string') return keysChanged[jsString] ? keysChanged[jsString] : jsString;
                else return jsString;
              }),
            );
          }
        }
      });
    });
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleRenameModuleOutput(action: DispatchAction<RenameModuleOutput>): SagaIterator {
  try {
    const {moduleUUID, oldName, newName} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    moduleRecord = moduleRecord.set('outputs', _.without(moduleRecord.get('outputs'), oldName).concat([newName]));
    moduleRecord = moduleRecord.set(
      'moduleConfig',
      moduleRecord.moduleConfig.mapEntries(([id, pluginConfig]) => {
        pluginConfig = pluginConfig.set(
          'config',
          pluginConfig.config.map((jsString: any, key: any) => {
            const {newValue, hasNewValue} = updateProperty(jsString, oldName, newName);
            if (hasNewValue) {
              return newValue;
            }
            return jsString;
          }),
        );
        if (id === oldName) {
          return [newName, pluginConfig.set('id', newName)];
        } else {
          return [id, pluginConfig];
        }
      }),
    );
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleRenameModuleEvent(action: DispatchAction<RenameModuleEvent>): SagaIterator {
  try {
    const {moduleUUID, oldName, newName} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    moduleRecord = moduleRecord.set('events', _.without(moduleRecord.get('events'), oldName).concat([newName]));
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    newAppConfig.pages.forEach((pageConfig, pageId) => {
      pageConfig.plugins.forEach((pluginConfig, pluginId) => {
        if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
          newAppConfig = newAppConfig.setIn(
            ['pages', pageId, 'plugins', pluginId, 'config', 'events'],
            newAppConfig
              .getIn(['pages', pageId, 'plugins', pluginId, 'config', 'events'])
              ?.map((event: EventHandlerConfig) => {
                if (event.label === oldName) return event.set('label', newName);
                return event;
              }),
          );
        }
      });
    });

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleUpdateModuleOutput(action: DispatchAction<UpdateModuleOutput>): SagaIterator {
  try {
    const {moduleUUID, propertyName, value} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    moduleRecord = moduleRecord.set(
      'moduleConfig',
      moduleRecord.moduleConfig.mapEntries(([id, pluginConfig]) => {
        if (id === propertyName) {
          return [id, pluginConfig.setIn(['config', 'value'], value)];
        } else {
          return [id, pluginConfig];
        }
      }),
    );
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleCreateModuleProperty(action: DispatchAction<CreateModuleProperty>): SagaIterator {
  try {
    const {moduleUUID, propertyType, propertyName, value} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    const configKey = propertyType === 'query' ? 'queries' : 'inputs';
    moduleRecord = moduleRecord.set(configKey, moduleRecord.get(configKey).concat([propertyName]));
    const modulePropertyConfig = new PluginConfig({
      id: propertyName,
      type: 'state',
      subtype: 'ModuleProperty',
      config: Immutable.Map({
        value: value,
      }),
      layout: new LayoutRecord({container: ''}),
    });
    moduleRecord = moduleRecord.set('moduleConfig', moduleRecord.moduleConfig.set(propertyName, modulePropertyConfig));
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    newAppConfig.pages.forEach((pageConfig, pageId) => {
      pageConfig.plugins.forEach((pluginConfig, pluginId) => {
        if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
          newAppConfig = newAppConfig.setIn(
            ['pages', pageId, 'plugins', pluginId, 'config'],
            newAppConfig.getIn(['pages', pageId, 'plugins', pluginId, 'config'])?.set(propertyName, value),
          );
        }
      });
    });

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
    yield put(closeModuleCreationDialog());
  } catch (e) {
    logger.error(e);
  }
}

export function* handleCreateModuleOutput(action: DispatchAction<CreateModuleOutput>): SagaIterator {
  try {
    const {moduleUUID, propertyName, value} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    moduleRecord = moduleRecord.set('outputs', moduleRecord.get('outputs').concat([propertyName]));
    const moduleOutputConfig = new PluginConfig({
      id: propertyName,
      type: 'state',
      subtype: 'ModuleOutput',
      config: Immutable.Map({
        value: value,
      }),
      layout: new LayoutRecord({container: ''}),
    });
    moduleRecord = moduleRecord.set('moduleConfig', moduleRecord.moduleConfig.set(propertyName, moduleOutputConfig));
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    newAppConfig.pages.forEach((pageConfig, pageId) => {
      pageConfig.plugins.forEach((pluginConfig, pluginId) => {
        if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
          newAppConfig = newAppConfig.setIn(
            ['pages', pageId, 'plugins', pluginId, 'config'],
            newAppConfig.getIn(['pages', pageId, 'plugins', pluginId, 'config'])?.set(propertyName, value),
          );
        }
      });
    });

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleCreateModuleEvent(action: DispatchAction<CreateModuleEvent>): SagaIterator {
  try {
    const {moduleUUID, eventName, value} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    moduleRecord = moduleRecord.set('events', moduleRecord.get('events').concat([eventName]));
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleDeleteModuleProperty(action: DispatchAction<DeleteModuleProperty>): SagaIterator {
  try {
    const {moduleUUID, propertyType, propertyName} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    const configKey = propertyType === 'query' ? 'queries' : 'inputs';
    moduleRecord = moduleRecord.set(configKey, _.without(moduleRecord.get(configKey), propertyName));
    moduleRecord = moduleRecord.set('moduleConfig', moduleRecord.moduleConfig.delete(propertyName));
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    newAppConfig.pages.forEach((pageConfig, pageId) => {
      pageConfig.plugins.forEach((pluginConfig, pluginId) => {
        if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
          newAppConfig = newAppConfig.setIn(
            ['pages', pageId, 'plugins', pluginId, 'config'],
            newAppConfig.getIn(['pages', pageId, 'plugins', pluginId, 'config'])?.delete(propertyName),
          );
        }
      });
    });

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleDeleteModuleOutput(action: DispatchAction<DeleteModuleOutput>): SagaIterator {
  try {
    const {moduleUUID, propertyName} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    moduleRecord = moduleRecord.set('outputs', _.without(moduleRecord.get('outputs'), propertyName));
    moduleRecord = moduleRecord.set('moduleConfig', moduleRecord.moduleConfig.delete(propertyName));
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    newAppConfig.pages.forEach((pageConfig, pageId) => {
      pageConfig.plugins.forEach((pluginConfig, pluginId) => {
        if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
          newAppConfig = newAppConfig.setIn(
            ['pages', pageId, 'plugins', pluginId, 'config'],
            newAppConfig.getIn(['pages', pageId, 'plugins', pluginId, 'config'])?.delete(propertyName),
          );
        }
      });
    });

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handleDeleteModuleEvent(action: DispatchAction<DeleteModuleEvent>): SagaIterator {
  try {
    const {moduleUUID, eventName} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;

    moduleRecord = moduleRecord.set('events', _.without(moduleRecord.get('events'), eventName));
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    newAppConfig.pages.forEach((pageConfig, pageId) => {
      pageConfig.plugins.forEach((pluginConfig, pluginId) => {
        if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
          newAppConfig = newAppConfig.setIn(
            ['pages', pageId, 'plugins', pluginId, 'config', 'events'],
            newAppConfig
              .getIn(['pages', pageId, 'plugins', pluginId, 'config', 'events'])
              ?.map((event: EventHandlerConfig) => {
                if (event.label === eventName) return event.set('label', undefined);
                return event;
              }),
          );
        }
      });
    });

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* handlePersistModuleInputs(action: DispatchAction<PersistModuleInputs>): SagaIterator {
  try {
    const {moduleUUID, pageId, moduleInstanceId, bSave} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    let newAppConfig = appConfig;
    let moduleRecord = appConfig.modules.get(moduleUUID);
    if (!moduleRecord) throw `ModuleRecord for ${moduleUUID} not found.`;
    const pageConfig = appConfig.getPage(pageId);
    const moduleInstanceConfig = pageConfig?.getPluginId(moduleInstanceId);

    moduleRecord = moduleRecord.set('persistInputBindings', bSave);
    if (bSave) {
      let bindings: Immutable.Map<string, string> = moduleRecord.get('inputBindings');
      moduleRecord.inputs?.forEach(inputName => {
        bindings = bindings.set(inputName, moduleInstanceConfig.config.get(inputName, ''));
      });
      moduleRecord = moduleRecord.set('inputBindings', bindings);
    } else {
      moduleRecord = moduleRecord.set('inputBindings', Immutable.Map());
    }
    newAppConfig = newAppConfig.setIn(['modules', moduleUUID], moduleRecord);

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    // yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function deleteModulePlugins(
  appConfig: AppConfig,
  appModel: AppModelType,
  pageId: string,
  pluginConfig: PluginConfig,
) {
  const moduleId = pluginConfig.id;
  const parentNamespace = pluginConfig.get('namespace') || new PluginNamespaceImpl([]);
  const moduleNamespace = new PluginNamespaceImpl(
    parentNamespace.getNamespace().concat([pluginConfig.config.get('childNamespace')]),
    moduleId,
  );
  const pageConfig = appConfig.pages.get(pageId);

  let newAppConfig = appConfig;
  let newAppModel = appModel;

  pageConfig?.plugins.forEach((pluginConfig, pluginId) => {
    const pageKeys = appModel.getPageKeysForId(pageId);
    if (isEqual(pluginConfig.namespace?.getNamespace(), moduleNamespace.getNamespace())) {
      newAppConfig = newAppConfig.deleteIn(['pages', pageId, 'plugins', pluginId]);
      pageKeys.forEach(pageKey => {
        newAppModel = newAppModel.deleteModelValue([pageKey, 'plugins', pluginId]);
        newAppModel.dependencyGraph.deletePlugin(pageKey, pluginId);
      });
    }
  });

  return {newAppConfig, newAppModel};
}

export function* recreateModulePlugins(moduleUUID: string) {
  const appConfig: AppConfig = yield select(selectAppConfig);
  const appModel: AppModelType = yield select(selectStageModel);

  let newAppConfig = appConfig;
  let newAppModel = appModel;

  // appConfig.pages.forEach((pageConfig, pageId) => {
  //   pageConfig.plugins.forEach((pluginConfig, pluginId) => {
  //     if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
  //       ({newAppConfig, newAppModel} = deleteModulePlugins(newAppConfig, newAppModel, pageId, pluginConfig));
  //     }
  //   });
  // });

  let affectedPlugins: Set<PluginIdTypePage> = new Set();
  for (const [pageId, pageConfig] of newAppConfig.pages) {
    const pageKeys = appModel.getPageKeysForId(pageId);
    let rerenderValues = false;
    for (let [pluginId, pluginConfig] of pageConfig.plugins) {
      if (pluginConfig.subtype === 'ModuleInstance' && pluginConfig.config.get('moduleUUID') === moduleUUID) {
        const moduleId = pluginConfig.id;
        const modulesCache = appConfig.get('modules');
        const moduleConfig = modulesCache.get(pluginConfig.config.get('moduleUUID'));
        const parentNamespace = pluginConfig.get('namespace') || new PluginNamespaceImpl([]);
        const moduleNamespace = new PluginNamespaceImpl(
          parentNamespace.getNamespace().concat([pluginConfig.config.get('childNamespace')]),
          moduleId,
        );
        let addedPlugins: Immutable.OrderedMap<string, PluginConfig>;
        ({appConfig: newAppConfig, addedPlugins} = initModulePlugins(pageId, pluginConfig, newAppConfig));
        for (let [newPluginId, newPluginConfig] of addedPlugins) {
          affectedPlugins.add({
            id: newPluginId,
            pluginType: newPluginConfig.subtype,
            pageKey: pageId,
          });
        }
        newAppModel = yield call(
          batchInitModelForPlugins,
          newAppConfig,
          newAppModel,
          Immutable.OrderedMap<string, PluginConfig>().set(pluginId, pluginConfig).merge(addedPlugins),
          pageId,
          pluginConfig.layout?.container,
        );
        rerenderValues = true;
      }
    }
    if (rerenderValues) {
      const scratchMemory: JSModel = {
        $global: {},
        unresolved: true,
        $context: {},
        hasCurrentPage: false,
        currentPage: {},
        hasIndex: false,
        i: 0,
      };
      let affectedPluginInstances: Set<PluginIdTypePage> = new Set();
      for (const pageKey of pageKeys) {
        for (let plugin of affectedPlugins) {
          affectedPluginInstances.add({...plugin, pageKey: pageKey});
        }
      }
      const renderOrder = newAppModel.dependencyGraph
        .topologicalSort()
        .filter((selector: Selector) => selector[0] === pageId);
      // .filter((selector: Selector) => newAppModel.dependencyGraph.lookupDynamicString(selector));
      for (const pageKey of pageKeys) {
        for (const selector of renderOrder) {
          const binding = newAppModel.dependencyGraph.lookupBinding(selector);
          const modelSelector = [pageKey].concat(selector.slice(1));
          newAppModel = evaluateJSBindingString(
            selector,
            modelSelector,
            binding.dynamicString,
            newAppModel.dependencyGraph,
            newAppModel,
            scratchMemory,
            undefined,
            newAppModel.dependencyGraph.lookupNamespace(selector),
            binding.binding.compiled,
          );
        }
      }
    }
    affectedPlugins.clear();
  }
  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
  yield put({
    type: DispatchActions.UPDATE_STAGE_MODEL,
    payload: {
      appModel: newAppModel,
      desc: 'recreateModulePlugins',
      meta: {moduleUUID},
    },
  });
  yield put(commitStageModel());
  for (let plugin of affectedPlugins) {
    const onPluginUpdate = GetRegisteredOnPluginUpdate(plugin.pluginType);
    if (onPluginUpdate) {
      yield put(triggerOnPluginUpdate(plugin, null, false, true));
    }
  }
}

export function* createModulePlugin(pluginId: string, pageId: string, moduleUUID: string) {
  const appConfig: AppConfig = yield select(selectAppConfig);
  const appModel: AppModelType = yield select(selectStageModel);

  let newAppConfig = appConfig;
  let newAppModel = appModel;

  let affectedPlugins: Set<PluginIdTypePage> = new Set();
  const pageKeys = appModel.getPageKeysForId(pageId);
  const pageConfig = newAppConfig.pages.get(pageId);
  const pluginConfig: PluginConfig = pageConfig.plugins.get(pluginId);
  let addedPlugins: Immutable.OrderedMap<string, PluginConfig>;
  ({appConfig: newAppConfig, addedPlugins} = initModulePlugins(pageId, pluginConfig, newAppConfig));
  for (let [newPluginId, newPluginConfig] of addedPlugins) {
    affectedPlugins.add({
      id: newPluginId,
      pluginType: newPluginConfig.subtype,
      pageKey: pageId,
    });
  }
  newAppModel = yield call(
    batchInitModelForPlugins,
    newAppConfig,
    newAppModel,
    Immutable.OrderedMap<string, PluginConfig>().set(pluginId, pluginConfig).merge(addedPlugins),
    pageId,
    pluginConfig.layout?.container,
  );
  const affectedPluginIds = addedPlugins
    .keySeq()
    .map(pluginId => pluginId)
    .toArray();
  const scratchMemory: JSModel = {
    $global: {},
    unresolved: true,
    $context: {},
    hasCurrentPage: false,
    currentPage: {},
    hasIndex: false,
    i: 0,
  };
  const contextId = appModel.evalContextStack.createEvaluationContext();
  let evalContext = appModel.evalContextStack.getEvaluationContext(contextId);

  const renderOrder = newAppModel.dependencyGraph
    .topologicalSort()
    .filter((selector: Selector) => selector[0] === pageId && affectedPluginIds.includes(selector[2]));
  for (const pageKey of pageKeys) {
    for (const selector of renderOrder) {
      const binding = newAppModel.dependencyGraph.lookupBinding(selector);
      const modelSelector = [pageKey].concat(selector.slice(1));
      newAppModel = evaluateJSBindingString(
        selector,
        modelSelector,
        binding.dynamicString,
        newAppModel.dependencyGraph,
        newAppModel,
        scratchMemory,
        contextId,
        newAppModel.dependencyGraph.lookupNamespace(selector),
        binding.binding.compiled,
      );
    }
  }
  const evalResults = evalContext.getResults();
  newAppModel = newAppModel.setAllValues(evalResults);

  yield put({
    type: DispatchActions.UPDATE_APP_CONFIG,
    payload: newAppConfig,
  });
  yield put({
    type: DispatchActions.UPDATE_STAGE_MODEL,
    payload: {
      appModel: newAppModel,
      desc: 'createModulePlugin',
      meta: {moduleUUID},
    },
  });
  yield put(commitStageModel());
  let affectedPluginInstances: Set<PluginIdTypePage> = new Set();
  for (const pageKey of pageKeys) {
    for (let plugin of affectedPlugins) {
      affectedPluginInstances.add({...plugin, pageKey: pageKey});
    }
  }
  for (let plugin of affectedPluginInstances) {
    const onPluginUpdate = GetRegisteredOnPluginUpdate(plugin.pluginType);
    if (onPluginUpdate) {
      yield put(triggerOnPluginUpdate(plugin, null, false, true));
    }
  }
  if (pageKeys.get(0)) yield put(selectPlugin([pageKeys.get(0), 'plugins', pluginId]));
}

export function generateNewModuleId(pageConfig: PageConfig) {
  const pluginIds = pageConfig.plugins.keySeq().toArray();
  const namespaces = pageConfig.plugins
    .filter(config => config.subtype === 'ModuleInstance')
    .valueSeq()
    .map(config => config.config.get('childNamespace'))
    .toArray();

  let i = 1;
  let uniquePluginId = `tile${i}`;
  while (true) {
    if (!pluginIds.includes(uniquePluginId) && !namespaces.includes(uniquePluginId)) break;
    i++;
    uniquePluginId = `tile${i}`;
  }
  return uniquePluginId;
}

export function* handleCreateModule(action: DispatchAction<CreateModule>): SagaIterator {
  try {
    const {
      pluginId,
      pageId,
      pageKey,
      variablesBySelector,
      inputs,
      events,
      modulePluginIds,
      moduleParentContainerId,
      moduleName,
    } = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    const appModel: AppModelType = yield select(selectStageModel);
    const pageConfig: PageConfig = appConfig.pages.get(pageId);
    const pluginConfig = pageConfig.plugins.get(pluginId);

    let newAppConfig = appConfig;

    // Setup the new Module record
    let moduleRecord = new ModuleRecord({moduleName, moduleUUID: uuidv4()});
    // Setup Pluginconfigs off module
    let moduleConfigMap = pageConfig.plugins.filter((config, pluginId) => modulePluginIds.includes(pluginId));
    moduleConfigMap = moduleConfigMap.set(pluginId, pluginConfig.setIn(['layout', 'container'], ''));
    // Setup new bindings
    // TODO: Copied from CreateModule Dialog -- refactor to single function
    function getUpdatedBindingString(oldString: string) {
      let outString = oldString;
      Object.entries(inputs).forEach(([inputName, varSelector]) => {
        outString = UNSAFE_renameVariables(outString, varSelector, `${inputName}.value`);
      });
      return outString;
    }
    Object.entries(variablesBySelector).forEach(([propSelector, bindings]) => {
      let newBindingString = getUpdatedBindingString(bindings.bindingString);
      const pluginSel = getPluginModelSelectorFromSelector(objsel(propSelector));
      if (pluginSel && pluginSel[pluginSel?.length - 1] === 'hidden') {
        moduleConfigMap = moduleConfigMap.setIn([pluginSel[0], 'layout', 'hidden'], newBindingString);
      } else {
        moduleConfigMap = moduleConfigMap.setIn([pluginSel[0], 'config'].concat(pluginSel?.slice(1)), newBindingString);
      }
    });

    // Fill in Forward event handlers and promote indiviual handlers to module.
    let eventTriggers = new Set<string>();
    let moduleEventHandlers = Immutable.List<ModuleEventHandlerConfig>();

    Object.entries(events).forEach(([eventName, eventDetails]) => {
      eventTriggers.add(eventName);
      let modulePluginConfig: PluginConfigType = moduleConfigMap.get(eventDetails.pluginId);
      let eventsList: Immutable.List<EventHandlerConfig> = modulePluginConfig.getIn(['config', 'events']);
      let forwardedEventParams = Immutable.Map();

      eventDetails.handlers.forEach(handlerDetails => {
        let eventHandler = handlerDetails.event;

        forwardedEventParams = forwardedEventParams.merge(eventHandler.params);

        eventsList = eventsList.remove(
          eventsList.findIndex((sourceEvent, index) => {
            return sourceEvent.equals(eventHandler);
          }),
        );
        let paramsForwarded = eventHandler.params.map((val: any, eventParamName: string) => {
          return `${EVENT_PARAM_IDENTIFIER}.${eventParamName}`;
        });
        let conditionForwarded = `${EVENT_PARAM_IDENTIFIER}.condition`;
        moduleEventHandlers = moduleEventHandlers.push(
          new ModuleEventHandlerConfig({
            eventHandler: eventHandler
              .set('label', eventName)
              .set('params', paramsForwarded)
              .set('condition', conditionForwarded),
          }),
        );
      });

      eventsList = eventsList.push(
        new EventHandlerConfig({
          params: forwardedEventParams,
          condition: eventDetails.handlers[0].event.condition,
          hasCondition: eventDetails.handlers[0].event.hasCondition,
          label: eventDetails.eventLabel,
          type: 'action',
          value: eventName,
          method: 'forwardModuleEvent',
        }),
      );

      modulePluginConfig = modulePluginConfig.setIn(['config', 'events'], eventsList);
      moduleConfigMap = moduleConfigMap.set(eventDetails.pluginId, modulePluginConfig);
    });
    moduleRecord = moduleRecord.set('events', Array.from(eventTriggers));
    moduleRecord = moduleRecord.set('moduleConfig', moduleConfigMap);
    moduleRecord = moduleRecord.set('defaultEventHandlers', moduleEventHandlers);
    //Setup Inputs
    let instanceVars = {};
    Object.entries(inputs).forEach(([key, value]) => {
      moduleRecord = moduleRecord.set('inputs', moduleRecord.inputs.concat([key]));
      let inputPluginConfig = new PluginConfig({
        id: key,
        type: 'state',
        subtype: 'ModuleProperty',
        config: Immutable.Map({value: ''}),
      });
      moduleRecord = moduleRecord.setIn(['moduleConfig', key], inputPluginConfig);

      instanceVars[key] = `{{${value}}}`;
    });
    // logger.info(moduleRecord.toJS());
    newAppConfig = newAppConfig.setIn(['modules', moduleRecord.moduleUUID], moduleRecord);

    const newModuleId = generateNewModuleId(pageConfig);
    let moduleInstanceConfig = new PluginConfig({
      id: newModuleId,
      type: 'widget',
      subtype: 'ModuleInstance',
      config: Immutable.Map({
        childNamespace: newModuleId,
        moduleUUID: moduleRecord.moduleUUID,
        moduleName: moduleRecord.moduleName,
        events: moduleEventHandlers.map(e => e.eventHandler),
        variantSelected: 'custom',
        changesDone: false,
        ...instanceVars,
      }),
      layout: new LayoutRecord({container: moduleParentContainerId}),
    });
    // logger.info(moduleInstanceConfig.toJS());

    // Add Instance at location of pluginId
    let pagePlugins = pageConfig.plugins.takeUntil((c, pid) => pid === pluginId);
    pagePlugins = pagePlugins.set(newModuleId, moduleInstanceConfig);
    pagePlugins = pagePlugins.set(pluginId, pageConfig.plugins.get(pluginId));
    pagePlugins = pagePlugins.concat(pageConfig.plugins.skipUntil((c, pid) => pid === pluginId).skip(1));

    //Delete old plugins
    const pageKeys = appModel.getPageKeysForId(pageId);
    const pluginsToDelete = getPluginsContained(pluginId, pageConfig).add(pluginConfig);
    let newAppModel = appModel;

    pluginsToDelete.forEach(config => {
      pagePlugins = pagePlugins.remove(config.id);
      pageKeys.forEach(pageKey => {
        newAppModel = newAppModel.deleteModelValue([pageKey, 'plugins', config.id]);
        newAppModel.dependencyGraph.deletePlugin(pageKey, config.id);
      });
    });

    newAppConfig = newAppConfig.setIn(['pages', pageId, 'plugins'], pagePlugins);

    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put({
      type: DispatchActions.UPDATE_STAGE_MODEL,
      payload: {
        appModel: newAppModel,
        desc: 'handleCreateModule',
        meta: {...action.payload},
      },
    });
    yield call(recreateModulePlugins, moduleRecord.moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export function* fillModuleMandtoryFields(action): SagaIterator {
  let countReload = 0;
  // yield delay(4000);
  try {
    const appConfig = yield select(selectAppConfig);
    let newAppConfig = yield select(selectAppConfig);
    let newAppModel = yield select(selectAppModel);
    const {pageId, pluginId, moduleUUID, onboarding, appId} = action.payload;
    const pageConfig = appConfig?.get('pages').get(pageId);
    let pagePlugins = pageConfig?.get('plugins');
    const pluginConfig = pageConfig.get('plugins').get(pluginId);
    const childNamespace = pluginConfig?.config?.get('childNamespace');
    const moduleRecord = appConfig?.modules?.get(moduleUUID);
    const editors = moduleRecord?.editors;
    const styleEditors = moduleRecord?.styleEditors;
    const basicEditors = moduleRecord?.basicEditors;
    let mandatoryEditors: Immutable.OrderedMap<string, ModuleEditorConfig> = Immutable.OrderedMap();
    styleEditors?.map((editorRecord: ModuleEditorConfig, key: string) => {
      if (editorRecord.mandatory) {
        mandatoryEditors = mandatoryEditors.set(key, editorRecord);
      }
    });
    basicEditors?.map((editorRecord: ModuleEditorConfig, key: string) => {
      if (editorRecord.mandatory) {
        mandatoryEditors = mandatoryEditors.set(key, editorRecord);
      }
    });
    editors?.map((editorRecord: ModuleEditorConfig, key: string) => {
      if (editorRecord.mandatory) {
        mandatoryEditors = mandatoryEditors.set(key, editorRecord);
      }
    });

    // yield pluginConfigUpdatePath();
    const getItems = function* () {
      countReload += 1;
      const productListRaw = yield (yield getShopifyObjectCache()).getProductsList();
      const collectionsListRaw = yield (yield getShopifyObjectCache()).getCollectionsList();
      const blogsListRaw = yield (yield getShopifyObjectCache()).getBlogsList();
      const productsLoaded = yield (yield getShopifyObjectCache()).getConfigValue('productsLoaded');
      const collectionsLoaded = yield (yield getShopifyObjectCache()).getConfigValue('collectionsLoaded');
      const blogsLoaded = yield (yield getShopifyObjectCache()).getConfigValue('blogsLoaded');
      if (!productsLoaded || !collectionsLoaded || !blogsLoaded) {
        if (countReload < 2) {
          yield delay(2000);
          yield getItems();
        } else {
          yield put(moduleMandtoryFieldsFailed(pageId, pluginId));
          return 'null';
        }
      } else {
        const productList = productListRaw.filter((e: any) => e?.featuredImage);
        const collectionsList = collectionsListRaw.filter(
          (e: any) => e?.image?.url && (!_.isNil(e?.productsCount) ? e?.productsCount > 0 : true),
        );
        const blogsList = blogsListRaw.filter((e: any) => e?.post_count);
        let productListIndex = 0;
        let collectionsListIndex = 0;
        let blogsListIndex = 0;
        const getLatestIndex = (type: string) => {
          if (type === 'product') {
            productListIndex += 1;
            if (productListIndex >= productList.length) {
              productListIndex = 0;
            }
            return productListIndex;
          } else if (type === 'blog') {
            blogsListIndex += 1;
            if (blogsListIndex >= blogsList.length) {
              blogsListIndex = 0;
            }
            return blogsListIndex;
          } else {
            collectionsListIndex += 1;
            if (collectionsListIndex >= collectionsList.length) {
              collectionsListIndex = 0;
            }
            return collectionsListIndex;
          }
        };
        const updates = [];
        mandatoryEditors?.map((editorRecord: ModuleEditorConfig) => {
          const selector = _.clone(editorRecord.selector);
          selector[0] = childNamespace + NAMESPACE_SPERATOR + selector[0];
          const currentPlugin = pagePlugins?.get(selector[0]);
          const editorType = editorRecord?.editorType;
          const storedValue = pagePlugins?.getIn(selector);
          if (editorType?.type == 'assetEditor') {
            const baseSelector = selector.slice(0, -1);
            const sourceTypeProperty = pagePlugins?.getIn([...baseSelector, editorType?.props?.sourceTypeProperty]);
            if (sourceTypeProperty?.toLowerCase() == 'url') {
              selector[selector.length - 1] = editorType?.props?.urlProperty;
            } else {
              selector[selector.length - 1] = editorType?.props?.assetProperty;
            }
            const storedValue = pagePlugins?.getIn(selector);
            if (!storedValue) {
              pagePlugins = pagePlugins?.setIn([...baseSelector, editorType?.props?.sourceTypeProperty], 'url');
              pagePlugins = pagePlugins?.setIn(
                [...baseSelector, editorType?.props?.urlProperty],
                'https://cdn.apptile.io/23a145ba-da98-4d6d-8c06-1a597b7a4a2f/19c387fc-19f0-486e-a4c8-e5352b3bf46e/original.png',
              );
              updates.push({
                pluginId: selector[0],
                pageId,
                config: pagePlugins.get(selector[0]).config,
              });
            }
          } else if (editorType?.type == 'listEditor') {
            const shopifyType = editorType?.props?.shopifyType;
            const newStoredValue = storedValue?.map((e: any, i: number) => {
              const item =
                shopifyType === 'Product'
                  ? productList[getLatestIndex('product')]
                  : collectionsList[getLatestIndex('collection')];
              if (!item) return e;
              e = {
                ...e,
                url: shopifyType === 'Product' ? item?.featuredImage : item.image?.url,
                navEntityId: item?.handle,
                navEntityType: shopifyType === 'Product' ? 'Product' : 'Collection',
                resizeMode: 'cover',
                title: item?.title,
                id: item?.id,
              };
              return e;
            });
            pagePlugins = pagePlugins?.setIn(selector, newStoredValue);
            updates.push({
              pluginId: selector[0],
              pageId,
              config: pagePlugins.get(selector[0]).config,
            });
          } else if (editorType?.type == 'shopifyCollectionHandleControl' && collectionsList.length > 0) {
            pagePlugins = pagePlugins?.setIn(selector, collectionsList[getLatestIndex('collection')].handle);
            updates.push({
              pluginId: selector[0],
              pageId,
              config: pagePlugins.get(selector[0]).config,
            });
          } else if (editorType?.type == 'shopifyProductHandleControl' && productList.length > 0) {
            pagePlugins = pagePlugins?.setIn(selector, productList[getLatestIndex('product')].handle);
            updates.push({
              pluginId: selector[0],
              pageId,
              config: pagePlugins.get(selector[0]).config,
            });
          } else if (editorType?.type == 'shopifyBlogHandleControl' && blogsList.length > 0) {
            pagePlugins = pagePlugins?.setIn(selector, blogsList[getLatestIndex('blog')].handle);
            updates.push({
              pluginId: selector[0],
              pageId,
              config: pagePlugins.get(selector[0]).config,
            });
          } else if (editorType?.type == 'shopifyBlogControl' && blogsList.length > 0) {
            pagePlugins = pagePlugins?.setIn(selector, `{{${JSON.stringify(blogsList[getLatestIndex('blog')])}}}`);
            updates.push({
              pluginId: selector[0],
              pageId,
              config: pagePlugins.get(selector[0]).config,
            });
          } else if (editorType?.type == 'shopifyCollectionIdControl' && collectionsList.length > 0) {
            pagePlugins = pagePlugins?.setIn(selector, collectionsList[getLatestIndex('collection')].id);
            updates.push({
              pluginId: selector[0],
              pageId,
              config: pagePlugins.get(selector[0]).config,
            });
          } else if (currentPlugin?.type == 'widget') {
            const widgetType = currentPlugin?.subtype;
            if (widgetType == 'VideoPlayerWidget') {
              const baseSelector = selector.slice(0, -1);
              pagePlugins = pagePlugins?.setIn(
                [...baseSelector, 'value'],
                'https://res.cloudinary.com/dxtmp1uf3/video/upload/v1697202245/VideoDemo_eb167r.mov',
              );
              updates.push({
                pluginId: selector[0],
                pageId,
                config: pagePlugins.get(selector[0]).config,
              });
            }
          }
        });
        yield put(bulkPluginUpdates(updates));
        yield put(moduleMandtoryFieldsFinished(pageId, pluginId));
        return '';
      }
    };
    yield getItems();
  } catch (error) {
    logger.error(error);
    const {pageId, pluginId} = action.payload;
    yield put(moduleMandtoryFieldsFailed(pageId, pluginId));
  }
}

type RemapModuleInstance = any;
export function* handleRemapModuleInstance(action: DispatchAction<RemapModuleInstance>): SagaIterator {
  try {
    const {moduleUUID, moduleInstanceId, pageId} = action.payload;
    const appConfig: AppConfig = yield select(selectAppConfig);
    const appModel: AppModelType = yield select(selectStageModel);
    const pageConfig: PageConfig = appConfig.pages.get(pageId);
    const pluginConfig = pageConfig.plugins.get(moduleInstanceId);

    let newAppConfig = appConfig;
    let newAppModel = appModel;
    ({newAppConfig, newAppModel} = deleteModulePlugins(newAppConfig, newAppModel, pageId, pluginConfig));
    newAppConfig = newAppConfig.setIn(
      ['pages', pageId, 'plugins', moduleInstanceId, 'config', 'moduleUUID'],
      moduleUUID,
    );
    yield put({
      type: DispatchActions.UPDATE_APP_CONFIG,
      payload: newAppConfig,
    });
    yield put({
      type: DispatchActions.UPDATE_STAGE_MODEL,
      payload: {
        appModel: newAppModel,
        desc: 'handleCreateModule',
        meta: {...action.payload},
      },
    });
    yield call(recreateModulePlugins, moduleUUID);
  } catch (e) {
    logger.error(e);
  }
}

export default function* editorModuleActionSagas(): SagaIterator {
  yield all([
    takeEvery(CREATE_MODULE_FROM_WIDGET, handleCreateModuleFromWidget),
    takeEvery(DispatchActions.CREATE_MODULE, handleCreateModule),
    takeEvery('WRAP_PLUGIN_INTO_TILE', makeSinglePluginTile),
    takeLatest(SAVE_MODULE_RECORD, handleSaveModule),
    takeLatest(DELETE_MODULE_RECORD, handleDeleteModule),
    takeLatest(DUPLICATE_MODULE_RECORD, handleDuplicateModule),
    takeLatest(UPDATE_MODULE_DEF_RECORD, handleUpdateModuleDefinition),
    takeLatest(SAVE_MODULE_VARIANT, handleSaveModuleVariant),
    takeEvery(FORWARD_MODULE_PLUGIN_PROPERTY, handleForwardModulePluginProperty),
    takeEvery(UPDATE_MODULE_PLUGIN_PROPERTY, handleUpdateModulePluginProperty),
    takeEvery(UPDATE_MODULE_PROPERTY, handleUpdateModuleProperty),
    takeEvery(ADD_MODULE_PLUGIN_PROPERTY_EDITOR, handleAddModulePluginPropertyEditor),
    takeEvery(DELETE_MODULE_PLUGIN_PROPERTY_EDITOR, handleDeleteModulePluginPropertyEditor),
    takeEvery(REORDER_MODULE_PLUGIN_PROPERTIES, handleReorderModuleProperties),
    takeEvery(FILL_MODULE_MANDATORY_FIELDS, fillModuleMandtoryFields),
    takeEvery(DispatchActions.BATCH_CREATE_MODULE_PLUGINS, handleBatchCreateModulePlugins),
    takeEvery(DispatchActions.RENAME_MODULE_PROPERTY, handleRenameModuleProperty),
    takeEvery(DispatchActions.RENAME_MODULE_OUTPUT, handleRenameModuleOutput),
    takeEvery(DispatchActions.RENAME_MODULE_EVENT, handleRenameModuleEvent),

    // takeEvery(DispatchActions.UPDATE_MODULE_PROPERTY, handleUpdateModuleProperty),
    takeEvery(DispatchActions.UPDATE_MODULE_OUTPUT, handleUpdateModuleOutput),
    takeEvery(DispatchActions.CREATE_MODULE_PROPERTY, handleCreateModuleProperty),
    takeEvery(DispatchActions.CREATE_MODULE_OUTPUT, handleCreateModuleOutput),
    takeEvery(DispatchActions.CREATE_MODULE_EVENT, handleCreateModuleEvent),
    takeEvery(DispatchActions.DELETE_MODULE_PROPERTY, handleDeleteModuleProperty),
    takeEvery(DispatchActions.DELETE_MODULE_OUTPUT, handleDeleteModuleOutput),
    takeEvery(DispatchActions.DELETE_MODULE_EVENT, handleDeleteModuleEvent),
    takeEvery(DispatchActions.PERSIST_MODULE_INPUTS, handlePersistModuleInputs),
    takeEvery(DispatchActions.REMAP_MODULE_INSTANCE, handleRemapModuleInstance),
  ]);
}
