import {MaterialCommunityIcons} from 'apptile-core';
import * as React from 'react';
import {getTheme} from '../utils/themeSelector';
const theme = getTheme();
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  LayoutChangeEvent,
  Pressable,
  StyleProp,
  ViewStyle,
  TextStyle,
} from 'react-native';
import Animated, {
  concat,
  Easing,
  useAnimatedStyle,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

interface CollapsiblePanelProps {
  title: string;
  children?: React.ReactNode;
  isOpen: boolean;
  index?: number;
  isHeaderHidden?: boolean;
  backgroundStyle?: StyleProp<ViewStyle>;
  headerStyle?: StyleProp<ViewStyle>;
  headerTextStyle?: StyleProp<TextStyle>;
  overflowStyle?: StyleProp<ViewStyle>;
  customHeader?: any;
  setOpen?: (open: boolean) => void;
}

type RefType = unknown;

const CollapsiblePanel = React.forwardRef<RefType, CollapsiblePanelProps>(
  ({title, children, isOpen, index, customHeader, setOpen, isHeaderHidden, ...props}, ref) => {
    const [expanded, setExpanded] = React.useState(isOpen);
    const [noHeader, setNoHeader] = React.useState(!!isHeaderHidden);
    const [height, setHeight] = React.useState(0);
    const sv_height = useSharedValue(0);
    const icon_rotate = useSharedValue(false);
    const rotateZ = useDerivedValue(() => {
      return withTiming(icon_rotate.value ? 90 : 0, {
        duration: 250,
        easing: Easing.linear,
      });
    });

    const topZindex = 100;
    const animatedExpandStyle = useAnimatedStyle(() => {
      return {
        height: withTiming(sv_height.value, {
          duration: 100,
          easing: Easing.linear,
        }),
      };
    }, [sv_height]);

    const animatedRotateStyle = useAnimatedStyle(() => {
      return {
        transform: [{rotateZ: `${rotateZ.value}deg`}],
      };
    });

    React.useEffect(() => {
      if (!expanded) {
        sv_height.value = 0;
      } else {
        sv_height.value = height;
      }
    }, [expanded, height, sv_height]);
    React.useEffect(() => {
      if (!expanded) {
        icon_rotate.value = false;
      } else {
        icon_rotate.value = true;
      }
    }, [expanded, icon_rotate]);
    React.useEffect(() => {
      setExpanded(isOpen);
    }, [isOpen]);

    const onLayout = React.useCallback(
      (event: LayoutChangeEvent) => {
        const measuredHeight = event.nativeEvent.layout.height;

        if (height !== measuredHeight) {
          setHeight(measuredHeight);
        }
      },
      [height],
    );

    return (
      <View style={[styles.background, {zIndex: index ? topZindex - index : 0}, props.backgroundStyle ?? {}]}>
        <View style={[styles.overflow, props.overflowStyle]}>
          {!noHeader && (
            <Pressable
              onPress={_ => {
                setExpanded(!expanded);
                setOpen && setOpen(!expanded);
              }}>
              {customHeader ? (
                <>{React.cloneElement(customHeader)}</>
              ) : (
                <View style={[styles.header, props.headerStyle ?? {}]}>
                  <Text style={[styles.headerText, props.headerTextStyle ?? {}]}>{title}</Text>
                  <Animated.View style={[styles.iconContainer, animatedRotateStyle]}>
                    <MaterialCommunityIcons style={styles.buttonText} name="chevron-right" />
                  </Animated.View>
                </View>
              )}
            </Pressable>
          )}
          <Animated.View ref={ref} style={[styles.contentOverflow, animatedExpandStyle]}>
            <Animated.View onLayout={onLayout} style={[styles.contentContainer]}>
              {expanded ? children : null}
            </Animated.View>
          </Animated.View>
        </View>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  background: {
    flex: 0,
    backgroundColor: theme.BACKGROUND_COLOR,
    flexBasis: 'auto',
    marginBottom: 5,
    borderWidth: 1,
    borderRadius: 4,
    borderColor: 'rgba(24,24,24, 0.2)',
  },
  overflow: {
    backgroundColor: theme.BACKGROUND_COLOR,
    overflow: 'hidden',
  },
  header: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 5,
    shadowColor: 'rgba(24,24,24, 0.3)',
    backgroundColor: theme.BACKGROUND_COLOR,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowRadius: 4,
  },
  headerText: {
    textAlign: 'left',
    fontSize: 11,
    color: theme.TEXT_COLOR,
  },
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 14,
    color: theme.TEXT_COLOR,
  },
  contentOverflow: {
    overflow: 'visible',
  },
  contentContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    padding: 0,
    backgroundColor: 'transparent',
  },
});

export default CollapsiblePanel;
