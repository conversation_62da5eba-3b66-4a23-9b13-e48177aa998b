//If there is window.CODEBUILD_BUILD_NUMBER then add a tag at the top right corner of the screen
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {getCommonStyles} from '../utils/themeSelector';
import {useSelector} from 'react-redux';
import {checkApptileEmailSelector} from '../selectors/FeatureGatingSelector';
const commonStyles = getCommonStyles();

export default function WebSDKBetaBuildTag() {
  const artifactId = global.WEB_SDK_ARTIFACT_ID;
  const isApptileUser = useSelector(checkApptileEmailSelector);
  if (!artifactId || artifactId === 'null' || !isApptileUser) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={[commonStyles.baseText, styles.text]}>Web Beta id: {artifactId}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'fixed',
    top: 0,
    right: 0,
    backgroundColor: 'green',
    padding: 4,
  },
  text: {
    color: '#fff',
    fontSize: 14,
  },
});
