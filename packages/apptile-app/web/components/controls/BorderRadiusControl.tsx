import Immutable from 'immutable';
import _ from 'lodash';
import React, {useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Text, TextInput, View} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useDerivedValue, useSharedValue, withTiming} from 'react-native-reanimated';

import theme from '@/root/web/styles-v2/theme';
import {getCommonStyles} from '../../utils/themeSelector';
import {ApptileWebIcon} from '../../icons/ApptileWebIcon';

import {ThemeEditorBaseProps, PropertyEditorProps} from '@/root/app/common/EditorControlTypes';
import {Bubble} from './TRBLControl';

const commonStyles = getCommonStyles();

type BorderRadiusProps = {
  value: string | number;
  defaultValue: string;
  name: string;
  label: string;
  config: Immutable.Map<string, string>;
  options: [string, string, string, string];
  onChange: (value: boolean) => void;
  onCustomPropChange: (key: string, value: string, debounced: boolean, remove?: boolean) => void;
} & ThemeEditorBaseProps &
  PropertyEditorProps;

const BorderRadiusControl: React.FC<BorderRadiusProps> = props => {
  const {
    value,
    label,
    name,
    config,
    options,
    onChange,
    onCustomPropChange,
    themeIndication,
    getThemeObj,
    configPathSelector,
  } = props;
  const [mainValue, setMainValue] = useState('');
  const [topLeftValue, setTopLeftValue] = useState('');
  const [topRightValue, setTopRightValue] = useState('');
  const [bottomRightValue, setBottomRightValue] = useState('');
  const [bottomLeftValue, setBottomLeftValue] = useState('');
  const [isExpanded, setIsExpanded] = useState<boolean>(
    !!topLeftValue || !!topRightValue || !!bottomRightValue || !!bottomLeftValue,
  );
  useEffect(() => {
    setMainValue(value ? String(value) : '');
    setTopLeftValue(config?.get(options[0]) ? String(config.get(options[0])) : '');
    setTopRightValue(config?.get(options[1]) ? String(config.get(options[1])) : '');
    setBottomRightValue(config?.get(options[2]) ? String(config.get(options[2])) : '');
    setBottomLeftValue(config?.get(options[3]) ? String(config.get(options[3])) : '');
  }, [config, options, value]);

  const debouncedOnCustomPropChange = _.debounce(
    (key: string, val: string, debounced: boolean, remove?: boolean) => onCustomPropChange(key, val, debounced, remove),
    450,
  );

  const icon_rotate = useSharedValue(0);
  const rotateZ = useDerivedValue(() => {
    return withTiming(icon_rotate.value * 90, {
      duration: 250,
      easing: Easing.linear,
    });
  });
  const animatedRotateStyle = useAnimatedStyle(() => {
    return {
      transform: [{rotateZ: `${rotateZ.value}deg`}],
    };
  });

  // Cleaning the entries in config while toggle
  const cleanUp = _.debounce((cleanup: 'main' | 'corner') => {
    if (cleanup === 'main') {
      if (!_.isEmpty(mainValue)) {
        setMainValue('');
        onCustomPropChange(name, '');
      }
    } else {
      if (!_.isEmpty(topLeftValue)) {
        setTopLeftValue('');
        onCustomPropChange(options[0], '');
      }

      if (!_.isEmpty(topRightValue)) {
        setTopRightValue('');
        onCustomPropChange(options[1], '');
      }

      if (!_.isEmpty(bottomRightValue)) {
        setBottomRightValue('');
        onCustomPropChange(options[2], '');
      }

      if (!_.isEmpty(bottomLeftValue)) {
        setBottomLeftValue('');
        onCustomPropChange(options[3], '');
      }
    }
  });

  const onMainValueChange = useCallback(
    val => {
      let newVal = Number(val).toString();
      if (val.trim() == '') newVal = '';
      else if (val.trim().endsWith('.')) newVal += '.';
      if (!isNaN(newVal)) {
        setMainValue(newVal);
        debouncedOnCustomPropChange(name, newVal, true, _.isEmpty(_.trim(newVal)));
        cleanUp('corner');
      }
    },
    [debouncedOnCustomPropChange, name, cleanUp],
  );

  const onCornerValueChange = useCallback(
    (val, idx) => {
      let newVal = Number(val).toString();
      if (val.trim() == '') newVal = '';
      else if (val.trim().endsWith('.')) newVal += '.';
      if (!isNaN(newVal)) {
        switch (idx) {
          case 0:
            setTopLeftValue(newVal);
            break;
          case 1:
            setTopRightValue(newVal);
            break;
          case 2:
            setBottomRightValue(newVal);
            break;
          case 3:
            setBottomLeftValue(newVal);
            break;
        }
        debouncedOnCustomPropChange(options[idx], newVal, true, _.isEmpty(_.trim(newVal)));
        cleanUp('main');
      }
    },
    [cleanUp, debouncedOnCustomPropChange, options],
  );

  const onFocus = useCallback(
    idx => {
      icon_rotate.value = idx;
    },
    [icon_rotate],
  );

  return (
    <View style={styles.container}>
      <View style={styles.splitRowContainer}>
        <View style={[styles.rowContainer]}>
          {label && <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>}
          <View style={[label ? commonStyles.inputContainer : {width: '100%'}]}>
            <View style={[styles.rowContainer]}>
              <TextInput
                style={[
                  styles.valueInput,
                  {
                    paddingLeft: 8,
                    paddingRight: 8,
                    width: '100%',
                    color:
                      isExpanded || !!topLeftValue || !!topRightValue || !!bottomRightValue || !!bottomLeftValue
                        ? theme.CONTROL_PLACEHOLDER_COLOR
                        : theme.CONTROL_INPUT_COLOR,
                  },
                  commonStyles.input,
                ]}
                blurOnSubmit={true}
                placeholder="0"
                value={
                  !!topLeftValue || !!topRightValue || !!bottomRightValue || !!bottomLeftValue ? 'Mixed' : mainValue
                }
                editable={
                  isExpanded || !!topLeftValue || !!topRightValue || !!bottomRightValue || !!bottomLeftValue
                    ? false
                    : true
                }
                onChangeText={onMainValueChange}
              />
              <Pressable
                style={[styles.button, commonStyles.input, isExpanded && styles.buttonActive]}
                onPress={() => {
                  setIsExpanded(!isExpanded);
                }}>
                {/* <View
                  style={[
                    styles.switchButton,
                    {borderColor: isExpanded ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR},
                  ]}
                /> */}
                {/* <MaterialCommunityIcons
                  name="rounded-corner"
                  size={12}
                  color={isExpanded ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR}
                /> */}
                <ApptileWebIcon
                  name="border-radius"
                  size={14}
                  color={isExpanded ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR}
                />
              </Pressable>
            </View>
            {isExpanded && (
              <View style={[styles.rowContainer, commonStyles.input, styles.bottomBar]}>
                <View style={[styles.bottomBarBox, {borderLeftWidth: 0}]}>
                  <Animated.View style={[animatedRotateStyle]}>
                    <ApptileWebIcon name="border-radius-left" size={14} />
                  </Animated.View>
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={topLeftValue}
                    configPathSelector={options[0] ? [...configPathSelector.slice(0, -1), options[0]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onCornerValueChange(text, 0)}
                    placeholder="0"
                    value={topLeftValue}
                    onFocus={() => onFocus(0)}
                  />
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={topRightValue}
                    configPathSelector={options[1] ? [...configPathSelector.slice(0, -1), options[1]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onCornerValueChange(text, 1)}
                    placeholder="0"
                    value={topRightValue}
                    onFocus={() => onFocus(1)}
                  />
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={bottomRightValue}
                    configPathSelector={options[2] ? [...configPathSelector.slice(0, -1), options[2]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onCornerValueChange(text, 2)}
                    placeholder="0"
                    value={bottomRightValue}
                    onFocus={() => onFocus(2)}
                  />
                </View>
                <View style={styles.bottomBarBox}>
                  <Bubble
                    value={bottomLeftValue}
                    configPathSelector={options[3] ? [...configPathSelector.slice(0, -1), options[3]] : []}
                    themeIndication={themeIndication}
                    getThemeObj={getThemeObj}
                  />
                  <TextInput
                    style={[styles.valueInput, styles.inputGroup]}
                    blurOnSubmit={true}
                    onChangeText={text => onCornerValueChange(text, 3)}
                    placeholder="0"
                    value={bottomLeftValue}
                    onFocus={() => onFocus(3)}
                  />
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'stretch',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
    justifyContent: 'center',
  },
  rowContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  splitRowContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  valueInput: {
    flex: 1,
    paddingVertical: 8,
    paddingLeft: 4,
    color: theme.CONTROL_INPUT_COLOR,
    fontFamily: theme.FONT_FAMILY,
    outlineStyle: 'none',
  },
  bottomBar: {
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    marginTop: 8,
    borderRadius: 6,
    // overflow: 'hidden',
  },
  bottomBarBox: {
    width: '20%',
    height: 30,
    // overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
    borderLeftWidth: 1,
    borderColor: theme.INPUT_BORDER,
  },
  inputGroup: {
    width: '90%',
    fontFamily: theme.FONT_FAMILY,
  },
  button: {
    flexDirection: 'row',
    width: 31,
    height: 31,
    marginLeft: 8,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonActive: {
    borderWidth: 2,
    borderColor: theme.CONTROL_ACTIVE_COLOR,
  },
  switchButton: {
    width: 12,
    height: 12,
    borderWidth: 2,
  },
  switchButtonRight: {
    width: 12,
    height: 12,
    borderWidth: 2,
    borderColor: theme.CONTROL_PLACEHOLDER_COLOR,
    borderRightColor: theme.PRIMARY_BORDER,
  },
});

export default BorderRadiusControl;
