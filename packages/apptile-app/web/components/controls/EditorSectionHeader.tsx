import React from 'react';
import {StyleSheet, Text, View} from 'react-native';

import { getTheme } from '../../utils/themeSelector';

import {MaterialCommunityIcons} from 'apptile-core';
import {ApptileWebIcon} from '../../icons/ApptileWebIcon.web';
import {Image} from 'react-native';
import imagePlanMapping from '../../common/featureGatingConstants';
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();
const theme = getTheme();
type EditorSectionHeaderProps = {
  name: string;
  label: string;
  icon?: string;
  iconSize?: number;
  iconType?: 'ApptileWebIcons' | 'Material Icon';
  textStyles?: any;
  isPremiumDesabled?: boolean;
};

const EditorSectionHeader: React.FC<EditorSectionHeaderProps> = props => {
  const {label, name, icon, iconType, iconSize = 12, textStyles, isPremiumDesabled} = props;

  return (
    <View style={styles.container}>
      <View style={{flexDirection: 'row', alignItems: 'center', gap: 5}}>
        <Text style={[commonStyles.heading, textStyles]}>{label}</Text>
        {isPremiumDesabled && (
          <Image source={imagePlanMapping['SMALL']} resizeMode="contain" style={{width: 20, height: 20}} />
        )}
      </View>
      {iconType &&
        icon &&
        (iconType === 'ApptileWebIcons' ? (
          <ApptileWebIcon name={icon} size={iconSize} />
        ) : (
          <MaterialCommunityIcons name={icon} size={iconSize} />
        ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
});

export default EditorSectionHeader;
