import _ from 'lodash';
import {editableTags} from 'apptile-core';

const getCommonConfig = (tagName?: string) => {
  tagName = tagName?.toLowerCase();

  return [
    {
      type: 'colorInput',
      name: `${_.trim(tagName ? `${tagName}_` : '')}backgroundColor`,
      props: {
        label: 'Background',
      },
    },
    {
      type: 'colorInput',
      name: `${_.trim(tagName ? `${tagName}_` : '')}color`,
      props: {
        label: 'Text Color',
      },
    },
    {
      type: 'trblValuesEditor',
      name: `${_.trim(tagName ? `${tagName}_` : '')}borderWidth`,
      props: {
        label: 'Border',
        options: [
          `${_.trim(tagName ? `${tagName}_` : '')}borderTopWidth`,
          `${_.trim(tagName ? `${tagName}_` : '')}borderRightWidth`,
          `${_.trim(tagName ? `${tagName}_` : '')}borderBottomWidth`,
          `${_.trim(tagName ? `${tagName}_` : '')}borderLeftWidth`,
        ],
      },
    },
    {
      type: 'colorInput',
      name: `${_.trim(tagName ? `${tagName}_` : '')}borderColor`,
      props: {
        label: 'Border Color',
      },
    },
    {
      type: 'borderRadiusEditor',
      name: `${_.trim(tagName ? `${tagName}_` : '')}borderRadius`,
      props: {
        label: 'Border Radius',
        options: [
          `${_.trim(tagName ? `${tagName}_` : '')}borderTopLeftRadius`,
          `${_.trim(tagName ? `${tagName}_` : '')}borderTopRightRadius`,
          `${_.trim(tagName ? `${tagName}_` : '')}borderBottomRightRadius`,
          `${_.trim(tagName ? `${tagName}_` : '')}borderBottomLeftRadius`,
        ],
        layout: 'square',
      },
    },
    {
      type: 'trblValuesEditor',
      name: `${_.trim(tagName ? `${tagName}_` : '')}padding`,
      props: {
        label: 'Padding',
        options: [
          `${_.trim(tagName ? `${tagName}_` : '')}paddingTop`,
          `${_.trim(tagName ? `${tagName}_` : '')}paddingRight`,
          `${_.trim(tagName ? `${tagName}_` : '')}paddingBottom`,
          `${_.trim(tagName ? `${tagName}_` : '')}paddingLeft`,
        ],
      },
    },
    {
      type: 'typographyInput',
      name: `${_.trim(tagName ? `${tagName}_` : '')}typography`,
      props: {
        label: 'Typography',
      },
    },
    {
      type: 'trblValuesEditor',
      name: `${_.trim(tagName ? `${tagName}_` : '')}margin`,
      props: {
        label: 'Margin',
        options: [
          `${_.trim(tagName ? `${tagName}_` : '')}marginTop`,
          `${_.trim(tagName ? `${tagName}_` : '')}marginRight`,
          `${_.trim(tagName ? `${tagName}_` : '')}marginBottom`,
          `${_.trim(tagName ? `${tagName}_` : '')}marginLeft`,
        ],
      },
    },
  ];
};

//  Addition editor config goes below like commented
const tagSpecificEditorConfig = {
  //   h1: [
  //     {
  //       type: 'colorInput',
  //       name: `borderColor`,
  //       props: {
  //         label: 'Border Color',
  //       },
  //     },
  //   ],
};

export const getEditorConfig = () => {
  return _.transform(
    editableTags,
    (result, tagName) => {
      _.set(result, tagName, [...getCommonConfig(tagName), ..._.get(tagSpecificEditorConfig, tagName, [])]);
    },
    {
      container: getCommonConfig().filter(entry => {
        return !['color'].includes(entry.name);
      }),
    },
  );
};
