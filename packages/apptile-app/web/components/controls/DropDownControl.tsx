import {MaterialCommunityIcons, Icon, allAvailablePlans, Plan} from 'apptile-core';
import {debounce, isEmpty} from 'lodash';
import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {getTheme} from '../../utils/themeSelector';

import CodeInput from '../codeEditor/codeInput';
import PopoverComponent from '../../components-v2/base/Popover';
import Fuse from 'fuse.js';
import CodeInputControl from '../controls/CodeInputControl';
import {currentPlanFeaturesSelector} from '../../selectors/FeatureGatingSelector';
import {useDispatch, useSelector} from 'react-redux';
import {setOpenPremiumModal} from '../../actions/editorActions';
import {Image} from 'react-native';
import imagePlanMapping from '../../common/featureGatingConstants';
import {getCommonStyles} from '../../utils/themeSelector';

const commonStyles = getCommonStyles();
const theme = getTheme();
type DropDownControlProps = {
  options: string[] | Record<string, any>[];
  defaultValue: string;
  label?: string;
  onChange: (value: string) => void;
  value: string;
  showIcon?: boolean;
  disableBinding?: boolean;
  nameKey?: string;
  valueKey?: string;
  iconKey?: string;
  gatingKey?: string;
  disabled?: boolean;
};

const DropDownControl: React.FC<DropDownControlProps> = ({
  value,
  label,
  onChange,
  options = [],
  defaultValue,
  disableBinding,
  nameKey,
  valueKey,
  iconKey,
  gatingKey = 'gating',
  disabled = false,
  showIcon = false,
}) => {
  const [isBinding, setIsBinding] = useState(false);
  const [selValue, setSelValue] = useState<string>(value);
  const [isObjectList, setIsObjectList] = useState(!!valueKey);
  const [showPopover, setShowPopover] = useState(false);
  let currentItemName = value || defaultValue;
  let currentItem = null;
  if (isObjectList && Array.isArray(options) && options.find(v => v[valueKey] === value)) {
    const filteredOption = options.filter(v => v[valueKey] === value)[0];
    currentItemName = filteredOption[nameKey ? nameKey : valueKey];
    currentItem = filteredOption;
  }

  const [currentOption, setCurrentOption] = useState<string>(currentItemName);
  useEffect(() => {
    if (isEmpty(value)) {
      setSelValue(defaultValue);
    } else {
      setSelValue(value);
    }

    if (disableBinding) {
      setIsBinding(false);
    } else {
      if (
        isEmpty(value) ||
        (!isObjectList && Array.isArray(options) && options.includes(value)) ||
        (isObjectList && Array.isArray(options) && options.find(v => v[valueKey] === value))
      )
        setIsBinding(false);
      else setIsBinding(true);
    }

    let currentItemName = value || defaultValue;
    if (isObjectList && Array.isArray(options) && options.find(v => v[valueKey] === value)) {
      const filteredOption = options.filter(v => v[valueKey] === value)[0];
      currentItemName = filteredOption[nameKey ? nameKey : valueKey];
      currentItem = filteredOption;
    }
    setCurrentOption(currentItemName);
  }, [value, options, disableBinding, defaultValue, isObjectList, valueKey, nameKey]);

  const onValueChange = useCallback(
    newVal => {
      onChange(newVal);
    },
    [onChange],
  );
  const debouncedOnValueChange = debounce(val => onValueChange(val), 450);
  const buttonRef = useRef(null);
  const containerRef = useRef(null);

  const FUSE_OPTIONS = {
    // isCaseSensitive: false,
    includeScore: true,
    shouldSort: true,
    // includeMatches: true,
    findAllMatches: true,
    minMatchCharLength: 1,
    // location: 0,
    // threshold: 0.6,
    // distance: 100,
    // useExtendedSearch: false,
    ignoreLocation: true,
    // ignoreFieldNorm: false,
    // fieldNormWeight: 1,
    ...(nameKey ? {keys: [nameKey, valueKey].filter(e => e)} : {}),
  };
  if (options.length == 0 || typeof options[0] === 'string') delete FUSE_OPTIONS.keys;
  const [fuseSearch, setFuseSearch] = useState(new Fuse(options ?? [], FUSE_OPTIONS));
  const [filteredOptions, setFilteredOptions] = useState(options);
  const [query, setQuery] = useState('');
  useEffect(() => {
    if (query == '') {
      setFilteredOptions(options);
    } else {
      setFilteredOptions(fuseSearch.search(query).map(e => e.item));
    }
  }, [fuseSearch, options, query]);
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const dispatch = useDispatch();

  return (
    <View ref={containerRef} style={styles.editorInputItem}>
      {label && (
        <View style={commonStyles.labelContainer}>
          {!disableBinding && !isBinding && (
            <Pressable onPress={() => setIsBinding(true)}>
              <MaterialCommunityIcons name="flash" size={18} />
            </Pressable>
          )}
          {!disableBinding && isBinding && (
            <Pressable onPress={() => setIsBinding(false)}>
              <MaterialCommunityIcons name="flash-off" size={18} />
            </Pressable>
          )}
          <Text style={[commonStyles.labelText]}>{label}</Text>
        </View>
      )}
      <View style={[label ? commonStyles.inputContainer : {width: '100%'}]}>
        {!isBinding && (
          <PopoverComponent
            positions={['bottom', 'left', 'right', 'top']}
            visible={showPopover}
            onVisibleChange={setShowPopover}
            trigger={
              <View
                ref={buttonRef}
                style={[
                  {
                    flexDirection: 'row',
                    alignItems: 'center',
                    padding: showPopover ? 7 : 8,
                    borderWidth: showPopover ? 1 : 0,
                    borderColor: theme.INPUT_BORDER,
                  },
                  commonStyles.input,
                ]}>
                <Text style={[commonStyles.inputText, {flex: 1, overflow: 'hidden'}]}>
                  {iconKey && currentItem && currentItem[iconKey] && (
                    <Icon
                      iconType={currentItem[iconKey].type}
                      name={currentItem[iconKey].name}
                      style={{paddingRight: 5}}
                    />
                  )}
                  {(isObjectList ? currentOption : currentOption || selValue) || 'Select Value'}
                </Text>
                <Text style={{width: 15}}>
                  <MaterialCommunityIcons
                    name={showPopover ? 'chevron-up' : 'chevron-down'}
                    size={16}
                    color={theme.SECONDARY_COLOR}
                  />
                </Text>
              </View>
            }>
            <View
              style={[
                styles.dropdownOptions,
                buttonRef?.current && {width: buttonRef?.current?.getBoundingClientRect()?.width},
                containerRef?.current && {
                  maxHeight: 170,
                },
              ]}>
              <View style={{paddingHorizontal: 5}}>
                <CodeInputControl
                  value={query}
                  onChange={(value: string) => setQuery(value)}
                  placeholder={'Search Option'}
                />
              </View>
              <View style={[{flex: 1, height: '100%', paddingLeft: 10, overflow: 'scroll'}]}>
                {filteredOptions.map((item: any, index: number) => {
                  const itemName = isObjectList ? item[nameKey ? nameKey : valueKey] : item;
                  const isGated =
                    isObjectList && gatingKey && item[gatingKey]
                      ? !currentPlanFeatures.includes(allAvailablePlans[item[gatingKey] as Plan])
                      : false;
                  return (
                    <View key={(isObjectList ? item[valueKey] : item) + '-' + index} style={{padding: 4}}>
                      <Pressable
                        disabled={disabled}
                        style={[
                          styles.popoverText,
                          value == (isObjectList ? item[valueKey] : item) && {
                            backgroundColor: theme.PRIMARY_OPAQUE_BACKGROUND,
                          },
                        ]}
                        onPress={() => {
                          setShowPopover(false);
                          if (isObjectList && gatingKey && item[gatingKey] && isGated) {
                            dispatch(setOpenPremiumModal(true, allAvailablePlans[item[gatingKey] as Plan]));
                            return;
                          }
                          setCurrentOption(itemName);
                          onValueChange(isObjectList ? item[valueKey] : item);
                        }}>
                        {showIcon && (
                          <View style={{width: 20}}>
                            {value == (isObjectList ? item[valueKey] : item) ? (
                              <MaterialCommunityIcons name={'check'} size={15} color={theme.PRIMARY_COLOR} />
                            ) : (
                              <>
                                {iconKey && item[iconKey] ? (
                                  <Icon iconType={item[iconKey].type} name={item[iconKey].name} />
                                ) : (
                                  <MaterialCommunityIcons name={'blank'} size={15} color={theme.PRIMARY_COLOR} />
                                )}
                              </>
                            )}
                          </View>
                        )}
                        <View
                          style={{
                            justifyContent: 'space-between',
                            flexDirection: 'row',
                            flex: 1,
                            alignItems: 'center',
                          }}>
                          <Text
                            style={[
                              commonStyles.baseText,
                              value == (isObjectList ? item[valueKey] : item) && {color: theme.PRIMARY_COLOR},
                            ]}>
                            {itemName}
                          </Text>
                          {isGated && (
                            <Pressable onPress={() => {}}>
                              <Image
                                source={imagePlanMapping['SMALL']}
                                resizeMode="contain"
                                style={{width: 22, height: 22}}
                              />
                            </Pressable>
                          )}
                        </View>
                      </Pressable>
                    </View>
                  );
                })}
              </View>
            </View>
          </PopoverComponent>
        )}
        {isBinding && (
          <CodeInput
            defaultValue={defaultValue}
            value={value}
            placeholder="{{item.value}}"
            onChange={(val: string) => debouncedOnValueChange(val)}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  editorInputItem: {
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  dropdownOptions: {
    width: 170,
    maxHeight: 170,
    boxShadow: '0px 2px 3px 0px rgba(0, 0, 0, 0.25)',
    borderRadius: 8,
    backgroundColor: theme.INPUT_BACKGROUND,
    overflow: 'hidden',
    marginTop: 5,
  },
  popoverText: {
    flexDirection: 'row',
    padding: 8,
    textAlign: 'left',
    width: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    alignItems: 'center',
    borderRadius: 8,
  },
  premiumButton: {backgroundColor: theme.INPUT_BACKGROUND, padding: 4, borderRadius: 3, marginLeft: 10},
});

export default DropDownControl;
