import {ScreenConfig} from 'apptile-core';
import React, {useCallback, useEffect, useState} from 'react';
import {connect} from 'react-redux';
import {selectScreensInNav} from '../../selectors/EditorSelectors';
import {EditorRootState} from '../../store/EditorRootState';
import DropDownControl from './DropDownControl';
import {ScreenConfigParams} from 'apptile-core';
import standardScreens from '../../common/screenConstants';

type ScreenSelectorProps = {
  defaultValue?: string;
  label: string;
  onChange: (value: string) => void;
  value: string;
  screens: ScreenConfig[];
  disableBinding?: boolean;
  editorV2?: boolean;
};

const styleSelect = {flex: 1, padding: 8, borderRadius: 4, borderColor: '#ccc'};

const ScreenSelectorControl: React.FC<ScreenSelectorProps> = ({
  value,
  label,
  onChange,
  defaultValue,
  disableBinding,
  editorV2 = false,
  screens,
}) => {
  const [screenOptions, setScreenOptions] = useState<{name: string | undefined; value: string}[]>([]);

  useEffect(() => {
    let screenValue:ScreenConfig[];
    if (window?.apptileWebSDK?.moduleExports?.functions?.fetchScreensForNavigationDropdown) {
        screenValue = window?.apptileWebSDK?.moduleExports?.functions?.fetchScreensForNavigationDropdown();
    } else {
      screenValue = screens;
    }
    setScreenOptions(
      editorV2
        ? [
            {name: 'Home', value: 'Home'},
            {name: 'Collection', value: 'Collection'},
            {name: 'Product', value: 'Product'},
            ...screenValue
              .filter((s: ScreenConfigParams) => !standardScreens.includes(s.name))
              .map((s: ScreenConfigParams) => ({name: s.name, value: s.name})),
          ]
        : screenValue?.map(s => ({name: s.name, value: s.name})),
    );
  }, [editorV2, screens]);
  const onValueChange = useCallback(
    newVal => {
      onChange(newVal);
    },
    [onChange],
  );

  return (
    <DropDownControl
      label={label}
      value={value}
      onChange={onValueChange}
      options={screenOptions}
      nameKey={'name'}
      valueKey={'value'}
      defaultValue={defaultValue || ''}
      disableBinding={disableBinding}
    />
  );
};

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {};
};
const mapStateToProps = (state: EditorRootState) => {
  return {
    screens: selectScreensInNav(state),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(ScreenSelectorControl);
