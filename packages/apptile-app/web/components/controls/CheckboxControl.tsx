import React, {useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {getTheme} from '../../utils/themeSelector';
const theme = getTheme();
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();

type CheckboxControlProps = {
  value: boolean;
  label?: string;
  onChange: (value: boolean) => void;
  fullSizeLabel?: boolean;
  disabled?: boolean;
  reverse?: boolean;
};

const CheckboxControl: React.FC<CheckboxControlProps> = ({
  value,
  label,
  onChange,
  disabled = false,
  fullSizeLabel = false,
  reverse = false,
}) => {
  const isEnabled = reverse ? !Boolean(value) : Boolean(value);
  return (
    <View style={[styles.container]}>
      {label && (
        <Text style={[commonStyles.labelText, commonStyles.labelContainer, fullSizeLabel && {flex: 1, width: 'unset'}]}>
          {label}
        </Text>
      )}
      <Pressable
        style={[styles.switch, isEnabled && styles.switchActive]}
        disabled={disabled}
        onPress={() => onChange(reverse ? isEnabled : !isEnabled)}>
        <View style={styles.switchButton} />
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
    width: '100%',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  switch: {
    flexDirection: 'row',
    width: 48,
    height: 24,
    borderRadius: 24,
    backgroundColor: theme.CONTROL_INPUT_COLOR,
  },
  switchActive: {
    backgroundColor: theme.CONTROL_ACTIVE_COLOR,
    justifyContent: 'flex-end',
  },
  switchButton: {
    width: 19,
    height: 19,
    margin: 2.5,
    borderRadius: 16,
    backgroundColor: theme.INPUT_BACKGROUND,
  },
});

export default CheckboxControl;
