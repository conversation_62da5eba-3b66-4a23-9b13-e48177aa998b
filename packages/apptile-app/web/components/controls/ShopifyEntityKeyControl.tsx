import React, { useEffect, useLayoutEffect } from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {
  ShopifyItemHandlePicker,
  ShopifyItemObjectPicker,
  ShopifyItemPickerProps,
  ShopifyItemIdPicker,
} from '../../integrations/shopify/components/ShopifyItemPicker';
import theme from '@/root/web/styles-v2/theme';
import { isSingleObjectString } from 'apptile-core';
import { HAS_TEMPLATE_REGEX } from 'apptile-core';

type ShopifyEntityKeyControlProps = {
  name: string;
  value: string;
  label: string;
  type: 'handle' | 'title' | 'all' | 'id';
  itemType: ShopifyItemPickerProps['itemType'];
  onChange: (value: string) => void;
};

const ShopifyEntityKeyControl: React.FC<ShopifyEntityKeyControlProps> = ({
  name,
  label,
  value,
  type,
  itemType,
  onChange,
}) => {
  const [currentValue, setCurrentValue] = React.useState(value);

  const onChangeHandler = (val: string) => {
    setCurrentValue(val);
    onChange(val);
  };

  const onAllChangeHandler = (val: string) => {
    setCurrentValue(val);
    onChange(`{{${JSON.stringify(val)}}}`);
  };

  useLayoutEffect(() => {
    if (typeof value == 'string' && isSingleObjectString(value)) {
      value.replace(HAS_TEMPLATE_REGEX, (_v: any, expr: string) => {
        setCurrentValue(JSON.parse(expr));
      });
    }
  }, [value]);

  return (
    <View style={styles.container}>
      <View style={styles.rowLayout}>
        {type == 'handle' && (
          <ShopifyItemHandlePicker
            name={name}
            label={label}
            itemType={itemType}
            value={currentValue}
            onChange={onChangeHandler}
          />
        )}
        {type == 'id' && (
          <ShopifyItemIdPicker
            name={name}
            label={label}
            itemType={itemType}
            value={currentValue}
            onChange={onChangeHandler}
          />
        )}
        {type == 'all' && (
          <ShopifyItemObjectPicker
            name={name}
            label={label}
            itemType={itemType}
            value={currentValue?.handle ?? ''}
            onChange={onAllChangeHandler}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
  },
});

export default ShopifyEntityKeyControl;
