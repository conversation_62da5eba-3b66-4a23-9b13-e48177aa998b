import {default as React, useCallback, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {startCase} from 'lodash';

import { getTheme } from '../../utils/themeSelector';
import {MaterialCommunityIcons} from 'apptile-core';
import {ApptileWebIcon} from '../../icons/ApptileWebIcon.web';
import CodeInputControl from './CodeInputControl';
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();


const theme = getTheme();

interface RadioGroupControlProps {
  value: string;
  label: string;
  defaultValue?: string;
  onChange: (value: string) => void;
  options?: string[] | {iconType?: string; icon?: string; text: string; value: string}[];
  disableBinding?: boolean;
  allowDeselect?: boolean;
}

const RadioGroupControl: React.FC<RadioGroupControlProps> = props => {
  const {value, label, options, disableBinding, allowDeselect, defaultValue, onChange} = props;

  const onValueChange = useCallback(
    newVal => {
      if (allowDeselect && newVal == value) onChange('');
      else onChange(newVal);
    },
    [allowDeselect, onChange, value],
  );

  const data =
    options &&
    options.map(val => {
      if (typeof val === 'string') return {icon: null, text: startCase(val), value: val};
      return val;
    });

  const [isBinding, setIsBinding] = useState(
    (!disableBinding &&
      value &&
      (`${value}`.startsWith('{{') || !data?.find(item => item.value === value || item.value === defaultValue))) ||
      false,
  );

  return (
    <View style={styles.container}>
      {label && (
        <View style={commonStyles.labelContainer}>
          {!disableBinding && !isBinding && (
            <Pressable onPress={() => setIsBinding(true)}>
              <MaterialCommunityIcons name="flash" size={18} />
            </Pressable>
          )}
          {!disableBinding && isBinding && (
            <Pressable onPress={() => setIsBinding(false)}>
              <MaterialCommunityIcons name="flash-off" size={18} />
            </Pressable>
          )}
          <Text style={commonStyles.labelText}>{label}</Text>
        </View>
      )}
      {!isBinding && (
        <View
          style={[
            styles.multiToggleWrapper,
            commonStyles.input,
            label ? commonStyles.inputContainer : {width: '100%'},
          ]}>
          {data &&
            data.map(item => {
              const isActive = item.value === value || item.value === defaultValue;
              return (
                <Pressable
                  key={item.value}
                  style={[styles.toggleButton, isActive && styles.toggleButtonActive]}
                  onPress={() => onValueChange(item.value)}>
                  {item.icon &&
                    (item?.iconType == 'apptile' ? (
                      <ApptileWebIcon
                        name={item.icon}
                        color={isActive ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR}
                        size={18}
                      />
                    ) : (
                      <MaterialCommunityIcons
                        name={item.icon}
                        color={isActive ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR}
                        size={18}
                      />
                    ))}
                  {item.text && (
                    <Text style={[commonStyles.inputText, isActive && styles.activeText]}>{item.text}</Text>
                  )}
                </Pressable>
              );
            })}
        </View>
      )}
      {isBinding && (
        <View style={[styles.multiToggleWrapper, label ? commonStyles.inputContainer : {width: '100%'}, {padding: 0}]}>
          <CodeInputControl
            defaultValue={defaultValue}
            value={value}
            placeholder="{{item.value}}"
            onChange={(val: string) => onValueChange(val)}
          />
        </View>
      )}
    </View>
  );
};

export default RadioGroupControl;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  multiToggleWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 4,
    gap: 12,
  },
  toggleButton: {
    padding: 4,
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'row',
    flexBasis: 'auto',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  toggleButtonActive: {
    backgroundColor: '#FFFFFF',
    borderRadius: theme.INPUT_BORDER_RADIUS,
    boxShadow: '0px 2px 2px rgba(0, 0, 0, 0.25)',
    textAlign: 'center',
  },
  activeText: {
    color: theme.CONTROL_ACTIVE_COLOR,
    fontWeight: theme.FONT_WEIGHT_BOLD,
  },
});
