import React, {useEffect} from 'react';
import {MaterialCommunityIcons} from 'apptile-core';
import {<PERSON>, Hu<PERSON>, Pointer, Saturation} from '@uiw/react-color';
import {View, StyleSheet, Text, useColorScheme, Pressable} from 'react-native';
import tinycolor, {ColorInput} from 'tinycolor2';
import {useSelector} from 'react-redux';
import {baseGlobalThemeConfig} from 'apptile-core';
import {selectAppConfig} from 'apptile-core';
import {ApptileThemeConfigParams} from 'apptile-core';
import {getTheme} from '../../../../utils/themeSelector';
import CodeInputControl from '../../CodeInputControl';
import {useTheme} from 'apptile-core';
import CodeInputControlV2 from '../../../controls-v2/CodeInputControl';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();

const commonTheme = getTheme();
interface ColorPickerProp {
  onSwatchColorChange: any;
  onThemeColorChange: any;
  color: string;
  value: string;
  setPopAsActive: any;
  inTheme?: boolean;
  heading?: string;
}

const reactColorStyles = {
  editableInput: {
    height: 34,
    paddingLeft: 10,
    borderRadius: 6,
    backgroundColor: '#FFFFFF',
    border: 'none',
    color: '#535353',
  },
  HSPicker: {
    width: '256px',
    height: '16px',
  },
};

const MyPicker = (props: ColorPickerProp) => {
  const {themeEvaluator} = useTheme();
  //to get dark or white
  const osColorScheme = useColorScheme() ?? 'light';

  //Picking color list from the theme
  const appConfig = useSelector(selectAppConfig);
  let theme = (appConfig?.get('theme')?.toJS() ?? baseGlobalThemeConfig) as ApptileThemeConfigParams;

  //getting the preset from the list then picking first 5 from the list.
  const presetColors = theme.colors[theme.isDarkModeSupported ? osColorScheme : 'light'];

  const {onSwatchColorChange, onThemeColorChange, color, value, setPopAsActive, inTheme, heading} = props;

  const [draftColor, setDraftColor] = React.useState(value);

  const onInputColorChange = (selectedValue: string) => {
    setDraftColor(selectedValue);
    if (selectedValue == '') onThemeColorChange(selectedValue);
    else {
      if (
        themeEvaluator(selectedValue) != selectedValue ||
        (selectedValue.startsWith('#') && selectedValue.length >= 4)
      )
        onThemeColorChange(selectedValue);
    }
  };

  const finalColor = tinycolor(color);

  return (
    <View style={styles.root}>
      <Pressable style={styles.closeButton} onPress={() => setPopAsActive(false)}>
        <Text style={commonStyles.baseText}>{heading ?? 'Color Picker'}</Text>
        <MaterialCommunityIcons name={'close'} size={16} color={commonTheme.CONTROL_PLACEHOLDER_COLOR} />
      </Pressable>
      <View style={styles.saturation}>
        <Saturation
          hsva={finalColor.toHsv()}
          radius="8px 8px 8px 8px"
          style={{
            width: '100%',
            height: 200,
          }}
          pointer={() => (
            <View
              style={[
                {
                  left: finalColor.toHsv().s * 100 + '%',
                  top: 100 - finalColor.toHsv().v * 100 + '%',
                },
                styles.saturationPointer,
                {
                  transform: 'translate( -7.5px, -7.5px)',
                },
              ]}
            />
          )}
          onChange={value => {
            setDraftColor('#' + tinycolor(value).toHex() + 'ff');
            onSwatchColorChange({rgb: {...tinycolor(value).toRgb(), ...{a: 100}}});
          }}
        />
      </View>
      <View style={styles.hue}>
        <Hue
          width={reactColorStyles.HSPicker.width}
          height={reactColorStyles.HSPicker.height}
          color={color}
          onChange={value => {
            const newColor = finalColor.toHsv();
            newColor.h = value.h;
            setDraftColor('#' + tinycolor(newColor).toHex() + 'ff');
            onSwatchColorChange({rgb: {...tinycolor(newColor).toRgb(), ...{a: 100}}});
          }}
          hue={finalColor.toHsv().h}
        />
      </View>
      <View style={styles.hue}>
        <Alpha
          width={reactColorStyles.HSPicker.width}
          height={reactColorStyles.HSPicker.height}
          hsva={finalColor.toHsv()}
          onChange={value => {
            const newColor = finalColor.toHsv();
            newColor.a = value.a;
            setDraftColor(tinycolor(newColor).toHex8String());
            onSwatchColorChange({rgb: tinycolor(newColor).toRgb()});
          }}
        />
      </View>
      <View style={styles.hexInputContainer}>
        <Text style={[commonStyles.baseText, commonStyles.labelContainer]}>Hex Code</Text>
        <View style={{flex: 1}}>
          <CodeInputControlV2 singleLine={true} value={draftColor} onChange={onInputColorChange} />
        </View>
      </View>
      {!inTheme && (
        <>
          <View style={styles.divider} />
          <View style={styles.circlePicker}>
            {Object.keys(presetColors)
              .slice(0, 6)
              .map(e => (
                <Pressable
                  key={e}
                  style={[
                    {
                      backgroundColor: tinycolor(presetColors[e]).toHex8String().toLowerCase(),
                    },
                    styles.circlePickerCard,
                    `colors.${e}` == draftColor && {
                      borderColor: commonTheme.PRIMARY_COLOR,
                    },
                  ]}
                  onPress={() => {
                    onInputColorChange(`colors.${e}`);
                  }}>
                  <Text
                    style={[
                      {color: '#c3c3c385'},
                      `colors.${e}` == draftColor && {
                        color: commonTheme.PRIMARY_COLOR,
                      },
                      `colors.${e}` == draftColor && styles.circlePickerSelectedCard,
                    ]}>
                    {e.startsWith('on') ? `o${e[2]}` : e[0].toUpperCase()}
                  </Text>
                </Pressable>
              ))}
          </View>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    backgroundColor: commonTheme.INPUT_BACKGROUND,
    borderColor: commonTheme.INPUT_BORDER,
    flex: 1,
    borderRadius: 8,
    borderWidth: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 12,
    position: 'relative',
  },
  closeButton: {
    marginBottom: 8,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  hexInputContainer: {
    width: '100%',
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  saturation: {
    marginVertical: 8,
    overflow: 'hidden',
    borderRadius: 8,
    width: '100%',
  },
  themeColorText: {
    flex: 1,
  },
  hue: {
    marginVertical: 8,
    overflow: 'hidden',
    borderRadius: 32,
    borderWidth: 1,
    borderColor: commonTheme.INPUT_BORDER,
  },
  circlePicker: {
    marginVertical: 8,
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
  },
  circlePickerCard: {
    borderWidth: 1.5,
    borderColor: commonTheme.INPUT_BORDER,
    borderRadius: 50,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  circlePickerSelectedCard: {
    borderRadius: 50,
    backgroundColor: commonTheme.INPUT_BACKGROUND,
    borderWidth: 1,
    padding: 3,
    minWidth: 25,
    minHeight: 25,
    textAlign: 'center',
  },
  divider: {
    backgroundColor: commonTheme.INPUT_BORDER,
    height: 1,
    marginTop: 20,
    marginBottom: 15,
    width: '100%',
  },
  saturationPointer: {
    height: 15,
    width: 15,
    backgroundColor: commonTheme.INPUT_BACKGROUND,
    borderColor: commonTheme.INPUT_BORDER,
    borderWidth: 1,
    borderRadius: 50,
    position: 'absolute',
  },
});

export default MyPicker;
