import React from 'react';
import {View, Text, StyleSheet, TextInput, Pressable} from 'react-native';
import {useTheme} from 'apptile-core';
import tinycolor, {ColorInput} from 'tinycolor2';
import CollapsiblePanel from '../../../CollapsiblePanel';
import {getTheme} from '../../../../utils/themeSelector';

import ApptileColorPicker from './apptileColorPicker';
import PopoverComponent from '@/root/web/components-v2/base/Popover';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();
const theme = getTheme();
interface ColorPickerProp {
  label: string;
  value: string;
  defaultValue?: string;
  placeholder?: string;
  name: string;
  onChange: (key: string, value: string) => void;
  inTheme?: boolean;
}

export const ColorPicker: React.FC<ColorPickerProp> = props => {
  const {themeEvaluator} = useTheme();

  const [isPopActive, setPopAsActive] = React.useState(false);
  const [draftColor, setDraftColor] = React.useState(tinycolor(props.value));
  const [currentColor, setCurrentColor] = React.useState('');

  React.useEffect(() => {
    setCurrentColor(props.value);
    setDraftColor(tinycolor(themeEvaluator(props.value)));
  }, [props.defaultValue, props.value, themeEvaluator]);

  //for "color picker" color change
  const onSwatchColorChange = (selectedColor: any) => {
    const color = tinycolor(selectedColor.rgb);

    setDraftColor(color);
    props.onChange(props.name, color.getAlpha() === 1 ? color.toHexString() : color.toHex8String());
  };

  //for Input color change
  const onThemeColorChange = (selectedValue: any) => {
    setDraftColor(tinycolor(themeEvaluator(selectedValue) as ColorInput));
    props.onChange(props.name, selectedValue);
  };

  return (
    <View>
      <View style={[styles.colorPickerContainer, {zIndex: isPopActive ? 1 : 0}]}>
        {props.label && (
          <View style={[commonStyles.labelContainer]}>
            <Text style={commonStyles.labelText}>{props.label}</Text>
          </View>
        )}
        <View style={[commonStyles.inputContainer, !props.label && {width: '100%'}]}>
          <PopoverComponent
            trigger={
              <View style={[styles.colorInputContainer, commonStyles.input]}>
                <View
                  style={[
                    styles.colorSwatch,
                    {
                      backgroundColor: draftColor.toHex8String(),
                    },
                  ]}
                />
                <Text style={commonStyles.inputText}>{draftColor.toHex8String()}</Text>
              </View>
            }
            visible={isPopActive}
            onVisibleChange={setPopAsActive}>
            <View style={styles.sketchPickerLayout}>
              <ApptileColorPicker
                inTheme={props.inTheme}
                setPopAsActive={setPopAsActive}
                color={draftColor.toHex8String()}
                onSwatchColorChange={onSwatchColorChange}
                onThemeColorChange={onThemeColorChange}
                value={props.value}
              />
            </View>
          </PopoverComponent>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  colorInputContainer: {
    flexDirection: 'row',
    padding: 4,
    borderWidth: 1,
    borderColor: '#0004',
    alignItems: 'center',
  },
  sketchPickerLayout: {
    flex: 1,
    marginRight: 15,
  },
  colorPickerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginRight: 4,
    alignItems: 'center',
    width: '100%',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  colorSwatch: {
    width: 23,
    height: 23,
    borderWidth: 1,
    borderColor: '#86868680',
    borderRadius: 4,
    marginRight: 12,
  },
  colorInput: {
    outlineStyle: 'none',
    fontSize: 10,
    color: '#535353',
    marginLeft: 5,
  },
});
