import React from 'react';
import {StyleSheet, Text, View, TextInput} from 'react-native';
import {toRespectiveValue} from 'apptile-core';
import {getTheme} from '@/root/web/utils/themeSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

type IQuadValue = {name: string; value: string};

export interface QuadPickerProps {
  value: [IQuadValue, IQuadValue, IQuadValue, IQuadValue, IQuadValue]; //! fixed length 5, first field is master
  label: string;
  defaultValue?: string;
  placeholder?: string;
  onChange: (key: string, value: number | string) => void;
  layout?: 'plus' | 'square';
}

export const QuadPicker: React.FC<QuadPickerProps> = ({value, defaultValue, label, placeholder, onChange, layout}) => {
  const onValueChange = React.useCallback(
    (qkey: string, qvalue: string) => {
      onChange(qkey, qvalue === '' ? qvalue : toRespectiveValue(qvalue));
    },
    [onChange],
  );

  if (layout === 'square') {
    return (
      <View style={styles.container}>
        <View style={[commonStyles.labelContainer]}>
          <Text style={commonStyles.labelText}>{label}</Text>
        </View>
        <View style={styles.quadContainer}>
          <View style={styles.rowContainer}>
            <InputEl
              value={value[1].value}
              defaultValue={defaultValue}
              placeholder={placeholder}
              name={value[1].name}
              onChange={onValueChange}
            />
            <InputEl
              value={value[2].value}
              defaultValue={defaultValue}
              placeholder={placeholder}
              name={value[2].name}
              onChange={onValueChange}
            />
          </View>
          <View style={styles.rowContainer}>
            <InputEl
              value={value[0].value}
              defaultValue={defaultValue}
              placeholder={placeholder}
              name={value[0].name}
              onChange={onValueChange}
            />
          </View>
          <View style={styles.rowContainer}>
            <InputEl
              value={value[4].value}
              defaultValue={defaultValue}
              placeholder={placeholder}
              name={value[4].name}
              onChange={onValueChange}
            />
            <InputEl
              value={value[3].value}
              defaultValue={defaultValue}
              placeholder={placeholder}
              name={value[3].name}
              onChange={onValueChange}
            />
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[commonStyles.labelContainer]}>
        <Text style={commonStyles.labelText}>{label}</Text>
      </View>
      <View style={styles.quadContainer}>
        <InputEl
          value={value[1].value}
          defaultValue={defaultValue}
          placeholder={placeholder}
          name={value[1].name}
          onChange={onValueChange}
        />
        <View style={styles.rowContainer}>
          <InputEl
            value={value[4].value}
            defaultValue={defaultValue}
            placeholder={placeholder}
            name={value[4].name}
            onChange={onValueChange}
          />
          <InputEl
            value={value[0].value}
            defaultValue={defaultValue}
            placeholder={placeholder}
            name={value[0].name}
            onChange={onValueChange}
          />
          <InputEl
            value={value[2].value}
            defaultValue={defaultValue}
            placeholder={placeholder}
            name={value[2].name}
            onChange={onValueChange}
          />
        </View>
        <InputEl
          value={value[3].value}
          defaultValue={defaultValue}
          placeholder={placeholder}
          name={value[3].name}
          onChange={onValueChange}
        />
      </View>
    </View>
  );
};

interface InputElProps {
  name: string;
  value: string;
  defaultValue?: string;
  placeholder?: string;
  onChange: (key: string, value: string) => void;
}

const InputEl: React.FC<InputElProps> = props => {
  const [value, styleValue] = React.useState('');

  React.useEffect(() => {
    styleValue(props.value);
  }, [props.value]);

  return (
    <View style={styles.inputContainer}>
      <TextInput
        style={styles.input}
        defaultValue={value || props.defaultValue}
        placeholder={props.placeholder || '0'}
        onChangeText={(value: string) => {
          props.onChange(props.name, value);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    zIndex: -1,
    marginHorizontal: 6,
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  quadContainer: {
    flex: 1,
    marginVertical: 10,
    alignItems: 'center',
  },
  rowContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  labelText: {
    marginHorizontal: 10,
    textAlign: 'center',
    textTransform: 'capitalize',
    fontSize: theme.FONT_SIZE,
    lineHeight: theme.LINE_HEIGHT,
    color: theme.FONT_COLOR,
  },
  labelContainer: {
    width: '100%',
    alignItems: 'flex-start',
  },
  inputContainer: {
    width: 55,
    height: 32,
    margin: 5,
    overflow: 'hidden',
    alignItems: 'center',
    backgroundColor: '#fff',
    justifyContent: 'center',
    borderRadius: 5,
  },
  input: {
    width: '100%',
    height: '100%',
    textAlign: 'center',
  },
});
