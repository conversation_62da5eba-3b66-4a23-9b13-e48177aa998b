import {EditorRootState} from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import React, {useEffect, useState} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import Button from '../../../components-v2/base/Button';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {connect, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {MaterialCommunityIcons, apptileStateSelector} from 'apptile-core';
import {fetchAppAssets, fetchAppAssetsLoadMore, saveImageRecord} from '../../../actions/editorActions';
import Modal from '@/root/web/components-v2/base/Modal';
import cloudinaryUploader from './cloudinaryUploader';
import CloudinaryLibrary from './cloudinaryLibrary';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


export interface PIAssetEditorProps<ConfigType = 'assetEditor'> extends EditorProps {
  showDialog: boolean;
  onCloseDialog: (show: boolean) => void;
  onChooseVideo: (videoUrl: string) => void;
  label?: string;
}

const AssetChooseDialog: React.FC<PIAssetEditorProps> = props => {
  const {showDialog, onCloseDialog, onChooseVideo} = props;
  const [selectedVideo, setSelectedVideo] = useState<string>('');
  const disableButtons = !selectedVideo;
  const [headerText, setHeaderText] = useState('Video library');
  const appId = useSelector(apptileStateSelector)?.appId;
  const cloudinaryUploaderRef = cloudinaryUploader(appId as string);

  useEffect(() => {
    setHeaderText('Video Library');
  }, [setHeaderText]);

  return (
    <Modal
      onVisibleChange={(visible: boolean) => {
        onCloseDialog(visible);
      }}
      visible={showDialog}
      disableOutsideClick={true}
      content={
        <View style={[theme.modal, styles.dialogStyle]}>
          <View>
            <View style={styles.modalTitleBar}>
              <Text style={[commonStyles.heading, styles.modalTitle]}>{headerText}</Text>
              <TouchableOpacity onPress={() => onCloseDialog(false)} style={[styles.closeButton]}>
                <MaterialCommunityIcons name="close" size={16} />
              </TouchableOpacity>
            </View>

            <CloudinaryLibrary selectedVideo={selectedVideo} setSelectedVideo={setSelectedVideo} />
          </View>

          <View style={styles.dialogFooter}>
            <Button
              variant="PILL"
              color="SECONDARY"
              size="MEDIUM"
              onPress={() => {
                cloudinaryUploaderRef.open();
              }}>
              + Upload
            </Button>

            <Button
              size="MEDIUM"
              onPress={() => onChooseVideo(selectedVideo)}
              containerStyles={styles.commonButtonStyles}
              disabled={disableButtons}>
              Select video
            </Button>
          </View>
        </View>
      }
    />
  );
};

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {
    ...bindActionCreators(
      {
        fetchAppAssets,
        fetchAppAssetsLoadMore,
        saveImageRecord,
      },
      dispatch,
    ),
  };
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    apptileState: state.apptile,
    assetState: state.asset,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(AssetChooseDialog);

const styles = StyleSheet.create({
  modalTitleBar: {
    flexShrink: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  commonButtonStyles: {
    width: 150,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: theme.FONT_WEIGHT_BOLD,
    lineHeight: 28,
  },
  cropButtonsContainer: {
    flexDirection: 'row',
    gap: 10,
  },
  dialogStyle: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    width: 720,
    minWidth: 500,
    height: 435,
    minHeight: 400,
    backgroundColor: '#fff',
    padding: 32,
    shadowColor: '#000',
    shadowOffset: {width: 1, height: 2},
    shadowRadius: 4,
    shadowOpacity: 0.3,
    overflow: 'hidden',
  },
  closeButton: {
    alignSelf: 'flex-end',
    flexBasis: 'auto',
    flexGrow: 1,
    flexShrink: 0,
    marginLeft: 'auto',
  },
  dialogBody: {
    flex: 1,
    flexBasis: 'auto',
    flexDirection: 'column',
    backgroundColor: '#fff',
    height: 324,
    overflow: 'scroll',
  },
  dialogFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    height: 32,
  },
  tabWrapper: {flex: 1, flexDirection: 'row'},
  tabTitle: {
    padding: 10,
  },
  activeTabTitle: {
    borderBottomColor: '#2196f3',
    borderBottomWidth: 2,
  },
  codeInputStyle: {
    flex: 4,
    borderRightWidth: 1,
    borderRadius: 5,
    borderBottomRightRadius: 0,
    borderTopRightRadius: 0,
    borderColor: '#ccc',
    overflow: 'hidden',
    zIndex: -1,
  },
  container: {
    padding: 8,
    flex: 1,
    flexDirection: 'row',
  },
  uploadbtn: {
    padding: 6,
    backgroundColor: '#2196f3',
    color: '#fff',
    flex: 1,
  },
  uploadButton: {
    elevation: 8,
    backgroundColor: 'black',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 30,
  },
  buttonText: {
    color: 'white',
  },
  cropButton: {
    fontWeight: '500',
  },
});
