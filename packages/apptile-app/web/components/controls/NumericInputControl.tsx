import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import theme from '@/root/web/styles-v2/theme';
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();

type NumericInputProps = {
  value: string;
  label?: string;
  defaultValue?: string;
  placeholder?: string;
  unit?: 'dp' | '%';
  noUnit?: boolean;
  onChange: (value: string) => void;
};

const NumericInputControl: React.FC<NumericInputProps> = ({
  label,
  value,
  defaultValue,
  unit,
  noUnit = false,
  onChange,
}) => {
  const [currentValue, setCurrentValue] = React.useState('');
  const [currentUnit, setCurrentUnit] = React.useState('');

  React.useEffect(() => {
    setCurrentValue(value ?? defaultValue);
    setCurrentUnit(unit ?? 'dp');
  }, [defaultValue, unit, value]);

  React.useEffect(() => {
    setCurrentUnit(checkUnit(`${currentValue}`));
  }, [currentValue]);

  const onChangeHandler = (val: string) => {
    const constructedVal = `${val}${!noUnit && currentUnit === '%' ? '%' : ''}`;
    setCurrentValue(constructedVal);
    onChange(constructedVal);
  };

  const onUnitChangeHandler = (val: 'dp' | '%') => {
    setCurrentUnit(val);
    onChange(`${pickValue(`${currentValue}`, val)}${val === '%' ? '%' : ''}`);
  };

  return (
    <View style={styles.container}>
      <View style={styles.rowLayout}>
        {label && <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>}
        <View
          style={[
            styles.rowLayout,
            label ? commonStyles.inputContainer : {width: '100%'},
            commonStyles.input,
            {overflow: 'hidden', paddingHorizontal: 4},
          ]}>
          <input
            type="number"
            value={pickValue(`${currentValue}`, currentUnit)}
            onChange={e => onChangeHandler(e.target.value)}
            placeholder="0"
            style={{
              ...webStyle.input,
              ...(noUnit ? {flex: 'none', width: '120%'} : {}),
            }}
          />
          {!noUnit && (
            <select
              style={webStyle.select}
              value={currentUnit}
              onChange={e => {
                onUnitChangeHandler(e.target.value as 'dp' | '%');
              }}>
              <option value="dp">dp</option>
              <option value="%">%</option>
            </select>
          )}
        </View>
      </View>
    </View>
  );
};

const checkUnit = (val: string) => {
  if (val?.includes('%')) {
    return '%';
  }
  return 'dp';
};

const pickValue = (val: string, unit: '%' | 'dp') => {
  if (unit === '%') {
    return val.split('%')[0];
  }
  return val?.includes('%') ? val.split('%')[0] : val;
};

const webStyle = {
  input: {
    width: '78%',
    border: 'none',
    padding: 8,
    borderRadius: theme.INPUT_BORDER_RADIUS,
    backgroundColor: theme.INPUT_BACKGROUND,
    outline: 'none',
  },
  select: {
    width: '25%',
    padding: 2,
    borderRadius: 4,
    borderColor: '#ccc',
    position: 'absolute',
    right: 4,
    top: 4,
  },
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
  },
});

export default NumericInputControl;
