import React, {useCallback, useRef} from 'react';
import {StyleSheet, Text, View, FlatList, Pressable} from 'react-native';
import Fuse from 'fuse.js';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CustomIconContext } from 'apptile-core';
import TextInput from '@/root/web/components-v2/base/TextInput';
import _ from 'lodash';
import PopoverComponent from '../../components-v2/base/Popover';
import theme from '../../styles-v2/theme';
import {iconList, Icon} from 'apptile-core';
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();

interface IconChooserControlProps {
  value: string;
  label: string;
  defaultValue?: string;
  placeholder?: string;
  iconTypeProp?: string;
  onChange: (value: string) => void;
  onCustomPropChange: (key: string, value: string) => void;
  config: Immutable.Map<string, any> | Immutable.Record<any>;
  name: string;
  CustomIcon?: any;
}

const FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  findAllMatches: true,
  minMatchCharLength: 1,
  ignoreLocation: true,
  keys: ['name'],
};

const IconChooserControl: React.FC<IconChooserControlProps> = ({
  value,
  label,
  iconTypeProp,
  onChange,
  onCustomPropChange,
  onBulkValueChange,
  config,
  CustomIcon,
}) => {
  const iconType = _.get(config, 'iconType');

  const [fuseSearch, setFuseSearch] = React.useState<Fuse<{type: string; name: string}>>(new Fuse([], FUSE_OPTIONS));
  const [searchTerm, setSearchTerm] = React.useState('');
  const [showPopover, setShowPopover] = React.useState(false);
  const [allIcons, setAllIcons] = React.useState(iconList);
  const totalItemsToShow = 25;
  const [filteredItems, setFilteredItems] = React.useState(allIcons.slice(0, totalItemsToShow));

  const onValueChange = useCallback(
    (type: string, name: string) => {
      onChange?.(name);
      onCustomPropChange?.(iconTypeProp ? iconTypeProp : 'iconType', type);
      if (onBulkValueChange) onBulkValueChange({iconName: name, iconType: type});
    },
    [onBulkValueChange, onChange, onCustomPropChange],
  );

  React.useEffect(() => {
    const icons = [
      ...Object.keys(CustomIcon?.getRawGlyphMap() ?? {})?.map(i => ({type: 'Custom Icon', name: i})),
      ...iconList,
    ];
    setAllIcons(icons);
    setFuseSearch(new Fuse(icons, FUSE_OPTIONS));
  }, [CustomIcon]);

  React.useEffect(() => {
    if (!searchTerm) {
      setFilteredItems(allIcons.slice(0, totalItemsToShow));
    } else {
      setFilteredItems(fuseSearch.search(searchTerm, {limit: totalItemsToShow}).map(e => e.item));
    }
  }, [allIcons, fuseSearch, searchTerm]);

  React.useEffect(() => {
    setSearchTerm(value);
  }, [value]);

  const onIconSetHandler = (type: string, name: string) => {
    setShowPopover(false);
    if (name !== value || iconType !== type) onValueChange(type, name);
    else setSearchTerm(value);
  };
  const buttonRef = useRef(null);
  const containerRef = useRef(null);

  // <Pressable onPress={() => setShowPopover(!showPopover)} style={[{flexDirection: 'row'}]}>
  // </Pressable>
  return (
    <>
      <View ref={containerRef} style={styles.container}>
        {label && (
          <View style={commonStyles.labelContainer}>
            <Text style={commonStyles.labelText}>{label}</Text>
          </View>
        )}
        <View style={[label ? commonStyles.inputContainer : {width: '100%'}]}>
          <PopoverComponent
            visible={showPopover}
            onVisibleChange={setShowPopover}
            positions={['bottom', 'left']}
            trigger={
              <View
                ref={buttonRef}
                style={[
                  {
                    flexDirection: 'row',
                    alignItems: 'center',
                    padding: showPopover ? 7 : 8,
                    borderWidth: showPopover ? 1 : 0,
                    borderColor: theme.INPUT_BORDER,
                    justifyContent: 'space-between',
                  },
                  commonStyles.input,
                ]}>
                <View style={{flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', width: '90%'}}>
                  <Text>
                    {iconType === 'Custom Icon' && CustomIcon ? (
                      <CustomIcon name={value} size={19} color={theme.CONTROL_INPUT_COLOR} />
                    ) : (
                      <Icon iconType={iconType} name={value} size={19} color={theme.CONTROL_INPUT_COLOR} />
                    )}
                  </Text>
                  <Text style={[commonStyles.baseText, {paddingHorizontal: 5}]}>
                    {_.startCase(value || 'Please Search A Icon')}
                  </Text>
                </View>
                <Text style={{width: '10%'}}>
                  <MaterialCommunityIcons
                    name={showPopover ? 'chevron-up' : 'chevron-down'}
                    size={14}
                    color={theme.SECONDARY_COLOR}
                  />
                </Text>
              </View>
            }>
            <View
              style={[
                styles.iconOptions,
                buttonRef?.current && {width: buttonRef?.current?.getBoundingClientRect()?.width},
                containerRef?.current && {
                  maxHeight: Math.min(170, window.innerHeight - containerRef?.current?.getBoundingClientRect()?.bottom),
                },
              ]}>
              <TextInput
                containerStyle={styles.flex1}
                value={searchTerm}
                placeholder="Search..."
                iconPosition={'LEFT'}
                icon={'magnify'}
                iconSize={'20'}
                onBlur={() => {
                  if (!searchTerm) onValueChange('Material Icon', '');
                }}
                onChange={e => {
                  setSearchTerm(e.target.value);
                }}
              />
              <FlatList
                style={styles.flatList}
                data={filteredItems}
                keyExtractor={(_, idx) => `${idx}`}
                showsVerticalScrollIndicator={false}
                renderItem={e => (
                  <Pressable
                    style={[styles.iconPreviewItem]}
                    onPress={() => onIconSetHandler(e.item.type, e.item.name)}>
                    {e.item.type === 'Custom Icon' && CustomIcon ? (
                      <CustomIcon name={e.item.name} size={20} color="#2a2a2a" />
                    ) : (
                      <Icon iconType={e.item.type} name={e.item.name} size={20} color="#2a2a2a" />
                    )}
                    <Text style={[styles.iconPreviewItemText]}>{e.item.name}</Text>
                  </Pressable>
                )}
              />
            </View>
          </PopoverComponent>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  flex1: {
    flex: 1,
  },
  container: {
    flex: 1,
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 12,
  },
  closeIcon: {
    width: 18,
    height: 18,
    borderRadius: 18,
    margin: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconPreviewItem: {
    padding: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconPreviewItemText: {
    fontFamily: "'Work Sans', sans-serif",
    color: '#191919',
    margin: 4,
  },
  iconOptions: {
    width: 170,
    maxHeight: 170,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    borderRadius: 9,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    marginTop: 5,
    padding: 5,
  },
});

function CustomIconConsumer(props: any) {
  return (
    <CustomIconContext.Consumer>
      {Icon => <IconChooserControl {...props} CustomIcon={Icon} />}
    </CustomIconContext.Consumer>
  );
}

export default CustomIconConsumer;
