import React, {useCallback, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {ApptileWebIcon} from '../../icons/ApptileWebIcon.web';
import RadioGroupControlV2 from '../controls-v2/RadioGroupControl';
import {getTheme} from '@/root/web/utils/themeSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

type AlignmentInputProps = {
  value: string;
  defaultValue: string;
  name: string;
  label: string;
  config: Immutable.Map<string, string>;
  onChange: (value: boolean) => void;
  onCustomPropChange: (key: string, value: string, debounced: boolean, remove?: boolean) => void;
};

//For now it only supports text alignment
const AlignmentControl: React.FC<AlignmentInputProps> = props => {
  const {defaultValue, label, onCustomPropChange, name, config, value} = props;

  return (
    <RadioGroupControlV2
      activeColor={theme.PRIMARY_COLOR}
      size={'SMALL'}
      options={[
        {
          iconType: 'apptile',
          icon: `alignment-${name === 'horizontalAlign' ? 'left' : 'top'}`,
          text: '',
          value: `${name === 'horizontalAlign' ? 'left' : 'top'}`,
        },
        {
          iconType: 'apptile',
          icon: `alignment-${name === 'horizontalAlign' ? 'center' : 'middle'}`,
          text: '',
          value: 'center',
        },
        {
          iconType: 'apptile',
          icon: `alignment-${name === 'horizontalAlign' ? 'right' : 'bottom'}`,
          text: '',
          value: `${name === 'horizontalAlign' ? 'right' : 'bottom'}`,
        },
      ]}
      disableBinding={true}
      label={label}
      value={value}
      onChange={function (value: string): void {
        onCustomPropChange(name, value, false);
      }}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  pressableContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selected: {
    backgroundColor: theme.PRIMARY_BACKGROUND,
  },
  labelText: {
    fontFamily: theme.FONT_FAMILY,
    marginBottom: 5,
  },

  general: {
    margin: 2,
    borderRadius: 3,
    padding: 5,
  },

  marginStart: {
    marginLeft: 0,
  },
});

export default AlignmentControl;
