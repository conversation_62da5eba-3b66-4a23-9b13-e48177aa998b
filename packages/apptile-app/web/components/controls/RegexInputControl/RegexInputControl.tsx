import React, {useCallback} from 'react';
import {StyleSheet, Text, View, Pressable} from 'react-native';
import CodeInput from '../../codeEditor/codeInput';
import {MaterialCommunityIcons} from 'apptile-core';
import {RegexList} from './RegexList';
import {uniq} from 'lodash';
import Fuse from 'fuse.js';
import {pluginConfigValueSetRaw} from 'apptile-core';
import {bindActionCreators} from 'redux';
import {connect} from 'react-redux';
import {getTheme} from '@/root/web/utils/themeSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

const FUSE_OPTIONS = {
  isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  findAllMatches: true,
  minMatchCharLength: 1,
  ignoreLocation: true,
  keys: ['label'],
};

interface RegexStruct {
  label: string;
  expression: RegExp;
}

interface RegexInputControlProps {
  pluginId: string;
  pageId: string;
  name: string;
  value: RegexStruct[];
  label: string;
  placeholder?: string | number;
  showRecommendation?: boolean;
  regexLabel?: string;
  onChange: (value: string) => void;
  setRawValue: typeof pluginConfigValueSetRaw;
}

const RegexInputControl: React.FC<RegexInputControlProps> = ({
  pluginId,
  pageId,
  value,
  name,
  label,
  placeholder,
  showRecommendation = true,
  regexLabel,
  setRawValue,
}) => {
  const [regex, setRegex] = React.useState('');
  const [regexName, setRegexName] = React.useState('');
  const [flag, setFlag] = React.useState('');
  const [fuseSearch, setFuseSearch] = React.useState(new Fuse(showRecommendation ? RegexList : [], FUSE_OPTIONS));
  const [filteredItems, setFilteredItems] = React.useState(fuseSearch?.search(regex));

  const regexArr = React.useMemo(() => (value ? value : []), [value]);

  const onValueChange = useCallback(
    newVal => {
      setRawValue(pluginId, pageId, name, newVal);
    },
    [name, pageId, setRawValue, pluginId],
  );

  React.useEffect(() => {
    if (showRecommendation) {
      const regexListWOSelectedItem = RegexList.filter(
        entryFromList =>
          !regexArr.some(entryFromRegArr => String(entryFromRegArr.expression) === String(entryFromList.expression)),
      );

      setFuseSearch(new Fuse(regexListWOSelectedItem, FUSE_OPTIONS));
    }
  }, [regexArr, showRecommendation]);

  React.useEffect(() => {
    setFilteredItems(fuseSearch.search(regex, {limit: 10}));
  }, [fuseSearch, regex]);

  const onDeleteHandler = (deleteIdx: number) => {
    onValueChange(regexArr.filter((_val, idx) => idx !== deleteIdx));
  };

  const onAddHandler = (regexLabelInput: string, regexInput: RegExp | string) => {
    if (regexInput === '' || regexLabelInput === '') return;
    const filteredFlag = uniq(flag.split('').filter(entry => flagsList.includes(entry))).join('');
    onValueChange([
      ...regexArr,
      {
        label: regexLabelInput,
        [regexLabel || 'label']: regexLabelInput,
        expression: new RegExp(regexInput, filteredFlag),
        expressionRaw: {regex: regexInput, flags: filteredFlag},
      },
    ]);
    setRegex('');
    setRegexName('');
    setFlag('');
  };

  const flagsList = ['i', 'g', 'm', 's', 'u', 'y', 'd'];

  return (
    <View style={styles.container}>
      <View style={[{alignSelf: 'flex-start'}, commonStyles.labelContainer]}>
        <Text style={commonStyles.labelText}>{label}</Text>
      </View>
      <View style={[commonStyles.inputContainer]}>
        <View style={[styles.CodeInputStyle]}>
          <CodeInput
            value={regexName}
            placeholder={regexLabel || 'label'}
            onChange={(_editor: unknown, _data: unknown, val: string) => setRegexName(val)}
          />
        </View>
        <View style={[styles.rowLayout, {marginTop: 4}]}>
          <View style={styles.rowLayout}>
            <Text style={styles.slashText}>/</Text>
            <View style={[styles.CodeInputStyle, {width: '80%'}]}>
              <CodeInput
                value={regex}
                placeholder="regex"
                onChange={(_editor: unknown, _data: unknown, val: string) => setRegex(val)}
              />
            </View>
            <Text style={styles.slashText}>/</Text>
            <View style={[styles.CodeInputStyle, {width: '10%'}]}>
              <CodeInput
                value={flag}
                placeholder="i"
                onChange={(_editor: unknown, _data: unknown, val: string) => setFlag(val)}
              />
            </View>
          </View>
          <View style={[styles.buttonContainer, {backgroundColor: '#0092bc'}]}>
            <Pressable onPress={() => onAddHandler(regexName, regex)}>
              <MaterialCommunityIcons name="plus" size={24} color="#fff" />
            </Pressable>
          </View>
        </View>
        <View style={styles.divider} />
        {regexArr.map((entry, index) => {
          return <RegexItem name={entry.label} onIconPress={() => onDeleteHandler(index)} key={index} mode="close" />;
        })}
        {filteredItems.map((entry, index) => (
          <RegexItem
            name={entry.item.label}
            onIconPress={() => onAddHandler(entry.item.label, entry.item.expression)}
            key={index}
            mode="plus"
          />
        ))}
      </View>
    </View>
  );
};

interface IRegexItem {
  name: string;
  mode: 'plus' | 'close';
  onIconPress: () => void;
}

const RegexItem: React.FC<IRegexItem> = props => {
  return (
    <View style={styles.regexItem}>
      <View style={styles.regexTextContainer}>
        <Text style={styles.fontText}>{props.name}</Text>
      </View>
      <View style={[styles.buttonContainer, {backgroundColor: props.mode === 'plus' ? '#0092bc' : '#FE5F55'}]}>
        <Pressable onPress={props.onIconPress}>
          <MaterialCommunityIcons name={props.mode === 'plus' ? 'plus' : 'close'} size={24} color="#fff" />
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 'auto',
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  labelText: {
    fontSize: 12,
    color: '#333',
    marginRight: 5,
  },
  divider: {
    marginTop: 10,
  },
  slashText: {fontSize: 12, color: '#e11d48', marginRight: 5},
  CodeInputStyle: {
    overflow: 'hidden',
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
  },
  regexItem: {
    flexDirection: 'row',
    width: '100%',
    marginBottom: 10,
    height: 40,
  },
  regexTextContainer: {backgroundColor: '#FFF', flexGrow: 1, paddingLeft: 5, height: '100%', justifyContent: 'center'},
  fontText: {},
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#CFFAFE',
    padding: 8,
    width: 40,
    height: 40,
    marginLeft: 5,
    position: 'relative',
  },
});

const mapDispatchToProps = (dispatch: any) => {
  return {
    ...bindActionCreators(
      {
        setRawValue: pluginConfigValueSetRaw,
      },
      dispatch,
    ),
  };
};

const mapStateToProps = () => {
  return {};
};

export default connect(mapStateToProps, mapDispatchToProps)(RegexInputControl);

