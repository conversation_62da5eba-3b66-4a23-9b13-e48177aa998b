import Immutable from 'immutable';
import React, {useCallback, useState} from 'react';
import {Button, StyleSheet, Text, View} from 'react-native';
import {PageParamConfig} from 'apptile-core';
import PagesSaveDialog from '../../views/pagesExport/pagesSaveDialog';
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();

interface ExportPageEditorProps {
  label: string;
  onChange: (value: any) => void;
  pageId: string;
  pageUUID: string | null;
  value: Immutable.Map<string, PageParamConfig>;
}

const ExportPageDialog: React.FC<ExportPageEditorProps> = ({pageUUID, pageId, label, onChange}) => {
  const [isExportDialogOpen, setOpenDialog] = useState(false);

  const onExport = useCallback(() => {
    setOpenDialog(true);
  }, []);

  const onCloseDialog = useCallback(() => {
    setOpenDialog(false);
  }, []);

  return (
    <View style={styles.container}>
      <View style={styles.rowLayout}>
        <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>
        <View style={styles.columnLayout}>
          <Button title="Export" onPress={onExport} />
        </View>
      </View>
      {isExportDialogOpen && (
        <PagesSaveDialog pageId={pageId} isOpen={isExportDialogOpen} pageUUID={pageUUID} onClose={onCloseDialog} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
    flex: 1,
    flexBasis: 'auto',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 4,
  },
  controlStyles: {
    flex: 1,
    maxWidth: '35%',
    padding: 2,
    borderColor: '#ddd',
    borderWidth: 1,
  },
});

export default ExportPageDialog;
