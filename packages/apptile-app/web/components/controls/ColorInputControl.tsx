import React from 'react';
import {ColorPicker} from './themeEditor/components';

interface ColorInputControlProps {
  value: string;
  label: string;
  name: string;
  defaultValue?: string;
  placeholder?: string;
  onChange: (value: string) => void;
}

const ColorInputControl: React.FC<ColorInputControlProps> = ({onChange, name, ...props}) => {
  return (
    <ColorPicker
      onChange={(colorName, colorValue) => {
        onChange(colorValue);
      }}
      name={name}
      {...props}
    />
  );
};

export default ColorInputControl;
