import {ApptileAnalyticsEventTypes} from 'apptile-core';
import {AnalyticsConfig} from 'apptile-core';
import {PropertyEditorProps} from '@/root/app/common/EditorControlTypes';
import Immutable from 'immutable';
import {debounce} from 'lodash';
import React, {FC, useCallback, useEffect, useState} from 'react';
import {Pressable, StyleSheet, Switch, Text, View} from 'react-native';
import {getCommonStyles} from '../../utils/themeSelector';
import CodeInput from '../codeEditor/codeInput';
import {AnalyticsEditorProps} from '../pluginEditorComponents';
import DropDownControl from './DropDownControl';
import ParamsInspector from './eventsEditor/ParamsInspector';

const AnalyticsEditor: FC<AnalyticsEditorProps> = props => {
  const commonStyles = getCommonStyles();
  const {
    pluginId,
    configProps,
    pageId,
    entityConfig,
    config,
    configSelectorPath,
    inModule,
    onChange,
    onCustomPropChange,
    onUpdateRawValue,
  } = props;
  // All analytics settings are at root level of entity config with name 'analytics'
  const [analyticsConfig, setAnalyticsConfig] = useState<AnalyticsConfig>(entityConfig?.get('analytics', undefined));
  useEffect(() => {
    if (entityConfig?.get('analytics', undefined) !== analyticsConfig)
      setAnalyticsConfig(entityConfig?.get('analytics', undefined));
  }, [analyticsConfig, entityConfig]);

  const enableAnalytics = useCallback(
    enabled => {
      let newConfig = analyticsConfig;
      if (enabled) {
        if (!analyticsConfig) {
          newConfig = new AnalyticsConfig({
            enabled,
            type: configProps?.defaultType,
          });
        }
      }
      newConfig = newConfig?.set('enabled', enabled);
      setAnalyticsConfig(newConfig);
      onUpdateRawValue(newConfig);
    },
    [analyticsConfig, onUpdateRawValue],
  );
  const onTypeChange = useCallback(
    newVal => {
      let newConfig = analyticsConfig.set('type', newVal);
      setAnalyticsConfig(newConfig);
      onUpdateRawValue(newConfig);
    },
    [analyticsConfig, onUpdateRawValue],
  );
  const onNameChange = useCallback(
    newVal => {
      let newConfig = analyticsConfig.set('name', newVal);
      setAnalyticsConfig(newConfig);
      onUpdateRawValue(newConfig);
    },
    [analyticsConfig, onUpdateRawValue],
  );
  const onDebouncedNameChange = debounce(onNameChange, 450);
  const onParamsChange = useCallback(
    newVal => {
      let newConfig = analyticsConfig.set('params', newVal);
      setAnalyticsConfig(newConfig);
      onUpdateRawValue(newConfig);
    },
    [analyticsConfig, onUpdateRawValue],
  );

  return !analyticsConfig || !analyticsConfig?.enabled ? (
    <View style={styles.editorInputRow}>
      <Switch onValueChange={enableAnalytics} value={analyticsConfig && analyticsConfig?.enabled} />
      <Text>Enable Analytics</Text>
    </View>
  ) : (
    <>
      <DropDownControl
        label="Event Type"
        defaultValue={configProps?.defaultType}
        value={analyticsConfig.type}
        onChange={onTypeChange}
        options={ApptileAnalyticsEventTypes}
      />
      <View style={styles.rowLayout}>
        <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>Event Name</Text>
        <View style={styles.CodeInputStyle}>
          <CodeInput
            value={analyticsConfig.name}
            // defaultValue={analyticsConfig.name}
            onChange={(value: string) => onDebouncedNameChange(value)}
          />
        </View>
      </View>
      <ParamsInspector
        {...{
          params: analyticsConfig?.params ?? Immutable.Map(),
          onParamsChange: onParamsChange,
        }}
      />
    </>
  );
};

export default AnalyticsEditor;

const styles = StyleSheet.create({
  editorInputItem: {flex: 1, flexDirection: 'column', flexWrap: 'wrap', margin: 5},
  editorInputRow: {flex: 1, flexDirection: 'row', flexWrap: 'wrap', margin: 3},
  editorInputLabel: {margin: 5},
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  CodeInputStyle: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: '#ccc',
    overflow: 'hidden',
  },
});
