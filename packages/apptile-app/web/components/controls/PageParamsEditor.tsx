import Immutable from 'immutable';
import {debounce} from 'lodash';
import React, {useCallback, useState} from 'react';
import {Button, Pressable, StyleSheet, Switch, Text, View} from 'react-native';
import {TextInput} from 'react-native-gesture-handler';
import {PageParamConfig} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import CheckboxControl from './CheckboxControl';
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();


interface PageParamsEditorProps {
  value: Immutable.Map<string, PageParamConfig>;
  label: string;
  onChange: (value: any) => void;
}

const PageParamsEditor: React.FC<PageParamsEditorProps> = ({value, label, onChange}) => {
  const [paramValues, setParamValues] = useState(value);
  const onDelete = useCallback(
    paramName => {
      let updatedValues = paramValues.remove(paramName);
      setParamValues(updatedValues);
      onChange(updatedValues);
    },
    [onChange, paramValues],
  );
  const onValueChange = useCallback(
    (paramName, property, newVal) => {
      let updatedValues = paramValues.set(paramName, paramValues.get(paramName).set(property, newVal));
      if (property === 'name')
        updatedValues = updatedValues.mapKeys(key => {
          if (key === paramName) return newVal;
          return key;
        });
      setParamValues(updatedValues);
      onChange(updatedValues);
    },
    [onChange, paramValues, value],
  );
  const debouncedOnValueChange = debounce(
    (paramName, property, newVal) => onValueChange(paramName, property, newVal),
    450,
  );
  const onAddParam = useCallback(() => {
    let i = 0;
    let paramName = `param${i}`;
    const paramNames = value.keySeq().toArray();
    while (true) {
      if (!paramNames.includes(paramName)) break;
      i++;
      paramName = `param${i}`;
    }
    setParamValues(paramValues.set(paramName, new PageParamConfig({name: paramName})));
    onChange(paramValues.set(paramName, new PageParamConfig({name: paramName})));
  }, [paramValues]);

  return (
    <View style={styles.container}>
      <View style={styles.rowLayout}>
        <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>
        <View style={styles.columnLayout}>
          {paramValues &&
            (paramValues as Immutable.Map<string, PageParamConfig>).entrySeq().map(([paramName, paramConfig]) => {
              return (
                <View style={styles.rowLayout} key={paramName}>
                  <TextInput
                    style={styles.controlStyles}
                    defaultValue={paramConfig.name}
                    onSubmitEditing={e => onValueChange(paramName, 'name', e.target.value)}
                    onBlur={e => {
                      e.target.value = e.target.defaultValue;
                    }}
                  />
                  <TextInput
                    style={styles.controlStyles}
                    defaultValue={paramConfig.defaultValue}
                    onChange={e => debouncedOnValueChange(paramName, 'defaultValue', e.target.value)}
                  />
                  <CheckboxControl
                    value={paramConfig.isRequired}
                    onChange={val => onValueChange(paramName, 'isRequired', val)}
                  />
                  <Pressable onPress={() => onDelete(paramName)}>
                    <MaterialCommunityIcons name="delete" color={'#333'} size={18} />
                  </Pressable>
                </View>
              );
            })}
          <Button title="Add Parameter" onPress={onAddParam} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    padding: 2,
  },
  columnLayout: {
    flexDirection: 'column',
    flex: 1,
    flexBasis: 'auto',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 4,
  },
  controlStyles: {
    flex: 1,
    maxWidth: '35%',
    padding: 2,
    borderColor: '#ddd',
    borderWidth: 1,
  },
});

export default PageParamsEditor;
