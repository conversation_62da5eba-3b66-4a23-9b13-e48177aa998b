import React, {useCallback, useEffect} from 'react';
import {StyleSheet, Text, TextInput, View} from 'react-native';
import { getTheme } from '../../utils/themeSelector';
import {getCommonStyles} from '../../utils/themeSelector';
const commonStyles = getCommonStyles();


const theme = getTheme();

type RangeSliderProps = {
  value: string;
  label?: string;
  defaultValue?: string;
  placeholder?: string;
  maxRange?: string;
  minRange?: string;
  steps?: string;
  onChange: (value: string) => void;
};

const RangeSliderControl: React.FC<RangeSliderProps> = ({value, label, defaultValue, onChange, ...props}) => {
  const [currentValue, setCurrentValue] = React.useState(value ?? defaultValue);
  const [currentInput, setCurrentInput] = React.useState(value ?? defaultValue);
  const [background, setBackground] = React.useState(value ?? defaultValue);
  const [message, setMessage] = React.useState('');
  const minRange = props.minRange ?? '0';
  const maxRange = props.maxRange ?? '100';
  const steps = props.steps ?? 1;

  const onChangeHandler = (val: string) => {
    setCurrentValue(val);
    if (val != currentInput) setCurrentInput(val);
    setMessage('');

    if (Number(val) < Number(minRange)) {
      val = minRange;
    }

    onChange(val);
  };

  const onCheckLimitAndSanitize = (val: any) => {
    const oldValue = currentValue;
    setCurrentInput(val);
    const parsedQty = Number(val);

    if (Number.isNaN(parsedQty)) {
      setCurrentInput(currentValue);
      onChangeHandler(String(currentValue));
    } else if (parsedQty < Number(minRange)) {
      setCurrentValue(oldValue);
      setMessage(`Min Value is ${minRange}`);
    } else if (parsedQty > Number(maxRange)) {
      setCurrentValue(oldValue);
      setMessage(`Max Value is ${maxRange}`);
    } else {
      onChangeHandler(String(parsedQty));
    }
  };

  const handleInputChange = useCallback(() => {
    const percent = ((Number(currentValue) - Number(minRange)) * 100) / (Number(maxRange) - Number(minRange)) + '%';
    let color = `linear-gradient(90deg, ${theme.CONTROL_ACTIVE_COLOR} ${percent}, ${theme.EDITOR_GREY_COLOR} ${percent})`;
    setBackground(color);
  }, [currentValue, maxRange, minRange]);

  useEffect(() => {
    handleInputChange();
  }, [currentInput, handleInputChange]);

  return (
    <View style={styles.container}>
      <View style={styles.rowLayout}>
        {label && <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>}
        <View style={[styles.rangeSliderContainer, label ? commonStyles.inputContainer : {width: '100%'}]}>
          <TextInput
            style={[styles.rangeValueBox, commonStyles.inputText]}
            value={currentInput}
            placeholder={props.placeholder}
            onChangeText={onCheckLimitAndSanitize}
          />
          <View style={{paddingLeft: 8, flex: 1}}>
            <input
              className="slider"
              type="range"
              min={minRange}
              max={maxRange}
              step={steps}
              value={currentValue}
              style={{
                background: background,
                flex: 1,
              }}
              onChange={e => onCheckLimitAndSanitize(e.target.value)}
              onInput={handleInputChange}
            />
          </View>
        </View>
      </View>
      {message && (
        <View>
          <Text style={[commonStyles.baseText, {color: theme.ERROR_BACKGROUND, paddingTop: 8, textAlign: 'center'}]}>
            {message}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
    alignItems: 'center',
  },
  rowLayout: {
    flex: 1,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rangeValueBox: {
    padding: 8,
    width: 48,
    borderRadius: theme.INPUT_BORDER_RADIUS,
    backgroundColor: theme.INPUT_BACKGROUND,
    outlineStyle: 'none',
  },
  rangeSliderContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center',
  },
});

export default RangeSliderControl;
