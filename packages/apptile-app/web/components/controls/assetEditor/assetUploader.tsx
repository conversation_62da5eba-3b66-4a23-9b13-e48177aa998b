import {default as React, useCallback, useEffect, useState} from 'react';
import Dropzone, {FileRejection} from 'react-dropzone';
import {StyleSheet, Text, View, Image, Pressable} from 'react-native';
import {connect} from 'react-redux';
import {bindActionCreators} from 'redux';
import {AppDispatch} from '../../../../app/store';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {uploadAppAsset} from '../../../actions/editorActions';
import theme from '@/root/web/styles-v2/theme';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import AssetCrop from './assetCrop/assetCrop';
import _ from 'lodash';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


export interface IAssetUploaderProps {
  uploadAppAsset: (uploadData: {file: File; metaData?: object}) => void;
  startUpload: () => void;
  closeUploader: () => void;
  disableButtons: (val: boolean) => void;
  changeHeader: (val: string) => void;
  onChangeURL: (val: string) => void;
  currentURL: string;
  uploaderRef: any;
  assetUploadProgress: Record<string, string>;
  isUploading: boolean;
  askURL: boolean;
  setShowCrop: (showCrop: boolean) => void;
  showCrop: boolean;
  startCropUpload: boolean;
  setStartCropUpload: (cropUpload: boolean) => void;
  cropPreview: boolean;
  aspectRatio: number;
  setCropPreview: (cropPreview: boolean) => void;
  setSkipCrop: (skipCrop: boolean) => void;
  skipCrop: boolean;
  bulk?: boolean;
  showOriginal: boolean;
  setShowOriginal: (showOriginal: boolean) => void;
}

const AssetUploader: React.FC<IAssetUploaderProps> = props => {
  const {
    changeHeader,
    startUpload,
    disableButtons,
    closeUploader,
    currentURL,
    isUploading,
    uploadAppAsset,
    onChangeURL,
    setShowCrop,
    showCrop,
    startCropUpload,
    setStartCropUpload,
    askURL,
    skipCrop,
    setSkipCrop,
    setCropPreview,
    bulk,
    showOriginal,
    setShowOriginal,
  } = props;
  const [files, setFiles] = useState<File[]>([]);
  const [message, setMessage] = useState<string>('');
  // const [preview, setPreview] = useState<Boolean>(false);
  const [uploadClicked, setUploadClicked] = useState<Boolean>(false);
  // const [link, setLink] = useState<string>('');
  const [croppedImage, setCroppedImage] = useState<File | null>();
  const [fileType, setFileType] = useState();

  const addFile = (selectedFiles: File[]) => {
    // NOTE: To enable multiple file upload support
    // const newFiles = [...files, ...selectedFiles];
    setFiles(selectedFiles);
    setFileType(selectedFiles[0]?.type);
  };

  //Using this function for uploading the giff type images
  const upload = useCallback(() => {
    if (files && files.length > 0) {
      files.forEach(file => {
        uploadAppAsset({file});
      });
      setFiles([]);
    }
  }, [files, uploadAppAsset]);

  useEffect(() => {
    changeHeader('Upload Image');
  }, [changeHeader]);

  useEffect(() => {
    if (croppedImage) {
      startUpload();
      changeHeader('Uploading...');
      setShowCrop(false);
      disableButtons(true);
    }
    if (isUploading) setUploadClicked(true);
  }, [croppedImage]);

  useEffect(() => {
    if (isUploading) setUploadClicked(true);
  }, [isUploading]);

  //Using this function to upload the cropped image
  const onDrop = (accepted: File[], rejected: FileRejection[]) => {
    if (Object.keys(rejected).length !== 0) {
      setMessage('Please submit valid file type');
    } else {
      addFile(accepted);
      setMessage('');
    }
  };

  useEffect(() => {
    const skipCropConditon = fileType === 'image/gif' || skipCrop || files.length > 1;
    props.uploaderRef.current = skipCropConditon && upload;
    if (skipCropConditon) {
      //Trigger for upload to start
      setCroppedImage(files[0]);
    }
    setSkipCrop(false);
  }, [showCrop, skipCrop]);

  // const addNewURL = () => {
  //   const [imgHeight, setImageHeight] = React.useState(0);
  //   Image.getSize(assetUrl, (width, height) => {
  //     setImageHeight(height);
  //   });
  //   return (
  //     <View>
  //       {assetUrl && onChangeURL && (
  //         <View style={styles.uploadedFileListView}>
  //           <Text style={styles.heading}>Preview</Text>
  //           <Image source={assetUrl} style={{width: 'auto', height: imgHeight, maxHeight: 200}} resizeMode="contain" />
  //         </View>
  //       )}
  //     </View>
  //   );
  // };

  useEffect(() => {
    if (isUploading === false && uploadClicked) {
      changeHeader('Image Library');
      disableButtons(false);
      closeUploader();
      setShowCrop(false);
      setCropPreview(false);
      setStartCropUpload(false);
    }
  }, [
    changeHeader,
    closeUploader,
    disableButtons,
    isUploading,
    props,
    uploadClicked,
    setStartCropUpload,
    setShowCrop,
    setCropPreview,
  ]);

  // const validateUrl = (url: string) =>
  //   /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:[/?#]\S*)?$/i.test(
  //     url,
  //   );

  return (
    <View style={styles.container}>
      <Text>{message}</Text>
      {!isUploading && !croppedImage && (
        <>
          {(!showCrop || _.isEmpty(files)) && (
            <Dropzone
              onDrop={onDrop}
              onDropAccepted={() => {
                setShowOriginal(true);
                setShowCrop(true);
              }}
              multiple={bulk ?? false}
              accept={{
                'image/jpeg': ['.jpeg', '.jpg', '.png', '.gif'],
              }}>
              {({getRootProps, getInputProps, open}) => (
                <View style={styles.dragdropUploader}>
                  <div {...getRootProps({className: 'dropzone'})} style={{display: 'flex', width: '100%', height: 248}}>
                    <input {...getInputProps()} className="fileInput" />
                    <View style={styles.dropableAreaWrapper}>
                      <Pressable onPress={open} style={[styles.uploaderPressable, styles.shadowProp]}>
                        <Image
                          resizeMode="cover"
                          source={require('../../../assets/images/placeholder-image.png')}
                          style={{width: 18, height: 18}}
                        />
                        {bulk ? (
                          <Text style={[commonStyles.baseText, styles.dropableArea]}>Upload Images</Text>
                        ) : (
                          <Text style={[commonStyles.baseText, styles.dropableArea]}>Upload an Image</Text>
                        )}
                      </Pressable>
                    </View>
                  </div>
                </View>
              )}
            </Dropzone>
          )}

          {showCrop && !_.isEmpty(files) && (
            <AssetCrop
              {...props}
              files={files}
              showOriginal={showOriginal}
              setShowOriginal={setShowOriginal}
              setCroppedImage={setCroppedImage}
              startCropUpload={startCropUpload}
            />
          )}
          {/* {(!showCrop || _.isEmpty(files)) && askURL && (
            <View style={styles.urlWrapper}>
              <Text style={[commonStyles.baseText]}>OR</Text>
              <View style={{width: '60%', overflow: 'hidden', paddingTop: 2}}>
                <CodeInputControl
                  value={link}
                  placeholder={`Current Link - ${currentURL || ''}`}
                  singleLine={true}
                  noOfLines={1}
                  onChange={(value: string) => {
                    setLink(value);
                    if (validateUrl(value.trim())) {
                      onChangeURL(value.trim());
                    } else if (_.isEmpty(value)) {
                      onChangeURL('');
                    }
                  }}
                />
                {link && !validateUrl(link) && (
                  <Text style={[commonStyles.baseText, commonStyles.errorText]}>Please Enter A Valid Link</Text>
                )}
              </View>
            </View>
          )} */}
        </>
      )}
      {(isUploading || !!croppedImage) && (
        <View style={styles.dragdropUploader}>
          <CircularProgressBase
            value={Number(props?.assetUploadProgress[Object.keys(props?.assetUploadProgress)[0]] ?? 0)}
            radius={68}
            activeStrokeColor={theme.CONTROL_ACTIVE_COLOR}
            inActiveStrokeColor={theme.CONTROL_PLACEHOLDER_COLOR}
            inActiveStrokeOpacity={0.5}
            inActiveStrokeWidth={4}
            activeStrokeWidth={4}>
            <Text style={commonStyles.baseText}>
              {Number(props?.assetUploadProgress[Object.keys(props?.assetUploadProgress)[0]] ?? 0)}% uploaded..
            </Text>
          </CircularProgressBase>
        </View>
      )}
    </View>
  );
};

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {
    ...bindActionCreators(
      {
        uploadAppAsset,
      },
      dispatch,
    ),
  };
};

const mapStateToProps = (state: EditorRootState) => {
  return {
    assetUploadProgress: state.asset.assetUploader.assetUploadProgress,
    isUploading: state.asset.assetUploader.isUploading,
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(AssetUploader);

const styles = StyleSheet.create({
  container: {marginHorizontal: 10, justifyContent: 'center', alignItems: 'center'},
  uploadedFileContainer: {flex: 1, flexDirection: 'column'},
  uploadedFileItem: {justifyContent: 'space-between', flexDirection: 'row'},
  uploadedFileTitle: {padding: 8, borderBottomWidth: 1, borderBottomColor: '#aaa'},
  uploadedFileText: {fontSize: 16, lineHeight: 16},
  uploadedFileListView: {flexDirection: 'column', flex: 1, flexBasis: 'auto', padding: 8},
  uploaderPressable: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F3F3',
    paddingHorizontal: 25,
    paddingVertical: 7,
    borderRadius: 8,
  },
  urlWrapper: {
    position: 'absolute',
    width: '100%',
    alignItems: 'center',
    top: 200,
    overflow: 'hidden',
  },
  dragdropUploader: {
    width: '100%',
    height: 300,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dropableAreaWrapper: {flexDirection: 'column', flex: 1, justifyContent: 'center', alignItems: 'center', padding: 8},
  dropableArea: {fontSize: 19, padding: 8},
  dropableAreaText: {fontSize: 12, padding: 4},
  headingContainer: {
    flexDirection: 'row',
  },
  shadowProp: {
    shadowColor: '#171717',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  heading: {
    flex: 1,
    fontFamily: theme.FONT_FAMILY,
    fontWeight: '700',
    fontSize: 15,
    lineHeight: 17,
    marginTop: 8,
    marginBottom: 16,
  },
});
