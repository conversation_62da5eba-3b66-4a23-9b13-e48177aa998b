import {EditorRootState} from '@/root/web/store/EditorRootState';
import theme from '@/root/web/styles-v2/theme';
import React, {useEffect, useState} from 'react';
import {Image, ImageSourcePropType, StyleSheet, TouchableOpacity, Text, View} from 'react-native';
import {useSelector} from 'react-redux';

export interface IAssetCardProps {
  assetId: string;
  selectedAsset: string;
  previouslySelectedAssetId: string;
  onSelectAsset: (assetId: string) => void;
  index: number;
}

const AssetCard: React.FC<IAssetCardProps> = props => {
  const {assetId, selectedAsset, onSelectAsset, previouslySelectedAssetId, index} = props;

  useEffect(() => {
    //For preselecting the first image in asset upload! also kept it in useEffect to wait for the component to mount
    if (index === 0) {
      onSelectAsset(assetId);
    }
  }, []);

  const assetState = useSelector((state: EditorRootState) => state.asset);
  const currentAsset = assetState.assetsById[assetId];

  const [thumburl, setThumburl] = useState(currentAsset ? currentAsset.thumbUrl : undefined);
  const isSelected = assetId === selectedAsset;
  const isPreviouslySelected = previouslySelectedAssetId === assetId;

  return (
    <TouchableOpacity
      style={[
        styles.CardWrapper,
        isSelected || (selectedAsset && isPreviouslySelected && isSelected) || (!selectedAsset && isPreviouslySelected)
          ? styles.selectedImageCard
          : {},
      ]}
      onPress={() => onSelectAsset(assetId)}>
      {thumburl && (
        <>
          <Image
            style={styles.cardImage}
            resizeMode="contain"
            source={thumburl as ImageSourcePropType}
            onError={() => setThumburl(currentAsset.fileUrl)}
          />
          {isSelected && (
            <View style={{position: 'absolute', top: 0, left: 0, height: 112, width: 112, backgroundColor: '#0008'}} />
          )}
        </>
      )}
    </TouchableOpacity>
  );
};

export default AssetCard;

const styles = StyleSheet.create({
  CardWrapper: {
    width: 112,
    height: 112,
    borderRadius: 4,
    overflow: 'hidden',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  selectedImageCard: {
    borderWidth: 2,
    borderColor: theme.CONTROL_ACTIVE_COLOR,
  },
  cardImage: {
    height: 112,
    width: 112,
  },
  cardActiveColor: {color: '#fff', textAlign: 'center'},
});
