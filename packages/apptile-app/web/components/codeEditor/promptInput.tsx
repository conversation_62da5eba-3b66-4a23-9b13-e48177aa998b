import React, {useRef, useState, useCallback, forwardRef, useImperative<PERSON><PERSON>le, useEffect} from 'react';
import {ActivityIndicator} from 'react-native';
import {availableModels} from './aiModels';
import {useSelector} from 'react-redux';
import {selectSelectedPluginConfig} from '../../selectors/EditorSelectors';

// Plugin tag interface
interface PluginTag {
  id: string;
  pluginName: string;
  elementId?: string;
}

function UploadIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25px" height="25px" fill="currentColor" viewBox="0 0 24 24">
      <path d="M12.588 11.646a1.5 1.5 0 1 1-2.97.418 1.5 1.5 0 0 1 2.97-.418Z"></path>
      <path
        fillRule="evenodd"
        d="M11.297 1.03c-1.732-.232-3.403.918-3.66 2.674L7.04 7.759l-3.33.468a3.148 3.148 0 0 0-2.68 3.555l1.197 8.508a3.148 3.148 0 0 0 3.555 2.68l8.508-1.197a3.148 3.148 0 0 0 2.68-3.555l-.2-1.42 1.285.172c1.732.233 3.403-.918 3.661-2.674l1.251-8.508c.26-1.766-1.03-3.329-2.769-3.562L11.297 1.03Zm5.183 13.71 1.842.248c.727.097 1.328-.389 1.415-.983l1.252-8.508c.085-.582-.338-1.192-1.057-1.289l-8.901-1.196c-.727-.097-1.329.389-1.416.983l-.51 3.474 3.113-.438a3.148 3.148 0 0 1 3.555 2.68l.707 5.03Zm-2.687-4.752a1.148 1.148 0 0 0-1.297-.977l-8.508 1.196a1.148 1.148 0 0 0-.977 1.297l.619 4.4 1.068-1.063a3.148 3.148 0 0 1 4.115-.283l6.08 4.582c.09-.195.128-.416.096-.644l-1.196-8.508Zm-1.109 9.991L7.61 16.155a1.148 1.148 0 0 0-1.5.103L3.978 18.38l.23 1.632a1.148 1.148 0 0 0 1.296.977l7.18-1.01Z"
        clipRule="evenodd"></path>
    </svg>
  );
}

// Styles object to centralize all styling
const createStyles = (themeColors: {
  EDITOR_BACKGROUND_COLOR: string;
  EDITOR_FOREGROUND_COLOR: string;
  EDITOR_DARK_BACKGROUND: string;
  EDITOR_ACCENT_COLOR: string;
}) => ({
  container: {
    borderRadius: 8,
    padding: 8,
    borderColor: themeColors.EDITOR_FOREGROUND_COLOR,
    position: 'relative' as const,
    boxShadow: `0px 0px 6px 0px ${themeColors.EDITOR_ACCENT_COLOR}`,
  },
  tagsContainer: {
    display: 'flex',
    flexWrap: 'wrap' as const,
    gap: 6,
    marginBottom: 8,
    minHeight: 0,
  },
  tag: {
    display: 'inline-flex',
    alignItems: 'center' as const,
    backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
    border: `1px solid ${themeColors.EDITOR_ACCENT_COLOR}`,
    borderRadius: 12,
    padding: '4px 8px',
    fontSize: 12,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    maxWidth: 200,
  },
  tagText: {
    marginRight: 6,
    overflow: 'hidden' as const,
    textOverflow: 'ellipsis' as const,
    whiteSpace: 'nowrap' as const,
  },
  tagRemove: {
    cursor: 'pointer' as const,
    color: '#ff6b6b',
    fontWeight: 'bold' as const,
    fontSize: 14,
    lineHeight: 1,
    padding: 0,
    border: 'none',
    background: 'transparent',
    display: 'flex',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    width: 16,
    height: 16,
    borderRadius: '50%',
    transition: 'background-color 0.2s',
  },
  modelSelector: {
    position: 'absolute' as const,
    border: 'solid',
    right: '10px',
    bottom: '64px',
    borderWidth: '1px',
    padding: '10px',
    borderRadius: '8px',
    background: themeColors.EDITOR_DARK_BACKGROUND,
  },
  modelSelectorGroup: {
    marginBottom: 5,
  },
  modelSelectorItem: {
    cursor: 'pointer' as const,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
  },
  editableDiv: {
    width: '100%',
    textAlign: 'left' as const,
    outline: 'none',
    maxHeight: 200,
    overflow: 'auto' as const,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
  },
  controlsContainer: {
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between' as const,
    paddingTop: 10,
  },
  leftControls: {
    display: 'flex',
    alignItems: 'center' as const,
  },
  uploadButton: {
    position: 'relative' as const,
    backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
    borderStyle: 'solid' as const,
    borderWidth: 1,
    borderColor: themeColors.EDITOR_FOREGROUND_COLOR,
    borderRadius: 4,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    fontWeight: 'bold' as const,
    display: 'flex',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 4,
    width: 28,
    height: 28,
    boxSizing: 'border-box' as const,
  },
  hiddenFileInput: {
    width: '100%',
    height: '100%',
    position: 'absolute' as const,
    left: -1,
    top: 8,
    opacity: 0,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 100,
    marginLeft: 8,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
  },
  rightControls: {
    display: 'flex',
    gridGap: 8,
  },
  button: {
    backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
    borderStyle: 'solid' as const,
    borderWidth: 1,
    borderColor: themeColors.EDITOR_ACCENT_COLOR,
    borderRadius: 4,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    fontWeight: 'bold' as const,
    cursor: 'pointer' as const,
    padding: 4,
    height: 28,
    boxSizing: 'border-box' as const,
  },
});

type PrompterInputProps = {
  themeColors: {
    EDITOR_BACKGROUND_COLOR: string;
    EDITOR_FOREGROUND_COLOR: string;
    EDITOR_DARK_BACKGROUND: string;
    EDITOR_ACCENT_COLOR: string;
  };
  llmProvider: 'openai' | 'claude' | 'none';
  llmModel: string;
  onSend: (message: string) => void;
  onModelSelect: (model: string, provider: 'openai' | 'claude') => void;
  isTokenLimitExpired?: boolean;
  onTokenLimitModalOpen?: () => void;
};
export type PrompterInputRefType = {
  clear: () => void;
  setStatus: (status: string) => void;
  setDisabled: (disabled: boolean) => void;
  addPluginTag: (tag: PluginTag) => void;
  removePluginTag: (tagId: string) => void;
  getPluginTags: () => PluginTag[];
};

const availableLLMs = availableModels;

export const PrompterInput = forwardRef<PrompterInputRefType, PrompterInputProps>(function (props, ref) {
  const {
    themeColors,
    llmModel,
    llmProvider,
    onSend,
    onModelSelect,
    isTokenLimitExpired: tokensExhausted,
    onTokenLimitModalOpen,
  } = props;
  const [isDisabled, setIsdisabled] = useState(false);
  const [selectorVisible, setSelectorVisible] = useState(false);
  const editableDiv = useRef<HTMLDivElement>(null);
  const statusEl = useRef<HTMLDivElement>(null);
  const currentSelectedPluginConfig = useSelector(selectSelectedPluginConfig);
  const [pluginTag, setPluginTag] = useState<string | null>(null);
  const pluginName = currentSelectedPluginConfig && currentSelectedPluginConfig.get('subtype');
  useEffect(() => {
    setPluginTag(pluginName);
  }, [pluginName]);

  // Create styles object using theme colors
  const styles = createStyles(themeColors);

  useImperativeHandle(
    ref,
    () => {
      return {
        clear: () => {
          if (editableDiv.current) {
            editableDiv.current.innerHTML = '';
          }
        },
        setStatus: (text: string) => {
          if (statusEl.current) {
            statusEl.current.innerText = text;
          }
        },
        setDisabled: (disabled: boolean) => {
          setIsdisabled(disabled);
        },
        appendTochatInput: (text: string) => {
          if (editableDiv.current) {
            editableDiv.current.innerHTML += text;
          }
        }
      };
    },
    [],
  );

  const handleSend = useCallback(() => {
    // Don't send if tokens are exhausted
    if (tokensExhausted) {
      onTokenLimitModalOpen?.();
      return;
    }

    if (editableDiv.current) {
      let message = editableDiv.current.innerHTML;

      // Add plugin context if there are tags
      if (pluginTag) {
        const pluginContext = `The following prompt is in regards to the plugin: ${pluginTag}`;
        message = pluginContext + '<br/>' + message;
      }

      onSend(message);
    }
  }, [tokensExhausted, onTokenLimitModalOpen, pluginTag, onSend]);

  // Handle click on input when tokens are exhausted
  const handleInputClick = useCallback(() => {
    if (tokensExhausted) {
      onTokenLimitModalOpen?.();
    }
  }, [tokensExhausted, onTokenLimitModalOpen]);

  const handleKeyDown = useCallback((ev: KeyboardEvent) => {
    if (ev.shiftKey && ev.key === "Enter" && editableDiv.current) {
      const newLine = document.createElement("div");
      editableDiv.current.appendChild(newLine);
      const range = document.createRange();
      range.selectNodeContents(newLine);
      range.collapse(false); 

      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } else if (ev.key === "Enter" && editableDiv.current) {
      handleSend();
    }
  }, [handleSend])

  const handleFilePick = useCallback((ev: React.ChangeEvent<HTMLInputElement>) => {
    const file = ev.target.files?.[0];
    if (file) {
      const fileReader = new FileReader();
      fileReader.onload = e => {
        const dataUrl = e.target?.result?.toString();
        if (dataUrl && editableDiv.current) {
          const existingContent = editableDiv.current.innerHTML;
          editableDiv.current.innerHTML = existingContent + `<img src="${dataUrl}">`;
        } else {
          console.error("Didn't get any data from file");
        }
        ev.target.value = "";
      };

      fileReader.onerror = err => {
        console.error('Failed to read file', err);
        ev.target.value = "";
      };

      fileReader.readAsDataURL(file);
    } else {
      console.log('Ignoring event: ', ev);
    }
  }, []);

  let modelSelector = null;
  if (selectorVisible) {
    modelSelector = (
      <div style={styles.modelSelector}>
        {availableLLMs.map(llmGroup => {
          const provider = llmGroup.provider;
          return (
            <div key={provider} style={styles.modelSelectorGroup}>
              {llmGroup.models.map(model => {
                let checkMark = null;
                if (model === llmModel && provider === llmProvider) {
                  checkMark = <span>✓</span>;
                } else {
                  checkMark = <span>&nbsp;&nbsp;&nbsp;</span>;
                }
                return (
                  <div
                    key={provider + model}
                    style={styles.modelSelectorItem}
                    onClick={() => {
                      onModelSelect(model, provider);
                      setSelectorVisible(false);
                    }}>
                    {checkMark} {provider}:{model}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    );
  }

  // Render plugin tags
  const renderPluginTags = () => {
    if (!pluginTag) return null;

    return (
      <div style={styles.tagsContainer}>
        <div style={styles.tag}>
          <span style={styles.tagText} title={pluginTag}>
            {pluginTag}
          </span>
          <button
            style={styles.tagRemove}
            onClick={() => setPluginTag(null)}
            onMouseEnter={e => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 107, 107, 0.2)';
            }}
            onMouseLeave={e => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
            title="Remove plugin tag">
            ×
          </button>
        </div>
      </div>
    );
  };

  return (
    <div style={{...styles.container, position: 'relative'}}>
      {modelSelector}
      {renderPluginTags()}
      <div
        id="prompter-input"
        ref={editableDiv}
        contentEditable={!isDisabled && !tokensExhausted}
        style={styles.editableDiv}
        onKeyDown={handleKeyDown}
      />
      <div style={styles.controlsContainer}>
        <div style={styles.leftControls}>
          <div style={styles.uploadButton}>
            <UploadIcon />
            <input
              type="file"
              style={styles.hiddenFileInput}
              onChange={handleFilePick}
              accept="image/png,image/jpeg"
              disabled={!!tokensExhausted}
            />
          </div>
          <div ref={statusEl} style={styles.statusText} />
        </div>
        {!isDisabled && !tokensExhausted && (
          <div style={styles.rightControls}>
            <button style={styles.button} onClick={() => setSelectorVisible(isVisible => !isVisible)}>
              AI -&gt; {llmProvider}:{llmModel}
            </button>
            <button onClick={handleSend} style={styles.button}>
              Run ↵
            </button>
          </div>
        )}
        {isDisabled && !tokensExhausted && (
          <div style={styles.rightControls}>
            <ActivityIndicator size={20} color={themeColors.EDITOR_FOREGROUND_COLOR} />
          </div>
        )}
      </div>

      {/* Token exhausted overlay */}
      {tokensExhausted && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            borderRadius: 8,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            cursor: 'pointer',
            zIndex: 10,
            backdropFilter: 'blur(2px)',
          }}
          onClick={handleInputClick}>
          <div
            style={{
              color: '#FF4500',
              fontSize: '16px',
              fontWeight: 'bold',
              marginBottom: '8px',
              textAlign: 'center',
            }}>
            🚫 Token Limit Reached
          </div>
          <div
            style={{
              color: themeColors.EDITOR_FOREGROUND_COLOR,
              fontSize: '14px',
              textAlign: 'center',
              opacity: 0.9,
            }}>
            Click for more info
          </div>
        </div>
      )}
    </div>
  );
});

export default PrompterInput;
