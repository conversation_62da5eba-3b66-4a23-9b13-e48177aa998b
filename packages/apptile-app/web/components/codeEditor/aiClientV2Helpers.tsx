import React, {ReactElement} from 'react';
import {toolsMap} from '../../sagas/frontendMcpTools';
import {ChatHistory, ChatMessage} from './aiClientV2';
import {themeColors} from './darkTheme';
import {ThemeColors} from './themetype';
import TextElement from '../../components-v2/base/TextElement';
import ApptileWebIcons from '@/root/web/icons/_ApptileWebIcons';
import theme from '../../styles-v2/theme';

// Common styles for reuse
export const styles = {
  messageContainer: {
    text: {
      color: themeColors.EDITOR_FOREGROUND_COLOR,
      fontFamily: theme.FONT_FAMILY_BODY,
      maxWidth: '90%',
      fontSize: 15,
      letterSpacing: 0.01,
    },
    userContainer: {
      display: 'flex',
      flexDirection: 'row' as 'row',
      justifyContent: 'flex-end',
      marginBottom: 16,
    },
    assistantContainer: {
      display: 'flex',
      flexDirection: 'row' as 'row',
      justifyContent: 'flex-start',
      marginBottom: 16,
    },
    toolUse: {
      container: (showing: boolean) => ({
        color: themeColors.EDITOR_FOREGROUND_COLOR,
        backgroundColor: themeColors.CHAT_MESSAGE_BACKGROUND || '#313131',
        borderRadius: 12,
        padding: 16,
        marginBottom: showing ? 12 : 0,
        position: 'relative' as const,
        maxWidth: '90%',
        boxShadow: '0px 3px 6px rgba(0,0,0,0.15)',
        transition: 'all 0.25s ease-in-out',
        display: 'flex',
        flexDirection: 'row' as 'row',
        justifyContent: 'space-between',
        gap: 10,
      }),
      header: (isPlugin: boolean, showing: boolean) => ({
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        color: isPlugin ? themeColors.EDITOR_STRING_COLOR : themeColors.EDITOR_CONSTANT_COLOR,
        fontWeight: 600,
        fontSize: 15,
        marginBottom: showing ? 14 : 0,
        cursor: 'pointer',
      }),
      body: {
        backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
        borderRadius: 10,
        padding: 14,
        transition: 'all 0.3s ease-in-out',
      },
    },
    toolResult: {
      container: {
        color: themeColors.EDITOR_FOREGROUND_COLOR,
        backgroundColor: themeColors.CHAT_MESSAGE_BACKGROUND || '#313131',
        borderRadius: 16,
        padding: 18,
        marginBottom: 12,
        maxWidth: '90%',
        boxShadow: '0px 3px 6px rgba(0,0,0,0.15)',
        animation: 'fadeIn 0.3s ease-in-out',
      },
      header: {
        fontWeight: 600,
        fontSize: 15,
        marginBottom: 10,
        color: themeColors.EDITOR_ACCENT_COLOR,
        display: 'flex',
        alignItems: 'center',
      },
      icon: {
        marginRight: 10,
      },
      content: {
        margin: 0,
        whiteSpace: 'pre-wrap' as const,
        backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
        borderRadius: 8,
        padding: 12,
        fontSize: 14,
        fontFamily: 'monospace',
      },
    },
    user: {
      color: themeColors.EDITOR_FOREGROUND_COLOR,
      backgroundColor: themeColors.USER_MESSAGE_BACKGROUND || '#0062FF',
      borderRadius: 16,
      padding: 16,
      maxWidth: '100%',
      fontSize: 15,
      letterSpacing: 0.01,
      boxShadow: '0px 2px 5px rgba(0,0,0,0.1)',
    },
    userImage: {
      container: {
        display: 'flex',
        justifyContent: 'flex-end',
        marginBottom: 8,
      },
      wrapper: {
        maxWidth: '300px',
        backgroundColor: themeColors.USER_MESSAGE_BACKGROUND || '#0062FF',
        borderRadius: 16,
        padding: 8,
        boxShadow: '0px 2px 5px rgba(0,0,0,0.1)',
      },
      image: {
        width: '100%',
        maxWidth: '280px',
        height: 'auto',
        minHeight: '120px',
        maxHeight: '400px',
        borderRadius: 12,
        objectFit: 'cover' as const,
        display: 'block',
        cursor: 'pointer',
        transition: 'transform 0.2s ease-in-out',
      },
    },
  },
  pluginAgentSection: {
    container: {
      marginTop: 16,
      padding: 12,
      margin: 10,
      backgroundColor: '#ffffff',
      borderRadius: 8,
      border: '1px solid #C8E6C9',
    },
    header: {
      color: themeColors.EDITOR_CONSTANT_COLOR,
      fontWeight: 'bold' as const,
      marginBottom: 8,
    },
    messageTypes: {
      text: {
        container: {
          backgroundColor: '#f0f0f0',
          borderRadius: 8,
          padding: 8,
        },
      },
      tool: (isToolUse: boolean) => ({
        margin: 0,
        overflowX: 'hidden' as const,
        whiteSpace: 'pre-wrap' as const,
        backgroundColor: isToolUse ? '#FFF8E1' : '#E3F2FD',
        borderRadius: 8,
        padding: 8,
        width: '100%',
      }),
      default: {
        margin: 0,
        overflowX: 'hidden' as const,
        whiteSpace: 'pre-wrap' as const,
        width: '100%',
      },
    },
  },
};

// Styled chat interface for the agent
export const agentChatStyles = {
  container: {
    backgroundColor: themeColors.CHAT_ASSISTANT_MESSAGE_BACKGROUND || '#313131',
    borderRadius: 18,
    padding: 16,
    marginTop: 8,
    marginBottom: 8,
    boxShadow: '0px 3px 10px rgba(0,0,0,0.2)',
    width: '100%',
    border: `1px solid ${themeColors.CHAT_BORDER_COLOR || '#444444'}`,
    transition: 'all 0.2s ease-in-out',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    cursor: 'pointer',
    padding: '4px 0',
  },
  agentInfo: {
    display: 'flex',
    alignItems: 'center',
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: '50%',
    backgroundColor: themeColors.EDITOR_ACCENT_COLOR,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    color: themeColors.CHAT_COMPLETED_CHECKMARK,
    boxShadow: '0px 2px 4px rgba(0,0,0,0.15)',
  },
  agentName: {
    fontWeight: 600,
    fontSize: 15,
    marginBottom: 2,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    letterSpacing: 0.01,
  },
  agentTitle: {
    color: '#A3A3A3',
    fontSize: 13,
    letterSpacing: 0.01,
  },
  message: {
    backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    boxShadow: '0px 2px 4px rgba(0,0,0,0.1)',
    position: 'relative' as const,
  },
  statusMessage: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusText: {
    fontWeight: 600,
    fontSize: 14,
  },
  statusIcon: {
    backgroundColor: themeColors.CHAT_COMPLETED_BACKGROUND || '#2A2A2A',
    borderRadius: '50%',
    width: 26,
    height: 26,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: themeColors.CHAT_COMPLETED_CHECKMARK || '#00FF00',
    boxShadow: '0px 1px 3px rgba(0,0,0,0.15)',
  },
  userMessage: {
    display: 'flex',
    alignItems: 'flex-start',
    flexDirection: 'column' as 'column',
    marginTop: 14,
  },
  userAvatar: {
    fontSize: 13,
    fontWeight: 600,
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    paddingBottom: 5,
    letterSpacing: 0.01,
  },
  userText: {
    backgroundColor: themeColors.USER_MESSAGE_BACKGROUND || '#0062FF',
    color: themeColors.EDITOR_FOREGROUND_COLOR,
    borderRadius: 14,
    padding: 14,
    maxWidth: 'calc(100% - 44px)',
    fontSize: 15,
    boxShadow: '0px 2px 5px rgba(0,0,0,0.1)',
  },
  responseText: {},
  promptText: {
    fontSize: 15,
    letterSpacing: 0.01,
  },
};

// Dropdown arrow component using ApptileWebIcons
function DropdownArrow({isOpen}: {isOpen: boolean}) {
  return (
    <ApptileWebIcons
      name="down-arrow"
      size={12}
      style={{
        transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
        transition: 'transform 0.2s ease-in-out',
        color: '#666666',
      }}
    />
  );
}

export function renderToolContent(
  toolName: string,
  message: ChatMessage,
  props: {
    onToggleDetail: () => void;
    setChatHistory?: React.Dispatch<React.SetStateAction<ChatHistory | null>>;
  },
) {
  const key = message.id + message.content_type;
  const shouldShowDetails = message.showing_details;
  let result: ReactElement<any, any>;
  // Styles for tool calls that match the design in the image
  const toolCallStyles = {
    container: {
      backgroundColor: themeColors.TOOL_CALL_BACKGROUND || '#292B32',
      color: themeColors.EDITOR_FOREGROUND_COLOR,
      borderRadius: 16,
      padding: '18px 22px',
      marginTop: 10,
      marginBottom: 10,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
      boxShadow: '0px 3px 10px rgba(0,0,0,0.2)',
      transition: 'all 0.2s ease-in-out',
    },
    parentContainer: {
      display: 'flex',
      flexDirection: 'column' as 'column',
      gap: 10,
      width: '100%',
    },
    contentSection: {
      display: 'flex',
      alignItems: 'center',
      flex: 1,
    },
    title: {
      fontSize: 15,
      fontWeight: 600,
      color: themeColors.EDITOR_FOREGROUND_COLOR,
      letterSpacing: 0.01,
    },
    checkmarkContainer: {
      width: 34,
      height: 34,
      borderRadius: '50%',
      backgroundColor: '#FFFFFF',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: '0px 2px 4px rgba(0,0,0,0.2)',
    },
    checkmark: {
      color: themeColors.CHAT_COMPLETED_CHECKMARK || '#4CAF50',
    },
    fileInfo: {
      fontFamily: 'monospace',
      fontSize: 14,
      color: themeColors.EDITOR_FOREGROUND_COLOR,
      opacity: 0.8,
      overflow: 'hidden' as const,
      textOverflow: 'ellipsis' as const,
      marginTop: 10,
    },
    errorContainer: {
      padding: 14,
      backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
      borderRadius: 10,
      color: '#FF6B6B',
      marginTop: 10,
      boxShadow: '0px 1px 3px rgba(0,0,0,0.1)',
    },
  };

  // Switch based on tool name to provide completely different UI for each tool
  switch (toolName) {
    case 'nocodelayer_generate_code_for_plugin': {
      try {
        const parsedBody = JSON.parse(message.content);

        result = (
          <div style={agentChatStyles.container}>
            <div onClick={props.onToggleDetail} style={agentChatStyles.header}>
              <div style={agentChatStyles.agentInfo}>
                <ApptileWebIcons style={agentChatStyles.avatar} name={'tiles'} size={20} />
                <div>
                  <div style={agentChatStyles.agentName}>{parsedBody?.name}</div>
                  <div style={agentChatStyles.agentTitle}>Developer Agent</div>
                </div>
              </div>
              <div>
                <DropdownArrow isOpen={shouldShowDetails} />
              </div>
            </div>

            {shouldShowDetails && (
              <div>
                {message.pluginAgentMessages && message.pluginAgentMessages.length > 0 && (
                  <div>
                    {message.pluginAgentMessages.map((agentMessage, index) => {
                      // Convert ChatMessagePre to ChatMessage by adding empty pluginAgentMessages array
                      const fullAgentMessage: ChatMessage = {
                        ...agentMessage,
                        pluginAgentMessages: [],
                      };

                      if (index === 0 && agentMessage.role === 'user') {
                        return (
                          <div key={agentMessage.id} style={agentChatStyles.userMessage}>
                            <div style={agentChatStyles.userAvatar}>Architect</div>
                            <div style={agentChatStyles.userText}>{agentMessage.content}</div>
                          </div>
                        );
                      }

                      return (
                        <div key={agentMessage.id}>
                          <ChatMessage
                            key={agentMessage.id}
                            message={fullAgentMessage}
                            themeColors={themeColors}
                            onlyAssistantMessage={true}
                            setChatHistory={props.setChatHistory}
                            onToggleDetail={() => {
                              // Get reference to the setChatHistory from parent scope
                              const updatedPluginMessages = [...message.pluginAgentMessages];

                              // Toggle the current message's showing_details property
                              updatedPluginMessages[index] = {
                                ...updatedPluginMessages[index],
                                showing_details: !updatedPluginMessages[index].showing_details,
                              };

                              // Also toggle the next message if it's a tool_result following a tool_use
                              if (
                                updatedPluginMessages[index + 1] &&
                                agentMessage.content_type === 'tool_use' &&
                                updatedPluginMessages[index + 1].content_type === 'tool_result'
                              ) {
                                updatedPluginMessages[index + 1] = {
                                  ...updatedPluginMessages[index + 1],
                                  showing_details: updatedPluginMessages[index].showing_details,
                                };
                              }

                              // Toggle the details for this specific plugin agent message
                              // Update the chat history with the modified plugin agent messages
                              props.setChatHistory &&
                                props.setChatHistory(prev => {
                                  if (!prev) return prev;

                                  // Create a new messages array to avoid mutating the original state
                                  const newMessages = [...prev.messages];

                                  // Find the parent message that contains these plugin agent messages
                                  const parentMessageIndex = newMessages.findIndex(msg => msg.id === message.id);

                                  if (parentMessageIndex !== -1) {
                                    // Create a new message object with updated pluginAgentMessages
                                    newMessages[parentMessageIndex] = {
                                      ...newMessages[parentMessageIndex],
                                      pluginAgentMessages: updatedPluginMessages,
                                    };
                                  }

                                  return {
                                    ...prev,
                                    messages: newMessages,
                                  };
                                });
                            }}
                          />
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
          </div>
        );
      } catch (err) {
        console.error('Error in parsing body for nocodelayer_generate_code_for_plugin:', err);
        const errorContainerStyle = {
          backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
          color: themeColors.EDITOR_FOREGROUND_COLOR,
          borderRadius: 16,
          padding: 12,
          marginTop: 6,
          marginBottom: 6,
          boxShadow: '0px 2px 8px rgba(0,0,0,0.08)',
        };

        result = (
          <div key={key} style={errorContainerStyle}>
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: 10,
              }}
              onClick={props.onToggleDetail}>
              <div style={{display: 'flex', alignItems: 'center'}}>
                <div
                  style={{
                    width: 36,
                    height: 36,
                    borderRadius: '50%',
                    backgroundColor: themeColors.EDITOR_BACKGROUND_COLOR,
                    marginRight: 8,
                  }}
                />
                <div>
                  <div style={{color: '#666', fontSize: 14}}>Tile Agent</div>
                </div>
              </div>
              <DropdownArrow isOpen={shouldShowDetails} />
            </div>
            {shouldShowDetails && (
              <div
                style={{
                  backgroundColor: '#fff',
                  borderRadius: 12,
                  padding: 12,
                  boxShadow: '0px 1px 3px rgba(0,0,0,0.05)',
                }}>
                <div>{message.content}</div>
              </div>
            )}
          </div>
        );
      }
      break;
    }

    case 'apply_file_ops': {
      let title = 'File created';
      let filePath = '';

      try {
        const parsedBody = JSON.parse(message.content);
        const fileOps = parsedBody.file_ops || [];

        // Extract file path from the first file operation
        if (fileOps.length > 0) {
          filePath = fileOps[0].path || '';
          const opType = fileOps[0].op_type?.toLowerCase() || 'update';

          // Set appropriate title based on operation type
          if (opType === 'create') {
            title = `Created ${filePath}`;
          } else if (opType === 'update') {
            title = `Updated ${filePath}`;
          } else if (opType === 'delete') {
            title = `Deleted ${filePath}`;
          } else {
            title = `${opType} ${filePath}`;
          }
        }

        // If there are multiple files, indicate that
        if (fileOps.length > 1) {
          title += ` and ${fileOps.length - 1} other file${fileOps.length > 2 ? 's' : ''}`;
        }

        // Create a simplified UI that matches the image design
        result = (
          <div key={key} style={toolCallStyles.container}>
            <div style={toolCallStyles.contentSection}>
              <div style={toolCallStyles.title}>{title}</div>
            </div>
            <div style={toolCallStyles.checkmarkContainer}>
              <ApptileWebIcons name="checkbox" size={18} style={toolCallStyles.checkmark} />
            </div>
          </div>
        );
      } catch (err) {
        console.error('Error in parsing body for apply_file_ops:', err);
        // Fallback to a generic message if parsing fails
        result = (
          <div key={key} style={toolCallStyles.container}>
            <div style={toolCallStyles.contentSection}>
              <div style={toolCallStyles.title}>File operation completed</div>
            </div>
            <div style={toolCallStyles.checkmarkContainer}>
              <ApptileWebIcons name="checkbox" size={18} style={toolCallStyles.checkmark} />
            </div>
          </div>
        );
      }
      break;
    }

    default: {
      // Default UI for any other tool
      let title = toolName;
      let details = '';

      // Try to extract meaningful information from the tool content
      try {
        const inputData = JSON.parse(message.content);

        // Set title based on tool content if available in toolsMap
        if (toolsMap.hasOwnProperty(toolName)) {
          let header = toolsMap[toolName].description;
          const variables = Object.keys(inputData);
          for (let i = 0; i < variables.length; ++i) {
            const varKey = variables[i];
            header = header.replace('__' + varKey + '__', inputData[varKey]);
          }
          title = header;
        }

        // Try to extract a file path or other meaningful information
        if (inputData.path) {
          details = inputData.path;
          title = `${title}: ${details}`;
        } else if (inputData.file_path) {
          details = inputData.file_path;
          title = `${title}: ${details}`;
        } else if (inputData.name) {
          details = inputData.name;
          title = `${title}: ${details}`;
        }
      } catch (err) {
        console.error('Unparsable input for tool:', toolName, err);
      }

      // Create a simplified UI that matches the image design
      result = (
        <div key={key} style={toolCallStyles.container}>
          <div style={toolCallStyles.contentSection}>
            <div style={toolCallStyles.title}>{title}</div>
          </div>
          <div style={toolCallStyles.checkmarkContainer}>
            <ApptileWebIcons name="checkbox" size={18} style={toolCallStyles.checkmark} />
          </div>
        </div>
      );
    }
  }

  return result;
}

export function renderAgentMessage(
  message: ChatMessage,
  colors: ThemeColors,
  props: {
    onToggleDetail: () => void;
    setChatHistory?: React.Dispatch<React.SetStateAction<ChatHistory | null>>;
  },
) {
  let result: ReactElement<any, any>;
  let key = message.id + message.content_type;
  switch (message.content_type) {
    case 'text':
      if (message.htmlcontent) {
        result = (
          <div key={key} dangerouslySetInnerHTML={{__html: message.htmlcontent}} style={styles.messageContainer.text} />
        );
      } else {
        result = (
          <TextElement key={key} style={styles.messageContainer.text}>
            {message.content}
          </TextElement>
        );
      }
      break;
    case 'tool_use':
      if (message.tool_name) {
        result = renderToolContent(message.tool_name, message, props);
      } else {
        result = (
          <div key={key} style={styles.messageContainer.toolUse.container(false)}>
            <div style={styles.messageContainer.toolUse.header(false, false)}>
              <div>Unknown Tool</div>
              <DropdownArrow isOpen={false} />
            </div>
          </div>
        );
      }
      break;
    case 'tool_result':
      result = message.showing_details ? (
        <div key={key} style={styles.messageContainer.toolResult.container}>
          <div style={styles.messageContainer.toolResult.header}>
            <ApptileWebIcons
              name="checkbox"
              size={16}
              color={themeColors.CHAT_COMPLETED_CHECKMARK || '#4CAF50'}
              style={styles.messageContainer.toolResult.icon}
            />
            Result
          </div>
          <pre style={styles.messageContainer.toolResult.content}>{message.content}</pre>
        </div>
      ) : (
        <div />
      );
      break;
    default: {
      result = (
        <div
          key={key}
          style={{
            color: colors.EDITOR_FOREGROUND_COLOR,
            backgroundColor: colors.CHAT_MESSAGE_BACKGROUND || '#313131',
            borderRadius: 16,
            padding: 16,
            maxWidth: '90%',
          }}>
          Not a renderable message. Content type: {message.content_type}
        </div>
      );
    }
  }
  return result;
}

export function renderUserMessage(message: ChatMessage): ReactElement<any, any> {
  const key = message.id + message.content_type;
  const userMessageStyle = styles.messageContainer.user;

  // Helper function to render HTML content
  const renderHtmlContent = () => (
    <div key={key} dangerouslySetInnerHTML={{__html: message.htmlcontent || ''}} style={userMessageStyle} />
  );

  // Helper function to render text content
  const renderTextContent = () => (
    <div key={key} dangerouslySetInnerHTML={{__html: message.content || ''}} style={userMessageStyle} />
  );

  // Helper function to render image content
  const renderImageContent = (mimeType: string) => (
    <div key={key} style={styles.messageContainer.userImage.container}>
      <div style={styles.messageContainer.userImage.wrapper}>
        <img
          src={`data:${mimeType};base64,${message.content}`}
          alt="User uploaded image"
          style={styles.messageContainer.userImage.image}
          onMouseEnter={e => {
            e.currentTarget.style.transform = 'scale(1.02)';
          }}
          onMouseLeave={e => {
            e.currentTarget.style.transform = 'scale(1)';
          }}
          onClick={() => {
            // Open image in new tab for full view
            const newWindow = window.open();
            if (newWindow) {
              newWindow.document.write(
                `<html><head><title>Image Preview</title></head><body style="margin:0;padding:20px;background:#000;display:flex;justify-content:center;align-items:center;min-height:100vh;"><img src="data:${mimeType};base64,${message.content}" style="max-width:100%;max-height:100%;object-fit:contain;" /></body></html>`,
              );
            }
          }}
        />
      </div>
    </div>
  );

  // Helper function to render hidden content
  const renderHiddenContent = () => (
    <TextElement key={key} style={[userMessageStyle, {display: 'none'}]}>
      {message.content}
    </TextElement>
  );

  // Check if content type is an image format
  const isImageType = (contentType: string): boolean => {
    return contentType.startsWith('image/');
  };

  switch (message.content_type) {
    case 'text':
      return message.htmlcontent ? renderHtmlContent() : renderTextContent();

    case 'image/jpeg':
    case 'image/jpg':
    case 'image/png':
      return message.htmlcontent ? renderHtmlContent() : renderImageContent(message.content_type);

    default:
      // Handle any other image types that might not be explicitly listed in the type definition
      if (isImageType(message.content_type)) {
        return message.htmlcontent ? renderHtmlContent() : renderImageContent(message.content_type);
      }
      return renderHiddenContent();
  }
}
