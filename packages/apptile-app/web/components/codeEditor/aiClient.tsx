import React, {useEffect, useRef, useState, RefObject, useCallback, ReactElement} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {EditorView} from 'codemirror';
import {ThemeColors} from './themetype';
import PrompterInput, {PrompterInputRefType as PrompterInputRefType} from './promptInput';
import {runChatCompletionRequest, SET_CHAT_RUNNING} from '../../actions/aiActions';
import {EditorRootState} from '../../store/EditorRootState';
import AutoSendPrompt from './AutoSendPrompt';
import { softRestartConfig } from '../../actions/editorActions';
import { toolsMap } from '../../sagas/frontendMcpTools';
import TokenCountDisplay from './TokenCountDisplay';
import TokenLimitModal from './TokenLimitModal';
import TokenApis from '../../api/TokenApi';
import { useParams } from 'react-router';
import { DEFAULT_MODEL, DEFAULT_PROVIDER } from './aiModels';

export type ChatMessage = {
  id: number;
  chatId: number;
  role: 'user'|'assistant';
  content_type: 'text'|'tool_use'|'tool_result'|'text/plain'|'image/jpg'|'image/jpeg'|'image/png';
  content: string;
  tool_call_id: string | null;
  createdAt: Date;
  htmlcontent: string | null;
  tool_name: string | null;
  showing_details: boolean;
};

type ChatHistory = {
  chat: {
    id: number;
    llm_model: string;
    llm_provider: 'openai' | 'claude';
    outputFile: string;
    type: 'widget' | 'datasource';
  };
  messages: ChatMessage[];
};

// Helper function to fetch chat history
export async function fetchChatMessages(
  historyApi: string,
  model: string,
  provider: 'openai' | 'claude',
): Promise<ChatHistory> {
  provider = provider || 'openai';
  const response = await fetch(`${historyApi}/provider/${provider}/model/${model}`);
  if (!response.ok) {
    throw new Error(`Failed to fetch chat history: ${response.statusText}`);
  }
  const history: ChatHistory = await response.json();

  history.messages.forEach(message => message.showing_details = false);
  // Validate messages
  // history.messages = history.messages.reverse();
  return history;
}

function ChatMessage(props: {message: ChatMessage, themeColors: ThemeColors, onToggleDetail: () => void}) {
  const {message, themeColors} = props;
  let result: ReactElement<any, any>;
  let key = message.id + message.content_type
  switch (message.content_type) {
    case "text":
      {
        if (message.htmlcontent) {
          result = (
            <div
              key={key}
              dangerouslySetInnerHTML={{__html: message.htmlcontent}}
              style={{
              color: themeColors.EDITOR_FOREGROUND_COLOR
            }}
          >
          </div>
          );
        } else {
          result = (
            <div
              key={key}
              style={{
              color: themeColors.EDITOR_FOREGROUND_COLOR
            }}
          >
              {message.content}
            </div>
          );
        }
      }
      break;
    case "tool_use":
      {
      let title = "Calling: " + message.tool_name;
      let pluginName = "";
        if (message.tool_name && toolsMap.hasOwnProperty(message.tool_name)) {
          let header = toolsMap[message.tool_name].description;
          try {
            const inputData = JSON.parse(message.content);
            const variables = Object.keys(inputData);
            for (let i = 0; i < variables.length; ++i) {
              const key = variables[i];
              header = header.replace('__' + key + '__', inputData[key]);
            }
            pluginName = inputData?.name;
          } catch (err) {
          console.error("Unparsable input: ", message);
          }
          title = header;
        }

      let body = <div>{message.content}</div>
      if (message.tool_name === "nocodelayer_generate_code_for_plugin") {
          try {
            const parsedBody = JSON.parse(message.content);
            body = (
              <div>
                <div style={{color: props.themeColors.EDITOR_CONSTANT_COLOR}}>
                  Handing over to the Tile Agent to create/update &nbsp;
                <span style={{color: props.themeColors.EDITOR_STRING_COLOR}}>{(pluginName || "").toUpperCase()}</span>
                </div>

              <pre style={{overflowX: 'auto', whiteSpace: 'pre-wrap'}}>
                {parsedBody.prompt}
              </pre>
              </div>
            );
          } catch (err) {
          console.error("Error in parsing body");
          }
      } else if (message.tool_name === "apply_file_ops") {
          try {
            const parsedBody = JSON.parse(message.content);
            const files = parsedBody.file_ops.map(file_op => {
            return <div>{file_op.path}</div>
            });
            body = (
              <div>
                <div>Editing these files</div>
                {files}
              </div>
            );
          } catch (err) {
          console.error("Error in parsing body");
          body = <div>{message.content}</div>
          }
        }

        result = (
          <div
            key={key}
            style={{
              color: themeColors.EDITOR_FOREGROUND_COLOR,
              borderWidth: 1,
              borderColor: themeColors.EDITOR_ACCENT_COLOR,
            borderBottomColor: message.showing_details ? 'transparent': themeColors.EDITOR_ACCENT_COLOR,
              borderStyle: 'solid',
              borderTopRightRadius: 8,
              borderTopLeftRadius: 8,
              borderBottomLeftRadius: message.showing_details ? 0 : 8,
              borderBottomRightRadius: message.showing_details ? 0 : 8,
              padding: 8,
              marginTop: 2,
              marginBottom: message.showing_details ? 2 : 0,
            position: 'relative'
          }}
        >
          <div style={{color: message.tool_name === "nocodelayer_generate_code_for_plugin" ? themeColors.EDITOR_STRING_COLOR : themeColors.EDITOR_CONSTANT_COLOR}}>
              {title}
            </div>
            {message.showing_details && body}
            <button onClick={props.onToggleDetail} style={{position: 'absolute', top: 4, right: 4}}>
              Detail
            </button>
          </div>
        );
      }
      break;
    case "tool_result":
      {
        result = message.showing_details ? (
          <div
            key={key}
            style={{
              color: themeColors.EDITOR_FOREGROUND_COLOR,
              borderWidth: 1,
              backgroundColor: themeColors.EDITOR_CONTRAST_BACKGROUND,
              borderColor: themeColors.EDITOR_ACCENT_COLOR,
              borderTopColor: 'transparent',
              borderStyle: 'solid',
              borderBottomRightRadius: 8,
              borderBottomLeftRadius: 8,
              padding: 8,
            marginBottom: 2
          }}
        >
            <pre>{message.content}</pre>
          </div>
      ) : <div></div>;
      }
      break;
    default:
    {
      result = (
        <div
          key={key}
          style={{
            color: themeColors.EDITOR_FOREGROUND_COLOR
          }}
        >
          Not a renderable message. Content type: {message.content_type}
        </div>
      );
    }
  }

  return result;
}

function renderMessage(
  key: string,
  text: string,
  type: string,
  sender: string,
  _isLast: boolean, // Unused parameter, kept for compatibility
  themeColors: ThemeColors,
) {
  const isUser = sender === 'user';
  const isAssistant = sender === 'assistant';
  // We don't need to check for tool sender as we use the styling based on isUser and isAssistant
  const isToolCall = type === 'tool_call';

  return (
    <div
      key={key}
      style={{
        display: 'flex',
        flexDirection: 'row',
        justifyContent: isUser ? 'flex-end' : 'flex-start',
        marginBottom: 10,
      }}>
      <div
        style={{
          backgroundColor: isUser
            ? themeColors.EDITOR_DARK_BACKGROUND
            : isAssistant
            ? themeColors.EDITOR_BACKGROUND_COLOR
            : themeColors.EDITOR_DARK_BACKGROUND,
          padding: 10,
          borderRadius: 10,
          width: 'auto',
          maxWidth: '90%',
        }}>
        <div
          style={{
            color: isUser
              ? themeColors.EDITOR_FOREGROUND_COLOR
              : isAssistant
              ? themeColors.EDITOR_FOREGROUND_COLOR
              : themeColors.EDITOR_FOREGROUND_COLOR,
          }}>
          {isToolCall ? (
            <div
              style={{
                backgroundColor: themeColors.EDITOR_DARK_BACKGROUND,
                padding: 10,
                borderRadius: 5,
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap',
                color: themeColors.EDITOR_FOREGROUND_COLOR,
              }}>
              <pre style={{overflowX: 'auto', whiteSpace: 'pre-wrap'}}>{text}</pre>
            </div>
          ) : (
            <pre
              style={{overflowX: 'auto', whiteSpace: 'pre-wrap'}}
              dangerouslySetInnerHTML={{__html: text}}
            />
          )}
        </div>
      </div>
    </div>
  );
}

type AIClientProps = {
  themeColors: ThemeColors;
  historyApi: string;
  completionApi: string;
  editorView: RefObject<EditorView | null>;
  onCompletionRunDone: (error?: any) => void;
  appId: string;
  usePlanner: boolean;
  chatWidthInPercentage: string;
  agentName: string;
  onArchitectClick: () => void;
};

export function AIClient(props: AIClientProps) {
  const [chatContinuationStatus, setChatContinuationStatus] = useState({status: 'notchecked', prompt: ''});
  const [chatHistory, setChatHistory] = useState<ChatHistory | null>(null);
  // Using dummy data for token count - will be replaced with actual API data
  const [tokenCount, setTokenCount] = useState({
    used: 0,
    max: 0,
  });
  const [showTokenLimitModal, setShowTokenLimitModal] = useState(false);
  const messageContainer = useRef<HTMLDivElement>(null);
  const prompterInputRef = useRef<PrompterInputRefType>(null);
  const liveMessageDiv = useRef<HTMLDivElement>(null);
  const tokenIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const dispatch = useDispatch();

  useEffect(() => {
    if (chatContinuationStatus.status !== 'checked') {
      const initialPrompt = sessionStorage.getItem('initialChatPrompt');
      if (initialPrompt) {
        setChatContinuationStatus({
          status: 'checked',
          prompt: initialPrompt,
        });
      } else {
        setChatContinuationStatus({
          status: 'checked',
          prompt: '',
        });
      }
    }

    console.log('Chat continuation status: ', chatContinuationStatus);
  }, [chatContinuationStatus]);

  const updateChatHistory = useCallback(
    (model: string, provider: 'claude' | 'openai') => {
      return fetchChatMessages(props.historyApi, model, provider)
        .then(history => {
          setChatHistory(history);
          setTimeout(() => {
            if (messageContainer.current) {
              messageContainer.current.scrollTop = messageContainer.current.scrollHeight;
            }
          }, 200);
        })
        .catch(err => {
          console.error('Failed to get chat history: ', err);
        });
    },
    [props.historyApi],
  );

  // Get AI state from Redux
  // const aiState = useSelector((state: EditorRootState) => state.ai);

  // Function to check token usage and update UI
  //Get org id from the url
  const orgId = useParams().orgId;

  const checkTokenUsage = useCallback(async () => {
    if (!orgId) return;
    try {
      // This would be the actual API call in production
      const response = await TokenApis.getTokenStats(orgId as string);

      // Update token count state
      setTokenCount({
        used: response.data.totalUsed,
        max: response.data.totalAllocated,
      });

      // Show modal if tokens are exhausted
      if (response.data.totalUsed >= response.data.totalAllocated) {
        setShowTokenLimitModal(true);
      }
    } catch (error) {
      console.error('Failed to fetch token stats:', error);
    }
  }, [orgId]);

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (tokenIntervalRef.current) {
        clearInterval(tokenIntervalRef.current);
        tokenIntervalRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    updateChatHistory(DEFAULT_MODEL, DEFAULT_PROVIDER);

    // Check token usage when component mounts
    checkTokenUsage();
  }, [checkTokenUsage, updateChatHistory]);

  let renderedMessages: Array<ReactElement<any, any>> = [];
  if (chatHistory) {
    renderedMessages = chatHistory.messages.map((message, index) => (
      <ChatMessage
        message={message}
        themeColors={props.themeColors}
        onToggleDetail={() => {
          setChatHistory(prev => {
            const newMessages = (prev?.messages ?? []).slice();
            newMessages[index].showing_details = !newMessages[index].showing_details;
            if (newMessages[index + 1]) {
              newMessages[index + 1].showing_details = !newMessages[index + 1].showing_details;
            }
            return {
              ...prev,
              messages: newMessages,
            };
          });
        }}
      />
    ));
  }

  const onSendPrompt = useCallback(
    (text: string) => {
      if (!chatHistory?.chat) {
        alert('chat is not initialized yet!');
        return;
      }
      prompterInputRef.current?.setDisabled(true);
      dispatch({
        type: SET_CHAT_RUNNING,
        payload: true,
      });

      const updateLiveMessageContent = (contentHtml: string, toolCall: string) => {
        const isNonEmpty = contentHtml || toolCall;
        if (isNonEmpty && liveMessageDiv.current) {
          liveMessageDiv.current.style.display = 'block';
          const textResponse = liveMessageDiv.current.querySelector('#textresponse');
          const toolResponse = liveMessageDiv.current.querySelector('#toolresponse');
          if (textResponse) {
            textResponse.innerText = contentHtml;
            textResponse.scrollTop = textResponse.scrollHeight;
          }

          if (toolResponse) {
            toolResponse.innerText = toolCall;
            toolResponse.scrollTop = toolResponse.scrollHeight;
          }
          // liveMessageDiv.current.innerHTML = `${contentHtml}<div>${toolCall}</div>`;
        } else if (!isNonEmpty) {
          updateChatHistory(chatHistory.chat.llm_model, chatHistory.chat.llm_provider).then(() => {
            if (liveMessageDiv.current) {
              const textResponse = liveMessageDiv.current.querySelector('#textresponse');
              const toolResponse = liveMessageDiv.current.querySelector('#toolresponse');
              liveMessageDiv.current.style.display = 'none';
              if (textResponse) {
                textResponse.innerText = '';
              }

              if (toolResponse) {
                toolResponse.innerText = '';
              }
            }
          });
          // fetch chat history
        }
      };

      // Dispatch the action to run chat completion
      dispatch(
        runChatCompletionRequest({
          prompt: text,
          model: chatHistory.chat.llm_model,
          provider: chatHistory.chat.llm_provider,
          completionUrl: props.completionApi,
          appId: props.appId,
          usePlanner: props.usePlanner,
          liveMessageSubscriber: updateLiveMessageContent,
          onCompleted: err => {
            if (prompterInputRef.current) {
              prompterInputRef.current.setDisabled(false);
              prompterInputRef.current.clear();
              dispatch({
                type: SET_CHAT_RUNNING,
                payload: false,
              });
            }
            console.log('[AGENT] soft reloading');
            // dispatch(softRestartConfig());
            props.onCompletionRunDone(err);

            // Check token usage after completion
            checkTokenUsage();
          },
        }),
      );
    },
    [
      chatHistory?.chat,
      props.completionApi,
      props.appId,
      props.usePlanner,
      props.onCompletionRunDone,
      liveMessageDiv,
      checkTokenUsage,
      updateChatHistory,
      dispatch,
    ],
  );
  const isTokenLimitExpired = tokenCount && tokenCount.max >= 0 && tokenCount.used >= tokenCount.max;

  return (
    <div
      id="thisone"
      style={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        width: props.chatWidthInPercentage,
        backgroundColor: props.themeColors.EDITOR_BACKGROUND_COLOR,
        position: 'relative',
        fontFamily: "'Plus Jakarta Sans', sans-serif",
      }}>
      <div
        style={{
          color: props.themeColors.EDITOR_FOREGROUND_COLOR,
          borderBottomStyle: 'solid',
          borderBottomWidth: 1,
          borderBottomColor: props.themeColors.EDITOR_FOREGROUND_COLOR,
        }}>
        {props.agentName !== 'Architect' && (
          <span>
            <button onClick={props.onArchitectClick}>Architect</button>&nbsp;/&nbsp;
          </span>
        )}
        {props.agentName}
      </div>
      <div
        ref={messageContainer}
        style={{
          flex: 1,
          overflowY: 'auto',
          padding: 10,
          display: 'flex',
          flexDirection: 'column',
        }}>
        {renderedMessages}
      </div>
      <div
        id="live-message"
        ref={liveMessageDiv}
        style={{
          backgroundColor: props.themeColors.EDITOR_CONTRAST_BACKGROUND,
          padding: 10,
          borderRadius: 8,
          color: props.themeColors.EDITOR_FOREGROUND_COLOR,
          position: 'absolute',
          bottom: 39,
          width: 'calc(100% - 20px)',
          minHeight: 86,
          display: 'none',
          zIndex: 1,
          opacity: 0.9,
          fontFamily: "'Plus Jakarta Sans', sans-serif",
        }}>
        <div style={{color: props.themeColors.EDITOR_FOREGROUND_COLOR}}>
          <div style={{color: props.themeColors.EDITOR_CONSTANT_COLOR}}>Receiving from agent</div>
          <div id="textresponse" style={{maxHeight: 36, overflowY: 'auto'}}></div>
          <div id="toolresponse" style={{maxHeight: 36, overflowY: 'auto'}}></div>
        </div>
      </div>
      <div style={{position: 'relative'}}>
        {tokenCount && (
          <TokenCountDisplay themeColors={props.themeColors} usedTokens={tokenCount.used} maxTokens={tokenCount.max} />
        )}
        <PrompterInput
          themeColors={props.themeColors}
          llmModel={(chatHistory && chatHistory.chat.llm_model) || 'none'}
          llmProvider={(chatHistory && chatHistory.chat.llm_provider) || 'none'}
          ref={prompterInputRef}
          isTokenLimitExpired={isTokenLimitExpired}
          onTokenLimitModalOpen={() => setShowTokenLimitModal(true)}
          onSend={onSendPrompt}
          onModelSelect={updateChatHistory}
        />
      </div>
      {/* TOOD(gaurav): Move this to saga */}
      {/* Auto-send component that will send the initial prompt when chat history is loaded */}
      {chatContinuationStatus.status === 'checked' && (
        <AutoSendPrompt
          initialPrompt={chatContinuationStatus.prompt}
          onSend={onSendPrompt}
          chatHistoryLoaded={!!chatHistory?.chat}
        />
      )}

      {/* Token limit modal */}
      <TokenLimitModal
        themeColors={props.themeColors}
        isOpen={showTokenLimitModal}
        onClose={() => setShowTokenLimitModal(false)}
      />
    </div>
  );
}
