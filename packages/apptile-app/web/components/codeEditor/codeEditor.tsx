import React, {FC, useRef, useEffect, useCallback, useState} from 'react';
import {StyleSheet} from 'react-native';
import {useDispatch, useSelector} from 'react-redux';

import {basicSetup, EditorView} from 'codemirror';
import {keymap} from '@codemirror/view'
import {Compartment, EditorState} from '@codemirror/state';
// import {vim, Vim} from '@replit/codemirror-vim';
import {javascript} from '@codemirror/lang-javascript';
import {AppDispatch, reloadNavigators} from 'apptile-core';
import {reloadExternalPlugins} from '../../../app/plugins/initPlugins';
// import {nord} from 'cm6-theme-nord';
import Animated, {useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import PrompterInput, {PrompterInputRefType as PrompterInputRefType} from './promptInput';
import {EDITOR_OPEN_CODE_EDITOR, EDITOR_CLOSE_CODE_EDITOR, softRestartConfig} from '../../actions/editorActions';
import {EditorState as StoreEditorState} from '../../common/webDatatypes';
import {
  tokyonightGogh,
  themeColors as tokyoThemeColors
} from './tokyonight';
import {
  goghLight,
  themeColors as goghLightThemeColors
} from './goghlight'
import { AIClient } from './aiClient';

const editorThemes = [
  {
    extension: tokyonightGogh,
    name: 'Dark',
    themeColors: tokyoThemeColors
  },
  {
    extension: goghLight,
    name: 'Light',
    themeColors: goghLightThemeColors
  }
]

// const enableVim = localStorage.getItem("enablevim");

const themeConfig = new Compartment();

const CodeEditor: FC<{editorState: StoreEditorState['codeEditor']; phoneContainerWidth: number;}> = 
function ({editorState, phoneContainerWidth = 0}) {
  const [currentThemeIndex, setCurrentThemeIndex] = useState(0);
  const theme = editorThemes[currentThemeIndex];
  const appId = useSelector(state => state.apptile.appId);
  const dispatch = useDispatch();

  const rootContainer = useRef(null);
  const editorView = useRef<EditorView>(null);

  const onCommit = useCallback(() => {
    if (editorView.current) {
      onCodeEditorSave(editorState, editorView.current.state.doc.toString(), dispatch);
    } else {
      console.error("Couldn't save from editor because there is no editorView.current");
    }
  }, [editorView, editorState]);

  const closeEditor = () => {
    dispatch({
      type: EDITOR_CLOSE_CODE_EDITOR,
    });
  };

  useEffect(() => {
    if (rootContainer.current) {
      const saveKeymap = keymap.of([
        {
          key: "Mod-s",  
          run: () => {
            onCommit();  
            return true;  
          }
        }
      ]);

      const extensions = [
        basicSetup, 
        javascript({jsx: true}), 
        // nord, 
        themeConfig.of(theme),
        EditorView.lineWrapping, 
        saveKeymap
      ];
      // if (enableVim) {
      //   Vim.defineEx('write', 'w', () => {
      //     onCommit();
      //   })
      //   Vim.defineEx('quit', 'q', () => {
      //     closeEditor();
      //   })
      //   extensions.unshift(vim())
      // }

      editorView.current = new EditorView({
        state: EditorState.create({
          doc: editorState.sourceCode,
          extensions,
        }),
        parent: rootContainer.current,
      });
    }
  }, [rootContainer]);

  useEffect(() => {
    let styleNode = document.querySelector('style#codemirrorstyles');
    if (!styleNode) {
      styleNode = document.createElement('style');
      document.head.appendChild(styleNode);
      styleNode.setAttribute('id', 'codemirrorstyles');
      styleNode.innerText = `
      .cm-editor {
        height: 100%;
      }
      `;
    }
  }, []);

  const opacity = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  // TODO(gaurav) persist the previous color so that we can revert to 
  // original after switching themes
  useEffect(() => {
    const root = document.querySelector("#app-root");
    let existingBackgroundColor: string|null = null ;
    if (root) {
      existingBackgroundColor = root.style.backgroundColor;
      root.style.backgroundColor = theme.themeColors.EDITOR_BACKGROUND_COLOR
    }
    opacity.value = withTiming(1, {duration: 500});

    return () => {
      opacity.value = withTiming(0, {duration: 500});
      if (root) {
        root.style.backgroundColor = existingBackgroundColor;
      }
    };
  }, []);

  return (
    <Animated.View
      style={[
        animatedStyle,
        {
          zIndex: 2,
          paddingTop: 25,
          borderStyle: 'solid',
          position: 'absolute',
          top: 0,
          bottom: 0,
          left: 0,
          width: `calc(100vw - ${phoneContainerWidth}px)`,
          backgroundColor: theme.themeColors.EDITOR_BACKGROUND_COLOR,
          display: 'flex',
        },
      ]}>
      <div style={styles.menubar}>
        <div style={{height: 20, borderRadius: 2, backgroundColor: "white", marginLeft: '10px'}}>
          <img style={{height: '100%', boxShadow: `${theme.themeColors.EDITOR_ACCENT_COLOR} 0px 0px 1px 0px`}} src={require("../../assets/images/logo.png")}></img>
        </div>
        <div
          style={{display: "flex"}}
        >
          <button 
            style={styles.button} 
            onClick={() => {
              setCurrentThemeIndex(prev => {
                const nextTheme = prev ? 0 : 1;
                if (editorView.current) {
                  editorView.current.dispatch({
                    effects: themeConfig.reconfigure(editorThemes[nextTheme])
                  });
                }
                return nextTheme;
              })
            }
          }>
            {currentThemeIndex === 0 ? "The Jedi way" : "Join the dark side"}
          </button>
          <button style={styles.button} onClick={onCommit}>
            save
          </button>
          <button style={styles.button} onClick={closeEditor}>
            close
          </button>
        </div>
      </div>
      <div style={styles.editorRoot}>
        <AIClient
          agentName={`Developer:${editorState.artefactName}`}
          appId={appId}
          themeColors={theme.themeColors}
          historyApi={`${window.PLUGIN_SERVER_URL}/plugins/chathistory/${appId}/${editorState.artefactName}`}
          completionApi={`${window.PLUGIN_SERVER_URL}/plugins/prompt/${appId}/${editorState.artefactName}`}
          editorView={editorView}
          chatWidthInPercentage="50%"
          onCompletionRunDone={(err) => {
            if (err) {
              console.error("[AGENT] Chat completion error: ", editorState.artefactName, err)
            } else {
              console.log("[AGENT] Completion run finished for: " + editorState.artefactName+ ". Reloading plugins and restarting.");
              reloadExternalPlugins({ uuid: appId }).then(() => {
                return dispatch(softRestartConfig());
              });
            }
          }}
          onArchitectClick={() => {}}
          usePlanner={false}
        />
        <div ref={rootContainer} style={styles.editorTab}></div>
      </div>
    </Animated.View>
  );
}

export default CodeEditor;

export function openCodeEditor(payload: {appId: string; artefactName: string; type: 'plugin'|'navigator';}, dispatch: AppDispatch) {
  let url = `${window.PLUGIN_SERVER_URL}/plugins/${payload.appId}/${payload.artefactName}/source`;
  if (payload.type === 'navigator') {
    url = `${window.PLUGIN_SERVER_URL}/navigators/${payload.appId}/${payload.artefactName}/source`;
  }

  fetch(url)
    .then(res => res.text())
    .then(sourceCode => {
      dispatch({
          type: EDITOR_OPEN_CODE_EDITOR,
          payload: {
            open: true,
            appId: payload.appId,
            type: payload.type,
            artefactName: payload.artefactName,
            sourceCode: sourceCode
          }
        });
    })
    .catch(err => {
      console.error("Error while getting sourcecode", err);
    });
}

export function onCodeEditorSave(editorState: StoreEditorState['codeEditor'], updatedCode: string, dispatch: AppDispatch) {
  let urlPrefix = `${window.PLUGIN_SERVER_URL}/plugins`
  if (editorState.type === 'navigator') {
    urlPrefix = `${window.PLUGIN_SERVER_URL}/navigators`
  }
  fetch(`${urlPrefix}/${editorState.appId}/${editorState.artefactName}/source`, {
    method: 'POST',
    body: JSON.stringify({code: updatedCode}),
    headers: {
      "content-type": "application/json"
    }
  })
  .then(res => {
    console.log("Plugin recompiled and ready: ", res)
    return fetch(`${urlPrefix}/${editorState.appId}/compileall`, {
      method: 'POST'
    })
  })
  .then(() => {
    if (editorState.type === 'navigator') {
      reloadNavigators(editorState.appId)
    } else {
      reloadExternalPlugins({uuid: editorState.appId})
    }
    dispatch(softRestartConfig())
  })
  .catch(err => {
    console.error("Could not update code: ", err);
  });
}

const styles = StyleSheet.create({
  menubar: {
    position: 'absolute',
    height: 20,
    display: 'flex',
    marginTop: -23,
    width: '100%',
    justifyContent: 'space-between'
  },
  editorRoot: {
    display: 'flex',
    flexDirection: 'row',
    flex: 1,
    height: 'calc(100vh - 28px)'
  },
  editorTab: {
    width: '25vw',
    flex: 1,
  },
  button: {
    cursor: 'pointer',
    // boxShadow: `${NORD_ACCENT_COLOR} 0px 0px 1px 0px`,
    borderStyle: 'solid',
    borderWidth: 1,
    borderRadius: 4,
    backgroundColor: "#363d47",
    color: "white",
    marginRight: 15
  },
});
