//If there is window.CODEBUILD_BUILD_NUMBER then add a tag at the top right corner of the screen
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {getCommonStyles} from '../utils/themeSelector';
import {useSelector} from 'react-redux';
import {checkApptileEmailSelector} from '../selectors/FeatureGatingSelector';
const commonStyles = getCommonStyles();

export default function BetaBuildTag() {
  const buildNumber = window.CODEBUILD_BUILD_NUMBER;
  const isApptileUser = useSelector(checkApptileEmailSelector);
  if (!buildNumber || !isApptileUser) {
    return null;
  }

  return (
    <View style={styles.container}>
      <Text style={[commonStyles.baseText, styles.text]}>Beta Build: {buildNumber}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'fixed',
    top: 0,
    right: 0,
    backgroundColor: 'green',
    padding: 4,
  },
  text: {
    color: '#fff',
    fontSize: 14,
  },
});
