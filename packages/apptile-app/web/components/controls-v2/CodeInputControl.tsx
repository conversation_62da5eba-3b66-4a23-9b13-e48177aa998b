import React, {useCallback, useEffect, useRef, useState} from 'react';
import {StyleSheet, Text, View, TextInput, ViewStyle, StyleProp} from 'react-native';

import {getTheme} from '../../utils/themeSelector';
import {getCommonStyles} from '../../utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface CodeInputControlProps {
  value: string;
  label?: string;
  defaultValue?: string | number;
  placeholder?: string | number;
  onChange: (value: string) => void;
  name?: string;
  singleLine?: boolean;
  validationPattern?: string;
  validationError?: string;
  noOfLines?: number;
  containerStyles?: StyleProp<ViewStyle>;
  inputStyles?: any;
  onKeyPress?: any;
  autoFocus?: boolean;
  onKeyDown?: (event: any, ref: any) => void;
}

const CodeInputControlV2: React.FC<CodeInputControlProps> = ({
  value,
  label,
  defaultValue,
  placeholder,
  name,
  onChange,
  singleLine = false,
  validationPattern = '',
  validationError = 'Validation Failed In Text Input',
  noOfLines,
  containerStyles,
  inputStyles,
  onKeyPress,
  autoFocus,
  onKeyDown
}) => {
  const onValueChange = useCallback(
    newVal => {
      onChange(singleLine ? newVal.replace(/[\n]/gim, '') : newVal);
    },
    [onChange, singleLine],
  );
  const [codeValue, setCodeValue] = useState(value);
  const textRef = useRef(null);
  const testRegex = (pattern: string, value: string) => {
    let regex = pattern,
      options = '';
    if (pattern.trim().startsWith('/')) {
      regex = pattern.slice(1, pattern.lastIndexOf('/'));
      options = pattern.slice(pattern.lastIndexOf('/') + 1);
    }
    try {
      return new RegExp(regex, options).test(value);
    } catch (e) {
      return true;
    }
  };
  useEffect(() => {
    setCodeValue(value);
  }, [value]);

  useEffect(() => {
    if (autoFocus && textRef.current) {
      textRef.current?.focus();
    }
  }, [autoFocus]);
  return (
    <View style={[styles.container, containerStyles]}>
      <View style={styles.controlContainer}>
        {label && <Text style={[commonStyles.labelText, commonStyles.labelContainer]}>{label}</Text>}
        <View style={[styles.CodeInputStyle, !!label && commonStyles.inputContainer]}>
          <textarea
            ref={textRef}
            defaultValue={codeValue}
            placeholder={placeholder as string}
            autoFocus={!!autoFocus}
            style={{
              borderRadius: theme.INPUT_BORDER_RADIUS,
              backgroundColor: theme.INPUT_BACKGROUND,
              fontFamily: theme.FONT_FAMILY,
              fontSize: theme.FONT_SIZE,
              fontWeight: theme.FONT_WEIGHT as
                | 'normal'
                | 'bold'
                | '100'
                | '200'
                | '300'
                | '400'
                | '500'
                | '600'
                | '700'
                | '800'
                | '900',
              lineHeight: `${theme.LINE_HEIGHT + 4}px`,
              color: theme.CONTROL_INPUT_COLOR,
              resize: 'none',
              outline: 'none',
              padding: 7,
              border:
                validationPattern != '' && !testRegex(validationPattern, codeValue)
                  ? `solid 1px ${theme.ERROR_BACKGROUND}`
                  : `solid 1px ${theme.INPUT_BACKGROUND}`,
              ...inputStyles,
            }}
            onChange={(event: any) => {
              setCodeValue(event.target.value);
              if (validationPattern == '' || testRegex(validationPattern, event.target.value))
                onValueChange(event.target.value);
            }}
            rows={singleLine ? 1 : noOfLines}
            wrap={'wrap'}
            onKeyPress={onKeyPress}
            onKeyDown={onKeyDown ? (e) => onKeyDown(e, textRef) : undefined}
          />
          {validationPattern != '' && !testRegex(validationPattern, codeValue) && (
            <Text style={[commonStyles.baseText, {color: theme.ERROR_BACKGROUND, paddingTop: 8, paddingLeft: 8}]}>
              {validationError}
            </Text>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.PRIMARY_MARGIN,
    width: '100%',
  },
  controlContainer: {
    flexDirection: 'row',
  },
  icon: {
    width: 28,
    height: 28,
    marginTop: 4,
    marginLeft: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#535353',
    borderRadius: 6,
  },
  CodeInputStyle: {
    flex: 1,
  },
});

export default CodeInputControlV2;
