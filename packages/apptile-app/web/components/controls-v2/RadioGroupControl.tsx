import {default as React, useCallback, useState} from 'react';
import {Pressable, StyleSheet, Text, View} from 'react-native';
import {startCase} from 'lodash';

import theme from '@/root/web/styles-v2/theme';
import {MaterialCommunityIcons} from 'apptile-core';
import {ApptileWebIcon} from '../../icons/ApptileWebIcon.web';
import CodeInputControl from '../controls/CodeInputControl';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const commonStyles = getCommonStyles();


interface RadioGroupControlProps {
  value: string;
  label: string;
  defaultValue?: string;
  onChange: (value: string) => void;
  options?: string[] | {iconType?: string; icon?: string; text: string; value: string}[];
  disableBinding?: boolean;
  allowDeselect?: boolean;
  activeColor?: string;
  size?: 'LARGE' | 'MEDIUM' | 'SMALL';
}

const RadioGroupControlV2: React.FC<RadioGroupControlProps> = props => {
  const {value, label, options, disableBinding, allowDeselect, defaultValue, onChange, activeColor, size = 'MEDIUM'} = props;

  const onValueChange = useCallback(
    newVal => {
      if (allowDeselect && newVal == value) onChange('');
      else onChange(newVal);
    },
    [allowDeselect, onChange, value],
  );

  const data =
    options &&
    options.map(val => {
      if (typeof val === 'string') return {icon: null, text: startCase(val), value: val};
      return val;
    });

  return (
    <View style={styles.container}>
      {label && (
        <View style={commonStyles.labelContainer}>
          <Text style={commonStyles.labelText}>{label}</Text>
        </View>
      )}
      <View
        style={[
          styles.multiToggleWrapper,
          commonStyles.input,
          label ? commonStyles.inputContainer : {width: '100%'},
          {borderRadius: theme.INPUT_BORDER_RADIUS + (size == 'LARGE' ? 6 : size == 'MEDIUM' ? 3 : 0)}
        ]}>
        {data &&
          data.map(item => {
            const isActive = item.value === value || item.value === defaultValue;
            return (
              <Pressable
                key={item.value}
                style={[styles.toggleButton, isActive && styles.toggleButtonActive, { minHeight: theme.PRIMARY_HEIGHT + (size == 'LARGE' ? 6 : size == 'MEDIUM' ? 3 : 0), borderRadius: theme.INPUT_BORDER_RADIUS + (size == 'LARGE' ? 6 : size == 'MEDIUM' ? 3 : 0), padding: size == 'LARGE'? 4 : size == 'MEDIUM' ? 3 : 2}]}
                onPress={() => onValueChange(item.value)}>
                {item.icon &&
                  (item?.iconType == 'apptile' ? (
                    <ApptileWebIcon
                      name={item.icon}
                      color={isActive ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR}
                      size={18}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name={item.icon}
                      color={isActive ? theme.CONTROL_ACTIVE_COLOR : theme.CONTROL_INPUT_COLOR}
                      size={18}
                    />
                  ))}
                {item.text && (
                  <Text style={[commonStyles.inputText, isActive && styles.activeText, isActive && activeColor && {color: activeColor}]}>{item.text}</Text>
                )}
              </Pressable>
            );
          })}
      </View>
    </View>
  );
};

export default RadioGroupControlV2;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT + 5,
    overflow: 'hidden',
  },
  multiToggleWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 2,
    gap: 6,
    height: '100%',
    borderRadius: theme.INPUT_BORDER_RADIUS + 6,
    overflow: 'hidden',
  },
  toggleButton: {
    flexGrow: 1,
    flexShrink: 1,
    flexDirection: 'row',
    flexBasis: 'auto',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  toggleButtonActive: {
    backgroundColor: '#FFFFFF',
    textAlign: 'center',
  },
  activeText: {
    fontWeight: theme.FONT_WEIGHT,
  },
});
