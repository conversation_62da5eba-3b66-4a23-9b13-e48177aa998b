import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Pressable, StyleSheet, Switch, Text, View, Image} from 'react-native';
import {connect, useSelector} from 'react-redux';
import {bindActionCreators} from 'redux';
import {IImageListItem} from '@/root/app/plugins/state/DisplayImageList/ImageListTypes';
import ImageListItem from './ImageListItem';
import _, { debounce } from 'lodash';
import {Icon, ScreenConfigParams, datasourceTypeModelSel, pluginConfigValueSetRaw, store} from 'apptile-core';
import {MaterialCommunityIcons} from 'apptile-core';
import SortableList from '../../SortableList';
import {imageListEditorProps} from '../../pluginEditorComponents';
import Tooltip from '@/root/web/components-v2/base/Tooltip/Index';
import CollapsiblePanel from '../../CollapsiblePanel';
import EditorSectionHeader from '../../controls/EditorSectionHeader';
import Button from '@/root/web/components-v2/base/Button';
import useMountEffect from '@/root/web/common/hooks/useMountEffect';
import { ShopifyObjectCacheCollection, ShopifyObjectCacheProduct } from '@/root/web/integrations/shopify/ShopifyObjectCacheTypes';
import Fuse from 'fuse.js';
import { ApptileWebIcon } from '@/root/web/icons/ApptileWebIcon.web';
import CodeInputControlV2 from '../CodeInputControl';
import { replaceNullCollectionImage, replaceProductImage } from '@/root/web/integrations/shopify/ShopifyObjectCache';
import { processShopifyGraphqlQueryResponse } from 'apptile-core';
import { TransformGetProductsPaginatedQuery, TransformSearchCollections } from 'apptile-shopify';
import {CollectionGqls as ProductCollectionGqls} from 'apptile-shopify';
import RadioGroupControlV2 from '../RadioGroupControl';
import { selectScreensInNav } from '@/root/web/selectors/EditorSelectors';
import standardScreens, { editableScreens } from '@/root/web/common/screenConstants';
import AssetEditor from '../../controls/assetEditor/assetEditor';
import Immutable from 'immutable';
import { EditorRootState } from '@/root/web/store/EditorRootState';
import {getTheme} from '@/root/web/utils/themeSelector';
import {getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

interface IItemSelection {
  onItemSelection: any;
  itemType: string;
  closeSelection: any;
};

const FUSE_OPTIONS = {
  // isCaseSensitive: false,
  includeScore: true,
  shouldSort: true,
  // includeMatches: true,
  findAllMatches: true,
  minMatchCharLength: 1,
  // location: 0,
  // threshold: 0.6,
  // distance: 100,
  // useExtendedSearch: false,
  ignoreLocation: true,
  // ignoreFieldNorm: false,
  // fieldNormWeight: 1,
  keys: ['name','value'],
};

const ItemSelection: React.FC<IItemSelection> = props => {
  const {onItemSelection, itemType, closeSelection} = props;
  const [currentItemType, setCurrentItemType] = useState<'Product' | 'Collection' | 'Screen' | 'External Link'>(itemType ? itemType : 'Product');  
  const [filteredItems, setFilteredItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [query, setQuery] = useState('');
  const shopifyModelSel = state => datasourceTypeModelSel(state, 'shopifyV_22_10');
  const ShopifyDSModel = shopifyModelSel(store.getState());
  const queryRunner = ShopifyDSModel?.get('queryRunner');
  const getProducts = async () => {
    if (queryRunner && queryRunner.runQuery) {
      const countryCode = ShopifyDSModel?.getIn(['shop','paymentSettings','countryCode']) ?? 'US';
      const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_PRODUCTS_FOR_DROPDOWN, {first: 50, query, countryCode});
      setIsLoading(true);
      const {transformedData: queryTransformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformGetProductsPaginatedQuery},
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      setFilteredItems(replaceProductImage(queryTransformedData));
      setIsLoading(false);
      return queryTransformedData;
    } else if (window?.apptileWebSDK?.moduleExports?.functions?.fetchProducts) {
      setIsLoading(true);
      const queryResponse = await window?.apptileWebSDK?.moduleExports?.functions?.fetchProducts({
        value: query,
      });
      if (query && queryResponse.length == 0) return getProducts();
      setFilteredItems(queryResponse);
      setIsLoading(false);
      return queryResponse;
    } else {
      return [];
    }
  };

  const getCollections = async () => {
    if (queryRunner && queryRunner.runQuery) {
      const countryCode = ShopifyDSModel?.getIn(['shop','paymentSettings','countryCode']) ?? 'US';
      const queryResponse = await queryRunner.runQuery('query', ProductCollectionGqls.SEARCH_COLLECTIONS_FOR_DROPDOWN, {first: 50, query, countryCode});
      setIsLoading(true);
      const {transformedData} = processShopifyGraphqlQueryResponse(
        queryResponse,
        {transformer: TransformSearchCollections},
        ShopifyDSModel?.get('shop'),
        ShopifyDSModel,
      );
      setFilteredItems(replaceNullCollectionImage(transformedData));
      setIsLoading(false);
      return transformedData;
    } else if (window?.apptileWebSDK?.moduleExports?.functions?.fetchCollections) {
      setIsLoading(true);
      const queryResponse = await window?.apptileWebSDK?.moduleExports?.functions?.fetchCollections({
        value: query,
      });
      if (query && queryResponse.length == 0) return getCollections();
      setFilteredItems(queryResponse);
      setIsLoading(false);
      return queryResponse;
    } else {
      return [];
    }
  };
  const debouncedSetQuery = debounce(setQuery, 100);


  const navScreens = useSelector(selectScreensInNav);
  const newEditableScreens: ScreenConfigParams[] = Array(editableScreens.length);
  navScreens
    .filter((s: ScreenConfigParams) => editableScreens.includes(s.name))
    .map(s => newEditableScreens.splice(editableScreens.indexOf(s.name), 1, s));
  let screens;
  const shopifyScreenList = [
    ...newEditableScreens
      .filter((s: ScreenConfigParams) => s)
      .map((s: ScreenConfigParams) => ({
        name: s.title,
        value: s.screen,
        icon: {
          name: s.iconName == 'home' && s.iconType == 'Material Icon' ? 'file-outline' : s.iconName,
          type: s.iconType,
        },
      })),
    ...navScreens
      .filter((s: ScreenConfigParams) => !standardScreens.includes(s.name))
      .map((s: ScreenConfigParams) => ({
        name: s.title,
        value: s.screen,
        icon: {
          name: s.iconName == 'home' && s.iconType == 'Material Icon' ? 'file-outline' : s.iconName,
          type: s.iconType,
        },
      })),
  ];
  if (window?.apptileWebSDK?.moduleExports?.functions?.fetchScreenList) {
    screens = window?.apptileWebSDK?.moduleExports?.functions?.fetchScreenList();
  } else {
    screens = shopifyScreenList;
  }
  const [fuseSearch, setFuseSearch] = useState(new Fuse(screens ?? [], FUSE_OPTIONS));
  const [screenOptions, setScreenOptions] = useState<any[]>(screens);
  const [imageItem, setImageItem] = useState<IImageListItem>({
    url: "",
    sourceType: "url",
    assetId: "",
    resizeMode: "cover",
    navEntityType: "",
    navEntityId: ""
  })
  const assetState = useSelector((state: EditorRootState) => state.asset);
  const currentAsset = assetState.assetsById[imageItem.assetId];

  useEffect(()=>{
    fuseSearch.setCollection(screens);
  },[screens]);

  useEffect(() => {
    if(currentItemType == 'Product') getProducts();
    else if(currentItemType == 'Collection') getCollections();
    else {
      setIsLoading(false);
      if(query == '') setScreenOptions(screens);
      else setScreenOptions(fuseSearch.search(query).map(e => e.item));
    }
  }, [currentItemType, query]);

  useEffect(() => {
    if (
      currentAsset &&
      imageItem.assetId === currentAsset.id &&
      imageItem.url !== currentAsset.thumbUrl
    ) {
      setImageItem({...imageItem, url: currentAsset.thumbUrl});
    }
  }, [currentAsset, imageItem])

  const onValueChange = useCallback(
    (key, value) => {
      setImageItem({...imageItem, ...{[key]: value, title: value}});
    },
    [imageItem]
  )
  const debouncedOnValueChange = debounce(onValueChange, 450);
  
  const onBulkValueChange = useCallback(
    update => {
      setImageItem({...imageItem, ...update});
    },
    [imageItem]
  )
    return (
    <View style={styles.addContainer}>
      <View style={{ marginTop: 4 }}>
        <View style={[styles.rowContainer, {justifyContent: 'space-between', marginBottom: 4}]}>
          <Text style={commonStyles.heading}>ADD {itemType ? itemType.toUpperCase() : 'ITEMS'}</Text>
          <MaterialCommunityIcons
            onPress={() => {
              closeSelection();
            }}
            name="close"
            size={14}
          />
        </View>
        {!itemType && <RadioGroupControlV2 size='LARGE' options={[{text: 'Products', value: 'Product'},{text: 'Collections', value: 'Collection'},{text: 'Screens', value: 'Screen'},{text: 'External Link', value: 'External Link'}]} value={currentItemType} onChange={(value)=>{    
          setFilteredItems([]);
          setIsLoading(true);
          setQuery('');
          setCurrentItemType(value);
        }} />}
        {currentItemType !== 'External Link' && <View style={[commonStyles.input, styles.inputContainer]}>
          <ApptileWebIcon name={'magnify'} size={19} color={theme.CONTROL_INPUT_COLOR} />
          <View style={{flex: 1}}>
            <CodeInputControlV2
              placeholder={'Search ' + currentItemType}
              singleLine={true}
              containerStyles={{marginVertical:0}}
              onChange={function (value: string): void {
                debouncedSetQuery(value);
              }}
            />
          </View>
        </View>}
        <View style={styles.itemContainer}>
          <View style={{height: '100%', overflow: 'scroll'}}>
            {isLoading && <Text style={styles.item}>Loading {itemType ?? 'item'}'s</Text>}
            {!isLoading &&
              !!filteredItems?.length && currentItemType != 'Screen' && 
              filteredItems?.slice(0, 50)?.map((item, index) => (
                <Pressable
                  style={[
                    styles.item,
                    {
                      borderBottomWidth: 1,
                      borderBottomColor: '#44444444',
                    },
                  ]}
                  key={index}
                  onPress={() => {
                    onItemSelection({
                      url: (item?.image?.url || item?.featuredImage?.url) ? item?.image?.url || item?.featuredImage?.url : 'https://cdn.apptile.io/23a145ba-da98-4d6d-8c06-1a597b7a4a2f/19c387fc-19f0-486e-a4c8-e5352b3bf46e/original.png',
                      sourceType: 'url',
                      assetId: '',
                      resizeMode: 'cover',
                      title: item?.title ?? '',
                      id: item?.id ?? '',
                      navEntityType: currentItemType ?? '',
                      navEntityId: item.handle,
                    });
                  }}>
                  <View style={styles.itemImageContainer}>
                    <Image
                      resizeMode="cover"
                      source={
                        item?.image?.url || item?.featuredImage?.url
                          ? {uri: item?.image?.url || item?.featuredImage?.url}
                          : require('../../../assets/images/placeholder-image.png')
                      }
                      style={styles.itemImage}
                    />
                  </View>
                  <View style={[styles.itemTextContainer]}>
                    <Text style={commonStyles.baseText}>{item.title}</Text>
                  </View>
                </Pressable>
              ))}
              {!isLoading && currentItemType == 'Screen' && 
                screenOptions?.slice(0, 50)?.map((item, index) => (
                  <Pressable
                    style={[
                      styles.item,
                      {
                        borderBottomWidth: 1,
                        borderBottomColor: '#44444444',
                      },
                    ]}
                    key={index}
                    onPress={() => {
                      onItemSelection({
                        url: 'https://cdn.apptile.io/1e32f186-366e-4baf-8a1b-92d8c7e0c0fb/44f66def-705c-4bfd-8c4b-6f80ee6db747/original.png',
                        sourceType: 'url',
                        assetId: '',
                        resizeMode: 'cover',
                        title: item.name ?? '',
                        navEntityType: item.value ?? 'Home',
                        navEntityId: 'nil',
                      });
                    }}>
                    <View style={styles.itemImageContainer}>
                      <Icon
                        iconType={item.icon.type}
                        name={item.icon.name}
                        size={22}
                        style={styles.itemImage}
                      />
                    </View>
                    <View style={[styles.itemTextContainer]}>
                      <Text style={commonStyles.baseText}>{item.name}</Text>
                    </View>
                  </Pressable>
              ))}
              {!isLoading && currentItemType == 'External Link' && (
                <View>
                  <CodeInputControlV2
                    label={'URL'}
                    defaultValue={imageItem.navEntityId}
                    singleLine={true}
                    onChange={(value: string) => debouncedOnValueChange('navEntityId', value)}
                  />
                  <AssetEditor
                    configProps={{
                      label: 'Image',
                      urlProperty: 'url',
                      assetProperty: 'assetId',
                      sourceTypeProperty: 'sourceType',
                      disableBinding: true,
                    }}
                    config={Immutable.Map(imageItem)}
                    onBulkValueChange={onBulkValueChange}
                  />
                  <Button onPress={() => onItemSelection({...imageItem, navEntityType: 'ExternalLink'})}>
                    Add
                  </Button>
                </View>
              )}
            {!isLoading && !filteredItems?.length && currentItemType != 'Screen' && currentItemType != 'External Link' && <Text style={commonStyles.baseText}>No Results Found</Text>}
            {!isLoading && !screenOptions?.length && currentItemType == 'Screen' && <Text style={commonStyles.baseText}>No Screens Found</Text>}
          </View>
        </View>
      </View>
    </View>
  );
};

export interface ImageListEditorControlProps extends imageListEditorProps {
  valueUpdated?: (value: any) => void;
}

const _ImageListEditorControl: React.FC<ImageListEditorControlProps> = props => {
  const {pageId, pluginId, name, value, configProps, valueUpdated, pluginConfigValueSetRaw} = props;
  const {label, disableAdd, shopifyType, minLength = '1', maxLength = '1000'} = configProps;
  const [imageList, setImageList] = useState<Array<IImageListItem>>(value);
  const onValueChange = useCallback(
    newVal => {
      setImageList(newVal);
      pluginConfigValueSetRaw(pluginId, pageId, name, newVal);
      valueUpdated && valueUpdated(newVal);
    },
    [name, pageId, pluginConfigValueSetRaw, pluginId],
  );
  /// FIXME: SHOPIFY related customizations
  const platform = useSelector(state => state.platform);
  //// FIXME: End SHOPIFY customizations

  const onAddItem = useCallback((item) => {
    if (imageList?.length < (isNaN(maxLength) ? 10000 : parseInt(maxLength))) {
      const newList = imageList.concat({
        url: '',
        sourceType: 'url',
        assetId: '',
        resizeMode: 'cover',
        navEntityType: shopifyType ?? '',
        navEntityId: '',
        ...item,
      });
      // setImageList(newList);
      onValueChange(newList);
    }
  }, [imageList, maxLength, onValueChange, shopifyType]);

  const onUpdateImageItem = useCallback(
    (index, item) => {
      const newList = imageList.slice();
      newList[index] = {...item};
      // setImageList(newList);
      onValueChange(newList);
    },
    [imageList, onValueChange],
  );
  const onDeleteImageItem = useCallback(
    index => {
      if (imageList?.length > (isNaN(minLength) ? 1 : parseInt(minLength))) {
        var newList = imageList;
        _.pullAt(newList, index);
        newList = newList.slice();
        // setImageList(newList);
        onValueChange(newList);
      }
    },
    [imageList, minLength, onValueChange],
  );
  const updateImagesOrder = useCallback(
    (reorderedList: [any, IImageListItem][]) => {
      let vals: IImageListItem[] = [];
      reorderedList?.map(([key, val]) => {
        vals.push(val);
      });
      setImageList(vals);
      onValueChange(vals);
    },
    [onValueChange],
  );
  const [listOpen, setListOpen] = useState(true);
  const [selectionOpen, setSelectionOpen] = useState(false);

  return (
    <View style={styles.container}>
      <View style={styles.columnLayout}>
          <CollapsiblePanel
            isOpen={listOpen}
            setOpen={(open: boolean) => {
              setListOpen(!listOpen);
            }}
            backgroundStyle={{borderWidth: 0}}
            title="section"
            customHeader={
              <EditorSectionHeader
                label={label}
                name={''}
                icon={listOpen ? 'chevron-up' : 'chevron-down'}
                iconSize={18}
                iconType={'Material Icon'}
              />
          }>
            <View style={[styles.columnLayout, {paddingBottom: 10, zIndex: 1}]}>
              {Array.isArray(imageList) && (
                <SortableList
                  dragKey={`${pluginId}-${name}-list-editor`}
                  data={_.toPairs(imageList)}
                  onChange={updateImagesOrder}
                  itemComponent={ImageListItemComponent}
                  componentProps={{
                    onUpdateImageItem,
                    onDeleteImageItem,
                    disableAdd,
                    shopifyType,
                    totalItems: imageList?.length,
                    minLength: parseInt(minLength, 10),
                  }}
                />
              )}
              {!disableAdd && (<>
              <View style={[styles.rowLayout, { alignItems: 'center', }]}>
                  {!selectionOpen && <Button color='PRIMARY' variant='PILL' size='SMALL' onPress={()=>{setSelectionOpen(true)}}>
                    + Add {shopifyType ? shopifyType : 'items'}
                  </Button>}
                  {selectionOpen && <Text style={[commonStyles.baseText]}>
                    Adding {shopifyType ? shopifyType : 'items'}
                  </Text> }
                </View>
              <CollapsiblePanel 
                backgroundStyle={{borderWidth: 0}}
                isHeaderHidden={true}
                title={''}
                isOpen={selectionOpen}>
                  <ItemSelection onItemSelection={onAddItem} closeSelection={()=>{setSelectionOpen(false)}} itemType={shopifyType} />
              </CollapsiblePanel></>)}
            </View>
          </CollapsiblePanel>
      </View>
    </View>
  );
};

const ImageListItemComponent = ({
  itemVal,
  itemKey,
  disableAdd,
  totalItems,
  minLength,
  shopifyType,
  onUpdateImageItem,
  onDeleteImageItem,
}) => {
  return (
    <ImageListItem
      key={itemKey}
      imageItem={itemVal}
      disableRemove={disableAdd}
      shopifyType={shopifyType}
      minLength={minLength}
      totalItems={totalItems}
      onUpdateImageItem={val => onUpdateImageItem(itemKey, val)}
      onDeleteImageItem={() => onDeleteImageItem(itemKey)}
    />
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    padding: 2,
    marginVertical: theme.PRIMARY_MARGIN,
    minHeight: theme.PRIMARY_HEIGHT,
    justifyContent: 'center',
  },
  tooltip: {
    flexDirection: 'column',
    gap: 6,
    padding: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    backgroundColor: '#FFFFFF',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  rowLayout: {
    flex: 1,
    flexDirection: 'row',
    marginVertical: 5,
  },
  labelText: {
    fontFamily: theme.FONT_FAMILY,
    minWidth: '30%',
    maxWidth: '50%',
    marginRight: 4,
    fontSize: theme.FONT_SIZE,
    lineHeight: theme.LINE_HEIGHT,
    color: theme.FONT_COLOR,
  },
  checkboxStyle: {},
  itemImage: {
    width: 24,
    height: 24,
    overflow: 'hidden',
    borderRadius: 2,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: theme.INPUT_BORDER,
    paddingHorizontal: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContainer: {
    marginTop: 4,
    padding: 2,
    height: 200,
    overflow: 'hidden',
  },
  item: {
    width: '100%',
    flexDirection: 'row',
    paddingVertical: 5,
  },
  itemImageContainer: {
    width: 24,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    borderRadius: 2,
  },
  itemTextContainer: {
    paddingLeft: 8,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  editIconContainer: {
    width: 24,
    overflow: 'hidden',
    justifyContent: 'center',
  },
  currentItemText: {
    paddingRight: 8,
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    justifyContent: 'center',
  },
  addContainer: {
    width: '100%',
    paddingHorizontal: 12,
    paddingTop: 12,
    borderWidth: 1,
    borderColor: theme.CONTROL_BORDER,
    borderRadius: 8,
    gap: 14,
    marginTop: 8,
  },
  rowContainer: {
    alignContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    flexBasis: 'auto',
  },
});

const mapDispatchToProps = (dispatch: AppDispatch) => {
  return {
    ...bindActionCreators(
      {
        pluginConfigValueSetRaw,
      },
      dispatch,
    ),
  };
};

const mapStateToProps = () => {
  return {};
};

export default connect(mapStateToProps, mapDispatchToProps)(_ImageListEditorControl);
