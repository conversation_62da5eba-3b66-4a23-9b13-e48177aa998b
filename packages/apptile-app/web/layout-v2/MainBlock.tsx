import React, {useCallback, useEffect, useState} from 'react';
import {LayoutChangeEvent, StyleSheet, View} from 'react-native';
import {useParams} from 'react-router';
import {useDispatch} from 'react-redux';
import theme from '../styles-v2/theme';
import {changeAppConfig} from 'apptile-core';
import ApptileApp from '@/root/app/ApptileApp';
import {ApptileCanvasScaleContext} from 'apptile-core';
import AppSizeSelector from '../views/editor/components/AppSizeSelector';
import {AppSizeDefinition, appSizeDefinitions} from '../common/appSizeTypes';
import {initApptileIsEditable, initApptileTilesMode} from 'apptile-core';
import Animated, {useAnimatedStyle, useSharedValue, withSpring} from 'react-native-reanimated';
import _ from 'lodash';
import {useIsPreview} from 'apptile-core';
import MainBlockModals from './components/MainBlockModals';
import {fetchAppBranchesWithScheduledOta} from '../actions/editorActions';

const styles = StyleSheet.create({
  main: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: '1vh',
  },
  appContainerWrapper: {
    height: '100%',
    flex: 1,
    justifyContent: 'center',
    minWidth: 420,
    alignItems: 'center',
    // maxHeight: 'calc(100% - 164px)',
  },
  appContainerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginVertical: 5,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    width: '100%',
    borderWidth: 1,
    borderColor: '#BFBFBF',
    zIndex: 2,
  },
  appContainerHeaderText: {
    color: theme.SECONDARY_COLOR,
    fontSize: 12,
    fontWeight: '500',
    lineHeight: 14,
    fontFamily: theme.FONT_FAMILY,
    paddingVertical: 8,
  },
  appContainerHeaderPopover: {
    zIndex: 1,
    width: 200,
    flexDirection: 'column',
    alignItems: 'flex-start',
    height: 175,
    overflow: 'hidden',
    padding: 0,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    opacity: 1,
    marginLeft: 45,
    marginTop: 10,
  },
  appPopoverScreenText: {
    flexDirection: 'row',
    paddingVertical: 10,
    textAlign: 'left',
  },
  appContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
    margin: 'auto',
    justifyContent: 'center',
  },
  actionButtonsContainer: {
    minWidth: 480,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  actionButton: {
    marginHorizontal: 12,
  },
  appScaleContainer: {
    width: 390,
    height: 844,
    flexGrow: 0,
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    alignSelf: 'center',
    overflow: 'visible',
    backgroundColor: 'rgba(0,0,0,0)',
  },
  appCanvas: {
    top: 0,
    left: 0,
    width: 390,
    height: 844,
    position: 'absolute',
    overflow: 'hidden',
    flexGrow: 0,
    flexShrink: 0,
    borderRadius: 30,
  },
  vriantsButton: {
    position: 'absolute',
    right: 0,
    width: 50,
    height: 50,
    backgroundColor: '#f8f8f8',
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 100,
  },
});

const MainBlock = () => {
  const {id: appId, orgId, forkId, branchName} = useParams();
  const dispatch = useDispatch();
  const isPreview = useIsPreview();
  const [viewHeight, setViewHeight] = useState(400);
  const [appSizeDef, setAppSizeDef] = useState(appSizeDefinitions[0]);
  const [appWidth, setAppWidth] = useState(appSizeDefinitions[0].size.width);
  const [appHeight, setAppHeight] = useState(appSizeDefinitions[0].size.height);
  const [canvasScale, setCanvasScale] = useState(0.75);
  const av_scale = useSharedValue(0.75);

  const setAppSize = useCallback(
    (appSize: AppSizeDefinition) => {
      if (!appSize) return;
      setAppSizeDef(appSize);
      setAppWidth(appSize.size.width);
      setAppHeight(appSize.size.height);
      const sc = (viewHeight * 0.97) / appSize.size.height;
      setCanvasScale(sc);
    },
    [viewHeight],
  );
  const styleAnimatedScale = useAnimatedStyle(() => {
    return {
      transform: [
        {
          scale: withSpring(av_scale.value, {restDisplacementThreshold: 0.001, restSpeedThreshold: 0.01}),
        },
      ],
    };
  }, [av_scale]);
  const onLayout = useCallback(
    (e: LayoutChangeEvent) => {
      const vh = e.nativeEvent.layout.height;
      setViewHeight(vh);
      const sc = (vh * 0.97) / appHeight;
      setCanvasScale(sc);
      logger.info('Canvas Scale: ', sc);
    },
    [appHeight],
  );
  useEffect(() => {
    av_scale.value = canvasScale;
  }, [av_scale, canvasScale]);

  useEffect(() => {
    dispatch(changeAppConfig(appId as string, orgId as string, forkId, branchName));
  }, [appId, dispatch, orgId, forkId, branchName]);

  useEffect(() => {
    dispatch(fetchAppBranchesWithScheduledOta(appId as string, forkId));
  }, []);

  useEffect(() => {
    dispatch(initApptileTilesMode(true));
    dispatch(initApptileIsEditable(true));
  }, [dispatch]);

  return (
    <View style={styles.main} nativeID="app-editor">
      <View style={[styles.appContainerWrapper]}>
        <View style={styles.appContainer} onLayout={onLayout}>
          <Animated.View id="app-canvas" style={[styles.appScaleContainer, {width: appWidth, height: appHeight}, styleAnimatedScale]}>
            <ApptileCanvasScaleContext.Provider value={canvasScale}>
              <div id="appPreviewContainer">
                <View
                  style={[
                    styles.appCanvas,
                    {width: appWidth, height: appHeight},
                    isPreview && {borderWidth: 12, borderRadius: 50},
                  ]}>
                  <ApptileApp />
                </View>
              </div>
            </ApptileCanvasScaleContext.Provider>
          </Animated.View>
          {/* <View style={styles.vriantsButton}><Image /></View> */}
        </View>
      </View>

      <View style={styles.actionButtonsContainer}>
        {/* <Button containerStyles={styles.actionButton} variant="PILL" icon="arrow-u-left-top" />
        <Button containerStyles={styles.actionButton} variant="PILL" icon="arrow-u-right-top" />
        <Button containerStyles={styles.actionButton} variant="PILL" icon="swap-vertical" />
        <Button
          containerStyles={styles.actionButton}
          variant="TEXT"
          color="SECONDARY"
          size="LARGE"
          icon="chevron-down"
          iconPosition="RIGHT"
          onPress={() => setAppSize()}
          >
          100%
        </Button> */}
        {isPreview && <AppSizeSelector label={appSizeDef?.label} onChange={setAppSize} />}
        <MainBlockModals />
      </View>
    </View>
  );
};

export default MainBlock;
