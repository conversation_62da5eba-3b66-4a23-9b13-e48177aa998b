import {useSelector, useDispatch} from 'react-redux';
import Button from '../components-v2/base/Button';
import {EditorRootState} from '@/root/web/store/EditorRootState';
import {generateMobilePreview, generateWebPreview} from '../../../apptile-core/actions/DispatchActions';
import {closeWebPreviewModal, resetMobilePreviewStatus} from '../actions/editorActions';
import Modal from '../components-v2/base/Modal';
import {useState, useEffect, useRef} from 'react';
import PopoverComponent from '../components-v2/base/Popover';
import QRCode from 'react-qr-code';
import {ActivityIndicator} from 'react-native';
import {APPTILE_PREVIEW_SCHEME} from '../../.env.json';
import theme from '../views/prompt-to-app/styles-prompt-to-app/theme';

const WebPreview = ({appId}: {appId: string}) => {
  const dispatch = useDispatch();
  const isGeneratingWebPreview = useSelector((state: EditorRootState) => state.editor.isGeneratingWebPreview);
  const isWebPreviewModalOpen = useSelector((state: EditorRootState) => state.editor.isWebPreviewModalOpen);
  const webPreviewLink = useSelector((state: EditorRootState) => state.editor.webPreviewLink);
  const generateWebPreviewError = useSelector((state: EditorRootState) => state.editor.generateWebPreviewError);

  const [copyButtonText, setCopyButtonText] = useState('Copy Link');
  const [copyButtonStyle, setCopyButtonStyle] = useState({
    padding: '10px 15px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    marginRight: '10px',
  });

  const handleCloseModal = () => {
    dispatch(closeWebPreviewModal());
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(webPreviewLink || '');
    setCopyButtonText('Copied!');
    setCopyButtonStyle({...copyButtonStyle, backgroundColor: 'green', color: 'white'});

    let timeout = setTimeout(() => {
      setCopyButtonText('Copy Link');
      setCopyButtonStyle({
        padding: '10px 15px',
        backgroundColor: '#007bff',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer',
        marginRight: '10px',
      });

      clearTimeout(timeout);
    }, 3000);
  };

  const renderModalContent = () => {
    if (generateWebPreviewError) {
      return (
        <div style={styles.modalContentContainer}>
          <h3>Error Generating Link</h3>
          <p style={{color: 'red'}}>{generateWebPreviewError}</p>
          <Button onPress={handleCloseModal} textStyles={styles.buttonText}>
            Close
          </Button>
        </div>
      );
    }
    if (webPreviewLink) {
      return (
        <div style={styles.modalContentContainer}>
          <h3>Web Preview Link Generated!</h3>
          <p>Share this link to preview your app:</p>
          <div style={{display: 'flex', alignItems: 'center', width: '100%', marginBottom: '1rem'}}>
            <span
              style={{
                flexGrow: 1,
                marginRight: '8px',
                padding: '8px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                backgroundColor: '#f9f9f9',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
              title={webPreviewLink} // Show full link on hover
            >
              {webPreviewLink}
            </span>
            <button onClick={handleCopyLink} style={copyButtonStyle}>
              {copyButtonText}
            </button>
          </div>
          <Button onPress={handleCloseModal} textStyles={styles.buttonText}>
            Close
          </Button>
        </div>
      );
    }
    return null;
  };

  return (
    <>
      <Button
        disabled={isGeneratingWebPreview}
        loading={isGeneratingWebPreview}
        onPress={() => dispatch(generateWebPreview(appId))}
        textStyles={isGeneratingWebPreview ? {color: 'transparent'} : {}}>
        Share Link
      </Button>
      <Modal
        visible={isWebPreviewModalOpen}
        onVisibleChange={isVisible => {
          if (!isVisible) {
            handleCloseModal();
          }
        }}
        content={renderModalContent()}
        disableOutsideClick
      />
    </>
  );
};

const MobilePreview = ({appId}: {appId: string}) => {
  const dispatch = useDispatch();
  const apptile = useSelector((state: EditorRootState) => state.apptile);
  // Access the editor state via the editor property in root state
  const isGeneratingMobilePreview = useSelector((state: EditorRootState) => state.editor.isGeneratingMobilePreview);
  const mobilePreviewStatus = useSelector((state: EditorRootState) => state.editor.mobilePreviewStatus);

  // States for popover management
  const [popoverVisible, setPopoverVisible] = useState(false);
  // const [currentStep, setCurrentStep] = useState<number>(1);
  const [currentStep, setCurrentStep] = useState<number>(3);
  const initialQRValue = useRef('');

  // Reset popover steps when closed (only if not generating)
  useEffect(() => {
    // Skip this effect if we're generating a preview - don't allow state reset
    if (mobilePreviewStatus === 'loading' || isGeneratingMobilePreview) return;

    if (!popoverVisible) {
      // setCurrentStep(1);
      // Reset the mobile preview status when popover is closed
      if (mobilePreviewStatus !== 'idle') {
        dispatch(resetMobilePreviewStatus());
      }
    }
  }, [popoverVisible, mobilePreviewStatus, dispatch, isGeneratingMobilePreview]);

  // Handle step progression
  // const handleNextStep = () => {
  //   setCurrentStep(2);
  // };

  // Handle mobile preview generation
  const handleGenerateMobilePreview = () => {
    // Ensure popover stays open
    if (!popoverVisible) {
      setPopoverVisible(true);
    }

    setCurrentStep(3);
    dispatch(generateMobilePreview(appId, apptile.forkId?.toString()!, apptile.appBranch!));
  };

  // Handle retry for failed generation
  const handleRetryGeneration = () => {
    // Ensure popover stays open
    if (!popoverVisible) {
      setPopoverVisible(true);
    }

    dispatch(generateMobilePreview(appId, apptile.forkId?.toString()!, apptile.appBranch!));
  };

  // Render popover content based on current step
  const renderPopoverContent = () => {
    switch (currentStep) {
      // case 1: // Initial QR code with Next button
      //   return (
      //     <>
      //       <div style={{textAlign: 'center', marginBottom: 16, backgroundColor: theme.DEFAULT_COLOR}}>
      //         Scan this QR to install
      //         <br />
      //         the Tiles.dev Preview App
      //       </div>
      //       <QRCode value={initialQRValue.current} size={120} />
      //       <div style={{marginTop: 16}}>
      //         <Button onPress={handleNextStep} textStyles={{fontSize: 14}}>
      //           Next
      //         </Button>
      //       </div>
      //     </>
      //   );

      // case 2: // Generate Mobile Preview button - REMOVED for single-click experience
      //   return (
      //     <Button
      //       disabled={isGeneratingMobilePreview}
      //       loading={isGeneratingMobilePreview}
      //       onPress={e => {
      //         e.stopPropagation();
      //         handleGenerateMobilePreview();
      //       }}
      //       textStyles={{fontSize: 14}}>
      //       Generate Preview
      //     </Button>
      //   );

      case 3: // Final QR code with message
        return mobilePreviewStatus === 'error' ? (
          // Error state
          <div>
            <div style={{textAlign: 'center', color: 'red', marginBottom: 16}}>Failed to generate mobile preview</div>
            <div style={{textAlign: 'center'}}>
              <Button
                onPress={e => {
                  e.stopPropagation();
                  handleRetryGeneration();
                }}
                textStyles={{fontSize: 14}}>
                Retry
              </Button>
            </div>
          </div>
        ) : (
          // Loading or Success state
          <>
            <div style={{position: 'relative'}}>
              {mobilePreviewStatus === 'loading' ? (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: 120,
                    width: 120,
                  }}>
                  <ActivityIndicator />
                </div>
              ) : (
                <QRCode value={`${APPTILE_PREVIEW_SCHEME}://?APP_ID=${appId}`} size={120} />
              )}
            </div>
            <div style={{textAlign: 'center', marginTop: 16, marginBottom: 16}}>
              {mobilePreviewStatus === 'loading' ? (
                'Generating preview...'
              ) : mobilePreviewStatus === 'success' ? (
                <>
                  Preview generated!
                  <br />
                  Scan this QR to preview
                </>
              ) : (
                'Scan this QR to preview'
              )}
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <PopoverComponent
      visible={popoverVisible}
      positions={['bottom']}
      containerStyle={{padding: 16}}
      trigger={
        <Button
          disabled={isGeneratingMobilePreview}
          onPress={() => {
            // setPopoverVisible(prev => !prev);
            handleGenerateMobilePreview();
          }}>
          Generate Mobile Preview
        </Button>
      }>
      <div style={styles.popoverContentDiv}>{renderPopoverContent()}</div>
    </PopoverComponent>
  );
};

const PreviewModule = ({appId}: {appId: string}) => {
  return (
    <div style={{marginRight: 10}}>
      <br />
      <WebPreview appId={appId} />
      <br />
      <MobilePreview appId={appId} />
    </div>
  );
};

const styles = {
  modalContentContainer: {
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 8,
    alignItems: 'center',
    minWidth: 300,
  },
  linkInput: {
    width: '90%',
    padding: '8px',
    margin: '10px 0',
    border: '1px solid #ccc',
    borderRadius: '4px',
  },
  copyButton: {
    padding: '10px 15px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    marginRight: '10px',
  },
  buttonText: {},
  popoverContentDiv: {
    backgroundColor: theme.DEFAULT_COLOR,
    width: 200,
    marginTop: 10,
    border: '1px solid black',
    borderRadius: 5,
    padding: '16px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  } as React.CSSProperties,
};

export default PreviewModule;
