import React, {useEffect, useRef, useState} from 'react';
import {useLocation, useMatch, useNavigate, useParams} from 'react-router';
import {Image, Pressable, StyleSheet, View} from 'react-native';
import Animated, {
  Easing,
  Extrapolation,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';

import NavigationTree from '../components-v2/NavigationTree';
import Button from '../components-v2/base/Button';
import Tabs from '../components-v2/composite/Tabs';
import TilesExplorer from '../components-v2/TilesExplorer';
import MenuItem from '../components-v2/base/Menu';
import {getMappedTileTagsForScreen} from '../common/tileConstants';
import {LeftPane as NotificationLeftPaneContent} from '@/root/web/views/notificationAdmin/shared/leftPane';
import {allAvailablePlans, MaterialCommunityIcons, SettingsConfig} from 'apptile-core';

import _, {pick} from 'lodash';
import BrandSettings from '@/root/web/views/settings/brand';
import {useDispatch, useSelector} from 'react-redux';
import {selectActiveScreen, selectSelectedPluginConfig} from '../selectors/EditorSelectors';
import {
  fetchAppIntegrations,
  fetchIntegrations,
  saveAppState,
  fetchIntegrationCategories,
} from '../actions/editorActions';
import {useIsPreview} from 'apptile-core';
import TextElement from '../components-v2/base/TextElement';
import {EditorRootState} from '../store/EditorRootState';
import {changeOnboardingMetadata, fetchBrand} from '../actions/onboardingActions';
import {getOnboardingMetadataWithKey} from '../selectors/OnboardingSelector';
import {GO_TO_TILES_CLICKED} from '../common/onboardingConstants';
import {BrandSettingsTypes} from 'apptile-core';
const BRAND_EXPOSED_TILES = BrandSettingsTypes.BRAND_EXPOSED_TILES,
  BRAND_SETTINGS_KEY = BrandSettingsTypes.BRAND_SETTINGS_KEY,
  BRAND_TILE_TAG = BrandSettingsTypes.BRAND_TILE_TAG,
  BRAND_TILE_TAG_DISPLAY = BrandSettingsTypes.BRAND_TILE_TAG_DISPLAY;
import {selectAppSettingsForKey} from 'apptile-core';
import {selectMandatoryCheck} from '../selectors/EditorModuleSelectors';
import {checkApptileEmailSelector, currentPlanFeaturesSelector} from '../selectors/FeatureGatingSelector';
import {Snapshots} from '../components-v2/Snapshots';
import {LanguageAndRegion} from '../components-v2/LanguageAndRegion';
import {navigationTreeComponent} from '../index.web';
import BlueprintsApi from '../api/BlueprintsApi';
import useWebSDKUpdate from '../common/useWebSDKUpdate';

const styles = StyleSheet.create({
  sidebarContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  sidebarLeft: {
    height: '100%',
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
    zIndex: 10,
    borderRightWidth: 1,
    borderColor: '#E5E5E5',
    paddingHorizontal: 10,
  },
  sidebarHeader: {
    height: '8vh',
    minHeight: 'calc(2vh + 48px)',
    paddingVertical: '1vh',
    flexDirection: 'row',
    marginBottom: 40,
  },
  logo: {
    width: 120,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  logoSmall: {
    width: 36,
    height: 36,
    marginHorizontal: 9,
    marginVertical: 11,
  },
  sidebarLeftSecondary: {
    position: 'absolute',
    top: 0,
    left: 80,
    bottom: 0,
    flex: 1,
    height: '100%',
    backgroundColor: '#fff',
    overflow: 'hidden',
    zIndex: -1,
  },
  sidebarContentContainer: {
    flex: 1,
    overflow: 'scroll',
  },
  leftSidebarWrapper: {width: 270},
  toTilesWrapper: {
    position: 'absolute',
    left: 80,
    top: 0,
    zIndex: -1,
    backgroundColor: '#F8FBF8',
    paddingHorizontal: 30,
    borderRadius: 21,
    height: 200,
    justifyContent: 'center',
    shadowColor: 'rgba(99, 99, 99, 0.45)',
    shadowOffset: {width: 0, height: 0},
    shadowRadius: 40,
  },
  tooltipMenuWrapper: {
    position: 'absolute',
    left: 0,
    top: -17,
    backgroundColor: '#000000',
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    zIndex: 9,
    height: 25,
  },
  leftEditorSidebarText: {
    fontSize: 12,
  },
  loaderImage: {
    width: 60,
    height: 60,
  },
});

type LeftSidebarProps = {
  mainBar?: string;
  secondaryBar?: string;
  customMainBar?: any;
};

const ToTilesModal = ({secondarySidebarZIndex}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const changeOnboarding = () => {
    dispatch(changeOnboardingMetadata({[GO_TO_TILES_CLICKED]: true}));
  };
  const onTileClicked = () => {
    navigate('../tiles');
    changeOnboarding();
  };

  const tileButtonClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, GO_TO_TILES_CLICKED),
  );

  const toTilesWrapperAnimation = useAnimatedStyle(() => ({
    transform: [{scale: secondarySidebarZIndex && !tileButtonClicked ? 1 : 0}],
    // display: secondarySidebarZIndex ? 'flex' : 'none',
    zIndex: secondarySidebarZIndex && !tileButtonClicked ? 9 : -1,
  }));

  return (
    <Animated.View style={[styles.toTilesWrapper, toTilesWrapperAnimation]}>
      <View style={{position: 'absolute', top: 53, left: -40}}>
        <MaterialCommunityIcons style={{fontSize: 70}} name={'menu-left'} color={'#F8FBF8'} />
      </View>
      <View style={{position: 'absolute', top: 20, right: 20}}>
        <Pressable onPress={changeOnboarding}>
          <MaterialCommunityIcons style={{fontSize: 20}} name={'close'} color={'#000000'} />
        </Pressable>
      </View>
      <TextElement color="SECONDARY" style={{marginBottom: 20}}>
        You are all set!
      </TextElement>
      <TextElement color="SECONDARY" style={{marginBottom: 20}}>
        Start building your app with{' '}
        <TextElement color="SECONDARY" style={{fontWeight: '700'}}>
          Tiles
        </TextElement>
      </TextElement>
      <Button onPress={onTileClicked} containerStyles={{height: 50, width: 200}} color="DEFAULT">
        Go to Tiles
      </Button>
    </Animated.View>
  );
};

const getShopPrimaryDomainReducer = (state: EditorRootState) => {
  return state.appModel?.getModelValue(['shopify', 'shop', 'primaryDomain', 'host']);
};

const LeftSidebar: React.FC<LeftSidebarProps> = props => {
  const {mainBar: currentMainBar, secondaryBar: currentSecondaryBar, customMainBar: CustomMainBar} = props;
  const match = useMatch('/dashboard/:orgId/app/:id');
  const {appId} = useSelector((state: EditorRootState) => state.apptile);
  const moduleConfig = useSelector(state => selectSelectedPluginConfig(state));
  const prevModuleConfig = useRef<Object | null>(null);

  const [mainBar, setMainBar] = React.useState(currentMainBar ?? '');
  const [secondaryBar, setSecondaryBar] = React.useState(currentSecondaryBar ?? '');

  //Closing the secondary bar based on the click of a tile once!
  if (prevModuleConfig.current === null && moduleConfig) {
    // Dispatch your action here
    if (secondaryBar === 'brand-settings') setSecondaryBar('');
    // Update the previous value ref
    prevModuleConfig.current = moduleConfig;
  }

  const appIntegrationIds = useSelector(state => state.integration.appIntegrationIds);
  const integrationsById = useSelector(state => state.integration.integrationsById);
  const appIntegrations = Object.values(pick(integrationsById, appIntegrationIds)).map(item => item.integrationCode);

  const navigate = useNavigate();
  const isPreview = useIsPreview();
  const params = useParams();
  const [secondarySidebarZIndex, setSecondarySidebarZIndex] = useState(false);
  const sidebarLeftSecondaryTranslateX = useSharedValue(-450);
  const [hasTileAnimationEverHappened, setTileAnimationEverHappend] = useState<boolean>(false);
  const [isTileAnimationHappening, setTileAnimationStatus] = useState<boolean>(false);
  useEffect(() => {
    !secondaryBar && setSecondarySidebarZIndex(false);
    sidebarLeftSecondaryTranslateX.value = withTiming(
      !secondaryBar || isPreview ? -450 : 0,
      {
        duration: 680,
        easing: Easing.bezier(0.25, 0.1, 0.25, 1),
      },
      () => {
        secondaryBar && setSecondarySidebarZIndex(true);
      },
    );
  }, [isPreview, secondaryBar, sidebarLeftSecondaryTranslateX]);
  const sidebarLeftSecondaryAnimation = useAnimatedStyle(() => ({
    transform: [{translateX: sidebarLeftSecondaryTranslateX.value}],
    zIndex: secondarySidebarZIndex ? 9 : -1,
  }));

  const sidebarWidth = useSharedValue(270);
  useEffect(() => {
    sidebarWidth.value = withTiming(isPreview ? 0 : secondaryBar ? 80 : 270, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, secondaryBar, sidebarWidth]);
  const sidebarLeftAnimation = useAnimatedStyle(() => ({
    width: sidebarWidth.value,
    // paddingHorizontal: interpolate(sidebarWidth.value, [0, 80], {extrapolateRight: Extrapolation.CLAMP}),
  }));

  //Fetching brand
  const dispatch = useDispatch();
  const primaryDomain = useSelector(getShopPrimaryDomainReducer);
  useEffect(() => {
    if (!_.isEmpty(primaryDomain)) dispatch(fetchBrand(primaryDomain));
  }, [dispatch, primaryDomain]);

  let leftpane = (
    <LeftPaneContent
      mainBar={mainBar}
      setMainBar={setMainBar}
      secondaryBar={secondaryBar}
      setSecondaryBar={setSecondaryBar}
    />
  );
  if (CustomMainBar) {
    leftpane = (
      <CustomMainBar
        mainBar={mainBar}
        setMainBar={setMainBar}
        secondaryBar={secondaryBar}
        setSecondaryBar={setSecondaryBar}
      />
    );
  }

  return (
    <View style={[styles.leftSidebarWrapper, secondaryBar ? {zIndex: 1} : {}]}>
      <View style={[styles.sidebarContainer, match ? {width: 340} : {}]}>
        <Animated.View style={[styles.sidebarLeft, sidebarLeftAnimation]}>
          {mainBar !== 'APP_EDITOR' && (
            <View style={[styles.sidebarHeader]}>
              <Pressable
                onPress={() => {
                  navigate(`/dashboard/${params.orgId}/app/${params.id}`);
                }}>
                {!secondaryBar ? (
                  <Image
                    style={styles.logo}
                    source={require('@/root/web/assets/images/logo.png')}
                    resizeMode="contain"
                  />
                ) : (
                  <Image
                    style={styles.logoSmall}
                    source={require('@/root/web/assets/images/apptile_icon.png')}
                    resizeMode="contain"
                  />
                )}
              </Pressable>
            </View>
          )}
          {leftpane}
        </Animated.View>
      </View>
      <Animated.View style={[styles.sidebarLeftSecondary, sidebarLeftSecondaryAnimation, {overflow: 'visible'}]}>
        <View
          style={[
            styles.sidebarContentContainer,
            {
              overflow: isTileAnimationHappening ? 'visible' : 'scroll',
              paddingRight: isTileAnimationHappening ? '8px' : '0px',
            },
            {width: secondaryBar === 'tiles' || secondaryBar === 'brand-settings' ? 330 : 260},
            {},
          ]}>
          {secondaryBar === 'pages' && React.createElement(navigationTreeComponent())}
          {secondaryBar === 'snapshots' && <Snapshots />}
          {secondaryBar === 'languageAndRegion' && <LanguageAndRegion />}

          {secondaryBar === 'tiles' && (
            <View style={{flex: 1}}>
              <AvailableTilesComponent
                appId={appId}
                appIntegrations={appIntegrations}
                appIntegrationObj={integrationsById}
                tileAnimation={{
                  hasTileAnimationEverHappened,
                  isTileAnimationHappening,
                  setTileAnimationEverHappend,
                  setTileAnimationStatus,
                }}
              />
            </View>
          )}
          {secondaryBar === 'brand-settings' && <BrandSettings />}
        </View>
      </Animated.View>
      {secondaryBar === 'brand-settings' && <ToTilesModal secondarySidebarZIndex={secondarySidebarZIndex} />}
    </View>
  );
};

const AvailableTilesComponent = ({appId, appIntegrations, appIntegrationObj, tileAnimation}: any) => {
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(fetchAppIntegrations(appId));
    dispatch(fetchIntegrations());
    dispatch(fetchIntegrationCategories());
  }, []);

  const activeScreen = useSelector(selectActiveScreen);

  const settingsSelector = settingsKey => state => selectAppSettingsForKey(state, settingsKey);
  const brandSettings: SettingsConfig = useSelector(settingsSelector(BRAND_SETTINGS_KEY));
  const brandSpecificTag = brandSettings.getSettingValue(BRAND_TILE_TAG) ?? '';
  const brandSpecificTagDisplay = brandSettings.getSettingValue(BRAND_TILE_TAG_DISPLAY) ?? '';
  const brandExposedTiles = brandSettings.getSettingValue(BRAND_EXPOSED_TILES) ?? '';
  const [exposedModules, setExposedModules] = useState<string[]>(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  const [blueprintDetails, setBlueprintDetails] = useState(null);
  const activeBlueprintUUID = useSelector((state: EditorRootState) => state.appConfig.current?.blueprintUUID);
  const {blueprints} = useSelector((state: EditorRootState) => state.blueprint);
  useEffect(() => {
    setExposedModules(brandExposedTiles?.split(',')?.filter(e => e) ?? []);
  }, [brandExposedTiles]);

  let [tileTags, setTileTags] = useState({});

  useEffect(() => {
    if (activeBlueprintUUID && typeof activeBlueprintUUID === 'string') {
      const blueprint = blueprints.find(blueprint => blueprint.id === activeBlueprintUUID);
      setBlueprintDetails(blueprint);
    }
  }, [activeBlueprintUUID, blueprints]);

  useEffect(() => {
    let mappedTiles = getMappedTileTagsForScreen(activeScreen?.[0]?.name);
    if (brandSpecificTag && brandSpecificTagDisplay && brandSpecificTag.trim() && brandSpecificTagDisplay.trim()) {
      mappedTiles = {...{[brandSpecificTag.trim()]: brandSpecificTagDisplay.trim()}, ...mappedTiles};
    }
    if (exposedModules.length > 0) {
      mappedTiles = {...{exposedTiles: 'My Tiles'}, ...mappedTiles};
    }

    if (blueprintDetails?.slug) {
      mappedTiles = {...{themeTiles: `${blueprintDetails?.slug}`}, ...mappedTiles};
    }
    setTileTags(mappedTiles);
  }, [activeScreen, exposedModules, blueprintDetails?.slug, setTileTags]);

  return (
    <Tabs
      rootStyles={{flex: 1, flexBasis: 'auto', padding: 10}}
      tabs={Object.keys(tileTags).map(tag => ({
        title: tileTags[tag],
        disableScroll: true,
        component: !blueprintDetails?.slug ? (
          <Image style={[styles.loaderImage]} source={require('@/root/web/assets/images/preloader.svg')} />
        ) : (
          <TilesExplorer
            tags={[tag]}
            appId={appId}
            themeSlug={blueprintDetails?.slug}
            appIntegrations={appIntegrations}
            appIntegrationObj={appIntegrationObj}
            tileAnimation={tileAnimation}
            screenBasedTiles={tileTags}
            legacyMode={false}
          />
        ),
      }))}
      noOfLines={2}
      activeVariant="FILLED-PILL"
      inactiveVariant="FILLED-PILL"
      activeColor="PRIMARY"
      inactiveColor="NEW_TAB"
      activeOpaque={true}
      size="SMALL"
      key={`tiles-blueprint-fetched-${!!blueprintDetails?.slug}`}
    />
  );
};

type LeftPaneContentProps = {
  mainBar: string;
  setMainBar: (val: string) => void;
  secondaryBar: string;
  setSecondaryBar: (val: string) => void;
};

const LeftPaneContent: React.FC<LeftPaneContentProps> = props => {
  const {mainBar} = props;

  switch (mainBar) {
    case 'DASHBOARD':
      return <DashBoardLeftPaneContent {...props} />;
    case 'MANUAL_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent />;
    case 'AUTOMATED_NOTIFICATION_PLAYGROUND':
      return <NotificationLeftPaneContent isAutomatedCampaign />;
    case 'APP_EDITOR':
      return <AppEditorLeftPaneContent {...props} />;
    case 'APP_SETTINGS':
      return <SettingsLeftPaneContent {...props} />;
    default:
      return <></>;
  }
};

type DashBoardLeftPaneContentProps = {} & LeftPaneContentProps;

const DashBoardLeftPaneContent: React.FC<DashBoardLeftPaneContentProps> = props => {
  const {setSecondaryBar} = props;

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const params = useParams();
  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;

  const [activeItem, setActiveItem] = React.useState<string>();

  useEffect(() => {
    setActiveItem(params['*']);
  }, [params['*']]);

  let defaultTabItems = {
    Dashboard: {
      isVisible: true,
      id: 'store',
      text: 'Dashboard',
      icon: 'house-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'store',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('store');
        navigate('store');
      },
    },
    YourDesign: {
      isVisible: true,
      id: 'design',
      text: 'Design',
      icon: 'your-design',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'design',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('design');
        navigate(`./../app-editor`);
      },
    },
    Notifications: {
      isVisible: true,
      id: 'notification',
      text: 'Notifications',
      icon: 'bell-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'notifications',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('notifications');
        navigate(`notifications`);
      },
    },
    Analytics: {
      isVisible: true,
      id: 'analytics',
      text: 'Analytics',
      icon: 'analytics-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'analytics',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('analytics');
        navigate(`analytics`);
      },
    },
    Integrations: {
      isVisible: true,
      id: 'integrations',
      text: 'Integrations',
      icon: 'download-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'integrations',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('integrations');
        navigate(`integrations`);
      },
    },
    Live: {
      isVisible: true,
      id: 'live',
      text: 'Live',
      icon: 'live-tv',
      iconType: 'MaterialIcons',
      isActive: activeItem === 'live',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('live');
        navigate(`live`);
      },
    },
    Themes: {
      isVisible: true,
      id: 'themes',
      text: 'Themes',
      icon: 'themes',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'themes',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('themes');
        navigate(`themes`);
      },
    },
    Settings: {
      isVisible: true,
      id: 'settings',
      text: 'Settings',
      icon: 'gear-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'settings',
      isComingSoon: false,
      onPress: () => {
        setSecondaryBar('');
        setActiveItem('settings');
        navigate(`settings`);
      },
    },
    Support: {
      isVisible: true,
      id: 'support',
      text: 'Help & Support',
      icon: 'book-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'support',
      isComingSoon: false,
      onPress: () => {
        window.open('https://apptile.com/help/', '_blank');
        setSecondaryBar('');
        setActiveItem('support');
      },
    },
    Build: {
      isVisible: isApptileEmail,
      id: 'build',
      text: 'Build',
      icon: 'book-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'support',
      isComingSoon: false,
      onPress: () => {
        navigate(
          `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/publish`,
        );
        setSecondaryBar('');
        setActiveItem('builds');
      },
    },
    Legacy: {
      isVisible: isApptileEmail,
      id: 'legacy',
      text: 'Studio',
      icon: 'book-outline',
      iconType: 'ApptileWebIcons',
      isActive: activeItem === 'support',
      isComingSoon: false,
      onPress: () => {
        window.open(
          `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/studio`,
          '_self',
        );
        setSecondaryBar('');
        setActiveItem('legacy');
      },
    },
    FORCE: {
      isVisible: isApptileEmail,
      id: 'FORCE',
      text: 'Force Save',
      icon: 'alert',
      isActive: activeItem === 'support',
      isComingSoon: false,
      onPress: () => {
        if (confirm('Are you sure you want to force save?')) {
          dispatch(saveAppState(false, true, 'FORCE SAVED', true, true));
        }
      },
    },
  };

  const externalDashboardRoutes = window.apptileWebSDK?.moduleExports?.dashboardRoutes;
  if (externalDashboardRoutes) {
    Object.keys(externalDashboardRoutes).forEach(key => {
      defaultTabItems[key] = {
        isVisible: externalDashboardRoutes[key].config.isVisible,
        id: externalDashboardRoutes[key].config.id,
        text: externalDashboardRoutes[key].config.text,
        icon: externalDashboardRoutes[key].config.icon,
        iconType: externalDashboardRoutes[key].config.iconType,
        isActive: activeItem === externalDashboardRoutes[key].config.id,
        isComingSoon: externalDashboardRoutes[key].config.isComingSoon,
        onPress: () => {
          setSecondaryBar('');
          setActiveItem(externalDashboardRoutes[key].config.id);
          navigate(externalDashboardRoutes[key].config.id);
        },
      };
    });
  }

  return (
    <View style={[styles.sidebarContentContainer, {overflowX: 'hidden'}]}>
      {Object.keys(defaultTabItems).map(key => {
        return (
          defaultTabItems[key].isVisible && (
            <MenuItem
              isComingSoon={defaultTabItems[key].isComingSoon}
              id={key}
              text={defaultTabItems[key].text}
              icon={defaultTabItems[key].icon}
              iconType={defaultTabItems[key].iconType}
              isActive={defaultTabItems[key].isActive}
              onPress={defaultTabItems[key].onPress}
            />
          )
        );
      })}
    </View>
  );
};

type AppEditorLeftPaneContentProps = {} & LeftPaneContentProps;

const availableAppEditorPanels = ['tiles', 'brand-settings', 'pages'];

const AppEditorLeftPaneContent: React.FC<AppEditorLeftPaneContentProps> = props => {
  const {secondaryBar, setSecondaryBar} = props;
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();
  const forks = useSelector(state => state.forks);

  const {panelName} = params;

  const [activeItem, setActiveItem] = React.useState<string>();

  useEffect(() => {
    if (panelName && _.includes(availableAppEditorPanels, panelName)) {
      setActiveItem(panelName);
      setSecondaryBar(panelName);
    } else {
      setSecondaryBar('tiles');
      setActiveItem('tiles');
    }
  }, [setSecondaryBar, location.pathname, location, panelName]);

  const mandatoryFields = useSelector(selectMandatoryCheck());
  const isApptileEmail = useSelector(checkApptileEmailSelector) ?? false;
  const currentPlanFeatures = useSelector(currentPlanFeaturesSelector);
  const isFeatureDisabled = !currentPlanFeatures.includes(allAvailablePlans.ENTERPRISE);

  const availableForkIds = forks?.appForkIds;

  let editorLeftSidebarItems;

  if (window.apptileWebSDK?.moduleExports?.editorLeftSidebarItems) {
    editorLeftSidebarItems = window.apptileWebSDK?.moduleExports?.editorLeftSidebarItems;
  } else {
    editorLeftSidebarItems = {
      languageAndRegion: {
        isVisible: availableForkIds?.length > 1 || isApptileEmail,
        id: 'languageAndRegion',
        text: 'Language & Region',
        icon: 'language-outline',
        iconType: 'Ionicons',
        isActive: activeItem === 'languageAndRegion',
        onPress: () => {
          if (activeItem === 'languageAndRegion') {
            setActiveItem('');
            setSecondaryBar('');
          } else {
            setActiveItem('languageAndRegion');
            setSecondaryBar('languageAndRegion');
          }
        },
      },
      snapshots: {
        isVisible: isApptileEmail || !isFeatureDisabled,
        id: 'snapshots',
        text: 'Version',
        icon: 'snapshots-outline',
        iconType: 'ApptileWebIcons',
        isActive: activeItem === 'snapshots',
        onPress: () => {
          setActiveItem('snapshots');
          setSecondaryBar('snapshots');
        },
      },
    };
  }

  return (
    <View style={[styles.sidebarContentContainer, {overflow: 'hidden'}]}>
      <MenuItem
        id={'dashboard'}
        text={'Dashboard'}
        icon={'back-arrow'}
        iconType="ApptileWebIcons"
        isActive={false}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('');
          navigate(
            `/dashboard/${params?.orgId}/app/${params?.id}/f/${params.forkId}/b/${params.branchName}/dashboard/store`,
          );
        }}
      />

      <MenuItem
        id={'tiles'}
        text={'Tiles'}
        icon={'tiles'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'tiles'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('tiles');
          setSecondaryBar('tiles');
        }}
      />

      <MenuItem
        id={'pages'}
        text={'Pages'}
        icon={'pages-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'pages'}
        redDot={mandatoryFields?.check ?? false}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('pages');
          setSecondaryBar('pages');
        }}
      />

      <MenuItem
        id={'brand-settings'}
        text={'Brand'}
        icon={'brands-outline'}
        iconType="ApptileWebIcons"
        isActive={activeItem === 'brand-settings'}
        vertical={!!secondaryBar}
        textStyles={styles.leftEditorSidebarText}
        onPress={() => {
          setActiveItem('brand-settings');
          setSecondaryBar('brand-settings');
        }}
      />

      <View style={{position: 'absolute', bottom: 0}}>
        {Object.keys(editorLeftSidebarItems).map(key => {
          return (
            editorLeftSidebarItems[key].isVisible && (
              <MenuItem
                key={key}
                id={editorLeftSidebarItems[key].id}
                text={editorLeftSidebarItems[key].text}
                icon={editorLeftSidebarItems[key].icon}
                iconType={editorLeftSidebarItems[key].iconType}
                isActive={editorLeftSidebarItems[key].isActive}
                vertical={!!secondaryBar}
                textStyles={styles.leftEditorSidebarText}
                onPress={editorLeftSidebarItems[key].onPress}
              />
            )
          );
        })}
      </View>
    </View>
  );
};

type SettingsLeftPaneContentProps = {} & LeftPaneContentProps;

export const SettingsLeftPaneContent: React.FC<SettingsLeftPaneContentProps> = props => {
  const {secondaryBar, setSecondaryBar} = props;
  const location = useLocation();
  const navigate = useNavigate();

  const [activeItem, setActiveItem] = React.useState<string>('');

  useEffect(() => {
    setSecondaryBar('');
    setActiveItem('');
  }, [setSecondaryBar, location.pathname, location]);

  const params = useParams();
  const customNavigate = path => {
    if (params.planId) {
      navigate('./../../../' + path);
    } else {
      navigate('./../' + path);
    }
  };

  return (
    <View style={[styles.sidebarContentContainer, {overflow: 'hidden'}]}>
      <MenuItem
        id={'settings'}
        text={'Settings'}
        icon={'back-arrow'}
        iconType="ApptileWebIcons"
        isActive={false}
        onPress={() => {
          setSecondaryBar('');
          setActiveItem('');
          customNavigate('');
        }}
      />
      <MenuItem
        id={'yourAccount'}
        text={'Your account'}
        icon={'account-cog'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'yourAccount'}
        onPress={() => {
          customNavigate('your-account');
          setActiveItem('yourAccount');
        }}
      />
      <MenuItem
        id={'billingAccount'}
        text={'Billing account'}
        icon={'point-of-sale'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'billingAccount'}
        onPress={() => {
          customNavigate('billing');
          setActiveItem('billingAccount');
        }}
      />
      {/* <MenuItem
        id={'appListing'}
        text={'App Listing'}
        icon={'rocket-launch'}
        iconType="MaterialCommunityIcons"
        isActive={activeItem === 'appListing'}
        onPress={() => {
          customNavigate('app-listing');
          setActiveItem('appListing');
        }}
      /> */}
    </View>
  );
};

export default LeftSidebar;
