import React, {useEffect, useState} from 'react';
import {DndProvider} from 'react-dnd';
import {HTML5Backend} from 'react-dnd-html5-backend';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Provider} from 'react-redux';
import {rootSaga} from 'apptile-core';
import {store, sagaMiddleware} from 'apptile-core';
import {injectReducer} from 'apptile-core';
import {WebFontLoader} from '@/root/web/views/settings/brand/fonts';
import {WebToastProvider} from './components/utils';
import {Route, Router, Routes, useLocation} from './routing.web';
import appSagas from './sagas/AppSaga';
import assetSagas from './sagas/AssetSaga';
import billingSagas from './sagas/BillingSaga';
import editorActionSagas from './sagas/editorActionsSaga';
import editorModuleActionsSagas from './sagas/editorModuleActionsSaga';
import orgSagas from './sagas/OrgSaga';
import platformSagas from './sagas/PlatformSaga';
import themeSagas from './sagas/ThemeSaga';
import userSagas from './sagas/UserSaga';
import settingsSagas from './sagas/SettingsSaga';
import {AssetReducer} from './store/AssetReducer';
import {BillingReducer} from './store/BillingReducer';
import {EditorReducer} from './store/EditorReducer';
import {OrgsReducer} from './store/OrgReducer';
import {PlatformReducer} from './store/PlatformReducer';
import {ToastReducer} from './store/ToastReducer';
import {UserReducer} from './store/UserReducer';
import {BranchReducer} from './store/BranchReducer';
import Login from './views/auth/Login';
import Signup from './views/auth/Signup';
import PlatformInit from './views/platform/PlatformInit';
import {IntegrationReducer} from './store/IntegrationReducer';
import integrationSagas from './sagas/IntegrationSaga';
import TilesActionsSaga from './sagas/TilesActionsSaga';
import PagesActionSaga from './sagas/PagesActionSaga';
import BlueprintActionSaga from './sagas/blueprintActionSaga';
import {TilesReducer} from './store/TilesReducer';
import {BlueprintReducer} from './store/BlueprintReducer';
import {PageReducer} from './store/PageReducer';
import editorAppConfigSagas from './sagas/editorAppConfigSaga';
import onboardingSagas from './sagas/OnboardingSaga';
import appBranchesSaga from './sagas/AppBranchSaga';
import {OnboardingReducer} from './store/onboardingReducer';
// import {IntercomProvider} from 'react-use-intercom';
// const INTERCOM_APP_ID = 'fdm0l8jd';

import Analytics from '@/root/web/lib/segment';
import {APPTILE_SEGMENT_WRITE_KEY} from '../.env.json';
import {ShopifyReducer} from './store/ShopifyReducer';
import shopifySagas from './sagas/ShopifySaga';
import {addCustomEventListener, selectPlugin} from 'apptile-core';
import {editorSetActiveAttachmentId, editorSetActiveAttachmentKey} from '@/root/web/actions/editorActions';

import {GetRegisteredNativePage} from '../app/views/prebuilt';
import {WEB_API_SERVER_ENDPOINT, APPTILE_VERTICAL_TYPE} from '../.env.json';
import EditableWidget from '../app/plugins/widgets/common/components/EditableWidget';
import {getLinkingPrefixesInDevAndWeb} from '../app/common/utils/getLinkingPrefixes';
import {getLinkingPrefixesInDistributedApp} from '../app/common/utils/mobile-only';
import {makeToast} from './actions/toastActions';
import {AppForkReducer} from './store/AppForkReducer';
import appForksSaga from './sagas/AppForkSaga';
import {SnapshotReducer} from './store/SnapshotReducer';
import liveSellingSagas from './sagas/liveSellingSaga';
import {LiveSellingReducer} from './store/liveSellingReducer';
import LiveSellingApp from './LiveSellingApp';
import {CartAssistReducer} from './store/CartAssistReducer';
import {AIReducer} from './store/AIReducer';
import AISaga from './sagas/AISaga';
import TileLogin from './views/prompt-to-app/components/auth/TileLogin';

const isTileDev = APPTILE_VERTICAL_TYPE === 'tile';

global.ENABLE_LOGROCKET = process.env.ENABLE_LOGROCKET;
global.WEB_API_SERVER_ENDPOINT = WEB_API_SERVER_ENDPOINT;
global.GetRegisteredNativePage = GetRegisteredNativePage;
global.EditableWidget = EditableWidget;
global.getLinkingPrefixesInDevAndWeb = getLinkingPrefixesInDevAndWeb;
global.getLinkingPrefixesInDistributedApp = getLinkingPrefixesInDistributedApp;
globalThis.store = store;

addCustomEventListener('editorSetActiveAttachmentId', pageId => {
  store.dispatch(editorSetActiveAttachmentId(pageId));
});

addCustomEventListener('editorSetActiveAttachmentKey', pageKey => {
  store.dispatch(editorSetActiveAttachmentKey(pageKey));
});

addCustomEventListener('makeToast', data => {
  store.dispatch(makeToast(data));
});

addCustomEventListener('renameSelectedPlugin', renamedId => {
  const state = editor.getState();
  const selectSelectedPluginSelector = state.editor.selectedPluginConfigSel;
  const selectedPluginSel = selectSelectedPluginSelector(state);
  selectedPluginSel[selectedPluginSel.length - 1] = renamedId;
  store.dispatch(selectPlugin(selectedPluginSel));
});

if (!__DEV__ && APPTILE_SEGMENT_WRITE_KEY) {
  try {
    Analytics.load(
      {writeKey: APPTILE_SEGMENT_WRITE_KEY, cdnURL: 'https://se-analytics.apptile.io'},
      {
        integrations: {
          'Segment.io': {
            apiHost: 'se-api.apptile.io/v1',
            protocol: 'https', // optional
          },
        },
      },
    );
  } catch (error) {
    logger.error('Segment identify: ', error);
  }
}

const WebRoutes: React.FC = () => {
  useLocation();
  Analytics.page();

  return (
    <Routes>
      <Route path="/login" element={isTileDev ? <TileLogin /> : <Login />} />
      <Route path="/signup" element={<Signup />} />
      <Route path="/live-selling/*" element={<LiveSellingApp />} />
      <Route path="*" element={<PlatformInit />} />
    </Routes>
  );
};

const WebApp = () => {
  const [booted, setBooted] = useState(false);

  useEffect(() => {
    injectReducer(store, {key: 'editor', reducer: EditorReducer});
    injectReducer(store, {key: 'user', reducer: UserReducer});
    injectReducer(store, {key: 'orgs', reducer: OrgsReducer});
    injectReducer(store, {key: 'asset', reducer: AssetReducer});
    injectReducer(store, {key: 'toast', reducer: ToastReducer});
    injectReducer(store, {key: 'platform', reducer: PlatformReducer});
    injectReducer(store, {key: 'billing', reducer: BillingReducer});
    injectReducer(store, {key: 'integration', reducer: IntegrationReducer});
    injectReducer(store, {key: 'tiles', reducer: TilesReducer});
    injectReducer(store, {key: 'blueprint', reducer: BlueprintReducer});
    injectReducer(store, {key: 'pages', reducer: PageReducer});
    injectReducer(store, {key: 'onboarding', reducer: OnboardingReducer});
    injectReducer(store, {key: 'liveSelling', reducer: LiveSellingReducer});
    injectReducer(store, {key: 'shopify', reducer: ShopifyReducer});
    injectReducer(store, {key: 'branches', reducer: BranchReducer});
    injectReducer(store, {key: 'forks', reducer: AppForkReducer});
    injectReducer(store, {key: 'snapshots', reducer: SnapshotReducer});
    injectReducer(store, {key: 'cartAssist', reducer: CartAssistReducer});
    injectReducer(store, {key: 'ai', reducer: AIReducer});

    sagaMiddleware.run(rootSaga, [
      editorActionSagas,
      editorModuleActionsSagas,
      userSagas,
      orgSagas,
      appSagas,
      assetSagas,
      themeSagas,
      billingSagas,
      platformSagas,
      settingsSagas,
      platformSagas,
      integrationSagas,
      TilesActionsSaga,
      editorAppConfigSagas,
      PagesActionSaga,
      BlueprintActionSaga,
      onboardingSagas,
      liveSellingSagas,
      shopifySagas,
      appBranchesSaga,
      appForksSaga,
      AISaga,
    ]);
    setBooted(true);
  }, []);

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      {booted ? (
        <Provider store={store}>
          <WebFontLoader />
          <DndProvider backend={HTML5Backend}>
            <WebToastProvider>
              <Router>
                <WebRoutes />
              </Router>
            </WebToastProvider>
          </DndProvider>
        </Provider>
      ) : (
        <></>
      )}
    </GestureHandlerRootView>
  );
};

const WebAppRoot = () => {
  return (
    // <IntercomProvider appId={INTERCOM_APP_ID} autoBoot={!__DEV__}>
    <WebApp />
    // </IntercomProvider>
  );
};
export default WebAppRoot;
